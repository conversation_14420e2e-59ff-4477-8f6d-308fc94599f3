<template>
  <el-radio-group v-model="localProjectType" class="project-type-filter" @change="handleProjectTypeChange">
    <el-radio-button label="SPCB">SPCB</el-radio-button>
    <el-radio-button label="SPCT">SPCT</el-radio-button>
  </el-radio-group>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  projectType: {
    type: String,
    default: 'SPCB'
  }
});

const emit = defineEmits(['update:projectType']);

const localProjectType = ref(props.projectType);

const handleProjectTypeChange = (value) => {
  emit('update:projectType', value);
};

watch(() => props.projectType, (newValue) => {
  localProjectType.value = newValue;
});
</script>

<style scoped>
.project-type-filter {
  margin-right: 10px;
}
</style>
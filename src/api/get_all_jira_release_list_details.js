import axios from 'axios'

axios.defaults.timeout = 50000

axios.interceptors.request.use(config => {
  // ...
  return config
}, error => {
  return Promise.error(error)
})

function get_all_jira_release_list_details() {
  return axios.get('https://autorelease.chatbot.shopee.io/api/get_all_jira_release_list_details'
  )
    .then(function (response) {
      // console.log(response);

      return response;
    })
    .catch(function (error) {
      console.log(error);
    });
}

export {
  get_all_jira_release_list_details
}

import axios from 'axios'

axios.defaults.timeout = 50000

axios.interceptors.request.use(config => {
  // ...
  return config
}, error => {
  return Promise.error(error)
})

function newMerge(params) {
  return axios.post('https://autorelease.chatbot.shopee.io/api/ar_start_merge', params
  )
    .then(function (response) {
      // console.log(response);

      return response.data;
    })
    .catch(function (error) {
      console.log(error);
    });
}

export {
  newMerge
}

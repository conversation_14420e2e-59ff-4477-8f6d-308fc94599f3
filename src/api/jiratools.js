// import request from '@/utils/request.js';
//
// export const merge = async (data) => {
//   return request({
//     url: 'http://10.105.37.105:8081/api/merge',
//     method: 'post',
//     data,
//   });
// };
import axios from 'axios'

axios.defaults.timeout = 50000

axios.interceptors.request.use(config => {
  // ...
  return config
}, error => {
  return Promise.error(error)
})

function autocheckdata(params) {
  return axios.post('https://autorelease.chatbot.shopee.io/api/jiratools', params
  )
    .then(function (response) {
      // console.log(response);

      return response.data;
    })
    .catch(function (error) {
      console.log(error);
    });
}

export {
  autocheckdata
}

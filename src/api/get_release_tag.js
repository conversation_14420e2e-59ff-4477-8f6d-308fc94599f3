import axios from 'axios'

axios.defaults.timeout = 50000

axios.interceptors.request.use(config => {
  // ...
  return config
}, error => {
  return Promise.error(error)
})

function get_release_tag() {
  return axios.get('https://autorelease.chatbot.shopee.io/api/get_jira_release_list'
  )
    .then(function (response) {
      // console.log(response);

      return response;
    })
    .catch(function (error) {
      console.log(error);
    });
}

export {
  get_release_tag
}

// import request from '@/utils/request.js';
//
// export const merge = async (data) => {
//   return request({
//     url: 'http://10.105.37.105:8081/api/merge',
//     method: 'post',
//     data,
//   });
// };
import axios from 'axios'

axios.defaults.timeout = 50000

axios.interceptors.request.use(config => {
  // ...
  return config
}, error => {
  return Promise.error(error)
})

function get_data_json() {
  return axios.get('https://autorelease.chatbot.shopee.io/api/read_data_json'
  )
    .then(function (response) {
      // console.log(response);

      return response;
    })
    .catch(function (error) {
      console.log(error);
    });
}

export {
  get_data_json
}

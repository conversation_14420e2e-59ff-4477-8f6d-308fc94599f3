/**
 * @description 项目简体中文配置
 * <AUTHOR> <EMAIL>
 */

export default {
  route: {
    home: '首页',
    // icons: '图标',
    // components: '组件',
    // eleComponents: 'Element 组件',
    // charts: '图表',
    // barChart: '柱状图表',
    // lineChart: '折线图',
    // mixedChart: '其他图表',
    // errorPages: '错误页面',
    // page401: '401',
    // page404: '404',
  },
  navbar: {
    logOut: '退出登录',
    github: '项目地址',
    theme: '切换换肤',
    full: '全屏',
    noFull: '退出全屏',
    refresh: '刷新',
    fold: '收起',
    unfold: '展开',
    size: '布局大小',
    profile: '个人中心',
  },
  login: {
    title: '登录',
    rules: {
      username: '请输入用户名',
      password: '请输入密码',
    },
    loginBtn: '登录',
    desc: '开箱即用的中后台管理系统',
    tip: '点击登录快速体验',
    username: '账号',
    password: '密码',
    // thirdparty: '第三方登录',
    rememberPwd: '记住密码',
    forgotPwd: '忘记密码',
  },
  // register: {
  //   title: '注册',
  //   registerBtn: '注册',
  //   username: '手机号',
  //   smsCode: '短信验证码',
  //   smsbtn: '获取验证码',
  //   password: '密码',
  //   confirmPwd: '确认密码',
  //   checkText: '我同意xxx隐私政策',
  // },
  theme: {
    change: '换肤',
    documentation: '换肤文档',
    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。',
    loading: '主题正在努力重置...',
    options: {
      theme1: '蓝白',
      theme2: '蓝黑',
      theme3: '绿白',
      theme4: '绿黑',
      theme5: '红白',
      theme6: '红黑',
    },
  },
  tagsView: {
    refresh: '重新加载',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeOthers: '关闭其它',
    closeAll: '关闭所有',
  },
  settings: {
    title: '主题设置',
    layout: '布局',
    theme: '主题',
    menuBg: '菜单主题',
    logo: 'Logo',
    tag: '标签',
    breadcurmb: '面包导航',
    fixed: '固定头部',
    fullscreen: '全屏',
    refresh: '刷新',
    notice: '通知',
    defaultBtn: '恢复默认',
    saveBtn: '保存',
  },
  layout: {
    vertical: '纵向',
    horizontal: '横向',
  },
  sayHi: {
    early: '早上好',
    morning: '上午好',
    noon: '中午好',
    afternoon: '下午好',
    evening: '晚上好',
  },
  notice: {
    msg: '欢迎登录',
  },
  // tabs: {
  //   notice: '通知',
  //   message: '消息',
  //   email: '邮件',
  // },
  indexPage: {
    descTitle: '开始您一天的工作吧！',
    resourceTitle: '自动发Merge（当前仅支持master到release，仅限QA使用，暂无其他功能）',
    autoTag: '自动打TAG（默认支持打release分支的TAG）',
    // orderTitle: '订单清单',
    order: {
      planned: '计划订单',
      finished: '已完成订单',
      unfinished: '未完成订单',
    },
    skillTitle: '技能列表',
    envTitle: '生产环境依赖信息',
    chartTitle: '基础平滑折线图',
  },
  errorPages: {
    title: '抱歉!',
    btn: '返回首页',
    404: {
      desc: '当前页面不存在...',
      remark: '请检查您输入的网址是否正确，或点击下面的按钮返回首页',
    },
    401: {
      desc: '你没有权限去该页面...',
      remark: '请联系管理员，或点击下面的按钮返回首页',
    },
  },
  echarts: {
    demo: '演示',
    line: {
      title: '折线图',
      demo1Title: '基础折线图',
      demo2Title: '基础平滑折线图',
      demo3Title: '堆叠面积图',
    },
    bar: {
      title: '柱状图',
      demo1Title: '基础柱状图',
      demo2Title: '某地区蒸发量和降水量',
    },
    other: {
      title: '其他图表',
      demo1Title: '基础 K 线图',
      demo2Title: '基础散点图',
      demo3Title: '圆角环形图',
      demo4Title: '基础雷达图',
      demo5Title: '数字动画仪表盘',
      demo6Title: '漏斗图',
    },
  },
  iconPage: {
    title: '图标库',
    demo: '演示',
    props: '属性',
    table: {
      label1: '参数',
      label2: '类型',
      label3: '可选值',
      label4: '默认值',
      label5: '说明',
    },
  },
  element: {
    title: 'Element-Plus 组件演示',
    btn: '按钮',
    radio: '单选',
    checkBox: '多选',
    datePicker: '日期选择器',
    dateTimePicker: '日期时间选择器',
  },
  confirm: {
    title: '操作提示',
    msg: '您确定要退出',
  },
  btn: {
    confirm: '确定',
    cancel: '取消',
  },
};

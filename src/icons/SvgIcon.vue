<template>
  <svg :class="className" :style="style" @click="handleClick">
    <use :xlink:href="iconName"/>
  </svg>
</template>
<script>
export default {
  props: {
    name: {
      type: String,
      required: true,
    },
    className: {
      type: String,
      default: '',
    },
    style: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    iconName() {
      return `#icon-${this.name}`;
      },
  },
  
  }

</script>

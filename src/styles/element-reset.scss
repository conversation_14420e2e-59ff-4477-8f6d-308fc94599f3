.el-popover.el-popper {
  min-width: 100px !important;
  padding: 5px 0 !important;
}
.el-menu-item {
  &.is-active {
    // background-color: $base-color-primary-light9 !important;
    border-right: $base-border-width-small solid $base-color-primary;
    span {
      color: $base-color-primary;
    }
  }
  &:hover {
    color: $base-color-primary !important;
    // background-color: $base-color-primary-light9 !important;
  }
}
.is-black {
  .el-menu-item {
    &.is-active {
      background-color: $base-color-primary !important;
      border-right: 0;
      span {
        color: $base-color-white;
      }
    }
    &:hover {
      color: $base-color-white !important;
      background-color: $base-color-primary !important;
    }
  }
}
.el-sub-menu__title:hover {
  background-color: transparent !important;
}
.el-menu--horizontal > .el-menu-item {
  &.is-active {
    border-right: none;
  }
}
.el-menu--horizontal {
  .el-menu-item {
    &.is-active {
      background-color: $base-color-primary !important;
      border-right: 0;
      span {
        color: $base-color-white;
      }
    }
    &:hover {
      color: $base-color-white !important;
      background-color: $base-color-primary !important;
    }
  }
  .el-sub-menu__title{
    color: $base-color-3 !important;
  }
 
}
.el-menu--horizontal.is-black{
  .el-sub-menu__title{
    color: $base-color-white !important;
  }
}
.el-sub-menu.is-black{
  .el-sub-menu__title{
    color: $base-color-white !important;
  }
}
.el-menu--collapse .el-menu-item{
  text-align: center;
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-sub-menu__title {
  height: $sub-menu__title-height !important;
  line-height: $sub-menu__title-height !important;
}

.el-header {
  --el-header-padding: 0;
}

.el-button--primary:active {
  background-color: $base-color-primary !important;
  border-color: $base-color-primary !important;
}

.el-menu--collapse .menu-icon{
  display: flex !important;
  justify-content: center;
}
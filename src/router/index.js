import {createRouter, createWebHashHistory, createWebHistory} from 'vue-router';
import monitor from '../views/monitor/monitor.vue';
import Layout from '@/layouts/index.vue';
import i18n from '@/locales';
import autorelease from '../views/autorelease/autorelease.vue';
import {IconPark} from '@icon-park/vue-next/es/all';
import {Van} from '@element-plus/icons-vue';
const { global } = i18n;
import HomeIcon from '@/icons/svg/bus.svg';


export const constantRoutes = [
  {
    path: '/',
    name: 'default',
    redirect: '/calendar',
    component: () => import('../views/calendar/calendar.vue'),
    hidden: true,
  },
  {
    path: '/index',
    name: 'Home',
    redirect: '/releasecheck',
    component: Layout,
    hidden: true,
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
    },
    hidden: true,
  },
  {
    path: '/401',
    name: '401',
    component: () => import('@/views/errorPage/401.vue'),
    hidden: true,
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/errorPage/404.vue'),
    hidden: true,
  },
];

export const asyncRoutes = [
  {
    path: '/:catchAll(.*)',
    component: Layout,
    redirect: '/calendar',
    name: 'calendar',
    children: [
      {
        path: '/calendar',
        name: 'calendar',
        component: () => import('../views/calendar/calendar.vue'),
        meta: {
          title: '发布日历',
          affix: true,
          icon: 'icon-calendar',
          keepAliveViews: true,
          noKeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/monitor',
    component: Layout,
    redirect: '/monitor',
    name: 'monitor',
    children: [
      {
        path: '/monitor',
        name: 'Monitor',
        component: () => import('../views/monitor/monitor.vue'),
        meta: {
          title: '部署监控',
          affix: true,
          icon: 'icon-home',
          keepAliveViews: true,
          noKeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/releasecheck',
    component: Layout,
    redirect: '/releasecheck',
    name: 'releasecheck',
    children: [
      {
        path: '/releasecheck',
        name: 'releasecheck',
        component: () => import('../views/autorelease/autorelease.vue'),
        meta: {
          title: '发布单检查',
          affix: true,
          icon: 'icon-bus-two',
          keepAliveViews: true,
          noKeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/releaseHistory',
    component: Layout,
    redirect: '/releaseHistory',
    name: 'releaseHistory',
    children: [
      {
        path: '/releaseHistory',
        name: 'releaseHistory',
        component: () => import('../views/releaseHistory/releaseHistory.vue'),
        meta: {
          title: '发布历史',
          affix: true,
          icon: 'icon-setting',
          keepAliveViews: true,
          noKeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/autorelease',
    component: Layout,
    redirect: '/autorelease',
    name: 'autorelease',
    children: [
      {
        path: '/autorelease',
        name: 'autorelease',
        component: () => import('../views/index/index.vue'),
        meta: {
          title: '自由发布（老天工）',
          affix: true,
          icon: 'icon-setting',
          keepAliveViews: true,
          noKeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/jiratools',
    component: Layout,
    redirect: '/jiratools',
    name: 'jiratools',
    children: [
      {
        path: '/jiratools',
        name: 'jiratools',
        component: () => import('../views/jiratools/jiratools.vue'),
        meta: {
          title: 'Jira工具',
          affix: true,
          icon: 'icon-setting',
          keepAliveViews: true,
          noKeepAlive: true,
        },
      },
    ],
  },



  // {
  //   path: '/monitor',
  //   name: 'monitor',
  //   component: monitor,
  // },
];
// const routers = [
//   { path: '/', component: () => import('../views/monitor/monitor_new.vue') },
//   { path: '/index', component: () => import('../views/index/index.vue') },
//   { path: '/autorelease', component: () => import('../views/autorelease/autorelease.vue') },
// ]

const router = createRouter({
  // history: createWebHashHistory(),
  history: createWebHistory(),
  routes: constantRoutes
  // routers
});

// reset router
export function resetRouter() {
  router.getRoutes().forEach((route) => {
    const { name } = route;
    if (name) {
      router.hasRoute(name) && router.removeRoute(name);
    }
  });
}

export default router;

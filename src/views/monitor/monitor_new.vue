<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" type="border-card">
    <el-tab-pane label="服务" name="first">
      <div class="index-conntainer">

        <el-card class="card" shadow="hover" width="50px" height="50px">

          <!--          <el-select v-model="selectedProject" placeholder="请选择项目组">-->
          <!--            <el-option-->
          <!--                v-for="project in projects"-->
          <!--                :key="project"-->
          <!--                :label="project"-->
          <!--                :value="project"-->
          <!--            ></el-option>-->
          <!--          </el-select>-->
          <el-table
              ref="tableRef"
              :data="failTableData"
              :columns="tableColumns"
              :row-class-name="tableRowClassName"
              style="width: auto; height: 100%"
              border
              :header-cell-style="{background:'#ee4d2d',color:'#f3f1f6'}"
          >
            <el-table-column
                prop="start_time"
                label="部署生效时间"
                sortable

                :default-sort="{ prop: 'start_time', order: 'descending' }"
            >
            </el-table-column>
            <el-table-column
                prop="end_time"
                label="任务结束时间"
                sortable
                :default-sort="{ prop: 'end_time', order: 'descending' }"
            >
            </el-table-column>
            <el-table-column
                prop="pipeline_name"
                label="服务"
                :min-width="200"
                :style="{ 'white-space': 'nowrap', 'min-width': 0 }"
            >
              <template #default="{ row }">
                <el-link
                    :underline="false"
                    v-bind:href="row.space_link"
                    target="_blank"
                    type="primary">
                  {{ row.pipeline_name }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="部署间隔" :min-width="60" header-></el-table-column>
            <el-table-column prop="build_type" label="类型" :min-width="55" header->
              <template #default="{ row }">
                <el-tag
                    :type="row.build_type === '灰度发布' ? 'info' : row.build_type === '全量发布' ? 'success' : 'warning'">
                  {{ row.build_type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="build_result" label="部署结果" :min-width="50" header->
              <template #default="{ row }">
                <el-tag :type="row.build_result === 'SUCCESS' ? 'success' : 'danger'">
                  {{ row.build_result }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="CID" label="部署地区" header-></el-table-column>
            <el-table-column
                prop="TAG"
                label="TAG"
                :filters="filters"
                :filter-method="filterHandler"
                :filtered-value="filteredValue"
                filter-placement="bottom-end"
                header-
            >
              <template #default="{ row }">
                <el-tag key="string" type="warning" class="mx-1" effect="light">
                  {{ row.TAG }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="executor" label="部署触发人" :min-width="65" header-></el-table-column>

          </el-table>

          <!--      <el-table-->
          <!--          :data="failTableData"-->
          <!--          :filter-method="handleFilter"-->
          <!--          border-->
          <!--          max-height="200"-->
          <!--          :empty-text="'No Fail Services'"-->
          <!--          :header-cell-style="{background:'#ee4d2d',color:'#f3f1f6'}"-->

          <!--      >-->
          <!--        <el-table-column prop="pipeline_name" label="Fail Services" :min-width="150"></el-table-column>-->
          <!--        <el-table-column prop="TAG" label="TAG" :min-width="150" header->-->
          <!--          <template #default="{ row }">-->
          <!--            <el-tag key="string" type="warning" class="mx-1" effect="light">-->
          <!--              {{ row.TAG }}-->
          <!--            </el-tag>-->
          <!--          </template>-->
          <!--        </el-table-column>-->
          <!--        <el-table-column prop="err_msg" label="Fail Reasion" :min-width="150" header-></el-table-column>-->
          <!--        <el-table-column prop="space_link" label="SPACE" :min-width="40" header->-->
          <!--          <template #default="{ row }">-->
          <!--            <el-link-->
          <!--                v-bind:href="row.space_link"-->
          <!--                target="_blank"-->
          <!--                :underline="false"-->
          <!--                type="primary">-->
          <!--              <el-icon class="el-icon&#45;&#45;right">-->
          <!--                <icon-view/>-->
          <!--              </el-icon>-->
          <!--              link-->
          <!--            </el-link>-->
          <!--          </template>-->
          <!--        </el-table-column>-->
          <!--        <el-table-column prop="build_result" label="Status" header->-->
          <!--          <template #default="{ row }">-->
          <!--            <el-tag :type="'danger'">-->
          <!--              {{ row.build_result }}-->
          <!--            </el-tag>-->
          <!--          </template>-->

          <!--        </el-table-column>-->
          <!--      </el-table>-->

        </el-card>

        <el-card shadow="hover">
          <!--      <h3>TAG Filters</h3>-->
          <el-select v-model="selected"
                     filterable
                     placeholder="请选择TAG来进行筛选"
                     clearable
          >
            <el-option
                v-for="tag in searchList"
                :key="tag"
                :label="tag"
                :value="tag"
            />
          </el-select>
          <!--          <el-button type="primary" @click="newclearFilter">Reset Filters</el-button>-->
          <!--          <el-button type="success" @click="refreshPage">Refresh Page</el-button>-->

          <el-table
              ref="tableRef"
              :data="filteredData"
              :columns="tableColumns"
              :row-class-name="tableRowClassName"
              style="width: auto; height: 100%"
              border
              :header-cell-style="{background:'#eef1f6',color:'#606266'}"
          >
            <el-table-column
                prop="start_time"
                label="部署生效时间"
                sortable

                :default-sort="{ prop: 'start_time', order: 'descending' }"
            >
            </el-table-column>
            <el-table-column
                prop="end_time"
                label="任务结束时间"
                sortable
                :default-sort="{ prop: 'end_time', order: 'descending' }"
            >
            </el-table-column>
            <el-table-column
                prop="pipeline_name"
                label="服务"
                :min-width="200"
                :style="{ 'white-space': 'nowrap', 'min-width': 0 }"
            >
              <template #default="{ row }">
                <el-link
                    :underline="false"
                    v-bind:href="row.space_link"
                    target="_blank"
                    type="primary">
                  {{ row.pipeline_name }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="部署间隔" :min-width="60" header-></el-table-column>
            <el-table-column prop="build_type" label="类型" :min-width="55" header->
              <template #default="{ row }">
                <el-tag
                    :type="row.build_type === '灰度发布' ? 'info' : row.build_type === '全量发布' ? 'success' : 'warning'">
                  {{ row.build_type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="build_result" label="部署结果" :min-width="50" header->
              <template #default="{ row }">
                <el-tag :type="row.build_result === 'SUCCESS' ? 'success' : 'danger'">
                  {{ row.build_result }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="CID" label="部署地区" header-></el-table-column>
            <el-table-column
                prop="TAG"
                label="TAG"
                :filters="filters"
                :filter-method="filterHandler"
                :filtered-value="filteredValue"
                filter-placement="bottom-end"
                header-
            >
              <template #default="{ row }">
                <el-tag key="string" type="warning" class="mx-1" effect="light">
                  {{ row.TAG }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="executor" label="部署触发人" :min-width="65" header-></el-table-column>

          </el-table>
        </el-card>

      </div>
    </el-tab-pane>

    <el-tab-pane label="配置" name="second"></el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import {ref, onMounted, computed, watch, reactive, watchEffect, nextTick, onUnmounted} from 'vue';
import type {ElTree, TabsPaneContext} from 'element-plus'
import {
  ElPagination,
  ElCard,
  ElTable,
  ElTableColumn,
  ElTag,
  ElSelect,
  ElOption,
  TableColumnCtx,
  TableInstance,
} from 'element-plus';
import axios from 'axios';
import {Edit, View as IconView} from '@element-plus/icons-vue';
import {ChatLineRound, Male} from '@element-plus/icons-vue';
import {get_data_json} from '@/api/get_data_json';
const activeName = ref('first');
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
const currentPage = ref(1);
const value = ref('')
const selected = ref('')
let failCount = ref(0);
// 每页显示条数
const pageSize = ref(10);

// 是否显示分页器
const pagination = ref(true);
const deployments = ref([]);
const selectedProject = ref('chatbot');
// const tableData = reactive([]);
const filters = reactive([]);
const searchList = reactive([]);
const projects = reactive([
  "chatbot", "inhouse", "channel", "data",
]);
const tagSet = new Set(); // 用来存储已经添加过的 TAG 值
interface User {
  pipeline_name: string
  build_type: string
  duration: string
  CID: string
  start_time: string
  end_time: string
  TAG: string
  space_link: string
  executor: string
  build_result: string
  err_msg: string
}

const tableData: User[] = reactive([]);
const tableRef = ref<TableInstance>()
const newTableData: User[] = reactive([]);
const failTableData = reactive([]);
const refreshPage = () => {
  location.reload();
}
// TODO: improvement typing when refactor table
const clearFilter = () => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  tableRef.value!.clearFilter()
}

const newclearFilter = () => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  selected.value = null
}
const formatter = (row: User, column: TableColumnCtx<User>) => {
  return row.pipeline_name
}
const filterTag = (value: string, row: User) => {
  return row.TAG === value
}
const filteredData = computed(() => {
  return selected.value ? newTableData.filter(item => item.TAG === selected.value) : newTableData
})
const filterHandler = (
    value: string,
    row: User,
    column: TableColumnCtx<User>
) => {
  const property = column['property']

  return row[property] === value
}

const warningRow = () => {
  return 'warning-row'
}

const tableRowClassName = ({
                             row,
                           }: {
  row: User
}) => {
  if (row.build_result === 'FAILURE') {
    return 'warning-row'
  }
  return ''
}

const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  newtableData: {
    type: Array,
    default: () => []
  }
})

function processArray(array) {
  console.log(array)
  const new_data = reactive([]);
  const latestEndTimeMap = new Map();
  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    const {pipeline_name, end_time, build_result} = item;
    const latestEndTime = latestEndTimeMap.get(pipeline_name);
    if (!latestEndTime || new Date(end_time) > new Date(latestEndTime)) {
      latestEndTimeMap.set(pipeline_name, end_time);
    }
  }

  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    const {pipeline_name, end_time, build_result} = item;
    if (build_result === 'FAILURE' && end_time === latestEndTimeMap.get(pipeline_name)) {
      new_data.push(item);
    }
  }
  return new_data;
}

const fetchData = async () => {
  const response = await get_data_json();
  const data = response.data.data;
  failTableData.splice(0, failTableData.length);
  newTableData.splice(0, newTableData.length);
  tableData.splice(0, tableData.length);
  tableData.push(...data);
  console.log(tableData);
  const temp_fail_data = processArray(tableData);
  console.log(temp_fail_data);
  failTableData.push(...temp_fail_data);
  console.log(failTableData);
  tableData.forEach((item, index) => {

    if (item.TAG && !tagSet.has(item.TAG)) {
      tagSet.add(item.TAG);
      filters.push({
        text: item.TAG,
        value: item.TAG,
      });
      searchList.push(item.TAG);
    }
    // console.log(index)
    // if (item.build_result === "FAILURE") {
    //   const latestEndTime = latestEndTimeMap.get(pipeline_name);
    //   if (!latestEndTime || new Date(end_time) > new Date(latestEndTime)) {
    //     latestEndTimeMap.set(pipeline_name, end_time);
    //     failTableData.push(
    //         {
    //           pipeline_name: item.pipeline_name,
    //           TAG: item.TAG,
    //           space_link: item.space_link,
    //           build_result: item.build_result,
    //           err_msg: item.err_msg,
    //           executor: item.executor,
    //           end_time: item.end_time,
    //           build_type: item.build_type,
    //           duration: item.duration,
    //           start_time: item.start_time,
    //           cid: item.CID,
    //         }
    //     );
    //   }
    //   //   failTableData.push(
    //   //     {
    //   //       pipeline_name: item.pipeline_name,
    //   //       TAG: item.TAG,
    //   //       space_link: item.space_link,
    //   //       build_result: item.build_result,
    //   //       err_msg: item.err_msg,
    //   //       executor: item.executor,
    //   //       end_time: item.end_time,
    //   //       build_type: item.build_type,
    //   //       duration: item.duration,
    //   //       start_time: item.start_time,
    //   //       cid: item.CID,
    //   //     }
    //   // )
    // }
    const everyone: User = {
      pipeline_name: item.pipeline_name,
      build_type: item.build_type,
      duration: item.duration,
      CID: item.CID,
      start_time: item.start_time,
      end_time: item.end_time,
      TAG: item.TAG,
      space_link: item.space_link,
      executor: item.executor,
      build_result: item.build_result,
      err_msg: item.err_msg,
    };
    if (item.TAG && !tagSet.has(item.TAG)) {
      tagSet.add(item.TAG);
      filters.push({
        text: item.TAG,
        value: item.TAG,
      });
      searchList.push(item.TAG);
    }
    newTableData.push(everyone)
  });
  // 发送请求到后台获取数据   // ...
}
// 模拟获取到的数据
//  const responseData = '这是从后台获取到的数据';

// 更新响应式数据
//  data.value = responseData;
// };
const intervalRef = ref(null);

onMounted(async () => {
  fetchData();
  const intervalId = setInterval(fetchData, 60000); // 每隔一分钟执行一次
  intervalRef.value = intervalId;
});
onUnmounted(() => {
      clearInterval(intervalRef.value);
    }
);

</script>

<style scoped>
.index-container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

div {
  font-size: 12px;
  margin: 5px;
  border: 1px;
  padding: 0;
}

</style>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}

.table-header {
  background-color: blue;
  color: white;
}
</style>

<style lang="scss" scoped>

.index-conntainer {
  //width: $base-width;
  .head-card {
    display: flex;
    align-items: center;
    padding: $base-main-padding;
    background-color: $base-color-white;

    &-content {
      padding-left: 15px;

      .desc {
        color: $base-font-color;
      }
    }
  }

  .content {
    margin: 5px 10px;
    display: flex;
    flex-direction: row; // 指定子元素在一行上排列
    justify-content: space-between; // 指定子元素之间的间距和位置
    .count-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        display: flex;
        flex-direction: column;
        text-align: center;

        .label {
          padding: 10px 0;
          font-size: $base-font-size-big;
        }

        .count {
          font-size: $base-font-size-max;
          font-weight: bolder;
          color: $base-color-primary;

          &.error {
            color: var(--el-color-danger);
          }

          &.success {
            color: var(--el-color-success);
          }
        }
      }
    }

    .title {
      margin: 0;
    }

    .skill-title {
      padding: 10px 0;
      font-weight: 500;
    }

    .card {
      margin-bottom: 15px;
      flex: 1;

      &-body {
        display: flex;
        flex-direction: row;
        //display: grid;
        grid-template-columns: repeat(4, 1fr);

        &.mobile {
          grid-template-columns: repeat(1, 1fr);
        }

        .item {
          box-sizing: border-box;
          padding: 10px 20px;
          margin-top: -1px;
          margin-left: -1px;
          overflow: hidden;
          cursor: pointer;
          border: 1px solid black;
          border: 1px solid #eee;
          transition: box-shadow 0.5;

          .lf {
            display: flex;
            align-items: center;
            max-width: 140px;

            .img {
              width: auto;
              max-width: 120px;
              height: auto;
              max-height: 40px;
            }
          }

          &:hover {
            box-shadow: $base-box-shadow;
          }

          .title {
            padding-left: 5px;
            font-size: 12px;
            font-weight: bold;
          }

          .desc {
            padding: 5px 0;
            font-size: 12px;
            line-height: 1.5;
            color: $base-font-color;
          }
        }
      }
    }
  }

}

.el-card + .el-card {
  margin-top: 20px;
}
</style>

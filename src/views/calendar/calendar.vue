<template>
  <div class="index-conntainer">
    <div class="content">
      <el-row :gutter="15">

        <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
          <el-card class="card" shadow="hover">
            <template #header>
              <h3 class="title">历史数据总览</h3>
            </template>
            <div class="count-box">
              <div class="item" v-for="(item, index) in orderList" :key="index">
                <span class="label">{{ t(item.key) }}</span>
                <CountTo
                  class="count"
                  :class="item.status"
                  :startVal="0"
                  :endVal="item.value"
                  :duration="3000"
                ></CountTo>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
          <el-card class="card" shadow="hover">
                      <el-table :data="tableData" height="117" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                        <el-table-column prop="date" label="发布日期" />
                        <el-table-column prop="name" label="发布单">
                          <template #default="{ row }">
                            <el-link
                              :underline="false"
                              v-bind:href="row.url"
                              target="_blank"
                              type="primary">
                              {{ row.name }}
                            </el-link>
                          </template>
                        </el-table-column>
                        <el-table-column prop="count" label="关联发布需求数量" />
                      </el-table>
          </el-card>
        </el-col>
<!--        <el-col>-->
<!--          <el-card class="card" shadow="hover">-->
<!--          <el-table :data="tableData" height="250" style="width: 100%">-->
<!--            <el-table-column prop="date" label="Date" width="180" />-->
<!--            <el-table-column prop="name" label="Name" width="180" />-->
<!--            <el-table-column prop="address" label="Address" />-->
<!--          </el-table>-->
<!--          </el-card>-->
<!--        </el-col>-->
        <el-col >
    <el-card class="card" shadow="hover" width="50px" height="50px">
      <template #header>
        <h3 class="title">发布详细日历</h3>
      </template>
      <div class="calendar-container">
      <el-calendar>
    <template #date-cell="{ data }">
      <p :class="data.isSelected ? 'is-selected' : ''">
        {{ data.day.split('-').slice(1).join('-') }}
        <br>
        <br>
        <el-link
          :underline="false"
          v-for="title in maketitle(data.day)"
          :key="title[1]"
          :href="title[1]"
          target="_blank"
          type="primary">
          {{ title[0] }}
        </el-link>
<!--        {{ maketitle(data.day) }}-->
      </p>
    </template>
  </el-calendar>
      </div>
    </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {getCalendarJiraReleaseList} from '@/api/getCalendarJiraReleaseList';
import {onMounted, reactive, ref, toRefs} from "vue";
import {TabsPaneContext} from "element-plus";
import {useI18n} from "vue-i18n";
import { CountTo } from 'vue3-count-to';
import {getAllJiraReleaseList} from '@/api/getAllJiraReleaseList';
import {get_all_jira_release_list_details} from '@/api/get_all_jira_release_list_details';
import packpage from '../../../package.json';
//import {consoleLog} from "echarts/types/src/util/log";
const formInline = reactive({
  user: '',
  region: '',
  date: '',
})
const onSubmit = () => {
  console.log('submit!')
}
const tableData = ref([]);

const {t} = useI18n();

const hour = new Date().getHours();
const state = reactive({
  list: [],
  prefix: '',
  orderList: [],
  skillList: [],
});
const thisTime =
  hour < 8
    ? t('sayHi.early')
    : hour <= 11
      ? t('sayHi.morning')
      : hour <= 13
        ? t('sayHi.noon')
        : hour < 18
          ? t('sayHi.afternoon')
          : t('sayHi.evening');
const sayHi = ref(thisTime);
const releaseData = ref();
const allreleaselist = ref();
onMounted(async () => {
  const releaseList = await getCalendarJiraReleaseList();
  releaseData.value = releaseList;
});
onMounted(async () => {
  const tempallreleaselist = await getAllJiraReleaseList();
  allreleaselist.value = tempallreleaselist;
  orderList.value[0].value = allreleaselist.value.todo;
  orderList.value[1].value = allreleaselist.value.done;
});
onMounted(async () => {
  const tempDetailRelease = await get_all_jira_release_list_details();
  console.log(tempDetailRelease.data);

  tableData.value = tempDetailRelease.data.data;
  console.log(tableData);
});
const orderList = ref([
  {
    key: '准备发布',
    value: 0,
    status: 'primary',
  },
  {
    key: '已完成发布',
    value: 0,
    status: 'success',
  },
  {
    key: '发布失败',
    value: 0,
    status: 'error',
  },
]);

const maketitle = (value) => {
  const tempdata = toRefs(releaseData.value);
  const finaldata = [];
  // 遍历键和值
  for (const key in tempdata) {
    if (tempdata[key].value.date === value) {
      finaldata.push([key,tempdata[key].value.url]);

      //return [key,tempdata[key].value.url];
    }
  }
  return finaldata;
  //return "";
}

</script>

<style scoped>

</style>
<style>

.is-selected {
  color: #1989fa;
}
.is-release{
  color: #ef6464;
}
</style>

<style lang="scss" scoped>

.index-conntainer {
  //width: $base-width;
  .head-card {
    display: flex;
    align-items: center;
    padding: $base-main-padding;
    background-color: $base-color-white;

    &-content {
      padding-left: 15px;

      .desc {
        color: $base-font-color;
      }
    }
  }

  .content {
    margin: 5px 10px;
    display: flex;
    flex-direction: row; // 指定子元素在一行上排列
    justify-content: space-between; // 指定子元素之间的间距和位置
    .count-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        display: flex;
        flex-direction: column;
        text-align: center;

        .label {
          padding: 10px 0;
          font-size: $base-font-size-big;
        }

        .count {
          font-size: $base-font-size-max;
          font-weight: bolder;
          color: $base-color-primary;

          &.error {
            color: var(--el-color-danger);
          }

          &.success {
            color: var(--el-color-success);
          }
        }
      }
    }

    .title {
      margin: 0;
    }

    .skill-title {
      padding: 10px 0;
      font-weight: 500;
    }

    .card {
      margin-bottom: 15px;
      flex: 1;
      &-body {
        display: flex;
        flex-direction: row;
        //display: grid;
        grid-template-columns: repeat(4, 1fr);

        &.mobile {
          grid-template-columns: repeat(1, 1fr);
        }

        .item {
          box-sizing: border-box;
          padding: 10px 20px;
          margin-top: -1px;
          margin-left: -1px;
          overflow: hidden;
          cursor: pointer;
          border: 1px solid black;
          border: 1px solid #eee;
          transition: box-shadow 0.5;

          .lf {
            display: flex;
            align-items: center;
            max-width: 140px;

            .img {
              width: auto;
              max-width: 120px;
              height: auto;
              max-height: 40px;
            }
          }

          &:hover {
            box-shadow: $base-box-shadow;
          }

          .title {
            padding-left: 5px;
            font-size: 12px;
            font-weight: bold;
          }

          .desc {
            padding: 5px 0;
            font-size: 12px;
            line-height: 1.5;
            color: $base-font-color;
          }
        }
      }
    }
  }

}
.el-card + .el-card {
  margin-top: 20px;
}


</style>
<style>
.calendar-container {
  max-height: 1000px;
  overflow: auto;
}
.el-calendar .el-calendar-table td {
  height: 180px; /* 设置每个格子的高度 */
}


</style>



<template>
    <div class="index-conntainer">
        <transition name="el-fade-in-linear">
            <el-card class="card" shadow="hover" width="50px" height="50px">
                <!--      <el-tabs-->
                <!--        v-model="activeName"-->
                <!--        type="card"-->
                <!--        class="demo-tabs"-->
                <!--        @tab-click="handleClick"-->
                <!--      >-->
                <!--          <el-row :gutter="20">-->
                <!--            <el-col :span="10"-->
                <!--                    :style="{ display: 'flex', flexDirection: 'row' }"><h3>Release List Now（only show the 1st month list）</h3></el-col>-->
                <!--            <el-col :span="11"-->
                <!--                    :style="{ display: 'flex', flexDirection: 'row' }">-->
                <!--              <el-statistic :value="projects.length">-->
                <!--              </el-statistic>-->
                <!--            </el-col>-->
                <!--          </el-row>-->
                <!--      <h3>Select Release</h3>-->
                <div class="ar-container">
                    <el-select v-model="selectedProjectFinished"
                               filterable
                               clearable
                               placeholder="please select release ticket"
                               style="width: 380px;"
                    >

                        <el-option
                                v-for="project in projects"
                                :key="project"
                                :label="project"
                                :value="project"
                        ></el-option>
                    </el-select>

                    <!--      <h3>Release Feature List</h3>-->
                    <!--          <el-tooltip-->
                    <!--            class="box-item"-->
                    <!--            effect="customized"-->
                    <!--            content="click to start auto release"-->
                    <!--            placement="top-start"-->
                    <!--          >-->
                    <!--            <el-button-->
                    <!--              type="danger"-->
                    <!--              @click="centerDialogVisible = true"-->
                    <!--              size="default"-->
                    <!--              element-loading-text="AR are trying to verify the data, please wait..."-->
                    <!--              v-loading.fullscreen.lock="fullscreenLoading">Make MR-->
                    <!--            </el-button>-->
                    <!--          </el-tooltip>-->
                    <!--          <el-button type="warning" size="default" @click="getNewData(selectedProjectFinished)">Refresh Status</el-button>-->
                    <!--          <el-button type="info" size="default" @click="visible = true">-->
                    <!--            Show all fail deatil-->
                    <!--          </el-button>-->
                    <!--          <el-tooltip-->
                    <!--            class="box-item"-->
                    <!--            effect="customized"-->
                    <!--            content="click to copy MR message to  Clipboard"-->
                    <!--            placement="top-start"-->
                    <!--          >-->
                    <!--            <el-button type="primary"-->
                    <!--                       size="default"-->
                    <!--                       @click="copyToClipboard">-->
                    <!--              Copy MSG</el-button>-->
                    <!--          </el-tooltip>-->
                    <!--          <el-tooltip-->
                    <!--            class="box-item"-->
                    <!--            effect="customized"-->
                    <!--            content="click to make seatalk message"-->
                    <!--            placement="top-start"-->
                    <!--          >-->
                    <!--            <el-button type="primary" size="default" @click="callseatalk">-->
                    <!--              Seatalk Alert-->
                    <!--            </el-button>-->

                    <!--          </el-tooltip>-->

                    <!--          <el-tooltip-->
                    <!--            class="box-item"-->
                    <!--            effect="customized"-->
                    <!--            content="click to make seatalk message"-->
                    <!--            placement="top-start"-->
                    <!--          >-->
                    <!--            <el-button type="primary" size="default" @click="callMRseatalkFE">-->
                    <!--              MR message alert-->
                    <!--            </el-button>-->
                    <!--            &lt;!&ndash;      <el-button type="info" size="small" @click="callseatalk">&ndash;&gt;-->
                    <!--            &lt;!&ndash;        copyMSG&ndash;&gt;-->
                    <!--            &lt;!&ndash;      </el-button>&ndash;&gt;-->
                    <!--          </el-tooltip>-->
                    <el-drawer v-model="visible" :show-close="false">
                        <template #header="{ titleId, titleClass }">
                            <h4 :id="titleId" :class="titleClass">{{ selectedProjectFinished }}</h4>
                        </template>
                        暂无失败信息。
                    </el-drawer>
                </div>

                <!--      <div v-if="show">-->
                <!--        <el-steps :active="active" finish-status="success">-->
                <!--          <el-step title="check JIRA data" description="if fail,please check the reason"-->
                <!--                   :status="jiraStatus"/>-->
                <!--          <el-step title="merge to master" :status="masterStatus"/>-->
                <!--          <el-step title="config check" :status="configStatus"/>-->
                <!--          <el-step title="staging regress" :status="regressStatus"/>-->
                <!--          <el-step title="merge to release" :status="releaseStatus"/>-->
                <!--          <el-step title="make TAG" :status="tagStatus"/>-->
                <!--          <el-step title="pre build" :status="prebuildStatus"/>-->
                <!--          <el-step title="services orchestration" :status="serviceStatus"/>-->
                <!--          <el-step title="deploy live" :status="deployStatus"/>-->
                <!--          <el-step title="merge to test/uat" :status="nonliveStatus"/>-->
                <!--        </el-steps>-->
                <!--        <el-button style="margin-top: 12px"-->
                <!--                   @click="next"-->
                <!--                   size="small"-->
                <!--        >Next step</el-button>-->

                <!--      </div>-->


                <el-table
                        :data="releaseTableData"
                        stripe
                        border
                        max-height="800"

                        :empty-text="'No data'"
                        :header-cell-style="{background:'#eef1f6',color:'#606266'}"
                >

                    <!--        <el-table-column type="expand">-->
                    <!--          <template #default="props">-->
                    <!--            <div>-->
                    <!--              <h3>Merge Details</h3>-->
                    <!--              <el-table :data="props.row.merge_list" border-->
                    <!--                        :header-cell-style="{background:'#def1ce',color:'#606266'}"-->
                    <!--              >-->
                    <!--                <el-table-column label="Repo Name" prop="repo_name"/>-->
                    <!--                <el-table-column label="Branch Name" prop="branch_name"/>-->
                    <!--                <el-table-column label="URL" prop="web_url">-->
                    <!--                  <template #default="{row}">-->
                    <!--                    <a-->
                    <!--                      :href="row.web_url"-->
                    <!--                      target="_blank"-->
                    <!--                    >{{ row.web_url }}-->
                    <!--                    </a>-->
                    <!--                  </template>-->
                    <!--                </el-table-column>-->
                    <!--                <el-table-column label="status" prop="merge_status"/>-->
                    <!--              </el-table>-->
                    <!--            </div>-->
                    <!--          </template>-->
                    <!--        </el-table-column>-->
                    <el-table-column prop="jira_key" label="Key" :min-width="60" header-align="center" align="center"
                    >

                    </el-table-column>

                    <!--        <el-table-column prop="services" label="Services" :formatter="formatContent"></el-table-column>-->
                    <el-table-column prop="type"
                                     label="Type"
                                     header-align="center" align="center"
                                     min-width="50"
                                     :filters="[
                            { text: 'Epic', value: 'Epic' },
                            { text: 'Bug', value: 'Bug' },
                            { text: 'Task', value: 'Task' },
                            { text: 'Sub-task', value: 'Sub-task' },
                            { text: 'Story', value: 'Story' },
                            ]"
                                     :filter-method="filterType"
                                     filter-placement="bottom-end"
                    >
                        <template #default="{ row }">
                            <el-tag
                                    :type="row.type === 'Epic' ? 'success' : row.type === 'Sub-task' ? 'warning' : row.type === 'Task' ? 'warning': 'danger'">
                                {{ row.type }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column prop="jira_title" label="Title" :min-width="200" header-
                    >

                        <template #default="{ row }">
                            <el-link
                                    :underline="false"
                                    v-bind:href="row.jira_link"
                                    target="_blank"
                                    type="primary">
                                {{ row.jira_title }}
                            </el-link>
                        </template>
                    </el-table-column>

                    <!--        <el-table-column prop="result" label="Result" :min-width="50" header-align="center" align="center"-->
                    <!--                         >-->
                    <!--          <template #default="{ row }">-->
                    <!--            <el-icon-->
                    <!--              v-if="row.result === 'pass' ? true : false"-->
                    <!--              :size="20"-->
                    <!--              :color="row.result === 'pass' ? '#67c23a' : '#F56C67'"-->
                    <!--            >-->
                    <!--              <SuccessFilled/>-->
                    <!--            </el-icon>-->
                    <!--            <el-icon-->
                    <!--              v-if="row.result === 'fail' ? true : false"-->
                    <!--              :size="20"-->
                    <!--              :color="row.result === 'pass' ? '#67c23a' : '#F56C67'"-->
                    <!--            >-->
                    <!--              <CircleCloseFilled/>-->
                    <!--            </el-icon>-->
                    <!--          </template>-->
                    <!--          &lt;!&ndash;              <el-icon&ndash;&gt;-->
                    <!--          &lt;!&ndash;                  :size="20"&ndash;&gt;-->
                    <!--          &lt;!&ndash;                  color="#67c23a"&ndash;&gt;-->
                    <!--          &lt;!&ndash;                ><SuccessFilled/></el-icon>&ndash;&gt;-->
                    <!--        </el-table-column>-->
                    <el-table-column
                            prop="services"
                            label="Services"
                            :min-width="150"
                            header-align="center"
                            align="center"
                            :style="{ 'white-space': 'pre-wrap' }"
                    >
                        <template #default="{ row }">
                            <div v-if="row.services !== ''">
                                <template v-for="(service, index) in row.services.split('\n')">
                                    <div :style="{ color: !checkServiceGrouping(service) ? '#F56C67' : 'inherit' }">
                                        {{ service }}
                                    </div>
                                </template>
                            </div>
                            <el-icon
                                    v-if="row.services === ''"
                                    :size="20"
                                    :color="'#F56C67'"
                            >
                                <CircleCloseFilled/>
                            </el-icon>
                        </template>
                    </el-table-column>
                    <el-table-column label="Data check" header-align="center" align="center">
                        <el-table-column prop="sign_off" label="Sign off" header-align="center" align="center" min-width="35">
                            <!--            <template #default="{ row }">-->
                            <!--              {{ row.sign_off }}-->
                            <!--            </template>-->
                            <template #default="{ row }">
                                <el-text
                                        v-if="row.sign_off !== '' ? true : false"
                                        :size="20"
                                        :color="row.sign_off === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    {{ row.sign_off }}
                                </el-text>
                                <el-icon
                                        v-if="row.sign_off === '' ? true : false"
                                        :size="20"
                                        :color="row.sign_off === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    <CircleCloseFilled/>
                                </el-icon>
                            </template>
                        </el-table-column>
                        <el-table-column prop="region" label="Region" header-align="center" align="center" min-width="35" >
                            <template #default="{ row }">
                                <el-text
                                        v-if="row.region !== '' ? true : false"
                                        :size="20"
                                        :color="row.region === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    {{ row.region }}
                                </el-text>
                                <el-icon
                                        v-if="row.region === '' ? true : false"
                                        :size="20"
                                        :color="row.region === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    <CircleCloseFilled/>
                                </el-icon>
                            </template>
                        </el-table-column>
                        <el-table-column prop="config_center" label="Config center" header-align="center" align="center" min-width="40"
                        >
                            <template #default="{ row }">
                                <el-text
                                        v-if="row.config_center !== '' ? true : false"
                                        :size="20"
                                        :color="row.config_center === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    {{ row.config_center }}
                                </el-text>
                                <el-icon
                                        v-if="row.config_center === '' ? true : false"
                                        :size="20"
                                        :color="row.config_center === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    <CircleCloseFilled/>
                                </el-icon>
                            </template>
                        </el-table-column>
                        <el-table-column prop="redis_change" label="Redis change" header-align="center" align="center" min-width="40"
                        >
                            <template #default="{ row }">
                                <el-text
                                        v-if="row.redis_change !== '' ? true : false"
                                        :size="20"
                                        :color="row.redis_change === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    {{ row.redis_change }}
                                </el-text>
                                <el-icon
                                        v-if="row.redis_change === '' ? true : false"
                                        :size="20"
                                        :color="row.redis_change === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    <CircleCloseFilled/>
                                </el-icon>
                            </template>
                        </el-table-column>
                        <el-table-column prop="DB_Change" label="DB Change" header-align="center" align="center" min-width="40"
                        >
                            <template #default="{ row }">
                                <el-text
                                        v-if="row.DB_Change !== '' ? true : false"
                                        :size="20"
                                        :color="row.DB_Change === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    {{ row.DB_Change }}
                                </el-text>
                                <el-icon
                                        v-if="row.DB_Change === '' ? true : false"
                                        :size="20"
                                        :color="row.DB_Change === 'pass' ? '#67c23a' : '#F56C67'"
                                >
                                    <CircleCloseFilled/>
                                </el-icon>
                            </template>
                        </el-table-column>
                    </el-table-column>

                    <!--        <el-table-column prop="jira_link" label="JIRA" :min-width="40" header- -->
                    <!--                         >-->
                    <!--          <template #default="{ row }">-->
                    <!--            <el-link-->
                    <!--              v-bind:href="row.jira_link"-->
                    <!--              target="_blank"-->
                    <!--              type="primary">-->
                    <!--              link-->
                    <!--            </el-link>-->
                    <!--          </template>-->
                    <!--        </el-table-column>-->
                    <!--        <el-table-column prop="merge_result" label="Merge status" header- -->
                    <!--                         >-->
                    <!--          <template #default="{ row }">-->
                    <!--            <el-tag :type="'success'">-->
                    <!--              no merge-->
                    <!--              &lt;!&ndash;              {{ row.merge_result }}&ndash;&gt;-->
                    <!--            </el-tag>-->
                    <!--          </template>-->

                    <!--        </el-table-column>-->
                    <!--        <el-table-column prop="merge_result" label="Merge status" type="expand"-->
                    <!--        >-->
                    <!--          <template #default="{ row }">-->
                    <!--            <el-tag :type="'success'">-->
                    <!--              no merge-->
                    <!--              &lt;!&ndash;              {{ row.merge_result }}&ndash;&gt;-->
                    <!--            </el-tag>-->
                    <!--          </template>-->

                    <!--        </el-table-column>-->
                    <el-table-column type="expand" label="MR" min-width="20">

                        <template #default="props">
                            <div>
                                <h3>Merge Details</h3>
                                <el-table :data="props.row.merge_list" border
                                          :header-cell-style="{background:'#def1ce',color:'#606266'}"
                                >
                                    <el-table-column label="Repo Name" prop="repo_name"/>
                                    <el-table-column label="Branch Name" prop="branch_name"/>
                                    <el-table-column label="URL" prop="web_url">
                                        <template #default="{row}">
                                            <a
                                                    :href="row.web_url"
                                                    target="_blank"
                                            >{{ row.web_url }}
                                            </a>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="status" prop="merge_status"/>
                                </el-table>
                            </div>
                        </template>

                    </el-table-column>
                    <el-table-column prop="Code_Merged" label="Code Merged" header-align="center" align="center" min-width="40"
                    >
                        <template #default="{ row }">
                            <el-text
                                    v-if="row.Code_Merged !== '' ? true : false"
                                    :size="20"
                                    :color="row.Code_Merged === 'pass' ? '#67c23a' : '#F56C67'"
                            >
                                {{ row.Code_Merged }}
                            </el-text>
                            <el-icon
                                    v-if="row.Code_Merged === '' ? true : false"
                                    :size="20"
                                    :color="row.Code_Merged === 'pass' ? '#67c23a' : '#F56C67'"
                            >
                                <CircleCloseFilled/>
                            </el-icon>
                        </template>
                    </el-table-column>
                    <!--          <el-table-column prop="merge_result" label="Merge status"-->
                    <!--          >-->
                    <!--            <template #default="{ row }">-->
                    <!--              <el-tag :type="'success'">-->
                    <!--                no merge-->
                    <!--                &lt;!&ndash;              {{ row.merge_result }}&ndash;&gt;-->
                    <!--              </el-tag>-->
                    <!--            </template>-->

                    <!--          </el-table-column>-->
                    <!--          <el-table-column-->
                    <!--            prop="operation"-->
                    <!--            label="Operate"-->
                    <!--            :min-width="55"-->
                    <!--            fixed="right"-->

                    <!--          >-->
                    <!--            <el-button-->
                    <!--              type="danger"-->
                    <!--              size="small"-->
                    <!--              @click="copyToClipboard"-->
                    <!--            >Copy-->
                    <!--            </el-button>-->
                    <!--          </el-table-column>-->
                </el-table>

                <!--        </el-tab-pane>-->
                <!--        <el-tab-pane label="服务编排" name="second">-->
                <el-scrollbar v-if="showfe">
                    <p v-for="item in 1" :key="item" class="scrollbar-fe-item">FE services</p>
                    <div class="data-list">
                        <div v-for="item in fe_services" :key="item.id" class="data-item">
                            <div class="data-item__title">服务名</div>
                            <div class="data-item__content">{{ item }}</div>
                        </div>
                    </div>
                </el-scrollbar>

                <el-scrollbar v-if="showbe">
                    <p v-for="item in 1" :key="item" class="scrollbar-be-item">BE services</p>
                    <div class="data-list">
                        <div v-for="item in be_services" :key="item.id" class="data-item">
                            <div class="data-item__title">服务名</div>

                            <div class="data-item__content">{{ item }}</div>
                        </div>
                    </div>
                </el-scrollbar>
                <!--      <el-card-->
                <!--        v-if="services_pane"-->
                <!--      >-->
                <!--        <h3>AR has been sorted by default. If you need to modify the order, please drag and drop the-->
                <!--          list below to sort, thank you.</h3>-->
                <!--        <el-button type="warning" @click="refreshPage">Save changes</el-button>-->
                <!--        //这一段拖动逻辑暂时下线了-->
                <!--        <div class="itxst">-->
                <!--          <div>-->
                <!--            <draggable-->
                <!--              :list="all_services"-->
                <!--              ghost-class="ghost"-->
                <!--              chosen-class="chosenClass"-->
                <!--              animation="300"-->
                <!--              @start="onStart"-->
                <!--              @end="onEnd"-->
                <!--            >-->
                <!--              <template #item="{ element }">-->
                <!--                <div class="item">-->
                <!--                  {{ element }}-->
                <!--                </div>-->
                <!--              </template>-->
                <!--            </draggable>-->
                <!--          </div>-->
                <!--        </div>-->
                <!--      </el-card>-->
                <el-empty description="暂无内容"/>
                <!--        </el-tab-pane>-->
                <!--        <el-tab-pane label="live部署" name="third">-->
                <!--          <el-card>-->
                <!--            <h3>部署</h3>-->
                <!--            <el-button type="success" @click="refreshPage">启动部署</el-button>-->
                <!--          </el-card>-->
                <!--          <el-card>-->
                <!--            <h3>已完成部署的服务</h3>-->
                <!--          </el-card>-->
                <!--          <el-card>-->
                <!--            <h3>正在部署的服务</h3>-->
                <!--          </el-card>-->
                <!--          <el-card>-->
                <!--            <h3>服务回滚</h3>-->
                <!--            <el-button type="primary" @click="refreshPage">单次回滚</el-button>-->
                <!--            <el-button type="primary" @click="refreshPage">全量回滚</el-button>-->
                <!--          </el-card>-->
                <!--            <el-empty description="No data" /></el-tab-pane>-->
                <!--      </el-tabs>-->


            </el-card>
        </transition>
        <!--    <transition name="el-fade-in-linear">-->
        <!--      <el-card class="card" shadow="hover" width="50px" height="50px">-->
        <!--        <h3>Release History</h3>-->


        <!--        <el-table-->
        <!--          :data="projects"-->
        <!--          stripe-->
        <!--          border-->
        <!--          max-height="250"-->
        <!--          :empty-text="'No data'"-->
        <!--          :header-cell-style="{background:'#eef1f6',color:'#606266'}"-->

        <!--        >-->
        <!--          <el-table-column prop="projects" label="Release key" :min-width="50" header- -->
        <!--          ></el-table-column>-->
        <!--          <el-table-column prop="projects" label="Title" :min-width="150" header- -->
        <!--          >-->

        <!--            <template #default="{ row }">-->
        <!--              {{ row }}-->
        <!--              &lt;!&ndash;            <el-tag key="string" type="success" class="mx-1" effect="light">&ndash;&gt;-->
        <!--              &lt;!&ndash;              {{ row.jira_title }}&ndash;&gt;-->
        <!--              &lt;!&ndash;            </el-tag>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column prop="jira_result" label="Release date" :min-width="150"-->
        <!--                           header- >-->
        <!--            <template #default="{ row }">-->
        <!--              2023年05月31日23:51:45-->
        <!--            </template>-->
        <!--          </el-table-column>-->

        <!--          <el-table-column prop="jira_link" label="JIRA" :min-width="40" header- -->
        <!--          >-->
        <!--            <template #default="{ row }">-->
        <!--              <el-link-->
        <!--                v-bind:href="row.jira_link"-->
        <!--                target="_blank"-->
        <!--                type="primary">-->
        <!--                <el-icon class="el-icon&#45;&#45;right">-->
        <!--                  <icon-view/>-->
        <!--                </el-icon>-->
        <!--                link-->
        <!--              </el-link>-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column prop="merge_result" label="Release result" header- -->
        <!--          >-->
        <!--            <template #default="{ row }">-->
        <!--              <el-tag :type="'success'">-->
        <!--                发布成功-->
        <!--                &lt;!&ndash;              {{ row.merge_result }}&ndash;&gt;-->
        <!--              </el-tag>-->
        <!--            </template>-->

        <!--          </el-table-column>-->
        <!--          <el-table-column-->
        <!--            prop="operation"-->
        <!--            label="Operate"-->
        <!--            :min-width="50"-->
        <!--            fixed="right"-->
        <!--            header- -->

        <!--          >-->
        <!--            <el-button-->
        <!--              type="success"-->
        <!--              size="small"-->
        <!--              @click="refreshPage"-->
        <!--            >Show detail-->
        <!--            </el-button>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->

        <!--      </el-card>-->
        <!--    </transition>-->
    </div>
    <!--  <el-backtop :bottom="100">-->
    <!--    <div-->
    <!--      style="-->
    <!--        height: 100%;-->
    <!--        width: 100%;-->
    <!--        background-color: var(&#45;&#45;el-bg-color-overlay);-->
    <!--        box-shadow: var(&#45;&#45;el-box-shadow-lighter);-->
    <!--        text-align: center;-->
    <!--        line-height: 40px;-->
    <!--        color: #1989fa;-->
    <!--      "-->
    <!--    >-->
    <!--      UP-->
    <!--    </div>-->
    <!--  </el-backtop>-->

    <el-dialog
            v-model="centerDialogVisible"
            title="Warning"
            width="30%"
            align-center
    >
        <span>Are you sure to begin AR merge?</span>
        <template #footer>
      <span class="dialog-footer">
        <el-button @click="centerDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="startAR">
          Confirm
        </el-button>
      </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import {ref, onMounted, computed, watch, reactive, watchEffect, nextTick} from 'vue';
    import type {ElTree} from 'element-plus'
    import draggable from "vuedraggable";
    import {copymsg} from '@/api/copymsg';


    // const copyToClipboard = () => {
    //   navigator.clipboard.writeText(textToCopy.value)
    //     .then(() => {
    //       alert('已复制到剪贴板');
    //     })
    //     .catch((error) => {
    //       console.error('复制到剪贴板失败:', error);
    //     });
    // };


    import {
        ElPagination,
        ElCard,
        ElTable,
        ElTableColumn,
        ElTag,
        ElSelect,
        ElOption,
        ElMessage,
        ElMessageBox,
        TableColumnCtx,
        TableInstance,
    } from 'element-plus';
    import axios from 'axios';
    import {Edit, View as IconView} from '@element-plus/icons-vue';
    import {ChatLineRound, Male} from '@element-plus/icons-vue';
    import {read_json} from '@/api/read_json';
    import {get_release_tag} from '@/api/get_release_tag';
    import {get_jira_release_list_finished} from '@/api/get_jira_release_list_finished';
    import {send_title} from '@/api/send_title';
    import {startAuto} from '@/api/startAuto';
    import {autocheckdata} from '@/api/autocheckdata';
    import {seatalk} from '@/api/seatalk';
    import {callMRseatalk} from '@/api/callMRseatalk';
    import {autochecknewdata} from '@/api/autochecknewdata';
    import {ElButton, ElDrawer} from 'element-plus'
    import {CircleCloseFilled} from '@element-plus/icons-vue'
    import type {TabsPaneContext} from 'element-plus'
    import {consoleLog} from "echarts/types/src/util/log";




    const dialogTableVisible = ref(false)
    const centerDialogVisible = ref(false)
    const activeName = ref('first')
    const handleClick = (tab: TabsPaneContext, event: Event) => {
        console.log(tab, event)
    }
    const fe_services = reactive([]);
    const be_services = reactive([]);
    // const checkresult = reactive([]);
    const all_services = reactive([]);
    // let sign_off = reactive([]);
    // let region = reactive([]);
    // let config_center = reactive([]);
    // let redis_change = reactive([]);
    let services_pane = ref(false);
    let visible = ref(false);
    let show = ref(false);
    let showfe = ref(false);
    let showbe = ref(false);
    let showresult = ref(false);
    let showresultsuccess = ref(false);
    let showresultfail = ref(false);
    let active = ref(0);
    let jiraStatus = ref("wait");
    let masterStatus = ref("wait");
    let releaseStatus = ref("wait");
    let configStatus = ref("wait");
    let tagStatus = ref("wait");
    let prebuildStatus = ref("wait");
    let serviceStatus = ref("wait");
    let deployStatus = ref("wait");
    let nonliveStatus = ref("wait");

    const next = () => {
        console.log(active.value)
        if (active.value++ > 9)
            active.value = 0
    }
    const selectedProjectFinished = ref();
    const value = ref('');
    // 每页显示条数
    const pageSize = ref(10);
    const fullscreenLoading = ref(false)
    // const popupShown = localStorage.getItem('popupShown') === 'true'
    // if (!popupShown) {
    //   ElMessageBox({
    //     title: 'AR-自动发布',
    //     message: '<span style="color: red;">当前页面正在开发，请谨慎使用</span>',
    //     confirmButtonText: '确定',
    //     type: 'warning',
    //     dangerouslyUseHTMLString: true
    //   }).then(() => {
    //     localStorage.setItem('popupShown', 'true')
    //   })
    // }
    interface User {
        release_title: string
        jira_key: string
        jira_title: string
        jira_link: string
        type: string
    }

    const filterType = (value: string, row: User) => {
        return row.type === value
    }

    const selectedRelease = ref();
    // const tableData = reactive([]);
    const projects = reactive([]);

    // sconst getIconText = computed((iconvalue) => {
    //   if (iconvalue === 'pass') {
    //     return '<SuccessFilled/>';
    //   } else {
    //     return '<CircleClose/>';
    //   }
    // });
    function getIconText(value) {
        console.log(value)
        if (value === 'pass') {
            console.log("1111111111111111111111111111111111111111111111111111")
            return "PASS";
        } else {
            return "FAIL";
        }
    }
    // const textToCopy = ref('1111111111');

    function getIconColor(value) {
        console.log(value)
        if (value === 'pass') {
            console.log("1111111111111111111111111111111111111111111111111111")
            return '#67c23a';
        } else {
            console.log("222222222222222222222222222222222222222222222222")
            return '#F56C67';
        }
    }
    // let cachedData = localStorage.getItem('releaseTableData')
    //
    // // 如果缓存存在，则将其用作响应式数据
    // let releaseTableData = reactive(cachedData ? JSON.parse(cachedData) : [])
    let releaseTableData = reactive( []);
    // 监听变量的更改，并在更改时更新缓存
    watch(releaseTableData, () => {
        localStorage.setItem('releaseTableData', JSON.stringify(releaseTableData))
    })
    // let releaseTableData = reactive([]);
    // let releaseTableData = ref([]);

    const tableRef = ref<TableInstance>()
    const failTableData = reactive([]);
    const refreshPage = () => {
        location.reload();
    }
    //拖拽开始的事件
    const onStart = () => {
        console.log("开始拖拽");
    };


    //拖拽结束的事件
    const onEnd = () => {
        console.log("结束拖拽");
    };

    const callseatalk = (tab: TabsPaneContext, event: Event) => {
        let fin_data = {
            jira_title: selectedProjectFinished.value,
        };
        seatalk(fin_data);
        console.log(tab, event)
        //startAuto(releaseTableData)
    }

    const callMRseatalkFE = (tab: TabsPaneContext, event: Event) => {
        let fin_data = {
            jira_title: selectedProjectFinished.value,
        };
        callMRseatalk(fin_data);
    }
    async function copyToClipboard() {
        let data_final = ref();
        let fin_data = {
            jira_title: selectedProjectFinished.value,
        };
        data_final = await copymsg(fin_data);

        console.log(data_final)
        navigator.clipboard.writeText(data_final.data)
            .then(() => {
                ElMessage({
                    message: '恭喜，MR信息已复制到剪切板！',
                    type: 'success',
                })
                //alert('已复制到剪贴板');
            })
            .catch((error) => {
                ElMessage.error('复制剪切板失败！')
            });
    }
    async function startAR() {
        centerDialogVisible.value = false
        fullscreenLoading.value = true
        let data_final = ref();
        data_final = await startAuto(releaseTableData);

        fullscreenLoading.value = false
        ElMessage({
            message: '已启动自动发布，请耐心等待，点击状态刷新即可。',
            type: 'success',
            duration: 5000,
        })
        console.log(data_final);

        data_final.data.forEach((item, index) => {
            console.log(item.result_all);
            // checkresult.push(item.result_all);
            // region.push(item.shopee_region);
            // config_center.push(item.config_center);
            // redis_change.push(item.redis_check);    sign_off.push(item.signoff_status);


            //releaseTableData[index]["checkresult"] = item.result_all
            // releaseTableData[index]["region"] = item.region
            // releaseTableData[index]["config_center"] = item.config_center
            // releaseTableData[index]["redis_change"] = item.redis_change
            if (!item.signoff_status) {
                releaseTableData[index].sign_off = ''
            } else {
                releaseTableData[index].sign_off = item.signoff_status;
            }
            if (!item.config_center) {
                releaseTableData[index].config_center = ''
            } else {
                releaseTableData[index].config_center = item.config_center;
            }
            if (!item.shopee_region) {
                releaseTableData[index].region = ''
            } else {
                releaseTableData[index].region = item.shopee_region;
            }
            if (!item.redis_check) {
                releaseTableData[index].redis_change = ''
            } else {
                releaseTableData[index].redis_change = item.redis_check;
            }
            if (!item.result) {
                releaseTableData[index].result = ''
            } else {
                releaseTableData[index].result = item.result;
            }
            if (!item.DB_Change) {
                releaseTableData[index].DB_Change = ''
            } else {
                releaseTableData[index].DB_Change = item.DB_Change;
            }
            if (!item.merge_list) {
                releaseTableData[index].merge_list = ''
            } else {
                releaseTableData[index].merge_list = item.merge_list;
            }
            if (!item.Code_Merged) {
                releaseTableData[index].Code_Merged = ''
            } else {
                releaseTableData[index].Code_Merged = item.Code_Merged;
            }
            // releaseTableData[index].sign_off = item.signoff_status;
            // releaseTableData[index].config_center = item.config_center;
            // releaseTableData[index].region = item.shopee_region;
            // releaseTableData[index].redis_change = item.redis_check;
            console.log(item.redis_check)
            console.log(releaseTableData)
            releaseTableData[index].services = '';
            item.services_list.services_list_be.forEach((service) => {
                if (releaseTableData[index].services === '') {
                    releaseTableData[index].services += `${service}`;
                }
                else{
                    releaseTableData[index].services += `\n${service}`;
                }
                if (!be_services.includes(service)) {
                    be_services.push(service)
                    all_services.push(service)
                }
            })
            item.services_list.services_list_fe.forEach((service) => {
                if (releaseTableData[index].services === '') {
                    releaseTableData[index].services += `${service}`;
                }
                else{
                    releaseTableData[index].services += `\n${service}`;
                }
                if (!fe_services.includes(service)) {
                    fe_services.push(service)
                    all_services.push(service)
                }
            })
            // if (releaseTableData[index].services === '') {
            //   releaseTableData[index].services = 'No services'
            // }

        })
        if (fe_services.length !== 0) {
            showfe.value = true
        }
        if (be_services.length !== 0) {
            showbe.value = true
        }
        // 计算未被正确分组的服务
        all_services.forEach(service => {
            if (!checkServiceGrouping(service)) {
                ungrouped_services.value.push(service);
            }
        });
        console.log("未正确分组的服务:", ungrouped_services.value);
        console.log(fe_services)
        console.log(be_services)
        showresult.value = true
        let allTrue = data_final.data.every(item => item.result !== "false");
        if (allTrue === true) {
            jiraStatus.value = "success";
        } else {
            jiraStatus.value = "error";
        }
        masterStatus.value = "success"
        // active.value = 2;
        console.log(allTrue); //
    }


    onMounted(async () => {
        //const releaseList = await get_release_tag();
        const releaseList = await get_jira_release_list_finished();
        const releaseData = releaseList.data.data;

        console.log(releaseData)
        // const lastIndex = releaseData.length - 1;
        // const lastElement = releaseData[lastIndex];
        if(selectedProjectFinished.value){
            if(!releaseData.includes(selectedProjectFinished.value)){
                selectedProjectFinished.value = '';
            }
        }

        if(!selectedProjectFinished.value){
            // selectedProjectFinished.value = lastElement;
            const currentDate = new Date();
            let closestDate = null;
            releaseData.forEach((str) => {
                    console.log(str)
                    const match = str.match(/bus-(\d{8})/);
                    if (match) {
                        const dateString = match[1];
                        const year = dateString.slice(0, 4);
                        const month = dateString.slice(4, 6) - 1;
                        const day = dateString.slice(6, 8);
                        const date = new Date(year, month, day);
                        if (date >= currentDate && (!closestDate || date < closestDate)) {
                            closestDate = date;
                            selectedProjectFinished.value = str;
                            console.log(selectedProjectFinished)
                        }
                    }
                }
            );
        }
        selectedRelease.value = releaseData;
        releaseData.forEach((item) => {
                projects.push(item);
            }
        )



// 检查页面上是否有内容
        if (document.body.innerHTML.trim().length > 0) {
            // 选择具有"el-empty"类的元素
            const emptyElement = document.querySelector('.el-empty');

            // 如果有内容，隐藏具有"el-empty"类的元素
            if (emptyElement) {
                emptyElement.style.display = 'none';
            }
        }
    });

    watch(selectedProjectFinished, (newValue, oldValue) => {
        if (newValue !== '') {
            releaseTableData.splice(0, releaseTableData.length);
            getData(newValue);

        }

    });

    watch(releaseTableData, (newValue, oldValue) => {
        if (releaseTableData.length === 0) {
            show.value = false
        }
        if (releaseTableData.length !== 0) {
            show.value = true
        }
    });

    watch(all_services, (newValue, oldValue) => {
        if (all_services.length === 0) {
            services_pane.value = false
        }
        if (all_services.length !== 0) {
            services_pane.value = true
        }
    });

    async function getreleaseData(value) {
        const releaseTableDataIds = new Set(releaseTableData.map((item) =>
            item.jira_key));
        // 这里编写请求数据的异步操作
// 将 releaseTableData 中每个对象的 id 属性存入 Set 对象中

        //releaseTableData.splice(0, releaseTableData.length);
        all_services.splice(0,all_services.length);
        be_services.splice(0,be_services.length);
        fe_services.splice(0,fe_services.length);
        console.log(`get data for ${value}`);
        let fin_data = {
            title: value,
        };
        let data_final = ref();
        data_final = await send_title(fin_data);
        console.log(data_final)
        if (data_final.data.length === 0) {
            console.log("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")
            releaseTableData.splice(0, releaseTableData.length);
        } else {
            console.log(releaseTableData)
            data_final.data.forEach((item) => {
                if (!releaseTableDataIds.has(item.jira_key)) {
                    releaseTableData.push(item);
                    releaseTableDataIds.add(item.jira_key);
                    console.log(releaseTableData)
                }
            });
        }

        // if (data_final.data.length === 0) {
        //   console.log("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")
        //   releaseTableData.splice(0, releaseTableData.length);
        // }
        // data_final.data.forEach((item) => {
        //   if (!releaseTableDataIds.has(item.jira_key)) {
        //     releaseTableData.push(item);
        //     releaseTableDataIds.add(item.jira_key);
        //   }
        // });

        console.log(releaseTableData);
    }

    async function getNewData(value) {
        showbe.value = false
        showfe.value = false
        console.log(value)
        await getreleaseData(value);
        fullscreenLoading.value = true

        let data_final = ref();
        console.log(releaseTableData)
        data_final = await autochecknewdata(releaseTableData);

        fullscreenLoading.value = false
        ElMessage({
            message: '已更新状态.',
            type: 'success',
            duration: 5000,
        })
        console.log(data_final);

        data_final.data.forEach((item, index) => {
            console.log(item.result_all);
            // checkresult.push(item.result_all);
            // region.push(item.shopee_region);
            // config_center.push(item.config_center);
            // redis_change.push(item.redis_check);    sign_off.push(item.signoff_status);


            //releaseTableData[index]["checkresult"] = item.result_all
            // releaseTableData[index]["region"] = item.region
            // releaseTableData[index]["config_center"] = item.config_center
            // releaseTableData[index]["redis_change"] = item.redis_change
            if (!item.signoff_status) {
                releaseTableData[index].sign_off = ''
            } else {
                releaseTableData[index].sign_off = item.signoff_status;
            }
            if (!item.config_center) {
                releaseTableData[index].config_center = ''
            } else {
                releaseTableData[index].config_center = item.config_center;
            }
            if (!item.shopee_region) {
                releaseTableData[index].region = ''
            } else {
                releaseTableData[index].region = item.shopee_region;
            }
            if (!item.redis_check) {
                releaseTableData[index].redis_change = ''
            } else {
                releaseTableData[index].redis_change = item.redis_check;
            }
            if (!item.result) {
                releaseTableData[index].result = ''
            } else {
                releaseTableData[index].result = item.result;
            }

            if (!item.merge_list) {
                releaseTableData[index].merge_list = ''
            } else {
                releaseTableData[index].merge_list = item.merge_list;
            }
            if (!item.Code_Merged) {
                releaseTableData[index].Code_Merged = ''
            } else {
                releaseTableData[index].Code_Merged = item.Code_Merged;
            }
            // releaseTableData[index].sign_off = item.signoff_status;
            // releaseTableData[index].config_center = item.config_center;
            // releaseTableData[index].region = item.shopee_region;
            // releaseTableData[index].redis_change = item.redis_check;
            console.log(item.redis_check)
            console.log(releaseTableData)
            releaseTableData[index].services = '';
            item.services_list.services_list_be.forEach((service) => {
                if (releaseTableData[index].services === '') {
                    releaseTableData[index].services += `${service}`;
                }
                else{
                    releaseTableData[index].services += `\n${service}`;
                }
                if (!be_services.includes(service)) {
                    be_services.push(service)
                    all_services.push(service)
                }
            })
            item.services_list.services_list_fe.forEach((service) => {
                if (releaseTableData[index].services === '') {
                    releaseTableData[index].services += `${service}`;
                }
                else{
                    releaseTableData[index].services += `\n${service}`;
                }
                if (!fe_services.includes(service)) {
                    fe_services.push(service)
                    all_services.push(service)
                }
            })
            // if (releaseTableData[index].services === '') {
            //   releaseTableData[index].services = 'No services'
            // }
        })

        if (fe_services.length !== 0) {
            showfe.value = true
        }
        if (be_services.length !== 0) {
            showbe.value = true
        }
        // 计算未被正确分组的服务
        all_services.forEach(service => {
            if (!checkServiceGrouping(service)) {
                ungrouped_services.value.push(service);
            }
        });
        console.log("未正确分组的服务:", ungrouped_services.value);
        console.log(fe_services)
        console.log(be_services)
        showresult.value = true
        let allTrue = data_final.data.every(item => item.result !== "false");
        // if (allTrue === true) {
        //   jiraStatus.value = "success";
        // } else {
        //   jiraStatus.value = "error";
        // }
        // masterStatus.value = "success"
        // active.value = 2;
        console.log(allTrue); //
    }


    async function getData(value) {
        showbe.value = false
        showfe.value = false
        console.log(value)
        await getreleaseData(value);
        fullscreenLoading.value = true

        let data_final = ref();
        console.log(releaseTableData)
        data_final = await autocheckdata(releaseTableData);

        fullscreenLoading.value = false
        ElMessage({
            message: '已更新状态.',
            type: 'success',
            duration: 5000,
        })
        console.log(data_final);

        data_final.data.forEach((item, index) => {
            console.log(item.result_all);
            // checkresult.push(item.result_all);
            // region.push(item.shopee_region);
            // config_center.push(item.config_center);
            // redis_change.push(item.redis_check);    sign_off.push(item.signoff_status);


            //releaseTableData[index]["checkresult"] = item.result_all
            // releaseTableData[index]["region"] = item.region
            // releaseTableData[index]["config_center"] = item.config_center
            // releaseTableData[index]["redis_change"] = item.redis_change
            if (!item.signoff_status) {
                releaseTableData[index].sign_off = ''
            } else {
                releaseTableData[index].sign_off = item.signoff_status;
            }
            if (!item.config_center) {
                releaseTableData[index].config_center = ''
            } else {
                releaseTableData[index].config_center = item.config_center;
            }
            if (!item.shopee_region) {
                releaseTableData[index].region = ''
            } else {
                releaseTableData[index].region = item.shopee_region;
            }
            if (!item.redis_check) {
                releaseTableData[index].redis_change = ''
            } else {
                releaseTableData[index].redis_change = item.redis_check;
            }
            if (!item.result) {
                releaseTableData[index].result = ''
            } else {
                releaseTableData[index].result = item.result;
            }

            if (!item.merge_list) {
                releaseTableData[index].merge_list = ''
            } else {
                releaseTableData[index].merge_list = item.merge_list;
            }
            if (!item.Code_Merged) {
                releaseTableData[index].Code_Merged = ''
            } else {
                releaseTableData[index].Code_Merged = item.Code_Merged;
            }
            if (!item.DB_Change) {
                releaseTableData[index].DB_Change = ''
            } else {
                releaseTableData[index].DB_Change = item.DB_Change;
            }
            // releaseTableData[index].sign_off = item.signoff_status;
            // releaseTableData[index].config_center = item.config_center;
            // releaseTableData[index].region = item.shopee_region;
            // releaseTableData[index].redis_change = item.redis_check;
            console.log(item.redis_check)
            console.log(releaseTableData)
            releaseTableData[index].services = '';
            item.services_list.services_list_be.forEach((service) => {
                if (releaseTableData[index].services === '') {
                    releaseTableData[index].services += `${service}`;
                }
                else{
                    releaseTableData[index].services += `\n${service}`;
                }
                if (!be_services.includes(service)) {
                    be_services.push(service)
                    all_services.push(service)
                }
            })

            item.services_list.services_list_fe.forEach((service) => {
                if (releaseTableData[index].services === '') {
                    releaseTableData[index].services += `${service}`;
                }
                else{
                    releaseTableData[index].services += `\n${service}`;
                }
                //releaseTableData[index].services += `\n${service}`;
                if (!fe_services.includes(service)) {
                    fe_services.push(service)
                    all_services.push(service)
                }
            })
            // if (releaseTableData[index].services === '') {
            //   releaseTableData[index].services = 'No services'
            // }
        })
        if (fe_services.length !== 0) {
            showfe.value = true
        }
        if (be_services.length !== 0) {
            showbe.value = true
        }
        // 计算未被正确分组的服务
        all_services.forEach(service => {
            if (!checkServiceGrouping(service)) {
                ungrouped_services.value.push(service);
            }
        });
        console.log("未正确分组的服务:", ungrouped_services.value);
        console.log(fe_services)
        console.log(be_services)
        console.log(releaseTableData)
        showresult.value = true
        let allTrue = data_final.data.every(item => item.result !== "false");
        // if (allTrue === true) {
        //   jiraStatus.value = "success";
        // } else {
        //   jiraStatus.value = "error";
        // }
        // masterStatus.value = "success"
        // active.value = 2;
        console.log(allTrue); //
    }

    function formatContent(row) {
        return `<div style="white-space: pre-wrap;">${row.services}</div>`;
    }
    watchEffect(() => {
        const savedOption = localStorage.getItem('selectedProjectFinished');
        let saveactive = localStorage.getItem('active');

        if (savedOption) {
            selectedProjectFinished.value = savedOption
        }
        if (saveactive) {
            active.value = saveactive
        }
    })


    watchEffect(() => {
        localStorage.setItem('selectedProjectFinished', selectedProjectFinished.value);
    })

    // 添加四个分组的定义
    const pingGroupA = reactive([
      'shopee-chatbot-intent', 'shopee-chatbot-admin', 'shopee-chatbot-adminasynctask', 
      'shopee-chatbot-adminconfigservice', 'shopee-chatbot-adminservice', 'shopee-chatbot-agentcontrol',
      'shopee-chatbot-asynctask', 'shopee-chatbot-auditlog', 'shopee-chatbot-botapi',
      'shopee-chatbot-context', 'shopee-chatbot-dm', 'shopee-chatbot-featurecenter',
      'shopee-chatbot-intentclarification', 'shopee-chatbot-messageasynctask', 'shopee-chatbot-messageservice',
      'shopee-chatbot-messageverification', 'shopee-chatbot-nlu', 'shopee-chatbot-ordercard',
      'shopee-chatbot-pilotapi', 'shopee-chatbotcommon-adminasynctask', 'shopee-chatbotcommon-adminconfigservice',
      'shopee-chatbotcommon-adminservice', 'shopee-chatbotcommon-agentcontrol', 'shopee-chatbotcommon-asynctask',
      'shopee-chatbotcommon-botapi', 'shopee-chatbotcommon-context', 'shopee-chatbotcommon-dm',
      'shopee-chatbotcommon-featurecenter', 'shopee-chatbotcommon-nlu', 'shopee-chatbotcommon-productrecommend',
      'shopee-chatbotcommon-rulebaseservice', 'shopee-chatbotcommon-shopconsole', 'shopee-chatbot-websocketgwy'
    ]);

    const pingGroupB = reactive([
      'shopee-chatbot-autotraining', 'shopee-annotation-admin', 'shopee-annotation-asynctask',
      'shopee-agorithmservice-component', 'shopee-chatbot-experimentmanagement', 'shopee-chatbot-featureapiproxy',
      'shopee-chatbot-modelgw', 'shopee-chatbot-realtime', 'shopee-chatbot-recallmanager',
      'shopee-chatbot-recallservice', 'shopee-chatbot-recommendation', 'shopee-chatbotcommon-apadmin',
      'shopee-chatbotcommon-apasynctask', 'shopee-chatbotcommon-component', 'shopee-chatbotcommon-experimentmanagement',
      'shopee-chatbotcommon-featureapiproxy', 'shopee-chatbotcommon-intent', 'shopee-chatbotcommon-kbadmin',
      'shopee-chatbotcommon-kbapi', 'shopee-chatbotcommon-kbasynctask', 'shopee-chatbotcommon-kblabelclarification',
      'shopee-chatbotcommon-modelgw', 'shopee-knowledgebase-admin', 'shopee-knowledgebase-api',
      'shopee-knowledgebase-asynctask', 'shopee-knowledgebase-labelclarification', 'shopee-chatbotcommon-promptmanagements'
    ]);

    const featureGroup = reactive([
      'shopee-chatbot-api', 'shopee-chatbot-autotraining', 'shopee-chatbotcommon-tfapiproxy',
      'shopee-chatbotcommon-tfeditor', 'shopee-chatbotcommon-tfserving', 'shopee-chatbotcommon-tfvariateserving',
      'shopee-taskflow-apiproxy', 'shopee-taskflow-editor', 'shopee-taskflow-taskflowserving',
      'shopee-taskflow-taskflowsop', 'shopee-taskflow-variateserving'
    ]);

    const feGroup = reactive([
      'shopee-autotrainingportal-adminstatic', 'shopee-annotation-adminstatic', 'shopee-cbrcmdplt-rcmdpltstatic',
      'shopee-chatbot-adminstatic', 'shopee-chatbot-chatbotcsatstatic', 'shopee-chatbot-chatbotrnstatic',
      'shopee-chatbot-chatbotstatic', 'shopee-chatbot-csatstatic', 'shopee-chatbot-dashboardstatic',
      'shopee-chatbot-tmcstatic', 'shopee-chatbotcommon-admincommonsaasstatic', 'shopee-chatbotcommon-adminsaasstatic',
      'shopee-chatbotcommon-adminstatic', 'shopee-chatbotcommon-annotationadminstatic', 'shopee-chatbotcommon-apadminsaasstatic',
      'shopee-chatbotcommon-csatstatic', 'shopee-chatbotcommon-kbadmincommonsaasstatic', 'shopee-chatbotcommon-kbadminsaasstatic',
      'shopee-chatbotcommon-shopconsolestatic', 'shopee-chatbotcommon-static', 'shopee-chatbotcommon-tfeadmincommonsaasstatic',
      'shopee-chatbotcommon-tfeadminsaasstatic', 'shopee-chatbotcommon-tmcsaasstatic', 'shopee-gec-gecstatic',
      'shopee-knowledgebase-adminstatic', 'shopee-taskflow-adminstatic', 'shopee-cschat-h5',
      'shopee-knowledgeplatform-adminstatic', 'shopee-chatbot-insights', 'shopee-chatbotcommon-insightssaasstatic-test',
      'shopee-chatbot-mmfchatbotconsole'
    ]);

    // 添加这个函数，用于检查服务是否被正确分组到任意一个小组
    function checkServiceGrouping(service) {
      if (fe_services.includes(service)) {
        // 如果是前端服务，应该在FE组里
        return feGroup.includes(service);
      } else if (be_services.includes(service)) {
        // 如果是后端服务，应该在平台BE1组、平台BE2组或功能BE组里
        return pingGroupA.includes(service) || pingGroupB.includes(service) || featureGroup.includes(service);
      }
      return false; // 如果既不是前端也不是后端服务，则认为没有分组
    }

    // 添加一个数组来存储未分组的服务
    const ungrouped_services = ref([]);
</script>

<style scoped>
    a {
        text-decoration: none;
    }
    .index-container {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

    }

    div {
        font-size: 12px;
        margin: 5px;
        border: 1px;
        padding: 0;
    }

</style>

<style>
    .el-table .warning-row {
        --el-table-tr-bg-color: var(--el-color-warning-light-9);
    }

    .el-table .success-row {
        --el-table-tr-bg-color: var(--el-color-success-light-9);
    }
    .el-table .cell {
        white-space: pre-wrap !important;
    }
    .table-header {
        background-color: blue;
        color: white;
    }
</style>
<style scoped>
    .n-gradient-text {
        font-size: 24px;
    }
</style>

<style scoped>
    .scrollbar-fe-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        margin: 10px; /* 删除默认外边距 */

        text-align: center;
        border-radius: 4px;
        background: var(--el-color-success-light-9);
        color: var(--el-color-success);
    }

    .scrollbar-be-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        margin: 10px; /* 删除默认外边距 */
        text-align: center;
        border-radius: 4px;
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
    }

    .ml-2 {

        margin: 10px; /* 添加10像素的间距 */

    }
</style>


<style scoped>
    .data-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .data-item {
        width: calc(33.33% - 5px); /* 将元素宽度从原来的 calc(33.33% - 10px) 调整为 calc(33.33% - 5px) */
        margin-bottom: 20px;
        border-radius: 5px;
        padding: 10px;
        background-color: #f5f5f5;
    }

    .data-item__title {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .data-item__content {
        color: #666;
    }
</style>

<style scoped>
    .itxst {
        width: 600px;
        display: flex;
    }

    .itxst > div:nth-of-type(1) {
        flex: 1;
    }

    .itxst > div:nth-of-type(2) {
        width: 270px;
        padding-left: 20px;
    }

    .item {
        border: solid 1px #eee;
        padding: 6px 10px;
        text-align: left;
    }

    .item:hover {
        cursor: move;
    }

    .item + .item {
        margin-top: 10px;
    }

    .ghost {
        border: solid 1px rgb(19, 41, 239);
    }

    .chosenClass {
        background-color: #f1f1f1;
    }
</style>

<style>
    .el-popper.is-customized {
        /* Set padding to ensure the height is 32px */
        padding: 6px 12px;
        background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
    }

    .el-popper.is-customized .el-popper__arrow::before {
        background: linear-gradient(45deg, #b2e68d, #bce689);
        right: 0;
    }
    .el-card + .el-card {
        margin-top: 20px;
    }


</style>
<style>
    .ar-container {
        display: flex;
        align-items: center;
    }
</style>


<template>
  <div class="index-conntainer">
    <transition name="el-fade-in-linear">
      <el-card class="card" shadow="hover" width="50px" height="50px">
        <div class="ar-container">
          <el-radio-group v-model="releaseTypeFilter" size="large" @change="handleReleaseTypeChange">
            <el-radio-button label="bus">Bus</el-radio-button>
            <el-radio-button label="adhoc">Adhoc</el-radio-button>
            <el-radio-button label="hotfix">Hotfix</el-radio-button>
            <el-radio-button label="all">全部</el-radio-button>
          </el-radio-group>
          
          <el-select v-model="selectedProjectFinished"
                     filterable
                     clearable
                     placeholder="please select release ticket"
                     style="width: 380px; margin-left: 10px;"
          >

            <el-option
                v-for="project in filteredProjects"
                :key="typeof project === 'string' ? project : project.title"
                :label="typeof project === 'string' ? project : project.title"
                :value="typeof project === 'string' ? project : project.title"
            ></el-option>
          </el-select>

          <el-drawer v-model="visible" :show-close="false">
            <template #header="{ titleId, titleClass }">
              <h4 :id="titleId" :class="titleClass">{{ selectedProject }}</h4>
            </template>
            暂无失败信息。
          </el-drawer>
        </div>

        <div class="ar-container">
          <el-table

              :data="releaseTableData"
              stripe
              border

              highlight-current-row
              fit
              :header-cell-style="{background:'#cacfd7',color:'#606266'}"
              :empty-text="'暂无数据'"
          >
            <el-table-column label="编号" min-width="21" header-align="center" align="center">
              <template #default="scope">
                {{scope.$index+1}}
              </template>
            </el-table-column>
            <el-table-column prop="type"
                             label="类型"
                             header-align="center" align="center"
                             min-width="30"
                             :filters="[
                            { text: 'Epic', value: 'Epic' },
                            { text: 'Bug', value: 'Bug' },
                            { text: 'Task', value: 'Task' },
                            { text: 'Sub-task', value: 'Sub-task' },
                            { text: 'Story', value: 'Story' },
                            ]"
                             :filter-method="filterType"
                             filter-placement="bottom-end"
            >

              <template #default="{ row }">

                <el-icon :class="getIconName(row.type)"></el-icon>
              </template>

            </el-table-column>

            <el-table-column prop="jira_key" label="单号" :min-width="60" header-align="center" align="center"
            >
              <template #default="{ row }">
                <el-link
                    :underline="false"
                    v-bind:href="row.jira_link"
                    target="_blank"
                    type="primary">
                  {{ row.jira_key }}
                </el-link>
              </template>
            </el-table-column>


            <el-table-column prop="jira_title" label="需求名" :min-width="150"
            >

            </el-table-column>

            <el-table-column prop="bug_resolution_rate" label="Bug解决率" :min-width="80" header-align="center" align="center">
              <template #default="{ row }">
                <span :style="{ color: Number(row.bug_resolved || 0) === Number(row.bug_total || 0) ? '#67C23A' : '#F56C6C' }">
                  <template v-if="typeof row.bug_resolved === 'number' && typeof row.bug_total === 'number'">
                    {{ row.bug_resolved }}/{{ row.bug_total }}
                  </template>
                  <template v-else>
                    {{ row.bug_resolved || 0 }}/{{ row.bug_total || 0 }}
                  </template>
                </span>
              </template>
            </el-table-column>

            <el-table-column label="周一" header-align="center" align="center">
              <el-table-column prop="sign_off" label="Signed off" header-align="center" align="center" min-width="40">


                <template #header slot-scope="scope">
                  Signed<br>off
                </template>
                <template #default="{ row }">

                  <el-icon
                      v-if="row.sign_off === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.sign_off === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.sign_off === '' ? true : false"
                      :size="20"
                      :color="row.sign_off === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="周二" header-align="center" align="center">
              <el-table-column type="expand" label="提MR" min-width="32">

                <template #default="props">
                  <div>
                    <div class="ar-container">
                      <el-tooltip
                          class="box-item"
                          effect="customized"
                          content="点击创建MR"
                          placement="top-start"
                      >
                        <el-button
                            type="danger"
                            @click="centerDialogVisible = true"
                            size="small"
                            :icon="CirclePlus"
                            element-loading-text="AR正在创建MR, 请耐心等待..."
                            v-loading.fullscreen.lock="fullscreenLoading">创建
                        </el-button>
                      </el-tooltip>

                      <el-tooltip
                          class="box-item"
                          effect="customized"
                          content="点击复制"
                          placement="top-start"
                      >
                        <el-button
                            type="primary"
                            size="small"
                            @click="copyToClipboard(props)"
                            :icon="ChatRound"
                            element-loading-text="AR正在处理数据，请耐心等待..."
                            v-loading.fullscreen.lock="fullscreenLoading">复制
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                          class="box-item"
                          effect="customized"
                          content="点击发送MR提醒到seatalk"
                          placement="top-start"
                      >
                        <el-button
                            type="primary"
                            size="small"
                            @click="sendSingleFeatureToCT(props)"
                            :icon="ChatRound"
                            element-loading-text="AR正在处理数据，请耐心等待..."
                            v-loading.fullscreen.lock="fullscreenLoading">发送
                        </el-button>
                      </el-tooltip>
                    </div>
                    <el-dialog
                        v-model="centerDialogVisible"
                        title="Warning"
                        width="30%"
                        align-center
                    >
                      <span>请确认是否开始自动提MR？发布单： {{ selectedProject }}</span>
                      <template #footer>
                        <span class="dialog-footer">
                          <el-button @click="centerDialogVisible = false">取消</el-button>
                          <el-button type="primary" @click="startSingleAR(props)">
                            确认
                          </el-button>
                        </span>
                      </template>
                    </el-dialog>
                    <el-table :data="props.row.merge_list" border
                              :header-cell-style="{background:'#def1ce',color:'#606266'}"
                    >
                      <el-table-column label="仓库" prop="repo_name"/>
                      <el-table-column label="分支" prop="branch_name"/>
                      <el-table-column label="PIC" prop="pic"/>
                      <el-table-column label="MR地址" prop="web_url">
                        <template #default="{row}">
                          <a
                              :href="row.web_url"
                              target="_blank"
                          >{{ row.web_url }}
                          </a>
                        </template>
                      </el-table-column>
                      <el-table-column label="MR状态" prop="merge_status"/>
                      <el-table-column label="MR作者" prop="author"/>
                    </el-table>
                  </div>
                </template>

              </el-table-column>

              <el-table-column prop="Code_Merged" label="Code Merged" header-align="center" align="center"
                               min-width="40"
              >
                <template #header slot-scope="scope">
                  Code<br>Merged
                </template>
                <template #default="{ row }">
                  <el-icon
                      v-if="row.Code_Merged === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.Code_Merged === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.Code_Merged === '' ? true : false"
                      :size="20"
                      :color="row.Code_Merged === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="周三" header-align="center" align="center">
              <el-table-column prop="config_center" label="Config Changed" header-align="center" align="center"
                               min-width="43"
              >
                <template #header slot-scope="scope">
                  Config<br>Changed
                </template>
                <template #default="{ row }">
                  <el-icon
                      v-if="row.config_center === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.config_center === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.config_center === '' ? true : false"
                      :size="20"
                      :color="row.config_center === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
              <el-table-column prop="DB_Change" label="DB Changed" header-align="center" align="center" min-width="43">
                <template #default="{ row }">
                  <el-icon
                      v-if="row.DB_Change === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.DB_Change === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.DB_Change === '' ? true : false"
                      :size="20"
                      :color="row.DB_Change === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
                <template #header slot-scope="scope">
                  DB<br>Changed
                </template>
              </el-table-column>
              <el-table-column
                  prop="services"
                  label="services"
                  min-width="180"
                  header-align="center"
                  align="center"
                  :style="{ 'white-space': 'pre-wrap' ,

            }"
              >
                <template #default="{ row }">
                  <el-text
                      v-if="row.services !== '' ? true : false"
                      :size="20"
                      :color="row.services === 'pass' ? '#67c23a' : '#F56C67'"


                  >
                    {{ row.services }}
                  </el-text>
                  <el-icon
                      v-if="row.services === '' ? true : false"
                      :size="20"
                      :color="row.services === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>

              <el-table-column prop="region" label="Region" header-align="center" align="center" min-width="37">
                <template #default="{ row }">
                  <el-text
                      v-if="row.region !== '' ? true : false"
                      :size="20"
                      :color="row.region === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    {{ row.region }}
                  </el-text>
                  <el-icon
                      v-if="row.region === '' ? true : false"
                      :size="20"
                      :color="row.region === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
            </el-table-column>


            <el-table-column prop="PM" label="PM" header-align="center" align="center" min-width="50">
              <template #default="{ row }">
                {{ row.PM }}
              </template>
            </el-table-column>

            <el-table-column prop="dev_pic" label="DEV PIC" header-align="center" align="center" min-width="40">
              <template #header slot-scope="scope">
                DEV<br>PIC
              </template>
              <template #default="{ row }">
                {{ row.dev_pic }}
              </template>
            </el-table-column>

            <el-table-column prop="qa_pic" label="QA" header-align="center" align="center" min-width="50">
              <template #default="{ row }">
                {{ row.qa_pic }}
              </template>
            </el-table-column>


            <el-table-column prop="status" label="Status" header-align="center" align="center" min-width="60">
              <template #default="{ row }">

                <el-tag class="bold-text"
                        effect="dark"
                        :type="getStatusName(row.status)"
                        :color="getColorName(row.status)"
                >{{ getBigName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

        </div>


        <div style="display: flex;">

          <el-table

              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#e78181',color:'#f8f7f7'}"
              :data="IN_pingGroupA"
              style="width: 100%"

              :empty-text="'暂无数据'"
          >

            <el-table-column header-align="center" label="平台BE1组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#819ee7',color:'#f8f7f7'}"
              header-align="center"
              :data="IN_pingGroupB"
              style="width: 100%"

              :empty-text="'暂无数据'"
          >
            <el-table-column header-align="center" label="平台BE2组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#81e7c8',color:'#f8f7f7'}"
              header-align="center"
              :data="IN_featureGroup"
              style="width: 100%"
              :empty-text="'暂无数据'"
          >
            <el-table-column header-align="center" label="功能BE组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#e7a881',color:'#f8f7f7'}"
              header-align="center"
              :data="IN_feGroup"
              style="width: 100%"
              :empty-text="'暂无数据'"
          >
            <el-table-column header-align="center" label="FE组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>


        </div>

      </el-card>
    </transition>
  </div>

</template>

<script lang="ts" setup>
import {ref, onMounted, computed, watch, reactive, watchEffect, nextTick, toRef, toRaw, onUnmounted} from 'vue';
import type {ElTree} from 'element-plus'
import draggable from "vuedraggable";
import {copymsg} from '@/api/copymsg';
import CircularJSON from 'circular-json';
import Sortable from "sortablejs";




import {
  ElPagination,
  ElCard,
  ElTable,
  ElTableColumn,
  ElTag,
  ElSelect,
  ElOption,
  ElMessage,
  ElMessageBox,
  TableColumnCtx,
  TableInstance,
} from 'element-plus';
import {Eleme, Loading, Refresh, CirclePlus, ChatRound, Bell} from '@element-plus/icons-vue'
import axios from 'axios';
import {Edit, View as IconView} from '@element-plus/icons-vue';
import {ChatLineRound, Male} from '@element-plus/icons-vue';
import {read_json} from '@/api/read_json';
import {get_release_tag} from '@/api/get_release_tag';
import {send_title} from '@/api/send_title';
import {startAuto} from '@/api/startAuto';
import {autocheckdata} from '@/api/autocheckdata';
import {seatalk} from '@/api/seatalk';
import {callMRseatalk} from '@/api/callMRseatalk';
import {autochecknewdata} from '@/api/autochecknewdata';
import {start_single_ar} from '@/api/start_single_ar';
import {mr_seatalk_single_feature_msg} from '@/api/mr_seatalk_single_feature_msg';
import {get_key_jira_release_list} from '@/api/get_key_jira_release_list';
import {newMerge} from '@/api/newMerge';
import {ElButton, ElDrawer} from 'element-plus'
import {CircleCloseFilled} from '@element-plus/icons-vue'
import type {TabsPaneContext} from 'element-plus'
//import {consoleLog} from "echarts/types/src/util/log";
import {signedOff} from '@/api//signedOff';
const dialogVisible = ref(false)
import {get_jira_release_list_finished} from '@/api/get_jira_release_list_finished';


const selectedProjectFinished = ref();

const fullscreenLoadingMR = ref(false);

const dialogTableVisible = ref(false)
const centerDialogVisible = ref(false)
const activeName = ref('first')
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
const fe_services = reactive([]);
const be_services = reactive([]);
const all_services = reactive([]);


const pingGroupA = reactive([
  {
    'shopee-chatbot-intent': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intent',
    'shopee-chatbot-admin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.admin',
    'shopee-chatbot-adminasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminasynctask',
    'shopee-chatbot-adminconfigservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminconfigservice',
    'shopee-chatbot-adminservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminservice',
    'shopee-chatbot-agentcontrol': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.agentcontrol',
    'shopee-chatbot-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.asynctask',
    'shopee-chatbot-auditlog': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.auditlog',
    'shopee-chatbot-botapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi',
    'shopee-chatbot-context': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.context',
    'shopee-chatbot-dm': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.dm',
    'shopee-chatbot-featurecenter': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featurecenter',
    'shopee-chatbot-intentclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intentclarification',
    'shopee-chatbot-messageasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageasynctask',
    'shopee-chatbot-messageservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageservice',
    'shopee-chatbot-messageverification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageverification',
    'shopee-chatbot-nlu': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.nlu',
    'shopee-chatbot-ordercard': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.ordercard',
    'shopee-chatbot-pilotapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.pilotapi',
    'shopee-chatbotcommon-adminasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminasynctask',
    'shopee-chatbotcommon-adminconfigservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminconfigservice',
    'shopee-chatbotcommon-adminservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminservice',
    'shopee-chatbotcommon-agentcontrol': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.agentcontrol',
    'shopee-chatbotcommon-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.asynctask',
    'shopee-chatbotcommon-botapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.botapi',
    'shopee-chatbotcommon-context': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.context',
    'shopee-chatbotcommon-dm': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.dm',
    'shopee-chatbotcommon-featurecenter': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featurecenter',
    'shopee-chatbotcommon-nlu': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu',
    'shopee-chatbotcommon-productrecommend': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.productrecommend',
    'shopee-chatbotcommon-rulebaseservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.rulebaseservice',
    'shopee-chatbotcommon-shopconsole': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsole'
  }]);

const pingGroupB = reactive([{
  'shopee-chatbot-autotraining': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining',
  'shopee-annotation-admin':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.admin',
  'shopee-annotation-asynctask':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.asynctask',
  'shopee-annotation-timetask':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.timetask',
  'shopee-annotation-dataproxy':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.dataproxy',
  'shopee-agorithmservice-component': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.ai_engineering.nlu_component',
  'shopee-chatbot-experimentmanagement': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.experimentmanagement',
  'shopee-chatbot-featureapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featureapiproxy',
  'shopee-chatbot-modelgw': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.modelgw',
  'shopee-chatbot-realtime': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.realtime',
  'shopee-chatbot-recallmanager': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallmanager',
  'shopee-chatbot-recallservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallservice',
  'shopee-chatbot-recommendation': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recommendation',
  'shopee-chatbotcommon-apadmin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadmin',
  'shopee-chatbotcommon-apasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apasynctask',
  'shopee-chatbotcommon-apdataproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apdataproxy',
  'shopee-chatbotcommon-component': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu_component',
  'shopee-chatbotcommon-experimentmanagement': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.experimentmanagement',
  'shopee-chatbotcommon-featureapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featureapiproxy',
  'shopee-chatbotcommon-intent': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intent',
  'shopee-chatbotcommon-kbadmin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmin',
  'shopee-chatbotcommon-kbapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbapi',
  'shopee-chatbotcommon-kbasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbasynctask',
  'shopee-chatbotcommon-kblabelclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kblabelclarification',
  'shopee-chatbotcommon-modelgw': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.modelgw',
  'shopee-knowledgebase-admin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.admin',
  'shopee-knowledgebase-api': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.api',
  'shopee-knowledgebase-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.asynctask',
  'shopee-knowledgebase-labelclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.labelclarification',
  'shopee-knowledgeplatform-admin':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin',
  'shopee-knowledgeplatform-api':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.api',
  'shopee-knowledgeplatform-qa_tools':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.qa_tools'

}]);
const featureGroup = reactive([{
  'shopee-chatbot-api': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.api',
  'shopee-chatbot-autotraining': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining',
  'shopee-chatbotcommon-tfapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfapiproxy',
  'shopee-chatbotcommon-tfeditor': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeditor',
  'shopee-chatbotcommon-tfserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfserving',
  'shopee-chatbotcommon-tfvariateserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfvariateserving',
  'shopee-taskflow-apiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.apiproxy',
  'shopee-taskflow-editor': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.editor',
  'shopee-taskflow-taskflowserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowserving',
  'shopee-taskflow-taskflowsop': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowsop',
  'shopee-taskflow-variateserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.variateserving'
}]);

const feGroup = reactive([
  {
    'shopee-autotrainingportal-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.autotrainingportal.adminstatic',
    'shopee-annotation-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.adminstatic',
    'shopee-cbrcmdplt-rcmdpltstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.cbrcmdplt.rcmdpltstatic',
    'shopee-chatbot-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminstatic',
    'shopee-chatbot-chatbotcsatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotcsatstatic',
    'shopee-chatbot-chatbotrnstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotrnstatic',
    'shopee-chatbot-chatbotstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotstatic',
    'shopee-chatbot-csatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.csatstatic',
    'shopee-chatbot-dashboardstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.dashboardstatic',
    'shopee-chatbot-tmcstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.tmcstatic',
    'shopee-chatbotcommon-admincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.admincommonsaasstatic',
    'shopee-chatbotcommon-adminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminsaasstatic',
    'shopee-chatbotcommon-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminstatic',
    'shopee-chatbotcommon-annotationadminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.annotationadminstatic',
    'shopee-chatbotcommon-apadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadminsaasstatic',
    'shopee-chatbotcommon-csatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.csatstatic',
    'shopee-chatbotcommon-kbadmincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmincommonsaasstatic',
    'shopee-chatbotcommon-kbadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadminsaasstatic',
    'shopee-chatbotcommon-shopconsolestatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsolestatic',
    'shopee-chatbotcommon-static': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.static',
    'shopee-chatbotcommon-tfeadmincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadmincommonsaasstatic',
    'shopee-chatbotcommon-tfeadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadminsaasstatic',
    'shopee-chatbotcommon-tmcsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tmcsaasstatic',
    'shopee-gec-gecstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.gecstatic',
    'shopee-knowledgebase-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.adminstatic',
    'shopee-taskflow-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.adminstatic'
  }

])

const openKB = () => {
  window.open('https://monitoring.infra.sz.shopee.io/grafana/d/kj1f3huVk/chatbot-fa-ban-kan-ban?orgId=10&from=now-1h&to=now&refresh=30s', '_blank');
}
onMounted(async () => {
  try {
    // 获取发布单列表
    const releaseList = await get_jira_release_list_finished();
    console.log('API返回数据:', releaseList);
    
    // 确保响应数据存在且有数据
    if (!releaseList || !releaseList.data || !releaseList.data.data) {
      console.error('API返回数据格式不正确:', releaseList);
      return;
    }
    
    const releaseData = releaseList.data.data;
    console.log('解析后的发布单数据:', releaseData);
    
    // 检查之前选择的发布单是否存在于当前获取的数据中
    if (selectedProjectFinished.value) {
      const projectExists = Array.isArray(releaseData) && releaseData.includes(selectedProjectFinished.value);
      
      if (!projectExists) {
        console.log('之前选择的发布单不在当前列表中，清空选择');
        selectedProjectFinished.value = '';
      }
    }
    
    // 如果没有选择发布单，自动选择最近的发布单
    if (!selectedProjectFinished.value) {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);
      let closestDate = null;
      
      if (Array.isArray(releaseData)) {
        releaseData.forEach((projectTitle) => {
          console.log('处理项目:', projectTitle);
          
          // 匹配日期格式: Bus-YYMMDD 或 Bus-YYYYMMDD
          const match = projectTitle.match(/(bus|adhoc|hotfix)-(\d{6,8})/i);
          if (match) {
            const dateString = match[2];
            let year, month, day;
            
            // 处理6位和8位日期格式
            if (dateString.length === 6) {
              year = parseInt('20' + dateString.substring(0, 2));
              month = parseInt(dateString.substring(2, 4)) - 1;
              day = parseInt(dateString.substring(4, 6));
            } else if (dateString.length === 8) {
              year = parseInt(dateString.substring(0, 4));
              month = parseInt(dateString.substring(4, 6)) - 1;
              day = parseInt(dateString.substring(6, 8));
            }
            
            const date = new Date(year, month, day);
            console.log(`日期解析: ${projectTitle} => ${date.toISOString().split('T')[0]}`);
            
            if (date >= currentDate && (!closestDate || date < closestDate)) {
              closestDate = date;
              selectedProjectFinished.value = projectTitle;
              console.log('自动选择发布单:', selectedProjectFinished.value);
            }
          }
        });
      }
    }
    
    // 设置下拉列表数据
    selectedRelease.value = releaseData;
    
    // 清空projects数组并填充对象格式的数据
    projects.splice(0, projects.length);
    
    if (Array.isArray(releaseData)) {
      releaseData.forEach((item) => {
        // 创建具有title属性的对象
        projects.push({ title: item });
      });
    }
    
    console.log('处理后的项目列表:', projects);
    
    // 初始化过滤的项目列表
    filterProjects();
    
    // 如果选择了发布单，加载数据
    if (selectedProjectFinished.value) {
      getData(selectedProjectFinished.value);
    }
    
    // 检查页面上是否有内容，隐藏空状态
    if (document.body.innerHTML.trim().length > 0) {
      const emptyElement = document.querySelector('.el-empty');
      if (emptyElement) {
        emptyElement.style.display = 'none';
      }
    }
  } catch (error) {
    console.error('获取发布单列表失败:', error);
  }
});



const IN_pingGroupA = computed(() => {
  const result = {};
  const pingGroupAKeys = pingGroupA.map(obj => Object.keys(obj)).flat();
  for (const item of be_services) {
    if (pingGroupAKeys.includes(item)) {
      const link = pingGroupA.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});
const IN_pingGroupB = computed(() => {
  const result = {};
  const pingGroupBKeys = pingGroupB.map(obj => Object.keys(obj)).flat();
  for (const item of be_services) {
    if (pingGroupBKeys.includes(item)) {
      const link = pingGroupB.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});

const IN_featureGroup = computed(() => {
  const result = {};
  const featureGroupKeys = featureGroup.map(obj => Object.keys(obj)).flat();
  for (const item of be_services) {
    if (featureGroupKeys.includes(item)) {
      const link = featureGroup.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});

const IN_feGroup = computed(() => {
  const result = {};
  const beGroupKeys = feGroup.map(obj => Object.keys(obj)).flat();
  for (const item of fe_services) {
    if (beGroupKeys.includes(item)) {
      const link = feGroup.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});

let services_pane = ref(false);
let visible = ref(false);
let show = ref(false);
let showfe = ref(false);
let showbe = ref(false);
let showresult = ref(false);
let active = ref(0);
let jiraStatus = ref("wait");
let masterStatus = ref("wait");
const tagType = ["success", "info", "warning", "danger"];
// 发布类型过滤器，从本地存储获取或使用默认值
const releaseTypeFilter = ref(localStorage.getItem('releaseTypeFilter') || 'bus');
const filteredProjects = ref([]); // 存储过滤后的项目列表

// 从本地存储获取上次使用的过滤类型
onMounted(() => {
  const savedFilter = localStorage.getItem('releaseTypeFilter');
  if (savedFilter) {
    releaseTypeFilter.value = savedFilter;
  }
  
  // 初始化过滤项目列表
  filterProjects();
  
  // 自动选择最近的发布单
  if (filteredProjects.value.length > 0 && !selectedProjectFinished.value) {
    selectClosestRelease();
  }
  
  // 检查页面上是否有内容
  if (document.body.innerHTML.trim().length > 0) {
    // 查找具有"el-empty"类的元素
    const emptyElement = document.querySelector('.el-empty');
    
    // 如果有内容，隐藏具有"el-empty"类的元素
    if (emptyElement) {
      emptyElement.style.display = 'none';
    }
  }
});

// 处理发布类型过滤变化
const handleReleaseTypeChange = () => {
  // 保存过滤类型到本地存储
  localStorage.setItem('releaseTypeFilter', releaseTypeFilter.value);
  filterProjects();
  
  // 过滤后自动选择最近的发布单
  if (filteredProjects.value.length > 0) {
    selectClosestRelease();
  } else {
    // 如果没有过滤结果，确保清空选择和数据
    selectedProjectFinished.value = '';
    releaseTableData.splice(0, releaseTableData.length);
  }
};

// 过滤项目列表
const filterProjects = () => {
  console.log('过滤项目列表，当前过滤类型:', releaseTypeFilter.value);
  
  if (releaseTypeFilter.value === 'all') {
    filteredProjects.value = projects;
  } else {
    // 转换为小写进行不区分大小写的匹配
    const filterType = releaseTypeFilter.value.toLowerCase();
    
    // 打印几个项目标题用于调试
    if (projects.length > 0) {
      console.log('前三个项目标题:', projects.slice(0, 3).map(p => p.title));
    }
    
    // 使用title属性进行匹配
    filteredProjects.value = projects.filter(project => {
      if (!project || !project.title) return false;
      const lowerTitle = project.title.toLowerCase();
      return lowerTitle.includes(filterType);
    });
  }
  
  console.log('过滤后项目数量:', filteredProjects.value.length);
  
  // 如果过滤后没有结果，清空选择的项目
  if (filteredProjects.value.length === 0) {
    selectedProjectFinished.value = '';
    // 同时清空表格数据
    releaseTableData.splice(0, releaseTableData.length);
  }
};

function getRandomElement() {
  const randomIndex = Math.floor(Math.random() * tagType.length);
  console.log(tagType[randomIndex]);
  return tagType[randomIndex];
}

const selectedProject = ref();
// 每页显示条数
const fullscreenLoading = ref(false)

interface User {
  release_title: string
  jira_key: string
  jira_title: string
  jira_link: string
  type: string
}

const filterType = (value: string, row: User) => {
  return row.type === value
}

const selectedRelease = ref();
const projects = reactive([]);

let releaseTableData = reactive([]);


const callseatalk = (tab: TabsPaneContext, event: Event) => {
  let fin_data = {
    jira_title: selectedProject.value,
  };
  seatalk(fin_data);
  ElMessage({
    message: '已进行checklist消息push，请耐心等待seatalk自动发送消息。',
    type: 'success',
    duration: 5000,
  })
}

const signedOffSeatalk = (tab: TabsPaneContext, event: Event) => {
  let fin_data = {
    jira_title: selectedProject.value,
  };
  signedOff(fin_data);
  ElMessage({
    message: '已进行Signed off消息push，请耐心等待seatalk自动发送消息。',
    type: 'success',
    duration: 5000,
  })
}

const callMRseatalkFE = (tab: TabsPaneContext, event: Event) => {
  let fin_data = {
    jira_title: selectedProject.value,
  };

  callMRseatalk(fin_data);
  ElMessage({
    message: '已进行MR消息push，请耐心等待seatalk自动发送消息。',
    type: 'success',
    duration: 5000,
  })
}

async function sendSingleFeatureToCT(row) {
  let data_final = ref();
  data_final = await mr_seatalk_single_feature_msg(row.row);
  console.log(data_final)
  navigator.clipboard.writeText(data_final.data)
      .then(() => {
        ElMessage({
          message: '恭喜，MR信息已发送到seatalk！',
          type: 'success',
        })
      })
      .catch((error) => {
        ElMessage.error('MR信息发送失败！')
      });
}

async function copyToClipboard(row) {
  let data_final = ref();
  let fin_data = {
    jira_title: selectedProject.value,
  };
  data_final = await copymsg(row.row);

  console.log(data_final)
  navigator.clipboard.writeText(data_final.data)
      .then(() => {
        ElMessage({
          message: '恭喜，MR信息已复制到剪切板！',
          type: 'success',
        })
        //alert('已复制到剪贴板');
      })
      .catch((error) => {
        ElMessage.error('复制剪切板失败！')
      });
}

async function startSingleAR(props) {
  //const rawRow = toRaw(props);
  console.log(props.row);
  //const jsonString = CircularJSON.stringify(props.toJSON());

  let data_final = ref();
  data_final = await start_single_ar(props.row);

}

async function getreleaseData(value) {

  console.log(releaseTableData);
  const releaseTableDataIds = new Set(releaseTableData.map((item) =>
      item.jira_key));
  // 这里编写请求数据的异步操作
// 将 releaseTableData 中每个对象的 id 属性存入 Set 对象中

  all_services.splice(0, all_services.length);
  be_services.splice(0, be_services.length);
  fe_services.splice(0, fe_services.length);
  console.log(`get data for ${value}`);
  let fin_data = {
    title: value,
  };
  let data_final = [];
  let temp_data : any;
  temp_data = await send_title(fin_data);
  console.log(temp_data);
  console.log(await send_title(fin_data))
  console.log(temp_data);
  if (temp_data.length === 0) {
    releaseTableData.splice(0, releaseTableData.length);
  } else {
    releaseTableData.splice(0, releaseTableData.length);
    temp_data.data.forEach((item) => {
      console.log(item);
      if (!releaseTableDataIds.has(item.jira_key)) {
        releaseTableData.push(item);
        releaseTableDataIds.add(item.jira_key);
      }
      // releaseTableData.push(item);
      // releaseTableDataIds.add(item.jira_key);
    });
  }


  console.log(releaseTableData);
}

async function refreshData(value) {
  showbe.value = false
  showfe.value = false
  console.log(value)

  await getreleaseData(value);
  fullscreenLoading.value = true

  console.log(releaseTableData)
  await getNewData(value)
  fullscreenLoading.value = false

  ElMessage({
    message: '已更新状态',
    type: 'success',
    duration: 5000,
  })
}

async function getNewData(value) {
  showbe.value = false
  showfe.value = false
  console.log(value)
  await getreleaseData(value);
  fullscreenLoading.value = true

  let data_final = ref();
  console.log(releaseTableData)
  data_final.value = await autochecknewdata(releaseTableData);

  fullscreenLoading.value = false

  console.log(data_final);
  const releaseDataLength = releaseTableData.length;
  data_final.value.data.slice(0,releaseDataLength).forEach((item, index) => {
    console.log(item.result_all);

    if (!item.signoff_status) {
      releaseTableData[index].sign_off = ''
    } else {
      releaseTableData[index].sign_off = item.signoff_status;
    }
    if (!item.config_center) {
      releaseTableData[index].config_center = ''
    } else {
      releaseTableData[index].config_center = item.config_center;
    }
    if (!item.Code_Merged) {
      releaseTableData[index].Code_Merged = ''
    } else {
      releaseTableData[index].Code_Merged = item.Code_Merged;
    }
    if (!item.shopee_region) {
      releaseTableData[index].region = ''
    } else {
      releaseTableData[index].region = item.shopee_region;
    }
    if (!item.redis_check) {
      releaseTableData[index].redis_change = ''
    } else {
      releaseTableData[index].redis_change = item.redis_check;
    }
    if (!item.DB_Change) {
      releaseTableData[index].DB_Change = ''
    } else {
      releaseTableData[index].DB_Change = item.DB_Change;
    }
    if (!item.result) {
      releaseTableData[index].result = ''
    } else {
      releaseTableData[index].result = item.result;
    }

    if (!item.merge_list) {
      releaseTableData[index].merge_list = ''
    } else {
      releaseTableData[index].merge_list = item.merge_list;
    }
    if (!item.status) {
      releaseTableData[index].status = ''
    } else {
      releaseTableData[index].status = item.status;
    }
    if (!item.dev_pic) {
      releaseTableData[index].dev_pic = ''
    } else {
      releaseTableData[index].dev_pic = item.dev_pic;
    }
    if (!item.PM) {
      releaseTableData[index].PM = ''
    } else {
      releaseTableData[index].PM = item.PM;
    }
    if (!item.qa_pic) {
      releaseTableData[index].qa_pic = ''
    } else {
      releaseTableData[index].qa_pic = item.qa_pic;
    }
    console.log(item.redis_check)
    console.log(releaseTableData)
    releaseTableData[index].services = '';
    item.services_list.services_list_be.forEach((service) => {
      if (releaseTableData[index].services === '') {
        releaseTableData[index].services += `${service}`;
      } else {
        releaseTableData[index].services += `\n${service}`;
      }
      if (!be_services.includes(service)) {
        be_services.push(service)
        all_services.push(service)
      }
    })
    item.services_list.services_list_fe.forEach((service) => {
      if (releaseTableData[index].services === '') {
        releaseTableData[index].services += `${service}`;
      } else {
        releaseTableData[index].services += `\n${service}`;
      }
      if (!fe_services.includes(service)) {
        fe_services.push(service)
        all_services.push(service)
      }
    })
  })

  if (fe_services.length !== 0) {
    showfe.value = true
  }
  if (be_services.length !== 0) {
    showbe.value = true
  }
  console.log(fe_services)
  console.log(be_services)
  showresult.value = true
  // let allTrue = data_final.data.every(item => item.result !== "false");
  // console.log(allTrue);
  // location.reload();
}


async function getData(value) {
  if (!value) {
    console.log('没有选择发布单，不进行数据查询');
    // 清空显示数据和状态
    releaseTableData.splice(0, releaseTableData.length);
    showbe.value = false;
    showfe.value = false;
    showresult.value = false;
    be_services.splice(0, be_services.length);
    fe_services.splice(0, fe_services.length);
    all_services.splice(0, all_services.length);
    return;
  }
  
  showbe.value = false
  showfe.value = false
  console.log(`获取发布单数据: ${value}`)
  await getreleaseData(value);
  fullscreenLoading.value = true

  let data_final = ref();
  console.log('releaseTableData前:', releaseTableData)
  data_final.value = await autocheckdata(releaseTableData);

  fullscreenLoading.value = false
  console.log('API响应:', data_final.value);
  
  if (!data_final.value || !data_final.value.data || data_final.value.data.length === 0) {
    console.log('API返回数据为空或格式不正确');
    // 清空表格数据
    releaseTableData.splice(0, releaseTableData.length);
    showresult.value = false;
    return;
  }
  
  const releaseDataLength = releaseTableData.length;
  releaseTableData.forEach((itemA) => {
    const matchedItemB = data_final.value.data.find((itemB) => itemA.jira_key === itemB.feature_key);
    if (matchedItemB) {
      console.log('匹配项数据:', matchedItemB);

      itemA.type = matchedItemB.type;
      itemA.jira_key = matchedItemB.feature_key;
      itemA.jira_link = `https://jira.shopee.io/browse/${matchedItemB.feature_key}`;
      itemA.jira_title = matchedItemB.feature_title;
      
      // 处理bug相关字段，确保转换为数字类型
      itemA.bug_resolved = Number(matchedItemB.bug_resolved || 0);
      itemA.bug_total = Number(matchedItemB.bug_total || 0);
      console.log(`Bug数据: ${itemA.bug_resolved}/${itemA.bug_total}`);
      
      if (!matchedItemB.signoff_status) {
        itemA.sign_off = ''
      } else {
        itemA.sign_off = matchedItemB.signoff_status;
      }
      if (!matchedItemB.config_center) {
        itemA.config_center = ''
      } else {
        itemA.config_center = matchedItemB.config_center;
      }
      if (!matchedItemB.shopee_region) {
        itemA.region = ''
      } else {
        itemA.region = matchedItemB.shopee_region;
      }
      if (!matchedItemB.redis_check) {
        itemA.redis_change = ''
      } else {
        itemA.redis_change = matchedItemB.redis_check;
      }
      if (!matchedItemB.result) {
        itemA.result = ''
      } else {
        itemA.result = matchedItemB.result;
      }

      if (!matchedItemB.merge_list) {
        itemA.merge_list = ''
      } else {
        itemA.merge_list = matchedItemB.merge_list;
      }
      if (!matchedItemB.status) {
        itemA.status = ''
      } else {
        itemA.status = matchedItemB.status;
      }
      if (!matchedItemB.Code_Merged) {
        itemA.Code_Merged = ''
      } else {
        itemA.Code_Merged = matchedItemB.Code_Merged;
      }
      if (!matchedItemB.DB_Change) {
        itemA.DB_Change = ''
      } else {
        itemA.DB_Change = matchedItemB.DB_Change;
      }
      if (!matchedItemB.dev_pic) {
        itemA.dev_pic = ''
      } else {
        itemA.dev_pic = matchedItemB.dev_pic;
      }
      if (!matchedItemB.PM) {
        itemA.PM = ''
      } else {
        itemA.PM = matchedItemB.PM;
      }
      if (!matchedItemB.qa_pic) {
        itemA.qa_pic = ''
      } else {
        itemA.qa_pic = matchedItemB.qa_pic;
      }
      itemA.services = '';
      matchedItemB.services_list.services_list_be.forEach((service) => {
        if (itemA.services === '') {
          itemA.services += `${service}`;
        } else {
          itemA.services += `\n${service}`;
        }
        if (!be_services.includes(service)) {
          be_services.push(service)
          all_services.push(service)
        }
      })

      matchedItemB.services_list.services_list_fe.forEach((service) => {
        if (itemA.services === '') {
          itemA.services += `${service}`;
        } else {
          itemA.services += `\n${service}`;
        }
        if (!fe_services.includes(service)) {
          fe_services.push(service)
          all_services.push(service)
        }
      })
    }
  });

  if (fe_services.length !== 0) {
    showfe.value = true
  }
  if (be_services.length !== 0) {
    showbe.value = true
  }
  console.log('fe_services:', fe_services)
  console.log('be_services:', be_services)
  console.log('最终表格数据:', releaseTableData)
  showresult.value = true
}

function getIconName(type) {
  if (type === 'Epic') {
    return 'Epic-icon';
  } else if (type === 'Sub-task') {
    return 'ST-icon';
  } else if (type === 'Task') {
    return 'Task-icon';
  } else if (type === 'Bug') {
    return 'Bug-icon';
  } else if (type === 'Story') {
    return 'Story-icon';
  }
}

function getColorName(status) {
  if (status === 'TO DO') {
    return '#42526e';
  } else if (status === 'Done') {
    return '#00875a';
  } else if (status === 'Waiting') {
    return '#42526e';
  } else if (status === 'Icebox') {
    return '#0052CC'
  } else if (status === 'Doing') {
    return '#0052CC'
  } else if (status === 'UAT') {
    return '#0052CC'
  } else if (status === 'Delivering') {
    return '#0052CC'
  } else if (status === 'Developing') {
    return '#0052CC'
  } else if (status === 'Testing') {
    return '#0052CC'
  } else if (status === 'TECH DESIGN') {
    return '#0052CC'
  } else {
    return '#0052CC'
  }
}


function getBigName(status) {
  if (status === 'TO DO') {
    return 'TO DO';
  } else if (status === 'Done') {
    return 'DONE';
  } else if (status === 'Waiting') {
    return 'WAITING';
  } else if (status === 'Icebox') {
    return 'ICEBOX';
  } else if (status === 'Doing') {
    return 'DOING'
  } else if (status === 'UAT') {
    return 'UAT'
  } else if (status === 'Delivering') {
    return 'DELIVERING'
  } else if (status === 'Developing') {
    return 'DEVELOPING'
  } else if (status === 'Testing') {
    return 'TESTING'
  } else {
    return status
  }
}


function getStatusName(status) {
  if (status === 'TO DO') {
    return 'info';
  } else if (status === 'Done') {
    return 'success';
  } else if (status === 'Waiting') {
    return 'info';
  } else if (status === 'Icebox') {
    return 'icebox';
  } else if (status === 'Doing') {
    return 'doing';
  } else if (status === 'UAT') {
    return 'uat';
  } else if (status === 'Delivering') {
    return 'delivering'
  } else if (status === 'Developing') {
    return 'developing'
  } else if (status === 'Testing') {
    return 'testing'
  }
}



onMounted(async () => {
  const new_releaseList = await get_jira_release_list_finished();
  const releaseData = new_releaseList.data.data;

  if (selectedProject.value) {
    if (releaseData.some(item => item.title === selectedProject.value)) {
    } else {
      selectedProject.value = '';
    }
  }

  if (!selectedProject.value) {
    const currentDate = new Date();
    let closestDate = null;
    releaseData.forEach((str) => {
          console.log(str)
          const match = str.title.match(/(bus|adhoc|hotfix)-(\d{8})/i);
          if (match) {
            const dateString = match[2];
            const year = dateString.slice(0, 4);
            const month = dateString.slice(4, 6) - 1;
            const day = dateString.slice(6, 8);
            const date = new Date(year, month, day, 0, 0, 0);
            currentDate.setHours(0, 0, 0, 0);
            if (date >= currentDate && (!closestDate || date < closestDate)) {
              closestDate = date;
              selectedProject.value = str.title;
              console.log(selectedProject)
            }
          }
        }
    );
  }
  selectedRelease.value = releaseData;
  releaseData.forEach((item) => {
        projects.push(item);
      }
  )

  // 初始化过滤的项目列表
  filterProjects();


// 检查页面上是否有内容
  if (document.body.innerHTML.trim().length > 0) {
    // 选择具有"el-empty"类的元素
    const emptyElement = document.querySelector('.el-empty');

    // 如果有内容，隐藏具有"el-empty"类的元素
    if (emptyElement) {
      emptyElement.style.display = 'none';
    }
  }
});

const form = reactive({
  name: '',
  merge: true,
});

watch(selectedProject, (newValue, oldValue) => {
  if (newValue !== '' && newValue !== oldValue) {
    releaseTableData.splice(0, releaseTableData.length);
    getData(newValue);
  }
  form.name = removeReleasePrefix(newValue);
});


watch(releaseTableData, (newValue, oldValue) => {
  localStorage.setItem('releaseTableData', JSON.stringify(releaseTableData))
  if (releaseTableData.length === 0) {
    show.value = false
  }
  if (releaseTableData.length !== 0) {
    show.value = true
  }
});

watch(all_services, (newValue, oldValue) => {
  if (all_services.length === 0) {
    services_pane.value = false
  }
  if (all_services.length !== 0) {
    services_pane.value = true
  }
});

watchEffect(() => {
  const savedOption = localStorage.getItem('selectedProject');
  let saveactive = localStorage.getItem('active');

  if (savedOption) {
    selectedProject.value = savedOption
  }
  if (saveactive) {
    active.value = saveactive
  }
})


watchEffect(() => {
  localStorage.setItem('selectedProject', selectedProject.value);
})

function removeReleasePrefix(refParam) {
  if (refParam) {
    let modifiedString = refParam.replace(/【Release】|发布单/g, "").replace(/\s/g, "");
    console.log(modifiedString);
    return modifiedString;
  }
  return "";
}




//平台1组
const selectedData1 = ref([]); // 保存选中的表格数据
//平台2组
const selectedData2 = ref([]); // 保存选中的表格数据
//功能组
const selectedData3 = ref([]); // 保存选中的表格数据
//前端组
const selectedData4 = ref([]); // 保存选中的表格数据


const handleSelectionChange1 = (selection) => {
  selectedData1.value = [];
  selectedData1.value = selection;
  console.log(processedData1.value);
  console.log(selectedData1.value);
};
const processedData1 = computed(() => {
  return selectedData1.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});
const getRowKey = (row: any) => row.id;
const defaultSort = {
  prop: 'id',
  order: 'ascending'
};
const handleSortChange = ({ newIndex, oldIndex }: { newIndex: number, oldIndex: number }) => {
  const movedItem = processedData1.value.splice(oldIndex, 1)[0];
  processedData1.value.splice(newIndex, 0, movedItem);
};
const handleSelectionChange2 = (selection) => {
  selectedData2.value = []
  selectedData2.value = selection;
};
const processedData2 = computed(() => {
  return selectedData2.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});
const handleSelectionChange3 = (selection) => {
  selectedData3.value = []
  selectedData3.value = selection;
};
const processedData3 = computed(() => {
  return selectedData3.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});
const handleSelectionChange4 = (selection) => {
  selectedData4.value = []
  selectedData4.value = selection;
};
const processedData4 = computed(() => {
  return selectedData4.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});


let mrTableData = reactive([]);
const submitForm = () => {
  dialogVisible.value = false;
  fullscreenLoadingMR.value = true;
  const mergedArray = selectedData1.value.concat(selectedData2.value, selectedData3.value, selectedData4.value);
  const uniqueArray = Array.from(new Set(mergedArray));

  const newArray = uniqueArray.map(item => item.name);
  console.log(newArray);
  const newData = {
    "services_list": newArray,
    "title": form.name,
    "if_merge": form.merge
  };
  console.log(newData);
  let data_fin = newMerge(newData);

  data_fin.then((result) => {
    console.log(result['repo']);
    for (let i in result['repo']) {
      console.log(i);
      console.log(result['repo'][i])
      mrTableData.push({
        repo: result['repo'][i]["repo"],
        url: result['repo'][i]["url"],
        status: result['repo'][i]["status"],
      });

    }
  });
  fullscreenLoadingMR.value = false;
  console.log(mrTableData)
};

watch(selectedProjectFinished, (newValue, oldValue) => {
  if (newValue !== '') {
    releaseTableData.splice(0, releaseTableData.length);
    getData(newValue);

  }

});
watchEffect(() => {
  const savedOption = localStorage.getItem('selectedProjectFinished');
  let saveactive = localStorage.getItem('active');

  if (savedOption) {
    selectedProjectFinished.value = savedOption
  }
  if (saveactive) {
    active.value = saveactive
  }
})
watchEffect(() => {
  localStorage.setItem('selectedProjectFinished', selectedProjectFinished.value);
})

// 监听过滤类型变化
watch(releaseTypeFilter, (newValue) => {
  // 保存过滤类型到本地存储
  localStorage.setItem('releaseTypeFilter', releaseTypeFilter.value);
  
  // 更新过滤项目
  filterProjects();
  
  // 过滤后自动选择最近的发布单
  if (filteredProjects.value.length > 0) {
    selectClosestRelease();
  } else {
    // 如果没有过滤结果，清空选择
    selectedProjectFinished.value = '';
    // 同时清空表格数据
    releaseTableData.splice(0, releaseTableData.length);
  }
});

// 监听项目选择变化
watch(selectedProjectFinished, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    releaseTableData.splice(0, releaseTableData.length);
    getData(newValue);
  }
  form.name = removeReleasePrefix(newValue);
});

// 选择最近的发布单
const selectClosestRelease = () => {
  const today = new Date();
  let closestIndex = -1;
  let minDiff = Infinity;
  let matchedProject = null;
  
  console.log('开始寻找最近的发布单...');
  
  filteredProjects.value.forEach((project, index) => {
    // 尝试用不同格式匹配日期
    // 格式1: Bus-YYMMDD / Adhoc-YYMMDD / Hotfix-YYMMDD
    const regex1 = /(?:Bus|Adhoc|Hotfix)-(\d{6})/;
    // 格式2: Bus-YYYYMMDD / Adhoc-YYYYMMDD / Hotfix-YYYYMMDD
    const regex2 = /(?:Bus|Adhoc|Hotfix)-(\d{8})/;
    
    let match = project.title.match(regex1);
    let dateStr = null;
    let projectDate = null;
    
    // 尝试匹配YYMMDD格式
    if (match && match[1]) {
      dateStr = match[1];
      const year = parseInt('20' + dateStr.substring(0, 2));
      const month = parseInt(dateStr.substring(2, 4)) - 1; // 月份从0开始
      const day = parseInt(dateStr.substring(4, 6));
      
      projectDate = new Date(year, month, day);
    } else {
      // 尝试匹配YYYYMMDD格式
      match = project.title.match(regex2);
      if (match && match[1]) {
        dateStr = match[1];
        const year = parseInt(dateStr.substring(0, 4));
        const month = parseInt(dateStr.substring(4, 6)) - 1; // 月份从0开始
        const day = parseInt(dateStr.substring(6, 8));
        
        projectDate = new Date(year, month, day);
      }
    }
    
    if (projectDate && !isNaN(projectDate.getTime())) {
      console.log(`项目: ${project.title}, 日期: ${projectDate.toISOString().split('T')[0]}`);
      
      // 计算与今天的差值（天数）
      const timeDiff = projectDate.getTime() - today.getTime();
      const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
      
      // 优先选择未来最近的发布单或今天的发布单
      if ((dayDiff >= 0 && dayDiff < minDiff) || 
          (dayDiff === 0 && closestIndex === -1)) {
        minDiff = dayDiff;
        closestIndex = index;
        matchedProject = project;
      }
    }
  });
  
  if (closestIndex !== -1 && matchedProject) {
    console.log(`自动选择最近的发布单: ${matchedProject.title}`);
    selectedProjectFinished.value = matchedProject.title;
    
    // 加载选中项目的数据
    getData(selectedProjectFinished.value);
  } else {
    console.log('没有找到合适的发布单');
    // 清空选择和表格数据
    selectedProjectFinished.value = '';
    releaseTableData.length = 0;
  }
};

</script>

<style scoped>
a {
  text-decoration: none;
}

.index-container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

}

div {
  font-size: 12px;
  margin: 5px;
  border: 1px;
  padding: 0;
}

</style>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}

.el-table .cell {
  white-space: pre-wrap !important;
}

.table-header {
  background-color: blue;
  color: white;
}
</style>
<style scoped>
.n-gradient-text {
  font-size: 24px;
}
</style>

<style scoped>
.scrollbar-fe-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  margin: 10px; /* 删除默认外边距 */

  text-align: center;
  border-radius: 4px;
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.scrollbar-be-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  margin: 10px; /* 删除默认外边距 */
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.ml-2 {

  margin: 10px; /* 添加10像素的间距 */

}
</style>


<style scoped>
.data-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.data-item {
  width: calc(33.33% - 5px); /* 将元素宽度从原来的 calc(33.33% - 10px) 调整为 calc(33.33% - 5px) */
  margin-bottom: 20px;
  border-radius: 5px;
  padding: 10px;
  background-color: #f5f5f5;
}

.data-item__title {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.data-item__content {
  color: #666;
}
</style>

<style scoped>
.itxst {
  width: 600px;
  display: flex;
}

.itxst > div:nth-of-type(1) {
  flex: 1;
}

.itxst > div:nth-of-type(2) {
  width: 270px;
  padding-left: 20px;
}

.item {
  border: solid 1px #eee;
  padding: 6px 10px;
  text-align: left;
}

.item:hover {
  cursor: move;
}

.item + .item {
  margin-top: 10px;
}

.ghost {
  border: solid 1px rgb(19, 41, 239);
}

.chosenClass {
  background-color: #f1f1f1;
}
</style>

<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}

.el-card + .el-card {
  margin-top: 20px;
}


</style>
<style>
.ar-container {
  display: flex;
  align-items: center;

}

</style>

<style>
.Epic-icon {
  background-image: url('@/icons/svg/epic.svg');
  width: 16px !important;
  height: 16px !important;
}

.ST-icon {
  background-image: url('@/icons/svg/sub-task.svg');
  width: 16px !important;
  height: 16px !important;
}

.Bug-icon {
  background-image: url('@/icons/svg/bug.svg');
  width: 16px !important;
  height: 16px !important;
}

.Story-icon {
  background-image: url('@/icons/svg/story.svg');
  width: 16px !important;
  height: 16px !important;
}

.Task-icon {
  background-image: url('@/icons/svg/task.svg');
  width: 16px !important;
  height: 16px !important;
}
</style>

<!--<style>-->
<!--//.el-table th {-->
<!--//  background-color: #cacfd7 !important; -->
<!--//  color: #333 !important; -->
<!--//} -->

<!--</style>-->
<style>
.to-do-text {
  background-color: #42526e;
  border-color: #42526e;
  color: #fff;
}

.done-text {
  background-color: #00875a;
  border-color: #00875a;
  color: #fff;
}

.doing-text {
  background-color: #0052cc;
  border-color: #0052cc;
  color: #fff;
}

.delivering-text {
  background-color: #0052cc;
  border-color: #0052cc;
  color: #fff;
}

.developing-text {
  background-color: #0052cc;
  border-color: #0052cc;
  color: #fff;
}

.waiting-text {
  background-color: #42526e;
  border-color: #42526e;
  color: #fff;
}

.bold-text {
  font-weight: bold;
}
</style>

<style scoped>
/* ... existing code ... */

.el-radio-group {
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .ar-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
  }
  
  .el-radio-group {
    margin-bottom: 0;
  }
}
</style>


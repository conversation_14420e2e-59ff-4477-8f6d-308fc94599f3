<template>
  <el-radio-group v-model="projectType" class="project-type-filter" @change="handleProjectTypeChange">
    <el-radio-button label="SPCB">SPCB</el-radio-button>
    <el-radio-button label="SPCT">SPCT</el-radio-button>
  </el-radio-group>
</template>

<script setup>
import { ref } from 'vue';

const projectType = ref(localStorage.getItem('projectTypeFilter') || 'SPCB');
const emit = defineEmits(['update:projectType']);

const handleProjectTypeChange = () => {
  localStorage.setItem('projectTypeFilter', projectType.value);
  emit('update:projectType', projectType.value);
};
</script>

<style scoped>
.project-type-filter {
  margin-right: 10px;
}
</style>
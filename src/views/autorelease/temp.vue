<template>
  <div class="index-conntainer">
    <transition name="el-fade-in-linear">
      <el-card class="card" shadow="hover" width="50px" height="50px">
        <div class="ar-container">
          <el-select v-model="selectedProject"
                     filterable
                     clearable
                     placeholder="请选择发布单"
                     style="width: 380px;"
          >
            <el-option
                v-for="project in projects"
                :key="project.title"
                :label="project.title"
                :value="project.title"
            >
              <span style="
          float: left;
          font-size: 13px;
        ">
                <el-link :href="'https://jira.shopee.io/browse/' + project.key" target="_blank" :underline="false">
                            <el-tooltip
                                class="box-item"
                                effect="customized"
                                content="点击跳转到JIRA"
                                placement="left-start"

                            >
                <el-tag type="danger">{{ project.key }}</el-tag>
                            </el-tooltip>
                </el-link>
              </span>
              <el-tooltip
                  class="box-item"
                  effect="customized"
                  content="点击拉取数据并且展示发布详情"
                  placement="right-start"

              >
              <span
                  style="
          float: left;
        "
              >{{ project.title }}</span>
              </el-tooltip>
            </el-option>
          </el-select>

          <el-tooltip
              class="box-item"
              effect="customized"
              content="点击进行后台数据更新"
              placement="top-start"
          >
            <el-button

                size="default"
                type="success"

                @click="refreshData(selectedProject)"
                :icon="Refresh"
                element-loading-text="AR正在进行后台数据更新，请耐心等待"
                v-loading.fullscreen.lock="fullscreenLoading"
            >更新
            </el-button>

          </el-tooltip>
          <el-tooltip
            class="box-item"
            effect="customized"
            content="点击发送Signed off字段提醒到seatalk"
            placement="top-start"
          >
            <el-button type="primary" size="default" @click="signedOffSeatalk" :icon="ChatRound">
              Signed off提醒
            </el-button>

          </el-tooltip>
          <el-tooltip
              class="box-item"
              effect="customized"
              content="点击发送MR提醒到seatalk"
              placement="top-start"

          >
            <el-button type="primary" size="default" @click="callMRseatalkFE" :icon="ChatRound">
              MR提醒
            </el-button>

          </el-tooltip>
          <el-tooltip
              class="box-item"
              effect="customized"
              content="点击发送checklist字段提醒到seatalk"
              placement="top-start"
          >
            <el-button type="primary" size="default" @click="callseatalk" :icon="ChatRound">
              Checklist提醒
            </el-button>

          </el-tooltip>

          <el-tooltip
              class="box-item"
              effect="customized"
              content="点击跳转monitor看板"
              placement="top-start"
          >
            <el-button type="danger" size="default" @click="openKB" :icon="ChatRound">
              monitor看板
            </el-button>

          </el-tooltip>
          <el-drawer v-model="visible" :show-close="false">
            <template #header="{ titleId, titleClass }">
              <h4 :id="titleId" :class="titleClass">{{ selectedProject }}</h4>
            </template>
            暂无失败信息。
          </el-drawer>
          <div class="ar-container">
            <el-icon :size="20">
              <Bell/>
            </el-icon>
            <el-text class="mx-1" type="warning" size="default">后台会不断更新数据，请自行刷新页面</el-text>
          </div>
        </div>

        <div class="ar-container">
          <el-table

              :data="releaseTableData"
              stripe
              border

              highlight-current-row
              fit
              :header-cell-style="{background:'#cacfd7',color:'#606266'}"
              :empty-text="'暂无数据'"
          >
            <el-table-column label="编号" min-width="21" header-align="center" align="center">
              <template #default="scope">
                {{scope.$index+1}}
              </template>
            </el-table-column>
            <el-table-column prop="type"
                             label="类型"
                             header-align="center" align="center"
                             min-width="30"
                             :filters="[
                            { text: 'Epic', value: 'Epic' },
                            { text: 'Bug', value: 'Bug' },
                            { text: 'Task', value: 'Task' },
                            { text: 'Sub-task', value: 'Sub-task' },
                            { text: 'Story', value: 'Story' },
                            ]"
                             :filter-method="filterType"
                             filter-placement="bottom-end"
            >

              <template #default="{ row }">

                <el-icon :class="getIconName(row.type)"></el-icon>
              </template>

            </el-table-column>

            <el-table-column prop="jira_key" label="单号" :min-width="60" header-align="center" align="center"
            >
              <template #default="{ row }">
                <el-link
                    :underline="false"
                    v-bind:href="row.jira_link"
                    target="_blank"
                    type="primary">
                  {{ row.jira_key }}
                </el-link>
              </template>
            </el-table-column>


            <el-table-column prop="jira_title" label="需求名" :min-width="150"
            >

            </el-table-column>

            <el-table-column label="周一" header-align="center" align="center">
              <el-table-column prop="sign_off" label="Signed off" header-align="center" align="center" min-width="40">


                <template #header slot-scope="scope">
                  Signed<br>off
                </template>
                <template #default="{ row }">

                  <el-icon
                      v-if="row.sign_off === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.sign_off === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.sign_off === '' ? true : false"
                      :size="20"
                      :color="row.sign_off === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="周二" header-align="center" align="center">
              <el-table-column type="expand" label="提MR" min-width="32">

                <template #default="props">
                  <div>
                    <div class="ar-container">
                      <el-tooltip
                          class="box-item"
                          effect="customized"
                          content="点击创建MR"
                          placement="top-start"
                      >
                        <el-button
                            type="danger"
                            @click="centerDialogVisible = true"
                            size="small"
                            :icon="CirclePlus"
                            element-loading-text="AR正在创建MR, 请耐心等待..."
                            v-loading.fullscreen.lock="fullscreenLoading">创建
                        </el-button>
                      </el-tooltip>

                      <el-tooltip
                          class="box-item"
                          effect="customized"
                          content="点击复制"
                          placement="top-start"
                      >
                        <el-button
                            type="primary"
                            size="small"
                            @click="copyToClipboard(props)"
                            :icon="ChatRound"
                            element-loading-text="AR正在处理数据，请耐心等待..."
                            v-loading.fullscreen.lock="fullscreenLoading">复制
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                          class="box-item"
                          effect="customized"
                          content="点击发送MR提醒到seatalk"
                          placement="top-start"
                      >
                        <el-button
                            type="primary"
                            size="small"
                            @click="sendSingleFeatureToCT(props)"
                            :icon="ChatRound"
                            element-loading-text="AR正在处理数据，请耐心等待..."
                            v-loading.fullscreen.lock="fullscreenLoading">发送
                        </el-button>
                      </el-tooltip>
                    </div>
                    <el-dialog
                        v-model="centerDialogVisible"
                        title="Warning"
                        width="30%"
                        align-center
                    >
                      <span>请确认是否开始自动提MR？发布单： {{ selectedProject }}</span>
                      <template #footer>
                        <span class="dialog-footer">
                          <el-button @click="centerDialogVisible = false">取消</el-button>
                          <el-button type="primary" @click="startSingleAR(props)">
                            确认
                          </el-button>
                        </span>
                      </template>
                    </el-dialog>
                    <el-table :data="props.row.merge_list" border
                              :header-cell-style="{background:'#def1ce',color:'#606266'}"
                    >
                      <el-table-column label="仓库" prop="repo_name"/>
                      <el-table-column label="分支" prop="branch_name"/>
                      <el-table-column label="PIC" prop="pic"/>
                      <el-table-column label="MR地址" prop="web_url">
                        <template #default="{row}">
                          <a
                              :href="row.web_url"
                              target="_blank"
                          >{{ row.web_url }}
                          </a>
                        </template>
                      </el-table-column>
                      <el-table-column label="MR状态" prop="merge_status"/>
                      <el-table-column label="MR作者" prop="author"/>
                    </el-table>
                  </div>
                </template>

              </el-table-column>

              <el-table-column prop="Code_Merged" label="Code Merged" header-align="center" align="center"
                               min-width="40"
              >
                <template #header slot-scope="scope">
                  Code<br>Merged
                </template>
                <template #default="{ row }">
                  <el-icon
                      v-if="row.Code_Merged === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.Code_Merged === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.Code_Merged === '' ? true : false"
                      :size="20"
                      :color="row.Code_Merged === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="周三" header-align="center" align="center">
              <el-table-column prop="config_center" label="Config Changed" header-align="center" align="center"
                               min-width="43"
              >
                <template #header slot-scope="scope">
                  Config<br>Changed
                </template>
                <template #default="{ row }">
                  <el-icon
                      v-if="row.config_center === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.config_center === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.config_center === '' ? true : false"
                      :size="20"
                      :color="row.config_center === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
              <el-table-column prop="DB_Change" label="DB Changed" header-align="center" align="center" min-width="43">
                <template #default="{ row }">
                  <el-icon
                      v-if="row.DB_Change === 'Confirmed' ? true : false"
                      :size="20"
                      :color="row.DB_Change === 'Confirmed' ? '#67c23a' : '#F56C67'"
                  >
                    <SuccessFilled/>
                  </el-icon>
                  <el-icon
                      v-if="row.DB_Change === '' ? true : false"
                      :size="20"
                      :color="row.DB_Change === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
                <template #header slot-scope="scope">
                  DB<br>Changed
                </template>
              </el-table-column>
              <el-table-column
                  prop="services"
                  label="services"
                  min-width="180"
                  header-align="center"
                  align="center"
                  :style="{ 'white-space': 'pre-wrap' }"
              >
                <template #default="{ row }">
                    <div v-if="row.services !== ''">
                        <template v-for="(service, index) in row.services.split('\n')">
                            <div :style="{ color: !checkServiceGrouping(service) ? '#F56C67' : 'inherit' }">
                                {{ service }}
                            </div>
                        </template>
                    </div>
                    <el-icon
                        v-if="row.services === ''"
                        :size="20"
                        :color="'#F56C67'"
                    >
                        <CircleCloseFilled/>
                    </el-icon>
                </template>
              </el-table-column>

              <el-table-column prop="region" label="Region" header-align="center" align="center" min-width="37">
                <template #default="{ row }">
                  <el-text
                      v-if="row.region !== '' ? true : false"
                      :size="20"
                      :color="row.region === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    {{ row.region }}
                  </el-text>
                  <el-icon
                      v-if="row.region === '' ? true : false"
                      :size="20"
                      :color="row.region === 'pass' ? '#67c23a' : '#F56C67'"
                  >
                    <CircleCloseFilled/>
                  </el-icon>
                </template>
              </el-table-column>
            </el-table-column>


            <el-table-column prop="PM" label="PM" header-align="center" align="center" min-width="50">
              <template #default="{ row }">
                {{ row.PM }}
              </template>
            </el-table-column>

            <el-table-column prop="dev_pic" label="DEV PIC" header-align="center" align="center" min-width="40">
              <template #header slot-scope="scope">
                DEV<br>PIC
              </template>
              <template #default="{ row }">
                {{ row.dev_pic }}
              </template>
            </el-table-column>

            <el-table-column prop="qa_pic" label="QA" header-align="center" align="center" min-width="50">
              <template #default="{ row }">
                {{ row.qa_pic }}
              </template>
            </el-table-column>


            <el-table-column prop="status" label="Status" header-align="center" align="center" min-width="60">
              <template #default="{ row }">

                <el-tag class="bold-text"
                        effect="dark"
                        :type="getStatusName(row.status)"
                        :color="getColorName(row.status)"
                >{{ getBigName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

        </div>
        <div class="ar-container">
        <el-text class="mx-1" type="danger">请勾选下面的服务，点击一键四连启动发布流程。前端4个仓库只支持打TAG，不支持合代码，请找bin.wang合代码，4个仓库为仓库为：</el-text>
        <el-link href="https://git.garena.com/shopee/marketing/web-chatbot"  :underline="false" type="danger" target="_blank">web-chatbot、</el-link>
        <el-link href="https://git.garena.com/shopee/seller-fe/cs-chat"  :underline="false" type="danger" target="_blank">cs-chat、</el-link>
        <el-link href="https://git.garena.com/shopee/chatbot/web-chatbot-csat"  :underline="false" type="danger" target="_blank">web-chatbot-csat、</el-link>
        <el-link href="https://git.garena.com/shopee/chatbot/web-csat-rn"  :underline="false" type="danger" target="_blank">web-csat-rn。</el-link>
        </div>

        <div style="display: flex;">

          <el-table

              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#e78181',color:'#f8f7f7'}"
              :data="IN_pingGroupA"
              style="width: 100%"
              @selection-change="handleSelectionChange1"
              :empty-text="'暂无数据'"
          >
            <el-table-column type="selection" width="55"/>
            <el-table-column header-align="center" label="平台BE1组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#819ee7',color:'#f8f7f7'}"
              header-align="center"
              :data="IN_pingGroupB"
              style="width: 100%"
              @selection-change="handleSelectionChange2"
              :empty-text="'暂无数据'"
          >
            <el-table-column type="selection" width="55"/>
            <el-table-column header-align="center" label="平台BE2组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#81e7c8',color:'#f8f7f7'}"
              header-align="center"
              :data="IN_featureGroup"
              style="width: 100%"
              @selection-change="handleSelectionChange3"
              :empty-text="'暂无数据'"
          >
            <el-table-column type="selection" width="55"/>
            <el-table-column header-align="center" label="功能BE组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
              ref="multipleTableRef"
              border
              :header-cell-style="{background:'#e7a881',color:'#f8f7f7'}"
              header-align="center"
              :data="IN_feGroup"
              style="width: 100%"
              @selection-change="handleSelectionChange4"
              :empty-text="'暂无数据'"
          >
            <el-table-column type="selection" width="55"/>
            <el-table-column header-align="center" label="FE组">
              <template #default="scope">
                <el-link :href="scope.row.link" target="_blank" :underline="false">{{ scope.row.name }}</el-link>
              </template>
            </el-table-column>
          </el-table>


        </div>

      </el-card>
    </transition>
    <el-card class="card" shadow="hover" width="50px" height="50px">


      <el-form
        :model="form"
        label-width="auto"


      >
        <div style="display: flex; justify-content: center;">
          <el-dialog
            v-model="dialogVisible"
            title="live-发布确认"
            width="30%"
            :before-close="handleClose"
          >
            <span>您正在进行live自动发布流程，请确认您的操作是否要继续。</span>
            <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">
          确认发布
        </el-button>
      </span>
            </template>
          </el-dialog>
        </div>
        <div style="display: flex;">
        <el-form-item
          label="标题"
          style="max-width: 460px"
        >
          <el-input
            v-model="form.name"
          />


        </el-form-item>
                      <el-tag class="ml-2" size="large">标题规范：bus/adhoc/bugfix-YYYYMMDD。若当日多次同类型发布，在标题后面自增号码以区分，示例：adhoc-20230831-1、adhoc-20230831-2。</el-tag>

        </div>
        <div style="display: flex;">
        <el-form-item label="是否合代码">
          <el-tooltip
              class="box-item"
              effect="customized"
              content="勾选则从master合代码到release，不勾选则默认只打TAG"
              placement="top-start"

          >
            <el-switch v-model="form.merge"/>
          </el-tooltip>
        </el-form-item>

        <el-form-item>

          <el-tooltip
              class="box-item"
              effect="customized"
              content="创建MR、合代码到release、打TAG、预编译"
              placement="top-start"
          >
            <el-button  type="success"
                        v-loading.fullscreen.lock="fullscreenLoadingMR"
                        element-loading-text="正在处理，辛苦等待一下，中途请不要关闭此页面..."
                        @click="dialogVisible = true"
                        >一键四连</el-button>
          </el-tooltip>

        </el-form-item>
        </div>
      </el-form>
      <el-table :data="mrTableData"
                border style="width: 100%"
                :header-cell-style="{background:'#cacfd7',color:'#606266'}"
                :empty-text="'暂无数据'"
      >
        <el-table-column prop="repo" label="仓库"  header-align="center" width="180" />
        <el-table-column prop="url" label="MR地址" header-align="center" width="500">
          <template #default="{row}">
            <a
              :href="row.url"
              target="_blank"
            >{{ row.url }}
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" header-align="center" />
      </el-table>
    </el-card>
  </div>


</template>

<script lang="ts" setup>
import {ref, onMounted, computed, watch, reactive, watchEffect, nextTick, toRef, toRaw, onUnmounted} from 'vue';
import type {ElTree} from 'element-plus'
import draggable from "vuedraggable";
import {copymsg} from '@/api/copymsg';
import CircularJSON from 'circular-json';
import Sortable from "sortablejs";




import {
  ElPagination,
  ElCard,
  ElTable,
  ElTableColumn,
  ElTag,
  ElSelect,
  ElOption,
  ElMessage,
  ElMessageBox,
  TableColumnCtx,
  TableInstance,
} from 'element-plus';
import {Eleme, Loading, Refresh, CirclePlus, ChatRound, Bell} from '@element-plus/icons-vue'
import axios from 'axios';
import {Edit, View as IconView} from '@element-plus/icons-vue';
import {ChatLineRound, Male} from '@element-plus/icons-vue';
import {read_json} from '@/api/read_json';
import {get_release_tag} from '@/api/get_release_tag';
import {send_title} from '@/api/send_title';
import {startAuto} from '@/api/startAuto';
import {autocheckdata} from '@/api/autocheckdata';
import {seatalk} from '@/api/seatalk';
import {callMRseatalk} from '@/api/callMRseatalk';
import {autochecknewdata} from '@/api/autochecknewdata';
import {start_single_ar} from '@/api/start_single_ar';
import {mr_seatalk_single_feature_msg} from '@/api/mr_seatalk_single_feature_msg';
import {get_key_jira_release_list} from '@/api/get_key_jira_release_list';
import {newMerge} from '@/api/newMerge';
import {ElButton, ElDrawer} from 'element-plus'
import {CircleCloseFilled} from '@element-plus/icons-vue'
import type {TabsPaneContext} from 'element-plus'
//import {consoleLog} from "echarts/types/src/util/log";
import {signedOff} from '@/api//signedOff';
const dialogVisible = ref(false)

const fullscreenLoadingMR = ref(false);

const dialogTableVisible = ref(false)
const centerDialogVisible = ref(false)
const activeName = ref('first')
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
const fe_services = reactive([]);
const be_services = reactive([]);
const all_services = reactive([]);


const pingGroupA = reactive([
  {
    'shopee-chatbot-intent': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intent',
    'shopee-chatbot-admin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.admin',
    'shopee-chatbot-adminasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminasynctask',
    'shopee-chatbot-adminconfigservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminconfigservice',
    'shopee-chatbot-adminservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminservice',
    'shopee-chatbot-agentcontrol': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.agentcontrol',
    'shopee-chatbot-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.asynctask',
    'shopee-chatbot-auditlog': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.auditlog',
    'shopee-chatbot-botapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi',
    'shopee-chatbot-context': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.context',
    'shopee-chatbot-dm': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.dm',
    'shopee-chatbot-featurecenter': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featurecenter',
    'shopee-chatbot-intentclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intentclarification',
    'shopee-chatbot-messageasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageasynctask',
    'shopee-chatbot-messageservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageservice',
    'shopee-chatbot-messageverification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageverification',
    'shopee-chatbot-nlu': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.nlu',
    'shopee-chatbot-ordercard': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.ordercard',
    'shopee-chatbot-pilotapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.pilotapi',
    'shopee-chatbotcommon-adminasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminasynctask',
    'shopee-chatbotcommon-adminconfigservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminconfigservice',
    'shopee-chatbotcommon-adminservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminservice',
    'shopee-chatbotcommon-agentcontrol': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.agentcontrol',
    'shopee-chatbotcommon-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.asynctask',
    'shopee-chatbotcommon-botapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.botapi',
    'shopee-chatbotcommon-context': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.context',
    'shopee-chatbotcommon-dm': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.dm',
    'shopee-chatbotcommon-featurecenter': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featurecenter',
    'shopee-chatbotcommon-nlu': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu',
    'shopee-chatbotcommon-productrecommend': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.productrecommend',
    'shopee-chatbotcommon-rulebaseservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.rulebaseservice',
    'shopee-chatbotcommon-shopconsole': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsole',
    'shopee-chatbot-websocketgwy':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.websocketgwy'

  }]);

const pingGroupB = reactive([{
  'shopee-chatbot-autotraining': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining',
  'shopee-annotation-admin':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.admin',
  'shopee-annotation-asynctask':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.asynctask',
  'shopee-agorithmservice-component': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.ai_engineering.nlu_component',
  'shopee-chatbot-experimentmanagement': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.experimentmanagement',
  'shopee-chatbot-featureapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featureapiproxy',
  'shopee-chatbot-modelgw': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.modelgw',
  'shopee-chatbot-realtime': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.realtime',
  'shopee-chatbot-recallmanager': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallmanager',
  'shopee-chatbot-recallservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallservice',
  'shopee-chatbot-recommendation': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recommendation',
  'shopee-chatbotcommon-apadmin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadmin',
  'shopee-chatbotcommon-apasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apasynctask',
  'shopee-chatbotcommon-component': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu_component',
  'shopee-chatbotcommon-experimentmanagement': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.experimentmanagement',
  'shopee-chatbotcommon-featureapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featureapiproxy',
  'shopee-chatbotcommon-intent': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intent',
  'shopee-chatbotcommon-kbadmin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmin',
  'shopee-chatbotcommon-kbapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbapi',
  'shopee-chatbotcommon-kbasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbasynctask',
  'shopee-chatbotcommon-kblabelclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kblabelclarification',
  'shopee-chatbotcommon-modelgw': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.modelgw',
  'shopee-knowledgebase-admin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.admin',
  'shopee-knowledgebase-api': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.api',
  'shopee-knowledgebase-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.asynctask',
  'shopee-knowledgebase-labelclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.labelclarification',
  'shopee-chatbotcommon-promptmanagements':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.promptmanagements'
}]);
const featureGroup = reactive([{
  'shopee-chatbot-api': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.api',
  'shopee-chatbot-autotraining': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining',
  'shopee-chatbotcommon-tfapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfapiproxy',
  'shopee-chatbotcommon-tfeditor': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeditor',
  'shopee-chatbotcommon-tfserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfserving',
  'shopee-chatbotcommon-tfvariateserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfvariateserving',
  'shopee-taskflow-apiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.apiproxy',
  'shopee-taskflow-editor': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.editor',
  'shopee-taskflow-taskflowserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowserving',
  'shopee-taskflow-taskflowsop': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowsop',
  'shopee-taskflow-variateserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.variateserving'
}]);

const feGroup = reactive([
  {
    'shopee-autotrainingportal-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.autotrainingportal.adminstatic',
    'shopee-annotation-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.adminstatic',
    'shopee-cbrcmdplt-rcmdpltstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.cbrcmdplt.rcmdpltstatic',
    'shopee-chatbot-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminstatic',
    'shopee-chatbot-chatbotcsatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotcsatstatic',
    'shopee-chatbot-chatbotrnstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotrnstatic',
    'shopee-chatbot-chatbotstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotstatic',
    'shopee-chatbot-csatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.csatstatic',
    'shopee-chatbot-dashboardstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.dashboardstatic',
    'shopee-chatbot-tmcstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.tmcstatic',
    'shopee-chatbotcommon-admincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.admincommonsaasstatic',
    'shopee-chatbotcommon-adminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminsaasstatic',
    'shopee-chatbotcommon-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminstatic',
    'shopee-chatbotcommon-annotationadminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.annotationadminstatic',
    'shopee-chatbotcommon-apadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadminsaasstatic',
    'shopee-chatbotcommon-csatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.csatstatic',
    'shopee-chatbotcommon-kbadmincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmincommonsaasstatic',
    'shopee-chatbotcommon-kbadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadminsaasstatic',
    'shopee-chatbotcommon-shopconsolestatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsolestatic',
    'shopee-chatbotcommon-static': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.static',
    'shopee-chatbotcommon-tfeadmincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadmincommonsaasstatic',
    'shopee-chatbotcommon-tfeadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadminsaasstatic',
    'shopee-chatbotcommon-tmcsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tmcsaasstatic',
    'shopee-gec-gecstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.gecstatic',
    'shopee-knowledgebase-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.adminstatic',
    'shopee-taskflow-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.adminstatic',
    'shopee-cschat-h5':'https://space.shopee.io/console/cmdb/overview/detail/shopee.customer_service_and_chatbot.customer_service.cschannel.cschat.h5',
    'shopee-knowledgeplatform-adminstatic':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.adminstatic',
    'shopee-chatbot-insights':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.adminportal.insightstatic',
    'shopee-chatbotcommon-insightssaasstatic-test':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.chatbotcommon.insightssaasstatic'

  }

])

const openKB = () => {
  window.open('https://monitoring.infra.sz.shopee.io/grafana/d/kj1f3huVk/chatbot-fa-ban-kan-ban?orgId=10&from=now-1h&to=now&refresh=30s', '_blank');
}

const IN_pingGroupA = computed(() => {
  const result = {};
  const pingGroupAKeys = pingGroupA.map(obj => Object.keys(obj)).flat();
  for (const item of be_services) {
    if (pingGroupAKeys.includes(item)) {
      const link = pingGroupA.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});
const IN_pingGroupB = computed(() => {
  const result = {};
  const pingGroupBKeys = pingGroupB.map(obj => Object.keys(obj)).flat();
  for (const item of be_services) {
    if (pingGroupBKeys.includes(item)) {
      const link = pingGroupB.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});

const IN_featureGroup = computed(() => {
  const result = {};
  const featureGroupKeys = featureGroup.map(obj => Object.keys(obj)).flat();
  for (const item of be_services) {
    if (featureGroupKeys.includes(item)) {
      const link = featureGroup.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});

const IN_feGroup = computed(() => {
  const result = {};
  const beGroupKeys = feGroup.map(obj => Object.keys(obj)).flat();
  for (const item of fe_services) {
    if (beGroupKeys.includes(item)) {
      const link = feGroup.find(obj => Object.keys(obj).includes(item))[item];
      result[item] = {
        name: item,
        link: link,
      };
    }
  }
  return Object.values(result);
});

let services_pane = ref(false);
let visible = ref(false);
let show = ref(false);
let showfe = ref(false);
let showbe = ref(false);
let showresult = ref(false);
let active = ref(0);
let jiraStatus = ref("wait");
let masterStatus = ref("wait");
const tagType = ["success", "info", "warning", "danger"];

function getRandomElement() {
  const randomIndex = Math.floor(Math.random() * tagType.length);
  console.log(tagType[randomIndex]);
  return tagType[randomIndex];
}

const selectedProject = ref();
// 每页显示条数
const fullscreenLoading = ref(false)

interface User {
  release_title: string
  jira_key: string
  jira_title: string
  jira_link: string
  type: string
}

const filterType = (value: string, row: User) => {
  return row.type === value
}

const selectedRelease = ref();
const projects = reactive([]);

let releaseTableData = reactive([]);


const callseatalk = (tab: TabsPaneContext, event: Event) => {
  let fin_data = {
    jira_title: selectedProject.value,
  };
  seatalk(fin_data);
  ElMessage({
    message: '已进行checklist消息push，请耐心等待seatalk自动发送消息。',
    type: 'success',
    duration: 5000,
  })
}

const signedOffSeatalk = (tab: TabsPaneContext, event: Event) => {
  let fin_data = {
    jira_title: selectedProject.value,
  };
  signedOff(fin_data);
  ElMessage({
    message: '已进行Signed off消息push，请耐心等待seatalk自动发送消息。',
    type: 'success',
    duration: 5000,
  })
}

const callMRseatalkFE = (tab: TabsPaneContext, event: Event) => {
  let fin_data = {
    jira_title: selectedProject.value,
  };

  callMRseatalk(fin_data);
  ElMessage({
    message: '已进行MR消息push，请耐心等待seatalk自动发送消息。',
    type: 'success',
    duration: 5000,
  })
}

async function sendSingleFeatureToCT(row) {
  let data_final = ref();
  data_final = await mr_seatalk_single_feature_msg(row.row);
  console.log(data_final)
  navigator.clipboard.writeText(data_final.data)
      .then(() => {
        ElMessage({
          message: '恭喜，MR信息已发送到seatalk！',
          type: 'success',
        })
      })
      .catch((error) => {
        ElMessage.error('MR信息发送失败！')
      });
}

async function copyToClipboard(row) {
  let data_final = ref();
  let fin_data = {
    jira_title: selectedProject.value,
  };
  data_final = await copymsg(row.row);

  console.log(data_final)
  navigator.clipboard.writeText(data_final.data)
      .then(() => {
        ElMessage({
          message: '恭喜，MR信息已复制到剪切板！',
          type: 'success',
        })
        //alert('已复制到剪贴板');
      })
      .catch((error) => {
        ElMessage.error('复制剪切板失败！')
      });
}

async function startSingleAR(props) {
  //const rawRow = toRaw(props);
  console.log(props.row);
  //const jsonString = CircularJSON.stringify(props.toJSON());

  let data_final = ref();
  data_final = await start_single_ar(props.row);

}

async function getreleaseData(value) {

  console.log(releaseTableData);
  const releaseTableDataIds = new Set(releaseTableData.map((item) =>
      item.jira_key));
  // 这里编写请求数据的异步操作
// 将 releaseTableData 中每个对象的 id 属性存入 Set 对象中

  all_services.splice(0, all_services.length);
  be_services.splice(0, be_services.length);
  fe_services.splice(0, fe_services.length);
  console.log(`get data for ${value}`);
  let fin_data = {
    title: value,
  };
  let data_final = [];
  let temp_data : any;
  temp_data = await send_title(fin_data);
  console.log(temp_data);
  console.log(await send_title(fin_data))
  console.log(temp_data);
  if (temp_data.length === 0) {
    releaseTableData.splice(0, releaseTableData.length);
  } else {
    releaseTableData.splice(0, releaseTableData.length);
    temp_data.data.forEach((item) => {
      console.log(item);
      if (!releaseTableDataIds.has(item.jira_key)) {
        releaseTableData.push(item);
        releaseTableDataIds.add(item.jira_key);
      }
      // releaseTableData.push(item);
      // releaseTableDataIds.add(item.jira_key);
    });
  }


  console.log(releaseTableData);
}

async function refreshData(value) {
  showbe.value = false
  showfe.value = false
  console.log(value)

  await getreleaseData(value);
  fullscreenLoading.value = true

  console.log(releaseTableData)
  await getNewData(value)
  fullscreenLoading.value = false

  ElMessage({
    message: '已更新状态',
    type: 'success',
    duration: 5000,
  })
}

async function getNewData(value) {
  showbe.value = false
  showfe.value = false
  console.log(value)
  await getreleaseData(value);
  fullscreenLoading.value = true

  let data_final = ref();
  console.log(releaseTableData)
  data_final.value = await autochecknewdata(releaseTableData);

  fullscreenLoading.value = false

  console.log(data_final);
  const releaseDataLength = releaseTableData.length;
  data_final.value.data.slice(0,releaseDataLength).forEach((item, index) => {
    console.log(item.result_all);

    if (!item.signoff_status) {
      releaseTableData[index].sign_off = ''
    } else {
      releaseTableData[index].sign_off = item.signoff_status;
    }
    if (!item.config_center) {
      releaseTableData[index].config_center = ''
    } else {
      releaseTableData[index].config_center = item.config_center;
    }
    if (!item.Code_Merged) {
      releaseTableData[index].Code_Merged = ''
    } else {
      releaseTableData[index].Code_Merged = item.Code_Merged;
    }
    if (!item.shopee_region) {
      releaseTableData[index].region = ''
    } else {
      releaseTableData[index].region = item.shopee_region;
    }
    if (!item.redis_check) {
      releaseTableData[index].redis_change = ''
    } else {
      releaseTableData[index].redis_change = item.redis_check;
    }
    if (!item.DB_Change) {
      releaseTableData[index].DB_Change = ''
    } else {
      releaseTableData[index].DB_Change = item.DB_Change;
    }
    if (!item.result) {
      releaseTableData[index].result = ''
    } else {
      releaseTableData[index].result = item.result;
    }

    if (!item.merge_list) {
      releaseTableData[index].merge_list = ''
    } else {
      releaseTableData[index].merge_list = item.merge_list;
    }
    if (!item.status) {
      releaseTableData[index].status = ''
    } else {
      releaseTableData[index].status = item.status;
    }
    if (!item.dev_pic) {
      releaseTableData[index].dev_pic = ''
    } else {
      releaseTableData[index].dev_pic = item.dev_pic;
    }
    if (!item.PM) {
      releaseTableData[index].PM = ''
    } else {
      releaseTableData[index].PM = item.PM;
    }
    if (!item.qa_pic) {
      releaseTableData[index].qa_pic = ''
    } else {
      releaseTableData[index].qa_pic = item.qa_pic;
    }
    console.log(item.redis_check)
    console.log(releaseTableData)
    releaseTableData[index].services = '';
    item.services_list.services_list_be.forEach((service) => {
      if (releaseTableData[index].services === '') {
        releaseTableData[index].services += `${service}`;
      } else {
        releaseTableData[index].services += `\n${service}`;
      }
      if (!be_services.includes(service)) {
        be_services.push(service)
        all_services.push(service)
      }
    })
    item.services_list.services_list_fe.forEach((service) => {
      if (releaseTableData[index].services === '') {
        releaseTableData[index].services += `${service}`;
      } else {
        releaseTableData[index].services += `\n${service}`;
      }
      if (!fe_services.includes(service)) {
        fe_services.push(service)
        all_services.push(service)
      }
    })
  })

  if (fe_services.length !== 0) {
    showfe.value = true
  }
  if (be_services.length !== 0) {
    showbe.value = true
  }
  // 识别未分组的服务
  ungrouped_services.value = getUngroupedServices(all_services, fe_services, be_services);
  console.log("未分组的服务:", ungrouped_services.value);
  console.log(fe_services)
  console.log(be_services)
  showresult.value = true
  // let allTrue = data_final.data.every(item => item.result !== "false");
  // console.log(allTrue);
  // location.reload();
}


async function getData(value) {
  showbe.value = false
  showfe.value = false
  console.log(value)
  await getreleaseData(value);
  fullscreenLoading.value = true

  let data_final = ref();
  console.log(releaseTableData)
  data_final.value = await autocheckdata(releaseTableData);

  fullscreenLoading.value = false
  console.log(data_final.value);
  const releaseDataLength = releaseTableData.length;
  releaseTableData.forEach((itemA) => {
    const matchedItemB = data_final.value.data.find((itemB) => itemA.jira_key === itemB.feature_key);
    if (matchedItemB) {

      itemA.type = matchedItemB.type;
      itemA.jira_key = matchedItemB.feature_key;
      itemA.jira_link = `https://jira.shopee.io/browse/${matchedItemB.feature_key}`;
      itemA.jira_title = matchedItemB.feature_title;
      if (!matchedItemB.signoff_status) {
        itemA.sign_off = ''
      } else {
        itemA.sign_off = matchedItemB.signoff_status;
      }
      if (!matchedItemB.config_center) {
        itemA.config_center = ''
      } else {
        itemA.config_center = matchedItemB.config_center;
      }
      if (!matchedItemB.shopee_region) {
        itemA.region = ''
      } else {
        itemA.region = matchedItemB.shopee_region;
      }
      if (!matchedItemB.redis_check) {
        itemA.redis_change = ''
      } else {
        itemA.redis_change = matchedItemB.redis_check;
      }
      if (!matchedItemB.result) {
        itemA.result = ''
      } else {
        itemA.result = matchedItemB.result;
      }

      if (!matchedItemB.merge_list) {
        itemA.merge_list = ''
      } else {
        itemA.merge_list = matchedItemB.merge_list;
      }
      if (!matchedItemB.status) {
        itemA.status = ''
      } else {
        itemA.status = matchedItemB.status;
      }
      if (!matchedItemB.Code_Merged) {
        itemA.Code_Merged = ''
      } else {
        itemA.Code_Merged = matchedItemB.Code_Merged;
      }
      if (!matchedItemB.DB_Change) {
        itemA.DB_Change = ''
      } else {
        itemA.DB_Change = matchedItemB.DB_Change;
      }
      if (!matchedItemB.dev_pic) {
        itemA.dev_pic = ''
      } else {
        itemA.dev_pic = matchedItemB.dev_pic;
      }
      if (!matchedItemB.PM) {
        itemA.PM = ''
      } else {
        itemA.PM = matchedItemB.PM;
      }
      if (!matchedItemB.qa_pic) {
        itemA.qa_pic = ''
      } else {
        itemA.qa_pic = matchedItemB.qa_pic;
      }
      itemA.services = '';
      matchedItemB.services_list.services_list_be.forEach((service) => {
        if (itemA.services === '') {
          itemA.services += `${service}`;
        } else {
          itemA.services += `\n${service}`;
        }
        if (!be_services.includes(service)) {
          be_services.push(service)
          all_services.push(service)
        }
      })

      matchedItemB.services_list.services_list_fe.forEach((service) => {
        if (itemA.services === '') {
          itemA.services += `${service}`;
        } else {
          itemA.services += `\n${service}`;
        }
        if (!fe_services.includes(service)) {
          fe_services.push(service)
          all_services.push(service)
        }
      })
    }
  });

  if (fe_services.length !== 0) {
    showfe.value = true
  }
  if (be_services.length !== 0) {
    showbe.value = true
  }
  console.log(fe_services)
  console.log(be_services)
  console.log(releaseTableData)
  showresult.value = true

}

function getIconName(type) {
  if (type === 'Epic') {
    return 'Epic-icon';
  } else if (type === 'Sub-task') {
    return 'ST-icon';
  } else if (type === 'Task') {
    return 'Task-icon';
  } else if (type === 'Bug') {
    return 'Bug-icon';
  } else if (type === 'Story') {
    return 'Story-icon';
  }
}

function getColorName(status) {
  if (status === 'TO DO') {
    return '#42526e';
  } else if (status === 'Done') {
    return '#00875a';
  } else if (status === 'Waiting') {
    return '#42526e';
  } else if (status === 'Icebox') {
    return '#0052CC'
  } else if (status === 'Doing') {
    return '#0052CC'
  } else if (status === 'UAT') {
    return '#0052CC'
  } else if (status === 'Delivering') {
    return '#0052CC'
  } else if (status === 'Developing') {
    return '#0052CC'
  } else if (status === 'Testing') {
    return '#0052CC'
  } else if (status === 'TECH DESIGN') {
    return '#0052CC'
  } else {
    return '#0052CC'
  }
}


function getBigName(status) {
  if (status === 'TO DO') {
    return 'TO DO';
  } else if (status === 'Done') {
    return 'DONE';
  } else if (status === 'Waiting') {
    return 'WAITING';
  } else if (status === 'Icebox') {
    return 'ICEBOX';
  } else if (status === 'Doing') {
    return 'DOING'
  } else if (status === 'UAT') {
    return 'UAT'
  } else if (status === 'Delivering') {
    return 'DELIVERING'
  } else if (status === 'Developing') {
    return 'DEVELOPING'
  } else if (status === 'Testing') {
    return 'TESTING'
  } else {
    return status
  }
}


function getStatusName(status) {
  if (status === 'TO DO') {
    return 'info';
  } else if (status === 'Done') {
    return 'success';
  } else if (status === 'Waiting') {
    return 'info';
  } else if (status === 'Icebox') {
    return 'icebox';
  } else if (status === 'Doing') {
    return 'doing';
  } else if (status === 'UAT') {
    return 'uat';
  } else if (status === 'Delivering') {
    return 'delivering'
  } else if (status === 'Developing') {
    return 'developing'
  } else if (status === 'Testing') {
    return 'testing'
  }
}



onMounted(async () => {
  const new_releaseList = await get_key_jira_release_list();
  const releaseData = new_releaseList.data.data;

  if (selectedProject.value) {
    if (releaseData.some(item => item.title === selectedProject.value)) {
    } else {
      selectedProject.value = '';
    }
  }

  if (!selectedProject.value) {
    const currentDate = new Date();
    let closestDate = null;
    releaseData.forEach((str) => {
          console.log(str)
          const match = str.title.match(/(bus|adhoc)-(\d{8})/);
          if (match) {
            const dateString = match[2];
            const year = dateString.slice(0, 4);
            const month = dateString.slice(4, 6) - 1;
            const day = dateString.slice(6, 8);
            const date = new Date(year, month, day, 0, 0, 0);
            currentDate.setHours(0, 0, 0, 0);
            if (date >= currentDate && (!closestDate || date < closestDate)) {
              closestDate = date;
              selectedProject.value = str.title;
              console.log(selectedProject)
            }
          }
        }
    );
  }
  selectedRelease.value = releaseData;
  releaseData.forEach((item) => {
        projects.push(item);
      }
  )


// 检查页面上是否有内容
  if (document.body.innerHTML.trim().length > 0) {
    // 选择具有"el-empty"类的元素
    const emptyElement = document.querySelector('.el-empty');

    // 如果有内容，隐藏具有"el-empty"类的元素
    if (emptyElement) {
      emptyElement.style.display = 'none';
    }
  }
});

const form = reactive({
  name: '',
  merge: true,
});

watch(selectedProject, (newValue, oldValue) => {
  if (newValue !== '' && newValue !== oldValue) {
    releaseTableData.splice(0, releaseTableData.length);
    getData(newValue);
  }
  form.name = removeReleasePrefix(newValue);
});


watch(releaseTableData, (newValue, oldValue) => {
  localStorage.setItem('releaseTableData', JSON.stringify(releaseTableData))
  if (releaseTableData.length === 0) {
    show.value = false
  }
  if (releaseTableData.length !== 0) {
    show.value = true
  }
});

watch(all_services, (newValue, oldValue) => {
  if (all_services.length === 0) {
    services_pane.value = false
  }
  if (all_services.length !== 0) {
    services_pane.value = true
  }
});

watchEffect(() => {
  const savedOption = localStorage.getItem('selectedProject');
  let saveactive = localStorage.getItem('active');

  if (savedOption) {
    selectedProject.value = savedOption
  }
  if (saveactive) {
    active.value = saveactive
  }
})


watchEffect(() => {
  localStorage.setItem('selectedProject', selectedProject.value);
})

function removeReleasePrefix(refParam) {
  if (refParam) {
    let modifiedString = refParam.replace(/【Release】|发布单/g, "").replace(/\s/g, "");
    console.log(modifiedString);
    return modifiedString;
  }
  return "";
}




//平台1组
const selectedData1 = ref([]); // 保存选中的表格数据
//平台2组
const selectedData2 = ref([]); // 保存选中的表格数据
//功能组
const selectedData3 = ref([]); // 保存选中的表格数据
//前端组
const selectedData4 = ref([]); // 保存选中的表格数据


const handleSelectionChange1 = (selection) => {
  selectedData1.value = [];
  selectedData1.value = selection;
  console.log(processedData1.value);
  console.log(selectedData1.value);
};
const processedData1 = computed(() => {
  return selectedData1.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});
const getRowKey = (row: any) => row.id;
const defaultSort = {
  prop: 'id',
  order: 'ascending'
};
const handleSortChange = ({ newIndex, oldIndex }: { newIndex: number, oldIndex: number }) => {
  const movedItem = processedData1.value.splice(oldIndex, 1)[0];
  processedData1.value.splice(newIndex, 0, movedItem);
};
const handleSelectionChange2 = (selection) => {
  selectedData2.value = []
  selectedData2.value = selection;
};
const processedData2 = computed(() => {
  return selectedData2.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});
const handleSelectionChange3 = (selection) => {
  selectedData3.value = []
  selectedData3.value = selection;
};
const processedData3 = computed(() => {
  return selectedData3.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});
const handleSelectionChange4 = (selection) => {
  selectedData4.value = []
  selectedData4.value = selection;
};
const processedData4 = computed(() => {
  return selectedData4.value.map((item, index) => {
    return { ...item, id: index + 1 };
  });
});


let mrTableData = reactive([]);
const submitForm = () => {
  dialogVisible.value = false;
  fullscreenLoadingMR.value = true;
  const mergedArray = selectedData1.value.concat(selectedData2.value, selectedData3.value, selectedData4.value);
  const uniqueArray = Array.from(new Set(mergedArray));

  const newArray = uniqueArray.map(item => item.name);
  console.log(newArray);
  const newData = {
    "services_list": newArray,
    "title": form.name,
    "if_merge": form.merge
  };
  console.log(newData);
  let data_fin = newMerge(newData);

  data_fin.then((result) => {
    console.log(result['repo']);
    for (let i in result['repo']) {
      console.log(i);
      console.log(result['repo'][i])
      mrTableData.push({
        repo: result['repo'][i]["repo"],
        url: result['repo'][i]["url"],
        status: result['repo'][i]["status"],
      });

    }
  });
  fullscreenLoadingMR.value = false;
  console.log(mrTableData)
};

// 添加检测未分组服务的函数
function getUngroupedServices(all_services, fe_services, be_services) {
  return all_services.filter(service => !fe_services.includes(service) && !be_services.includes(service));
}

// 添加一个数组存储未分组的服务
const ungrouped_services = ref([]);

// 添加一个函数，用于检查服务是否被正确分组到任意一个小组
function checkServiceGrouping(service) {
  if (fe_services.includes(service)) {
    // 如果是前端服务，应该在FE组里
    const feGroupKeys = feGroup.map(obj => Object.keys(obj)).flat();
    return feGroupKeys.includes(service);
  } else if (be_services.includes(service)) {
    // 如果是后端服务，应该在平台BE1组、平台BE2组或功能BE组里
    const pingGroupAKeys = pingGroupA.map(obj => Object.keys(obj)).flat();
    const pingGroupBKeys = pingGroupB.map(obj => Object.keys(obj)).flat();
    const featureGroupKeys = featureGroup.map(obj => Object.keys(obj)).flat();
    return pingGroupAKeys.includes(service) || pingGroupBKeys.includes(service) || featureGroupKeys.includes(service);
  }
  return false; // 如果既不是前端也不是后端服务，则认为没有分组
}

// 在处理服务的地方添加计算未分组服务的逻辑
if (fe_services.length !== 0) {
  showfe.value = true
}
if (be_services.length !== 0) {
  showbe.value = true
}
// 计算未被正确分组的服务
const ungroupedServices = [];
all_services.forEach(service => {
  if (!checkServiceGrouping(service)) {
    ungroupedServices.push(service);
  }
});
console.log("未正确分组的服务:", ungroupedServices);
console.log(fe_services)
console.log(be_services)

</script>

<style scoped>
a {
  text-decoration: none;
}

.index-container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

}

div {
  font-size: 12px;
  margin: 5px;
  border: 1px;
  padding: 0;
}

</style>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}

.el-table .cell {
  white-space: pre-wrap !important;
}

.table-header {
  background-color: blue;
  color: white;
}
</style>
<style scoped>
.n-gradient-text {
  font-size: 24px;
}
</style>

<style scoped>
.scrollbar-fe-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  margin: 10px; /* 删除默认外边距 */

  text-align: center;
  border-radius: 4px;
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.scrollbar-be-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  margin: 10px; /* 删除默认外边距 */
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.ml-2 {

  margin: 10px; /* 添加10像素的间距 */

}
</style>


<style scoped>
.data-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.data-item {
  width: calc(33.33% - 5px); /* 将元素宽度从原来的 calc(33.33% - 10px) 调整为 calc(33.33% - 5px) */
  margin-bottom: 20px;
  border-radius: 5px;
  padding: 10px;
  background-color: #f5f5f5;
}

.data-item__title {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.data-item__content {
  color: #666;
}
</style>

<style scoped>
.itxst {
  width: 600px;
  display: flex;
}

.itxst > div:nth-of-type(1) {
  flex: 1;
}

.itxst > div:nth-of-type(2) {
  width: 270px;
  padding-left: 20px;
}

.item {
  border: solid 1px #eee;
  padding: 6px 10px;
  text-align: left;
}

.item:hover {
  cursor: move;
}

.item + .item {
  margin-top: 10px;
}

.ghost {
  border: solid 1px rgb(19, 41, 239);
}

.chosenClass {
  background-color: #f1f1f1;
}
</style>

<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}

.el-card + .el-card {
  margin-top: 20px;
}


</style>
<style>
.ar-container {
  display: flex;
  align-items: center;

}

</style>

<style>
.Epic-icon {
  background-image: url('@/icons/svg/epic.svg');
  width: 16px !important;
  height: 16px !important;
}

.ST-icon {
  background-image: url('@/icons/svg/sub-task.svg');
  width: 16px !important;
  height: 16px !important;
}

.Bug-icon {
  background-image: url('@/icons/svg/bug.svg');
  width: 16px !important;
  height: 16px !important;
}

.Story-icon {
  background-image: url('@/icons/svg/story.svg');
  width: 16px !important;
  height: 16px !important;
}

.Task-icon {
  background-image: url('@/icons/svg/task.svg');
  width: 16px !important;
  height: 16px !important;
}
</style>

<!--<style>-->
<!--//.el-table th {-->
<!--//  background-color: #cacfd7 !important; /* 设置表头背景色 */-->
<!--//  color: #333 !important; /* 设置表头字体颜色 */-->
<!--//}-->

<!--</style>-->
<style>
.to-do-text {
  background-color: #42526e;
  border-color: #42526e;
  color: #fff;
}

.done-text {
  background-color: #00875a;
  border-color: #00875a;
  color: #fff;
}

.doing-text {
  background-color: #0052cc;
  border-color: #0052cc;
  color: #fff;
}

.delivering-text {
  background-color: #0052cc;
  border-color: #0052cc;
  color: #fff;
}

.developing-text {
  background-color: #0052cc;
  border-color: #0052cc;
  color: #fff;
}

.waiting-text {
  background-color: #42526e;
  border-color: #42526e;
  color: #fff;
}

.bold-text {
  font-weight: bold;
}
</style>


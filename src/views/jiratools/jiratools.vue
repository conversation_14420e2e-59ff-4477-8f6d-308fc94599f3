<template>
  <div>
    <button @click="showDialog">New</button>
    <div v-if="dialogVisible">
      <input v-model="description" placeholder="Description">
      <input v-model="jql" placeholder="JQL">
      <input v-model="cron" placeholder="Cron Expression">
      <input v-model="groupId" placeholder="Seatalk Group ID">
      <button @click="save">Save</button>
    </div>
    <div v-for="item in items" :key="item.id">
      <div>{{ item.description }}</div>
      <div>{{ item.jql }}</div>
      <div>{{ item.cron }}</div>
      <div>{{ item.groupId }}</div>
      <input type="checkbox" v-model="item.switch">
      <button @click="execute(item.id)">立即执行</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      description: '',
      jql: '',
      cron: '',
      groupId: '',
      items: []
    }
  },
  methods: {
    showDialog() {
      this.dialogVisible = true;
    },
    save() {
      // 调用后端接口保存数据
      // 示例：this.$http.post('/api/saveData', { description: this.description, jql: this.jql, cron: this.cron, groupId: this.groupId })
      // 保存成功后将数据添加到items数组中
      this.items.push({ description: this.description, jql: this.jql, cron: this.cron, groupId: this.groupId, switch: false });
      this.dialogVisible = false;
    },
    execute(id) {
      // 调用后端接口执行计划任务
      // 示例：this.$http.post('/api/executeTask', { id: id })
    }
  }
}
</script>

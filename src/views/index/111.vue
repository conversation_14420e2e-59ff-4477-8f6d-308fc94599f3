<template>
  <div class="index-conntainer">
    <div class="head-card">
      <!--      <div class="avatar">-->
      <!--        <el-avatar :size="50" :src="avatar"></el-avatar>-->
      <!--      </div>-->
      <div class="head-card-content">
        <h2 class="title">{{ sayHi }}! 小虾们, {{ t('indexPage.descTitle') }}</h2>
        <p class="desc">
          ChatbotQA天工平台是基于 vue3 + vite2 + Element-Plus + Vue-Router4.x + Vuex4.x +
          Javascript开发的QA工具平台，目前正在测试阶段，仅支持自动发MR，自动打TAG，感谢使用！
        </p>
      </div>
    </div>
    <el-form-item class="content">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">
          <el-card class="card" shadow="hover">
            <template #header>
              <h3 class="title">{{ t('indexPage.resourceTitle') }}</h3>
            </template>
            <div class="example-block">
              <span class="example-demonstration">仓库选择</span>
              <el-cascader
                :options="test"
                :props="props"
                v-model="value"
                collapse-tags
                clearable
                @change="handleChange"
              />
            </div>
            <el-form
              :label-position="labelPosition"
              label-width="100px"
              :model="formLabelAlign"
              style="max-width: 1080px"
            >
              <el-form-item label="MR标题">
                <el-input v-model="form.name" />
              </el-form-item>
              <el-form-item label="审批人">
                <el-checkbox-group v-model="form.type">
                  <el-checkbox label="<EMAIL>" name="type" />
                  <el-checkbox label="<EMAIL>" name="type" />
                  <el-checkbox label="<EMAIL>" name="type" />
                  <el-checkbox label="<EMAIL>" name="type" />
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="MR描述">
                <el-input v-model="form.desc" type="textarea" />
              </el-form-item>
              <br />
              <div style="text-align: -webkit-center">
                <el-form label-width="100px" style="width: max-content">
                  <el-form-item>
                    <el-button type="primary" @click="onSubmit">提交MR申请</el-button>
                  </el-form-item>
                </el-form>
              </div>
              <br />
              <div style="text-align: -webkit-center">
                <el-form label-width="100px" style="width: max-content">
                  <el-form-item>
                    <el-button type="primary" @click="table = true">查看MR请求结果</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
          <el-card class="card" shadow="hover">
            <template #header>
              <h3 class="title">{{ t('indexPage.autoTag') }}</h3>
            </template>
            <div class="example-block">
              <span class="example-demonstration">仓库选择</span>
              <el-cascader
                :options="options"
                :props="props"
                v-model="value"
                collapse-tags
                clearable
                @change="handleChange"
              />
            </div>
            <el-form
              :label-position="labelPosition"
              label-width="100px"
              :model="formLabelAlign"
              style="max-width: 1080px"
            >
              <el-form-item label="TAG标题">
                <el-input v-model="form.name" />
              </el-form-item>

              <br />
              <div style="text-align: -webkit-center">
                <el-form label-width="100px" style="width: max-content">
                  <el-form-item>
                    <el-button type="primary" @click="onTAGSubmit">提交TAG申请</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </el-form-item>
  </div>
  <el-drawer v-model="table" title="自动Merge状态" direction="rtl" size="50%">
    <el-table :data="gridData">
      <el-table-column property="name" label="仓库名" width="200" />
      <el-table-column property="address" label="MR地址" />
      <el-table-column property="status" label="状态" />
    </el-table>
  </el-drawer>
</template>

<style scoped>
  .example-block {
    margin: 1rem;
  }

  .example-demonstration {
    margin: 1rem;
  }
</style>

<script>
  export default {
    name: 'Index',
  };
</script>

<script setup>
  import { ref, computed, reactive, onBeforeMount, getCurrentInstance } from 'vue';
  import { merge } from '@/api/merge';
  import { tag } from '@/api/tag';
  import { ElMessage } from 'element-plus';
  import { useI18n } from 'vue-i18n';
  import { getResouceList } from '@/api/index';
  import axios from 'axios';

  import { useStore } from 'vuex';
  import { createRouter as enumerableKeys } from 'vue-router';

  const table = ref(false);
  const gridData = [{}];
  let test = ref([
    {
      value: 'seller',
      label: 'seller',
      children: [],
    },
  ]);
  let services = {};
  const service = axios.create({
    baseURL: '', // 请求本地json文件，那么baseURL取空字符串，域名就会是项目域名
    timeout: 30000,
  });
  service.get('/services_id.json').then((res) => {
    services = res.data;
    //console.log(services);
    for (let key in services) {
      let temp = {
        value: key,
        label: key,
      };
      //console.log(temp);
      test.value[0]['children'].push(temp);
      //console.log(key);
    }
    console.log(test);
  });

  const value = ref([]);
  const store = useStore();

  const { t } = useI18n();

  const state = reactive({
    list: [],
    prefix: '',
    orderList: [],
    skillList: [],
  });

  const hour = new Date().getHours();
  const thisTime =
    hour < 8
      ? t('sayHi.early')
      : hour <= 11
      ? t('sayHi.morning')
      : hour <= 13
      ? t('sayHi.noon')
      : hour < 18
      ? t('sayHi.afternoon')
      : t('sayHi.evening');
  const sayHi = ref(thisTime);
  // const avatar = ref('https://i.gtimg.cn/club/item/face/img/2/15922_100.gif');

  const props = { multiple: true };

  let options = [
    {
      value: 'Chatbot',
      label: 'Chatbot',
      children: [
        {
          value: 'chatbot-asynctask',
          label: 'chatbot-asynctask',
        },
        {
          value: 'chatbot-botapi',
          label: 'chatbot-botapi',
        },
        {
          value: 'dialogue-management',
          label: 'dialogue-management',
        },
        {
          value: 'intent-service',
          label: 'intent-service',
        },
        {
          value: 'chatbot-chat',
          label: 'chatbot-chat',
        },
        {
          value: 'web-microfe-annotation-portal',
          label: 'web-microfe-annotation-portal',
        },
        {
          value: 'annotation',
          label: 'annotation',
        },
        {
          value: 'web-microfe-knowledge-base',
          label: 'web-microfe-knowledge-base',
        },
        {
          value: 'knowledge-base',
          label: 'knowledge-base',
        },
        {
          value: 'chatflow-editor',
          label: 'chatflow-editor',
        },
        {
          value: 'task-flow',
          label: 'task-flow',
        },
        {
          value: 'chatbot-model',
          label: 'chatbot-model',
        },
        {
          value: 'intent-clarification',
          label: 'intent-clarification',
        },
        {
          value: 'report-service',
          label: 'report-service',
        },
        {
          value: 'data-service',
          label: 'data-service',
        },
        {
          value: 'web-chatbot-csat',
          label: 'web-chatbot-csat',
        },
        {
          value: 'alert',
          label: 'alert',
        },
        {
          value: 'metric-service',
          label: 'metric-service',
        },
        {
          value: 'liveagent-control',
          label: 'liveagent-control',
        },
        {
          value: 'auto-training-portal',
          label: 'auto-training-portal',
        },
        {
          value: 'auto-training',
          label: 'auto-training',
        },
        {
          value: 'feature-center',
          label: 'feature-center',
        },
        {
          value: 'audit-log',
          label: 'audit-log',
        },
        {
          value: 'admin-config-service',
          label: 'admin-config-service',
        },
        {
          value: 'chatbot-qa-cicd',
          label: 'chatbot-qa-cicd',
        },
        {
          value: 'adminservce',
          label: 'adminservce',
        },
        {
          value: 'adminservce',
          label: 'adminservce',
        },
      ],
    },
    {
      value: 'marketing',
      label: 'marketing',
      children: [
        {
          value: 'chatbot',
          label: 'chatbot',
        },
        {
          value: 'web-chatbot-admin',
          label: 'web-chatbot-admin',
        },
        {
          value: 'web-chatbot',
          label: 'web-chatbot',
        },
      ],
    },
    {
      value: 'seller',
      label: 'seller',
      children: [
        {
          value: 'seller-fe/cs-chat',
          label: 'seller-fe/cs-chat',
        },
        {
          value: 'seller-server/cs/cs',
          label: 'seller-server/cs/cs',
        },
      ],
    },
  ];

  const form = reactive({
    name: '',
    type: [],
    desc: '',
  });

  // const handleChange = (value) => {
  //   const Nodes = this.$refs.myCascader;
  //   console.log(Nodes);
  // };

  const open2 = () => {
    ElMessage({
      message: '恭喜，提交成功！！！！',
      type: 'success',
    });
  };

  const onSubmit = () => {
    console.log('submit!');
    let array = value.value;
    let array_final = [];
    for (let i = 0; i < array.length; i++) {
      let array2 = array[i];
      for (let y = 0; y < array2.length; y++) {
        if (y === array2.length - 1) {
          array_final.push(array2[y]);
        }
      }
    }
    let fin_data = {
      title: form.name,
      repo_list: array_final,
      approve_list: form.type,
      desc: form.desc,
    };
    let data_final = merge(fin_data);
    data_final.then((result) => {
      console.log(result['repo_list']);
      for (let i in result['repo_list']) {
        console.log('i:', i);
        console.log('result:', result['repo_list'][i]);
        gridData.push({
          name: i,
          address: result['repo_list'][i][0],
          status: result['repo_list'][i][1],
        });
        console.log('griddate:', gridData);
        open2();
      }
    });
  };

  const onTAGSubmit = () => {
    console.log('onTAGSubmit!');
    let array = value.value;
    let array_final = [];
    for (let i = 0; i < array.length; i++) {
      let array2 = array[i];
      for (let y = 0; y < array2.length; y++) {
        if (y === array2.length - 1) {
          array_final.push(array2[y]);
        }
      }
    }
    let fin_data = {
      title: form.name,
      repo_list: array_final,
    };
    let data_final = tag(fin_data);
  };
  const labelPosition = ref('right');

  const formLabelAlign = reactive({
    name: '',
    region: '',
    type: '',
  });

  // onBeforeMount(() => {
  //   // onGetResouceList();
  // });
  // const handleChange = (value) => {
  //   // const ins = getCurrentInstance();
  //   // console.log(ins);
  // };
</script>

<style lang="scss" scoped>
  .index-conntainer {
    width: $base-width;

    .head-card {
      display: flex;
      align-items: center;
      padding: $base-main-padding;
      background-color: $base-color-white;

      &-content {
        padding-left: 15px;

        .desc {
          color: $base-font-color;
        }
      }
    }

    .content {
      margin: 15px 0;

      .count-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .item {
          display: flex;
          flex-direction: column;
          text-align: center;

          .label {
            padding: 10px 0;
            font-size: $base-font-size-big;
          }

          .count {
            font-size: $base-font-size-max;
            font-weight: bolder;
            color: $base-color-primary;

            &.error {
              color: var(--el-color-danger);
            }

            &.success {
              color: var(--el-color-success);
            }
          }
        }
      }

      .title {
        margin: 0;
      }

      .skill-title {
        padding: 10px 0;
        font-weight: 500;
      }

      .card {
        margin-bottom: 15px;

        &-body {
          display: grid;
          grid-template-columns: repeat(4, 1fr);

          &.mobile {
            grid-template-columns: repeat(1, 1fr);
          }

          .item {
            box-sizing: border-box;
            padding: 10px 20px;
            margin-top: -1px;
            margin-left: -1px;
            overflow: hidden;
            cursor: pointer;
            border: 1px solid black;
            border: 1px solid #eee;
            transition: box-shadow 0.5;

            .lf {
              display: flex;
              align-items: center;
              max-width: 140px;

              .img {
                width: auto;
                max-width: 120px;
                height: auto;
                max-height: 40px;
              }
            }

            &:hover {
              box-shadow: $base-box-shadow;
            }

            .title {
              padding-left: 5px;
              font-size: 18px;
              font-weight: bold;
            }

            .desc {
              padding: 5px 0;
              font-size: 13px;
              line-height: 1.5;
              color: $base-font-color;
            }
          }
        }
      }
    }
  }
</style>

<template>
  <el-tabs
      v-model="activeName"
      class="demo-tabs"
      type="border-card"
      @tab-click="handleClick">
    <el-tab-pane label="自动发布" name="first">
      <div class="index-conntainer">
        <div class="head-card">
          <div class="head-card-content">
            <h2 class="title">{{ sayHi }}! Guys, {{ t('indexPage.descTitle') }}</h2>
            <el-text class="mx-1" type="danger" size="large">
              Notice,前端4个仓库已在本页面下架,请找bin.wang合代码,下架仓库为：
            </el-text>
            <el-text class="mx-1" type="danger" size="large">web-chatbot、</el-text>
            <el-text class="mx-1" type="danger" size="large">cs-chat、</el-text>
            <el-text class="mx-1" type="danger" size="large">web-chatbot-csat、</el-text>
            <el-text class="mx-1" type="danger" size="large">web-csat-rn</el-text>
            <p class="desc">
              标签规范：bus/adhoc/bugfix-YYYYMMDD
            </p>
            <p class="desc">
              示例：bus-20230831、adhoc-20230831、adhoc-20230831-2、hotfix-20230831
            </p>
            <p class="desc">
              自动合代码包含以下功能:
            </p>
            <p class="desc">
              1.自动提MR（master->release）
            </p>
            <p class="desc">
              2.自动合代码（master->release）
            </p>
            <p class="desc">
              3.自动打TAG（在release分支打TAG）
            </p>
            <p class="desc">
              4.自动预编译（打tag后自动触发）
            </p>
          </div>

        </div>
        <div class="content">
          <el-row :gutter="5">
            <el-col>
              <el-card class="card" shadow="hover">
                <template #header>
                  <h3 class="title">Auto MR---TAG---BUILD</h3>
                </template>
                <div class="example-block">
                  <el-cascader
                      :options="options"
                      :props="props"
                      v-model="value"
                      placeholder="选择仓库"
                      collapse-tags
                      clearable
                      filterable
                      ref="cascader"
                      @change="handleChange"
                  />
                  <el-form
                      :label-position="labelPosition"
                      label-width="100px"
                      :model="formLabelAlign"
                      style="max-width: 1080px"
                  >
                    <el-form-item label="MR标题">
                      <el-input v-model="form.name"/>
                    </el-form-item>


                    <div style="display: flex; justify-content: center;">
                      <el-dialog
                          v-model="dialogVisible"
                          title="live-发布确认"
                          width="30%"
                          :before-close="handleClose"
                      >
                        <span>您正在进行live自动发布流程，请确认您的操作是否要继续。</span>
                        <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onSubmit">
          确认发布
        </el-button>
      </span>
                        </template>
                      </el-dialog>
                    </div>
                  </el-form>
                  <el-button
                      type="primary"
                      :disabled="!isMRSubmitValid"
                      v-loading.fullscreen.lock="fullscreenLoading"
                      element-loading-text="正在处理，辛苦等待一下，中途请不要关闭此页面..."
                      @click="dialogVisible = true"
                      style="margin-right: 20px;
                              margin-left: 30px"
                  >创建MR
                  </el-button>
                  <el-button type="primary" @click="table = true">展示MR结果</el-button>
                </div>

              </el-card>

            </el-col>


          </el-row>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="打Tag(无代码合入)" name="second">
      <div class="index-conntainer">
        <div class="head-card">
          <div class="head-card-content">
            <h2 class="title">{{ sayHi }}! Guys, {{ t('indexPage.descTitle') }}</h2>
            <el-text class="mx-1" type="danger" size="large">Notice,本页面不包含合代码的功能，请转移到Merge页面或者人工合代码到release！
            </el-text>
            <p class="desc">
              本页面包含以下功能（注意，不包含自动提MR跟合代码，请转移到Merge页面）
            </p>
            <p class="desc">
              1.自动打TAG（在release分支打TAG）
            </p>
            <p class="desc">
              2.自动预编译（打tag后自动触发）
            </p>
          </div>

          <!--          <img src="/src/assets/index/backgroup.png" style="max-width: 50%; height: auto"/>-->

        </div>
        <div class="content">
          <el-row :gutter="20">
<!--            <el-col :xs="24" :sm="24" :md="24" :lg="16" :xl="16">-->

<!--            </el-col>-->

            <el-col>
              <el-card class="card" shadow="hover">
                <template #header>
                  <h3 class="title">Auto TAG---BUILD</h3>
                </template>

                <div class="example-block">
                  <el-cascader
                      :options="optionsTag"
                      :props="props"
                      v-model="value"
                      collapse-tags
                      ref="cascader"
                      placeholder="请输入搜索"
                      clearable
                      filterable
                      @change="handleChange"
                  />
                  <el-form
                      :label-position="labelPosition"
                      label-width="100px"
                      :model="formLabelAlign"
                      style="max-width: 1080px"
                  >
                    <el-form-item label="TAG标题">
                      <el-input v-model="form.name"/>
                    </el-form-item>

                  </el-form>
                  <el-form label-width="100px" style="width: max-content;margin-left: 20px">


                    <el-button type="primary" :disabled="!isMRSubmitValid" @click="onTAGSubmit">
                      submit TAG
                    </el-button>
                  </el-form>
                </div>
              </el-card>
            </el-col>

          </el-row>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
  <el-drawer v-model="table" title="自动Merge状态" direction="rtl" size="50%">
    <el-table :data="gridData">
      <el-table-column property="name" label="仓库名" width="200"/>
      <el-table-column property="address" label="MR地址">
        <template #default="{row}">
          <a
              :href="row.address"
              target="_blank"
          >{{ row.address }}
          </a>
        </template>
      </el-table-column>
      <el-table-column property="status" label="状态"/>
    </el-table>
  </el-drawer>
</template>

<style scoped>
.example-block {
  display: flex;
  margin-left: 10px;
  margin-right: 10px;


}

.example-demonstration {
  display: flex;
  margin: 1rem;
  margin-left: 10px;
  margin-right: 10px;
}
</style>

<script>
export default {
  name: 'Index',
};
</script>

<script setup>
import {InfoFilled} from '@element-plus/icons-vue';
import {ref, computed, reactive, onBeforeMount, getCurrentInstance, watchEffect, watch, nextTick} from 'vue';
import {ElLoading, ElMessageBox} from 'element-plus'
import {merge} from '@/api/merge';
import {tag} from '@/api/tag';
import {ElMessage} from 'element-plus';
import {useI18n} from 'vue-i18n';
import {getResouceList} from '@/api/index';
import axios from 'axios';

import {useStore} from 'vuex';
// import {watch} from "vue/dist/vue";
const activeName = ref('first')

const dialogVisible = ref(false)

const table = ref(false);
const gridData = [{}];

let test = ref([
  {
    value: 'chatbot动态仓库',
    label: 'chatbot动态仓库',
    children: [],
  },
  {
    value: 'marketing',
    label: 'marketing',
    children: [
      {
        value: 'chatbot',
        label: 'chatbot',
      },
      {
        value: 'web-chatbot-admin',
        label: 'web-chatbot-admin',
      },
      {
        value: 'web-chatbot',
        label: 'web-chatbot',
      },
    ],
  },
  {
    value: 'seller',
    label: 'seller',
    children: [
      {
        value: 'seller-fe/cs-chat',
        label: 'seller-fe/cs-chat',
      },
      {
        value: 'seller-server/cs/cs',
        label: 'seller-server/cs/cs',
      },
      {
        value: 'seller-server/pilot/api',
        label: 'seller-server/piolt/api',
      },
    ],
  },
  {
    value: 'channel-FE',
    label: 'channel-FE',
    children: [
      {
        value: 'channel-config',
        label: 'channel-config',
      },
      {
        value: 'webform',
        label: 'webform',
      },
      {
        value: 'webform-client',
        label: 'webform-client',
      },
      {
        value: 'case-tracking-rn',
        label: 'case-tracking-rn',
      },

      {
        value: 'service-portal',
        label: 'service-portal',
      },
      {
        value: 'help-center-agent',
        label: 'help-center-agent',
      },
      {
        value: 'help-center',
        label: 'help-center',
      },
      {
        value: 'help-center-node',
        label: 'help-center-node',
      },

      {
        value: 'csat-client',
        label: 'csat-client',
      },
      {
        value: 'live-chat',
        label: 'live-chat',
      },
      {
        value: 'web-chatbot',
        label: 'web-chatbot',
      },
      {
        value: 'cs-chat',
        label: 'cs-chat',
      },
    ],
  },
  {
    value: 'channel-BE',
    label: 'channel-BE',
    children: [
      {
        value: 'channel-email',
        label: 'channel-email',
      },
      {
        value: 'channel-form',
        label: 'channel-form',
      },
      {
        value: 'channel-call',
        label: 'channel-call',
      },
      {
        value: 'call',
        label: 'call',
      },
      {
        value: 'channel-socialmedia',
        label: 'channel-socialmedia',
      },
      {
        value: 'socialmedia',
        label: 'socialmedia',
      },
      {
        value: 'casetracking',
        label: 'casetracking',
      },
      {
        value: 'comment',
        label: 'comment',
      },
      {
        value: 'helpcenter',
        label: 'helpcenter',
      },
      {
        value: 'chat',
        label: 'chat',
      },
      {
        value: 'chatbot-chat',
        label: 'chatbot-chat',
      },
      {
        value: 'eventfactory',
        label: 'eventfactory',
      },
    ],
  },
]);

const value = ref([]);

// const value = computed({
//   get() {
//     return JSON.parse(localStorage.getItem('selectedValues')) || []
//   },
//   set(value) {
//     localStorage.setItem('selectedValues', JSON.stringify(value))
//   }
// })
// onMounted(() => {
//   value.value = JSON.parse(localStorage.getItem('selectedValues')) || []
// })
// onBeforeUnmount(() => {
//   localStorage.setItem('selectedValues', JSON.stringify(value.value))
// })

const cascaderRef = ref(null);

const store = useStore();

const {t} = useI18n();

const state = reactive({
  list: [],
  prefix: '',
  orderList: [],
  skillList: [],
});
const fullscreenLoading = ref(false);

const hour = new Date().getHours();
const thisTime =
    hour < 8
        ? t('sayHi.early')
        : hour <= 11
            ? t('sayHi.morning')
            : hour <= 13
                ? t('sayHi.noon')
                : hour < 18
                    ? t('sayHi.afternoon')
                    : t('sayHi.evening');
const sayHi = ref(thisTime);

let services = {};
const service = axios.create({
  baseURL: '', // 请求本地json文件，那么baseURL取空字符串，域名就会是项目域名
  timeout: 30000,
});
service.get('/services_id.json').then((res) => {
  services = res.data;
  //console.log(services);
  for (let key in services) {
    let temp = {
      value: key,
      label: key,
    };
    //console.log(temp);
    test.value[0]['children'].push(temp);
    //console.log(key);
  }
  console.log(test);
});

const popupShownAR = localStorage.getItem('popupShownAR') === 'true'
if (!popupShownAR) {
  ElMessageBox({
    title: 'AR-自动发布',
    message: '<span style="color: red;">注意，跟channel共用的仓库，已下架，无法进行merge，但可以打TAG，请知悉！</span>',
    confirmButtonText: '确定',
    type: 'warning',
    dangerouslyUseHTMLString: true
  }).then(() => {
    localStorage.setItem('popupShownAR', 'true')
  })
}

const props = {multiple: true};
const options = [
  {
    value: 'Chatbot',
    label: 'Chatbot',
    children: [
      {
          value: 'admin-config-service',
          label: 'admin-config-service',
      },
      {
          value: 'adminasynctask',
          label: 'adminasynctask',
      },
      {
          value: 'adminservice',
          label: 'adminservice',
      },
      {
          value: 'alert',
          label: 'alert',
      },
      {
          value: 'annotation',
          label: 'annotation',
      },
      {
          value: 'annotation-saas',
          label: 'annotation-saas',
      },
      {
          value: 'api-store',
          label: 'api-store',
      },
      {
          value: 'audit-log',
          label: 'audit-log',
      },
      {
          value: 'auto-training',
          label: 'auto-training',
      },
      {
          value: 'auto-training-portal',
          label: 'auto-training-portal',
      },
      {
          value: 'chatbot-asynctask',
          label: 'chatbot-asynctask',
      },
      {
          value: 'chatbot-botapi',
          label: 'chatbot-botapi',
      },
      {
          value: 'chatbot-context',
          label: 'chatbot-context',
      },
      {
          value: 'chatbot-model',
          label: 'chatbot-model',
      },
      {
          value: 'chatbot-ordercard',
          label: 'chatbot-ordercard',
      },
      {
          value: 'chatbot-pilot-api',
          label: 'chatbot-pilot-api',
      },
      {
        value: 'chatbot-platform-portal',
        label: 'chatbot-platform-portal',
      },
      {
          value: 'chatbot-prompt',
          label: 'chatbot-prompt',
      },
      {
          value: 'chatbot-qa-cicd',
          label: 'chatbot-qa-cicd',
      },
      {
          value: 'chatflow-editor',
          label: 'chatflow-editor',
      },
      {
          value: 'data-service',
          label: 'data-service',
      },
      {
          value: 'dialogue-management',
          label: 'dialogue-management',
      },
      {
          value: 'feature-center',
          label: 'feature-center',
      },
      {
          value: 'intent-clarification',
          label: 'intent-clarification',
      },
      {
          value: 'intent-service',
          label: 'intent-service',
      },
      {
          value: 'knowledge-base',
          label: 'knowledge-base',
      },
      {
          value: 'knowledge-platform',
          label: 'knowledge-platform',
      },
      {
          value: 'liveagent-control',
          label: 'liveagent-control',
      },
      {
          value: 'message-service',
          label: 'message-service',
      },
      {
          value: 'metric-service',
          label: 'metric-service',
      },
      {
          value: 'nlu-service',
          label: 'nlu-service',
      },
      {
          value: 'operation-analysis-client',
          label: 'operation-analysis-client',
      },
      {
          value: 'operation-analysis-service',
          label: 'operation-analysis-service',
      },
      {
          value: 'platform',
          label: 'platform',
      },
      {
          value: 'report-service',
          label: 'report-service',
      },
      {
          value: 'task-flow',
          label: 'task-flow',
      },
      {
          value: 'web-chatbot-admin-saas',
          label: 'web-chatbot-admin-saas',
      },
      {
          value: 'web-microfe-annotation-portal',
          label: 'web-microfe-annotation-portal',
      },
      {
          value: 'web-microfe-annotation-saas',
          label: 'web-microfe-annotation-saas',
      },
      {
          value: 'web-microfe-knowledge-base',
          label: 'web-microfe-knowledge-base',
      },
      {
          value: 'web-microfe-operation-portal',
          label: 'web-microfe-operation-portal',
      },
      {
          value: 'web-microfe-tmc',
          label: 'web-microfe-tmc',
      }
    ],
  },
  {
    value: 'data',
    label: 'data',
    children: [
      {
        value: 'metric-service',
        label: 'metric-service',
      },
      {
        value: 'web-microfe-operation-portal',
        label: 'web-microfe-operation-portal',
      },
      {
        value: 'report-service',
        label: 'report-service',
      },
      {
        value: 'web-ssar',
        label: 'web-ssar',
      },
      {
        value: 'web-dashboard',
        label: 'web-dashboard',
      },
      {
        value: 'data-service',
        label: 'data-service',
      },
      {
        value: 'web-microfe-insights',
        label: 'web-microfe-insights',
      },
    ],
  },
  {
    value: 'marketing',
    label: 'marketing',
    children: [
      {
        value: 'chatbot',
        label: 'chatbot',
      },
      {
        value: 'web-chatbot-admin',
        label: 'web-chatbot-admin',
      },
      // {
      //   value: 'web-chatbot',
      //   label: 'web-chatbot',
      // },
    ],
  },
  {
    value: 'seller',
    label: 'seller',
    children: [
      // {
      //   value: 'seller-fe/cs-chat',
      //   label: 'seller-fe/cs-chat',
      // },
      {
        value: 'seller-server/cs/cs',
        label: 'seller-server/cs/cs',
      },
      {
        value: 'seller-server/pilot/api',
        label: 'seller-server/piolt/api',
      },
    ],
  },
  {
    value: 'channel-FE',
    label: 'channel-FE',
    children: [
      {
        value: 'channel-config',
        label: 'channel-config',
      },
      {
        value: 'webform',
        label: 'webform',
      },
      {
        value: 'webform-client',
        label: 'webform-client',
      },
      {
        value: 'case-tracking-rn',
        label: 'case-tracking-rn',
      },

      {
        value: 'service-portal',
        label: 'service-portal',
      },
      {
        value: 'help-center-agent',
        label: 'help-center-agent',
      },
      {
        value: 'help-center',
        label: 'help-center',
      },
      {
        value: 'help-center-node',
        label: 'help-center-node',
      },

      {
        value: 'csat-client',
        label: 'csat-client',
      },
      {
        value: 'live-chat',
        label: 'live-chat',
      },
      // {
      //   value: 'web-chatbot',
      //   label: '（禁选）web-chatbot',
      // },
      // {
      //   value: 'cs-chat',
      //   label: '（禁选）cs-chat',
      // },
    ],
  },
  {
    value: 'channel-BE',
    label: 'channel-BE',
    children: [
      {
        value: 'channel-email',
        label: 'channel-email',
      },
      {
        value: 'channel-form',
        label: 'channel-form',
      },
      {
        value: 'channel-call',
        label: 'channel-call',
      },
      {
        value: 'call',
        label: 'call',
      },
      {
        value: 'channel-socialmedia',
        label: 'channel-socialmedia',
      },
      {
        value: 'socialmedia',
        label: 'socialmedia',
      },
      {
        value: 'casetracking',
        label: 'casetracking',
      },
      {
        value: 'comment',
        label: 'comment',
      },
      {
        value: 'helpcenter',
        label: 'helpcenter',
      },
      {
        value: 'chat',
        label: 'chat',
      },
      {
        value: 'chatbot-chat',
        label: 'chatbot-chat',
      },
      {
        value: 'eventfactory',
        label: 'eventfactory',
      },
    ],
  },
];

const optionsTag = [
  {
    value: 'Chatbot',
    label: 'Chatbot',
    children: [
      {
          value: 'admin-config-service',
          label: 'admin-config-service',
      },
      {
          value: 'adminasynctask',
          label: 'adminasynctask',
      },
      {
          value: 'adminservice',
          label: 'adminservice',
      },
      {
          value: 'alert',
          label: 'alert',
      },
      {
          value: 'annotation',
          label: 'annotation',
      },
      {
          value: 'annotation-saas',
          label: 'annotation-saas',
      },
      {
          value: 'api-store',
          label: 'api-store',
      },
      {
          value: 'audit-log',
          label: 'audit-log',
      },
      {
          value: 'auto-training',
          label: 'auto-training',
      },
      {
          value: 'auto-training-portal',
          label: 'auto-training-portal',
      },
      {
          value: 'chatbot-asynctask',
          label: 'chatbot-asynctask',
      },
      {
          value: 'chatbot-botapi',
          label: 'chatbot-botapi',
      },
      {
          value: 'chatbot-chat',
          label: 'chatbot-chat',
      },
      {
          value: 'chatbot-context',
          label: 'chatbot-context',
      },
      {
          value: 'chatbot-model',
          label: 'chatbot-model',
      },
      {
          value: 'chatbot-ordercard',
          label: 'chatbot-ordercard',
      },
      {
          value: 'chatbot-pilot-api',
          label: 'chatbot-pilot-api',
      },
      {
        value: 'chatbot-platform-portal',
        label: 'chatbot-platform-portal',
      },
      {
          value: 'chatbot-prompt',
          label: 'chatbot-prompt',
      },
      {
          value: 'chatbot-qa-cicd',
          label: 'chatbot-qa-cicd',
      },
      {
          value: 'chatflow-editor',
          label: 'chatflow-editor',
      },
      {
          value: 'data-service',
          label: 'data-service',
      },
      {
          value: 'dialogue-management',
          label: 'dialogue-management',
      },
      {
          value: 'feature-center',
          label: 'feature-center',
      },
      {
          value: 'intent-clarification',
          label: 'intent-clarification',
      },
      {
          value: 'intent-service',
          label: 'intent-service',
      },
      {
          value: 'knowledge-base',
          label: 'knowledge-base',
      },
      {
          value: 'knowledge-platform',
          label: 'knowledge-platform',
      },
      {
          value: 'liveagent-control',
          label: 'liveagent-control',
      },
      {
          value: 'message-service',
          label: 'message-service',
      },
      {
          value: 'metric-service',
          label: 'metric-service',
      },
      {
          value: 'nlu-service',
          label: 'nlu-service',
      },
      {
          value: 'operation-analysis-client',
          label: 'operation-analysis-client',
      },
      {
          value: 'operation-analysis-service',
          label: 'operation-analysis-service',
      },
      {
          value: 'platform',
          label: 'platform',
      },
      {
          value: 'report-service',
          label: 'report-service',
      },
      {
          value: 'task-flow',
          label: 'task-flow',
      },
      {
          value: 'web-chatbot-admin-saas',
          label: 'web-chatbot-admin-saas',
      },
      {
          value: 'web-chatbot-csat',
          label: 'web-chatbot-csat',
      },
      {
          value: 'web-microfe-annotation-portal',
          label: 'web-microfe-annotation-portal',
      },
      {
          value: 'web-microfe-annotation-saas',
          label: 'web-microfe-annotation-saas',
      },
      {
          value: 'web-microfe-knowledge-base',
          label: 'web-microfe-knowledge-base',
      },
      {
          value: 'web-microfe-operation-portal',
          label: 'web-microfe-operation-portal',
      },
      {
          value: 'web-microfe-tmc',
          label: 'web-microfe-tmc',
      },
    ],
  },
  {
    value: 'data',
    label: 'data',
    children: [
      {
        value: 'metric-service',
        label: 'metric-service',
      },
      {
        value: 'web-microfe-operation-portal',
        label: 'web-microfe-operation-portal',
      },
      {
        value: 'report-service',
        label: 'report-service',
      },
      {
        value: 'web-ssar',
        label: 'web-ssar',
      },
      {
        value: 'web-dashboard',
        label: 'web-dashboard',
      },
      {
        value: 'data-service',
        label: 'data-service',
      },
      {
        value: 'web-microfe-insights',
        label: 'web-microfe-insights',
      },
    ],
  },
  {
    value: 'marketing',
    label: 'marketing',
    children: [
      {
        value: 'chatbot',
        label: 'chatbot',
      },
      {
        value: 'web-chatbot-admin',
        label: 'web-chatbot-admin',
      },
      {
        value: 'web-chatbot',
        label: 'web-chatbot',
      },
    ],
  },
  {
    value: 'seller',
    label: 'seller',
    children: [
      {
        value: 'seller-fe/cs-chat',
        label: 'seller-fe/cs-chat',
      },
      {
        value: 'seller-server/cs/cs',
        label: 'seller-server/cs/cs',
      },
      {
        value: 'seller-server/pilot/api',
        label: 'seller-server/piolt/api',
      },
    ],
  },
  {
    value: 'channel-FE',
    label: 'channel-FE',
    children: [
      {
        value: 'channel-config',
        label: 'channel-config',
      },
      {
        value: 'webform',
        label: 'webform',
      },
      {
        value: 'webform-client',
        label: 'webform-client',
      },
      {
        value: 'case-tracking-rn',
        label: 'case-tracking-rn',
      },

      {
        value: 'service-portal',
        label: 'service-portal',
      },
      {
        value: 'help-center-agent',
        label: 'help-center-agent',
      },
      {
        value: 'help-center',
        label: 'help-center',
      },
      {
        value: 'help-center-node',
        label: 'help-center-node',
      },

      {
        value: 'csat-client',
        label: 'csat-client',
      },
      {
        value: 'live-chat',
        label: 'live-chat',
      },
      {
        value: 'web-chatbot',
        label: '（禁选）web-chatbot',
      },
      {
        value: 'cs-chat',
        label: '（禁选）cs-chat',
      },
    ],
  },
  {
    value: 'channel-BE',
    label: 'channel-BE',
    children: [
      {
        value: 'channel-email',
        label: 'channel-email',
      },
      {
        value: 'channel-form',
        label: 'channel-form',
      },
      {
        value: 'channel-call',
        label: 'channel-call',
      },
      {
        value: 'call',
        label: 'call',
      },
      {
        value: 'channel-socialmedia',
        label: 'channel-socialmedia',
      },
      {
        value: 'socialmedia',
        label: 'socialmedia',
      },
      {
        value: 'casetracking',
        label: 'casetracking',
      },
      {
        value: 'comment',
        label: 'comment',
      },
      {
        value: 'helpcenter',
        label: 'helpcenter',
      },
      {
        value: 'chat',
        label: 'chat',
      },
      {
        value: 'chatbot-chat',
        label: 'chatbot-chat',
      },
      {
        value: 'eventfactory',
        label: 'eventfactory',
      },
    ],
  },
];

const form = reactive({
  name: '',
  type: [],
  desc: '',
});


const open2 = () => {
  ElMessage({
    message: '恭喜，请等待几秒钟，点击MR结果按钮查看',
    type: 'success',
  });
};

const onSubmit = () => {
  dialogVisible.value = false;
  console.log('submit!');
  let array = value.value;
  let array_final = [];
  let array_from = [];
  fullscreenLoading.value = true;

  for (let i = 0; i < array.length; i++) {
    let array2 = array[i];
    for (let y = 0; y < array2.length; y++) {
      if (y === array2.length - 1) {
        array_final.push(array2[y]);
      }
      if (y === 0) {
        array_from.push(array2[y]);
      }
    }
  }
  let fin_data = {
    title: form.name,
    repo_list: array_final,
    repo_from: array_from,
  };
  let data_final = merge(fin_data);
  data_final.then((result) => {
    console.log(result['repo_list']);
    for (let i in result['repo_list']) {
      console.log('i:', i);
      console.log('result:', result['repo_list'][i]);
      gridData.push({
        name: i,
        address: result['repo_list'][i][0],
        status: result['repo_list'][i][1],
      });
      console.log('griddate:', gridData);
      open2();
      fullscreenLoading.value = false;
      table.value = true;
    }
  });


};

const onTAGSubmit = () => {
  console.log('onTAGSubmit!');
  let array = value.value;
  let array_final = [];
  let array_from = [];
  for (let i = 0; i < array.length; i++) {
    let array2 = array[i];
    for (let y = 0; y < array2.length; y++) {
      if (y === array2.length - 1) {
        array_final.push(array2[y]);
      }
      if (y === 0) {
        array_from.push(array2[y]);
      }
    }
  }
  let fin_data = {
    title: form.name,
    repo_list: array_final,
    repo_from: array_from,
  };
  let data_final = tag(fin_data);
  open2();
};
const labelPosition = ref('right');

const formLabelAlign = reactive({
  name: '',
  region: '',
  type: '',
});
const isMRSubmitValid = ref(true);

watchEffect(() => {
  isMRSubmitValid.value = Boolean(form.name) && Boolean(value.value);
  console.log(isMRSubmitValid.value)
});
// watch(value, () => {
//   localStorage.setItem('servicesValue', value.value)
// })
// watch(value, (newValue) => {
//   nextTick(() => {
//     cascaderRef.value.setValue(newValue);
//   });
// });
// watchEffect(() => {
//   let tempValue = localStorage.getItem('servicesValue');
//   console.log(tempValue)
//   // if (tempValue) {
//   //   value.value = tempValue
//   // }
//   nextTick(() => {
//     cascaderRef.value.setValue(tempValue);
//   });
//   value.value = tempValue
// })
</script>

<style lang="scss" scoped>
.index-conntainer {
  //width: $base-width;
  .head-card {
    margin: 5px 10px;

    display: flex;
    align-items: center;
    padding: $base-main-padding;
    background-color: $base-color-white;

    &-content {
      padding-left: 5px;

      .desc {
        color: $base-font-color;
      }
    }
  }

  .content {
    margin: 5px 40px;
    //display: flex;
    display: block;
    width: 95%;
    flex-direction: row; // 指定子元素在一行上排列
    justify-content: space-between; // 指定子元素之间的间距和位置

    .count-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        display: flex;
        flex-direction: column;
        text-align: center;

        .label {
          padding: 10px 0;
          font-size: $base-font-size-big;
        }

        .count {
          font-size: $base-font-size-max;
          font-weight: bolder;
          color: $base-color-primary;

          &.error {
            color: var(--el-color-danger);
          }

          &.success {
            color: var(--el-color-success);
          }
        }
      }
    }

    .title {
      margin: 0;
    }

    .skill-title {
      padding: 10px 0;
      font-weight: 500;
    }

    .card {
      margin-bottom: 5px;
      flex: 1;

      &-body {
        display: flex;
        flex-direction: row;
        grid-template-columns: repeat(4, 1fr);

        &.mobile {
          grid-template-columns: repeat(1, 1fr);
        }

        .item {
          box-sizing: border-box;
          padding: 10px 20px;
          margin-top: -1px;
          margin-left: -1px;
          overflow: hidden;
          cursor: pointer;
          border: 1px solid black;
          border: 1px solid #eee;
          transition: box-shadow 0.5;

          .lf {
            display: flex;
            align-items: center;
            max-width: 140px;

            .img {
              width: auto;
              max-width: 120px;
              height: auto;
              max-height: 40px;
            }
          }

          &:hover {
            box-shadow: $base-box-shadow;
          }

          .title {
            padding-left: 5px;
            font-size: 12px;
            font-weight: bold;
          }

          .desc {
            padding: 5px 0;
            font-size: 12px;
            line-height: 1.5;
            color: $base-font-color;
          }
        }
      }
    }
  }

}

.el-card + .el-card {
  margin-top: 5px;
}
</style>
<style>
.demo-tabs > .el-tabs__content {
  padding: 5px;
  color: #353638;
  font-size: 12px;
  font-weight: 600;
}
</style>

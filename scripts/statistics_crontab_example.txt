# ChatBot AutoRelease 统计系统 Crontab 配置示例
# 
# 注意：这些任务仅供参考，请根据实际需求调整
# 使用前请修改路径为实际的项目路径
#
# 安装方法：
# 1. 复制需要的任务到你的crontab文件
# 2. 修改路径为实际的项目路径
# 3. 运行 crontab -e 编辑crontab
# 4. 保存并退出
#
# 查看crontab: crontab -l
# 编辑crontab: crontab -e
# 删除crontab: crontab -r

# 项目路径配置（请修改为实际路径）
PROJECT_PATH="/Users/<USER>/git/chatbot-ar-be"
PYTHON_PATH="/usr/local/bin/python"
MANAGE_PY="$PROJECT_PATH/manage.py"

# 日志文件路径
LOG_PATH="/var/log/chatbot-ar-statistics"

# ============================================================================
# 统计系统定时任务配置
# ============================================================================

# 每15分钟执行系统健康检查
*/15 * * * * cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY run_statistics_job health_check >> $LOG_PATH/health_check.log 2>&1

# 每小时执行统计汇总（生成小时级汇总和系统快照）
0 * * * * cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY run_statistics_job hourly_summary >> $LOG_PATH/hourly_summary.log 2>&1

# 每日凌晨1点执行日统计汇总（生成日级汇总和日报）
0 1 * * * cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY run_statistics_job daily_summary >> $LOG_PATH/daily_summary.log 2>&1

# 每周一凌晨2点执行周统计汇总（生成周级汇总和周报）
0 2 * * 1 cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY run_statistics_job weekly_summary >> $LOG_PATH/weekly_summary.log 2>&1

# 每周日凌晨3点执行数据清理（清理90天前的数据）
0 3 * * 0 cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY run_statistics_job data_cleanup >> $LOG_PATH/data_cleanup.log 2>&1

# 每日凌晨4点执行批量处理（处理所有统计数据）
0 4 * * * cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY run_statistics_job batch_process >> $LOG_PATH/batch_process.log 2>&1

# ============================================================================
# 可选任务（根据需要启用）
# ============================================================================

# 每月1号凌晨5点生成月度汇总
# 0 5 1 * * cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY generate_statistics --type user_activity --period monthly >> $LOG_PATH/monthly_summary.log 2>&1

# 每日备份统计数据（如果需要）
# 0 6 * * * cd $PROJECT_PATH && $PYTHON_PATH $MANAGE_PY dumpdata app01.BotAccessEvent app01.CommandExecutionRecord > /backup/statistics_$(date +\%Y\%m\%d).json

# ============================================================================
# 日志轮转配置建议
# ============================================================================

# 创建日志目录
# sudo mkdir -p /var/log/chatbot-ar-statistics
# sudo chown $USER:$USER /var/log/chatbot-ar-statistics

# 配置logrotate（在 /etc/logrotate.d/chatbot-ar-statistics）:
# /var/log/chatbot-ar-statistics/*.log {
#     daily
#     missingok
#     rotate 30
#     compress
#     delaycompress
#     notifempty
#     create 644 $USER $USER
# }

# ============================================================================
# 监控和告警配置建议
# ============================================================================

# 监控crontab任务执行状态
# */5 * * * * if ! pgrep -f "run_statistics_job" > /dev/null; then echo "Statistics jobs may be stuck" | mail -s "ChatBot AR Statistics Alert" <EMAIL>; fi

# 检查日志文件大小
# 0 0 * * * find /var/log/chatbot-ar-statistics -name "*.log" -size +100M -exec echo "Large log file: {}" \; | mail -s "ChatBot AR Log Size Alert" <EMAIL>

# ============================================================================
# 测试命令
# ============================================================================

# 测试单个任务（手动运行）:
# cd /Users/<USER>/git/chatbot-ar-be && python manage.py run_statistics_job health_check

# 查看可用任务:
# cd /Users/<USER>/git/chatbot-ar-be && python manage.py run_statistics_job --list

# 查看任务详细信息:
# cd /Users/<USER>/git/chatbot-ar-be && python manage.py run_statistics_job --info

# ============================================================================
# 性能优化建议
# ============================================================================

# 1. 如果系统负载较高，可以调整任务执行时间，避免与业务高峰期重叠
# 2. 可以将不同任务分散到不同时间执行，避免同时运行多个任务
# 3. 对于大数据量的清理任务，可以考虑分批执行
# 4. 监控任务执行时间，如果某个任务执行时间过长，考虑优化或分解

# ============================================================================
# 故障排查
# ============================================================================

# 查看任务执行日志:
# tail -f /var/log/chatbot-ar-statistics/health_check.log

# 查看Django日志:
# tail -f /path/to/django/logs/django.log | grep statistics

# 检查数据库连接:
# cd /Users/<USER>/git/chatbot-ar-be && python manage.py dbshell

# 手动测试统计功能:
# cd /Users/<USER>/git/chatbot-ar-be && python test_statistics_system.py

# ============================================================================
# 安全注意事项
# ============================================================================

# 1. 确保crontab任务以适当的用户权限运行
# 2. 日志文件应该有适当的权限设置
# 3. 定期检查和清理日志文件
# 4. 监控任务执行状态，及时发现异常
# 5. 备份重要的统计数据

# ============================================================================
# 联系信息
# ============================================================================

# 如有问题请联系：
# 项目负责人: <EMAIL>
# 文档位置: docs/STATISTICS_SYSTEM_GUIDE.md

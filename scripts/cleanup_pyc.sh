#!/bin/bash

# ChatBot AutoRelease 项目清理脚本
# 清理 .pyc 文件和 __pycache__ 目录

echo "🧹 开始清理 Python 缓存文件..."

# 删除 .pyc 文件（排除虚拟环境）
echo "删除 .pyc 文件..."
find . -name "*.pyc" -not -path "./venv*" -not -path "./env*" -delete

# 删除 .pyo 文件
echo "删除 .pyo 文件..."
find . -name "*.pyo" -not -path "./venv*" -not -path "./env*" -delete

# 删除 __pycache__ 目录
echo "删除 __pycache__ 目录..."
find . -name "__pycache__" -not -path "./venv*" -not -path "./env*" -type d -exec rm -rf {} + 2>/dev/null

# 删除 .DS_Store 文件 (macOS)
echo "删除 .DS_Store 文件..."
find . -name ".DS_Store" -delete 2>/dev/null

# 删除临时文件
echo "删除临时文件..."
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.swp" -delete 2>/dev/null
find . -name "*.swo" -delete 2>/dev/null
find . -name "*~" -delete 2>/dev/null

# 清理测试生成的文件
echo "清理测试生成的文件..."
rm -f statistics_test_report.json 2>/dev/null
rm -f sample_daily_report.html 2>/dev/null
rm -f test_*.html 2>/dev/null

echo "✅ 清理完成！"

# 显示清理后的状态
echo ""
echo "📊 清理后状态："
echo "剩余 .pyc 文件数量: $(find . -name "*.pyc" -not -path "./venv*" | wc -l)"
echo "剩余 __pycache__ 目录数量: $(find . -name "__pycache__" -not -path "./venv*" -type d | wc -l)"
echo "剩余 .DS_Store 文件数量: $(find . -name ".DS_Store" | wc -l)"

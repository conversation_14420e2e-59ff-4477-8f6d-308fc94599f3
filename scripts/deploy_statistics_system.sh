#!/bin/bash

# ChatBot AutoRelease 统计系统自动化部署脚本
# 版本: v1.0.0
# 作者: <EMAIL>

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_user() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查必要的命令
check_dependencies() {
    log_info "检查依赖..."
    
    local deps=("python" "git" "curl")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            log_error "$dep 未安装"
            exit 1
        fi
    done
    
    log_success "依赖检查通过"
}

# 检查数据库类型
detect_database() {
    log_info "检测数据库类型..."

    # 从Django配置中检测数据库类型
    local db_engine=$(python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()
from django.conf import settings
print(settings.DATABASES['default']['ENGINE'])
" 2>/dev/null)

    if [[ "$db_engine" == *"mysql"* ]]; then
        DB_TYPE="mysql"
        log_info "检测到 MySQL 数据库"
    elif [[ "$db_engine" == *"postgresql"* ]]; then
        DB_TYPE="postgresql"
        log_info "检测到 PostgreSQL 数据库"
    else
        log_warning "无法检测数据库类型: $db_engine"
        DB_TYPE="mysql"  # 默认为MySQL，因为项目使用MySQL
        log_info "默认使用 MySQL"
    fi
}

# 创建备份
create_backup() {
    log_info "创建数据库备份..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if [[ "$DB_TYPE" == "postgresql" ]]; then
        # 从Django设置中获取数据库信息
        local db_info=$(python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()
from django.conf import settings
db = settings.DATABASES['default']
print(f\"{db['HOST']} {db['PORT']} {db['USER']} {db['NAME']}\")
")
        
        read -r db_host db_port db_user db_name <<< "$db_info"
        
        log_info "备份 PostgreSQL 数据库: $db_name"
        pg_dump -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" > "$backup_file"
        
    elif [[ "$DB_TYPE" == "mysql" ]]; then
        local db_info=$(python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()
from django.conf import settings
db = settings.DATABASES['default']
print(f\"{db['HOST']} {db['PORT']} {db['USER']} {db['NAME']}\")
")
        
        read -r db_host db_port db_user db_name <<< "$db_info"
        
        log_info "备份 MySQL 数据库: $db_name"

        # 检查是否有密码
        local db_password=$(python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()
from django.conf import settings
print(settings.DATABASES['default']['PASSWORD'])
")

        if [[ -z "$db_password" ]]; then
            log_info "使用无密码连接"
            mysqldump -h "$db_host" -P "$db_port" -u "$db_user" "$db_name" > "$backup_file"
        else
            mysqldump -h "$db_host" -P "$db_port" -u "$db_user" -p"$db_password" "$db_name" > "$backup_file"
        fi
    else
        log_warning "无法自动备份，请手动备份数据库"
        read -p "是否已完成手动备份？(y/N): " confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            log_error "请先完成数据库备份"
            exit 1
        fi
        return
    fi
    
    if [[ -f "$backup_file" && -s "$backup_file" ]]; then
        log_success "数据库备份完成: $backup_file"
        echo "export BACKUP_FILE=$backup_file" > .deployment_backup
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    local available=$(df . | tail -1 | awk '{print $4}')
    local required=1048576  # 1GB in KB
    
    if [[ $available -lt $required ]]; then
        log_error "磁盘空间不足，需要至少1GB可用空间"
        exit 1
    fi
    
    log_success "磁盘空间充足"
}

# 运行测试
run_tests() {
    log_info "运行部署前测试..."
    
    if [[ -f "test_statistics_comprehensive.py" ]]; then
        if python test_statistics_comprehensive.py; then
            log_success "测试通过"
        else
            log_error "测试失败，请修复后重试"
            exit 1
        fi
    else
        log_warning "测试文件不存在，跳过测试"
    fi
}

# 检查迁移状态
check_migration_status() {
    log_info "检查迁移状态..."
    
    local migration_status=$(python manage.py showmigrations app01 --plan | grep "0024_add_statistics_models" || true)
    
    if [[ -z "$migration_status" ]]; then
        log_error "找不到统计系统迁移文件"
        exit 1
    fi
    
    if echo "$migration_status" | grep -q "\[X\]"; then
        log_warning "统计系统迁移已经执行过"
        read -p "是否继续？(y/N): " confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            exit 0
        fi
    fi
    
    log_success "迁移状态检查完成"
}

# 执行数据库迁移
run_migration() {
    log_info "执行数据库迁移..."
    
    # 预检查迁移
    log_info "预检查迁移计划..."
    python manage.py migrate --plan
    
    read -p "确认执行迁移？(y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_error "用户取消迁移"
        exit 1
    fi
    
    # 执行迁移
    log_info "开始执行迁移: $(date)"
    if python manage.py migrate app01 0024; then
        log_success "迁移执行成功: $(date)"
    else
        log_error "迁移执行失败"
        exit 1
    fi
    
    # 验证迁移结果
    log_info "验证迁移结果..."
    python manage.py showmigrations app01 | grep "0024_add_statistics_models"
    
    # 检查新表
    python manage.py dbshell << 'EOF'
SELECT table_name FROM information_schema.tables 
WHERE table_name LIKE 'bot_%' 
   OR table_name LIKE 'command_%' 
   OR table_name LIKE 'system_%' 
   OR table_name LIKE 'user_%' 
   OR table_name LIKE 'cronjob_%';
EOF
    
    log_success "迁移验证完成"
}

# 更新配置
update_configuration() {
    log_info "更新配置文件..."
    
    # 备份当前配置
    local config_backup="djangoProject/settings.py.backup.$(date +%Y%m%d_%H%M%S)"
    cp djangoProject/settings.py "$config_backup"
    log_info "配置文件已备份: $config_backup"
    
    # 检查是否已有统计配置
    if grep -q "STATISTICS_ENABLED" djangoProject/settings.py; then
        log_warning "配置文件中已存在统计系统配置"
        return
    fi
    
    # 添加统计系统配置
    cat >> djangoProject/settings.py << 'EOF'

# ================================
# 统计系统配置 (自动添加)
# ================================

# 基础配置
STATISTICS_ENABLED = True
STATISTICS_ASYNC = True
STATISTICS_SERVICE_ENABLED = True
STATISTICS_REPORTS_ENABLED = True

# 中间件配置 (保守启动，建议后续手动启用)
STATISTICS_MIDDLEWARE_ENABLED = False
STATISTICS_COLLECT_ALL_REQUESTS = False

# 数据保留配置
STATISTICS_DATA_RETENTION_DAYS = 90
STATISTICS_ACCESS_EVENT_RETENTION_DAYS = 30
STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS = 7

# 性能配置
STATISTICS_BUFFER_SIZE = 100
STATISTICS_FLUSH_INTERVAL = 60
STATISTICS_MAX_RESPONSE_CONTENT_LENGTH = 5000
STATISTICS_MAX_ERROR_MESSAGE_LENGTH = 1000

# 告警配置
STATISTICS_ALERT_ERROR_RATE_THRESHOLD = 10.0
STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD = 5.0
STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD = 5

EOF
    
    # 验证配置
    if python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()
from django.conf import settings
assert getattr(settings, 'STATISTICS_ENABLED', False) == True
print('配置验证成功')
"; then
        log_success "配置更新完成"
    else
        log_error "配置验证失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启应用服务..."
    
    # 检测服务管理方式
    if systemctl is-active --quiet django-app 2>/dev/null; then
        log_info "使用 systemd 重启服务"
        sudo systemctl restart django-app
        sudo systemctl status django-app --no-pager
    elif pgrep -f "supervisord" > /dev/null; then
        log_info "使用 supervisor 重启服务"
        sudo supervisorctl restart chatbot-ar-be
        sudo supervisorctl status chatbot-ar-be
    elif pgrep -f "pm2" > /dev/null; then
        log_info "使用 PM2 重启服务"
        pm2 restart chatbot-ar-be
        pm2 status
    else
        log_warning "无法检测服务管理方式，请手动重启服务"
        read -p "服务已重启？(y/N): " confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            log_error "请重启服务后继续"
            exit 1
        fi
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    log_success "服务重启完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 检查API接口
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/statistics/realtime/dashboard/ || echo "000")
    
    if [[ "$api_status" == "200" ]]; then
        log_success "API接口正常"
    else
        log_error "API接口异常 (HTTP $api_status)"
        return 1
    fi
    
    # 检查监控面板
    local dashboard_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/statistics/dashboard/ || echo "000")
    
    if [[ "$dashboard_status" == "200" ]]; then
        log_success "监控面板正常"
    else
        log_error "监控面板异常 (HTTP $dashboard_status)"
        return 1
    fi
    
    # 运行功能测试
    if [[ -f "test_dashboard_access.py" ]]; then
        if python test_dashboard_access.py; then
            log_success "功能测试通过"
        else
            log_error "功能测试失败"
            return 1
        fi
    fi
    
    log_success "部署验证完成"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f .deployment_backup
}

# 主函数
main() {
    log_info "开始 ChatBot AutoRelease 统计系统部署"
    log_info "部署时间: $(date)"
    
    # 检查用户权限
    check_user
    
    # 检查依赖
    check_dependencies
    
    # 检测数据库
    detect_database
    
    # 检查磁盘空间
    check_disk_space
    
    # 运行测试
    run_tests
    
    # 检查迁移状态
    check_migration_status
    
    # 创建备份
    create_backup
    
    # 执行迁移
    run_migration
    
    # 更新配置
    update_configuration
    
    # 重启服务
    restart_service
    
    # 验证部署
    if verify_deployment; then
        log_success "🎉 统计系统部署成功！"
        log_info "监控面板: http://your-domain/api/statistics/dashboard/"
        log_info "API文档: 参考 docs/STATISTICS_SYSTEM_GUIDE.md"
    else
        log_error "部署验证失败，请检查日志"
        exit 1
    fi
    
    # 清理
    cleanup
    
    log_info "部署完成时间: $(date)"
}

# 信号处理
trap cleanup EXIT

# 执行主函数
main "$@"

#!/usr/bin/env python3
"""
废弃函数监控脚本
用于监控日志中是否出现废弃函数的警告
"""

import os
import re
import sys
from datetime import datetime, timedelta
import subprocess

DEPRECATED_FUNCTIONS = [
    'MRnotdeal_20230726',
    'failuremsg', 
    'failuremsg_old',
    'mergefromfeature',
    'timeformat'
]

LOG_PATHS = [
    'logs/',
    '/var/log/',
    './logs/'
]

def check_logs_for_deprecation_warnings():
    """检查日志文件中的废弃警告"""
    print("🔍 检查废弃函数调用警告...")
    print("=" * 50)
    
    warnings_found = []
    
    for log_path in LOG_PATHS:
        if os.path.exists(log_path):
            print(f"检查目录: {log_path}")
            try:
                # 使用grep查找废弃警告
                cmd = f"find {log_path} -name '*.log' -type f -exec grep -l 'DeprecationWarning\\|deprecated' {{}} \\;"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout:
                    log_files = result.stdout.strip().split('\n')
                    for log_file in log_files:
                        print(f"  发现可能的警告: {log_file}")
                        warnings_found.append(log_file)
                else:
                    print(f"  ✅ 未发现废弃警告")
            except Exception as e:
                print(f"  ❌ 检查失败: {e}")
    
    return warnings_found

def generate_monitoring_report():
    """生成监控报告"""
    report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"\n📊 废弃函数监控报告")
    print(f"生成时间: {report_time}")
    print("=" * 50)
    
    # 检查Git分支
    try:
        branch_result = subprocess.run(['git', 'branch', '--show-current'], 
                                     capture_output=True, text=True)
        current_branch = branch_result.stdout.strip()
        print(f"当前分支: {current_branch}")
    except:
        print("无法获取Git分支信息")
    
    # 检查最近的commits
    try:
        commit_result = subprocess.run(['git', 'log', '--oneline', '-5'], 
                                     capture_output=True, text=True)
        print(f"\n最近的提交:")
        for line in commit_result.stdout.strip().split('\n')[:3]:
            print(f"  {line}")
    except:
        pass
    
    # 检查日志中的警告
    warnings = check_logs_for_deprecation_warnings()
    
    print(f"\n📋 监控建议:")
    if not warnings:
        print("✅ 未发现废弃函数调用警告")
        print("✅ 可以考虑进入下一阶段删除")
    else:
        print("⚠️  发现可能的废弃函数调用")
        print("⚠️  建议进一步调查后再删除")
    
    print(f"\n🗓️  下次检查建议: {(datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d')}")

def create_safe_deletion_script():
    """创建安全删除脚本"""
    script_content = '''#!/usr/bin/env python3
"""
安全删除废弃函数脚本
只有在确认无警告的情况下才执行
"""

import re

FUNCTIONS_TO_DELETE = [
    ('MRnotdeal_20230726', 3343, 259),
    ('failuremsg', 3863, 166), 
    ('failuremsg_old', 4027, 144),
    ('timeformat', 3834, 14),
    # mergefromfeature 最后删除，因为风险较高
]

def safe_delete_function(func_name):
    """安全删除单个函数"""
    print(f"准备删除函数: {func_name}")
    
    # 这里可以添加实际的删除逻辑
    # 暂时只打印，需要手动确认后再实施
    print(f"⚠️  请手动确认删除 {func_name}")

if __name__ == "__main__":
    print("请先运行监控脚本确认无警告后再执行删除")
'''
    
    with open('safe_delete_deprecated_functions.py', 'w') as f:
        f.write(script_content)
    
    print("\n📄 创建了安全删除脚本: safe_delete_deprecated_functions.py")

if __name__ == "__main__":
    print("🚀 废弃函数监控器启动")
    print("=" * 50)
    
    generate_monitoring_report()
    create_safe_deletion_script()
    
    print("\n📋 后续步骤:")
    print("1. 部署当前代码到测试环境")
    print("2. 运行系统1-2周，观察是否有DeprecationWarning")
    print("3. 定期运行此监控脚本")
    print("4. 确认无警告后，运行safe_delete_deprecated_functions.py")
    print("5. 删除后进行充分测试") 
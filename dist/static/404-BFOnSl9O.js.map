{"version": 3, "file": "404-BFOnSl9O.js", "sources": ["../../src/views/errorPage/404.vue"], "sourcesContent": ["<template>\n  <ErrorPage type=\"404\" :title=\"t('errorPages.404.desc')\" :msg=\"t('errorPages.404.remark')\" />\n</template>\n\n<script setup>\n  import ErrorPage from '@/components/ErrorPage/index.vue';\n  import { useI18n } from 'vue-i18n';\n  const { t } = useI18n();\n</script>\n"], "names": ["t", "useI18n"], "mappings": "oIAKA,MAAAA,EAAAA,GAAAC"}
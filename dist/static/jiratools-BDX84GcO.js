import{H as l,o as e,h as i,l as o,ab as t,be as n,k as s,F as d,r as a,t as u,ct as p}from"./index-awKTxnvj.js";const r={key:0},c=["onUpdate:modelValue"],h=["onClick"];const g=l({data:()=>({dialogVisible:!1,description:"",jql:"",cron:"",groupId:"",items:[]}),methods:{showDialog(){this.dialogVisible=!0},save(){this.items.push({description:this.description,jql:this.jql,cron:this.cron,groupId:this.groupId,switch:!1}),this.dialogVisible=!1},execute(l){}}},[["render",function(l,g,m,v,b,V){return e(),i("div",null,[o("button",{onClick:g[0]||(g[0]=(...l)=>V.showDialog&&V.showDialog(...l))},"New"),b.dialogVisible?(e(),i("div",r,[t(o("input",{"onUpdate:modelValue":g[1]||(g[1]=l=>b.description=l),placeholder:"Description"},null,512),[[n,b.description]]),t(o("input",{"onUpdate:modelValue":g[2]||(g[2]=l=>b.jql=l),placeholder:"JQL"},null,512),[[n,b.jql]]),t(o("input",{"onUpdate:modelValue":g[3]||(g[3]=l=>b.cron=l),placeholder:"Cron Expression"},null,512),[[n,b.cron]]),t(o("input",{"onUpdate:modelValue":g[4]||(g[4]=l=>b.groupId=l),placeholder:"Seatalk Group ID"},null,512),[[n,b.groupId]]),o("button",{onClick:g[5]||(g[5]=(...l)=>V.save&&V.save(...l))},"Save")])):s("",!0),(e(!0),i(d,null,a(b.items,(l=>(e(),i("div",{key:l.id},[o("div",null,u(l.description),1),o("div",null,u(l.jql),1),o("div",null,u(l.cron),1),o("div",null,u(l.groupId),1),t(o("input",{type:"checkbox","onUpdate:modelValue":e=>l.switch=e},null,8,c),[[p,l.switch]]),o("button",{onClick:e=>V.execute(l.id)},"立即执行",8,h)])))),128))])}]]);export{g as default};
//# sourceMappingURL=jiratools-BDX84GcO.js.map

{"version": 3, "file": "releaseHistory--Pd21q9h.js", "sources": ["../../src/api/get_jira_release_list_finished.js", "../../src/views/releaseHistory/releaseHistory.vue"], "sourcesContent": ["import axios from 'axios'\n\naxios.defaults.timeout = 50000\n\naxios.interceptors.request.use(config => {\n  // ...\n  return config\n}, error => {\n  return Promise.error(error)\n})\n\nfunction get_jira_release_list_finished() {\n  return axios.get('https://autorelease.chatbot.shopee.io/api/get_jira_release_list_finished', {\n    params: {\n      project_type: 'SPCB,SPCT',\n      release_type: 'Bus,Adhoc,Hotfix'\n    }\n  })\n    .then(function (response) {\n      // console.log(response);\n\n      return response;\n    })\n    .catch(function (error) {\n      console.log(error);\n    });\n}\n\nexport {\n  get_jira_release_list_finished\n}\n", "<template>\n  <div class=\"index-conntainer\">\n    <transition name=\"el-fade-in-linear\">\n      <el-card class=\"card\" shadow=\"hover\" width=\"50px\" height=\"50px\">\n        <div class=\"ar-container\">\n          <ProjectTypeFilter v-model:projectType=\"projectTypeFilter\" />\n          <el-radio-group v-model=\"releaseTypeFilter\" class=\"release-type-filter\" @change=\"handleReleaseTypeChange\">\n            <el-radio-button label=\"bus\">Bus</el-radio-button>\n            <el-radio-button label=\"adhoc\">Adhoc</el-radio-button>\n            <el-radio-button label=\"hotfix\">Hotfix</el-radio-button>\n            <el-radio-button label=\"all\">全部</el-radio-button>\n          </el-radio-group>\n          \n          <el-select v-model=\"selectedProjectFinished\"\n                     filterable\n                     clearable\n                     placeholder=\"please select release ticket\"\n                     style=\"width: 380px; margin-left: 10px;\"\n          >\n\n            <el-option\n                v-for=\"project in filteredProjects\"\n                :key=\"typeof project === 'string' ? project : project.title\"\n                :label=\"typeof project === 'string' ? project : project.title\"\n                :value=\"typeof project === 'string' ? project : project.title\"\n            ></el-option>\n          </el-select>\n\n          <el-drawer v-model=\"visible\" :show-close=\"false\">\n            <template #header=\"{ titleId, titleClass }\">\n              <h4 :id=\"titleId\" :class=\"titleClass\">{{ selectedProject }}</h4>\n            </template>\n            暂无失败信息。\n          </el-drawer>\n        </div>\n\n        <div class=\"ar-container\">\n          <el-table\n\n              :data=\"releaseTableData\"\n              stripe\n              border\n\n              highlight-current-row\n              fit\n              :header-cell-style=\"{background:'#cacfd7',color:'#606266'}\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column label=\"编号\" min-width=\"21\" header-align=\"center\" align=\"center\">\n              <template #default=\"scope\">\n                {{scope.$index+1}}\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"type\"\n                             label=\"类型\"\n                             header-align=\"center\" align=\"center\"\n                             min-width=\"30\"\n                             :filters=\"[\n                            { text: 'Epic', value: 'Epic' },\n                            { text: 'Bug', value: 'Bug' },\n                            { text: 'Task', value: 'Task' },\n                            { text: 'Sub-task', value: 'Sub-task' },\n                            { text: 'Story', value: 'Story' },\n                            ]\"\n                             :filter-method=\"filterType\"\n                             filter-placement=\"bottom-end\"\n            >\n\n              <template #default=\"{ row }\">\n\n                <el-icon :class=\"getIconName(row.type)\"></el-icon>\n              </template>\n\n            </el-table-column>\n\n            <el-table-column prop=\"jira_key\" label=\"单号\" :min-width=\"60\" header-align=\"center\" align=\"center\"\n            >\n              <template #default=\"{ row }\">\n                <el-link\n                    :underline=\"false\"\n                    v-bind:href=\"row.jira_link\"\n                    target=\"_blank\"\n                    type=\"primary\">\n                  {{ row.jira_key }}\n                </el-link>\n              </template>\n            </el-table-column>\n\n\n            <el-table-column prop=\"jira_title\" label=\"需求名\" :min-width=\"150\"\n            >\n\n            </el-table-column>\n\n            <el-table-column prop=\"bug_resolution_rate\" label=\"Bug解决率\" :min-width=\"80\" header-align=\"center\" align=\"center\">\n              <template #default=\"{ row }\">\n                <span :style=\"{ color: Number(row.bug_resolved || 0) === Number(row.bug_total || 0) ? '#67C23A' : '#F56C6C' }\">\n                  <template v-if=\"typeof row.bug_resolved === 'number' && typeof row.bug_total === 'number'\">\n                    {{ row.bug_resolved }}/{{ row.bug_total }}\n                  </template>\n                  <template v-else>\n                    {{ row.bug_resolved || 0 }}/{{ row.bug_total || 0 }}\n                  </template>\n                </span>\n              </template>\n            </el-table-column>\n\n            <el-table-column label=\"周一\" header-align=\"center\" align=\"center\">\n              <el-table-column prop=\"sign_off\" label=\"Signed off\" header-align=\"center\" align=\"center\" min-width=\"40\">\n\n\n                <template #header slot-scope=\"scope\">\n                  Signed<br>off\n                </template>\n                <template #default=\"{ row }\">\n\n                  <el-icon\n                      v-if=\"row.sign_off === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.sign_off === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.sign_off === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.sign_off === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n            </el-table-column>\n            <el-table-column label=\"周二\" header-align=\"center\" align=\"center\">\n              <el-table-column type=\"expand\" label=\"提MR\" min-width=\"32\">\n\n                <template #default=\"props\">\n                  <div>\n                    <div class=\"ar-container\">\n                      <el-tooltip\n                          class=\"box-item\"\n                          effect=\"customized\"\n                          content=\"点击创建MR\"\n                          placement=\"top-start\"\n                      >\n                        <el-button\n                            type=\"danger\"\n                            @click=\"centerDialogVisible = true\"\n                            size=\"small\"\n                            :icon=\"CirclePlus\"\n                            element-loading-text=\"AR正在创建MR, 请耐心等待...\"\n                            v-loading.fullscreen.lock=\"fullscreenLoading\">创建\n                        </el-button>\n                      </el-tooltip>\n\n                      <el-tooltip\n                          class=\"box-item\"\n                          effect=\"customized\"\n                          content=\"点击复制\"\n                          placement=\"top-start\"\n                      >\n                        <el-button\n                            type=\"primary\"\n                            size=\"small\"\n                            @click=\"copyToClipboard(props)\"\n                            :icon=\"ChatRound\"\n                            element-loading-text=\"AR正在处理数据，请耐心等待...\"\n                            v-loading.fullscreen.lock=\"fullscreenLoading\">复制\n                        </el-button>\n                      </el-tooltip>\n                      <el-tooltip\n                          class=\"box-item\"\n                          effect=\"customized\"\n                          content=\"点击发送MR提醒到seatalk\"\n                          placement=\"top-start\"\n                      >\n                        <el-button\n                            type=\"primary\"\n                            size=\"small\"\n                            @click=\"sendSingleFeatureToCT(props)\"\n                            :icon=\"ChatRound\"\n                            element-loading-text=\"AR正在处理数据，请耐心等待...\"\n                            v-loading.fullscreen.lock=\"fullscreenLoading\">发送\n                        </el-button>\n                      </el-tooltip>\n                    </div>\n                    <el-dialog\n                        v-model=\"centerDialogVisible\"\n                        title=\"Warning\"\n                        width=\"30%\"\n                        align-center\n                    >\n                      <span>请确认是否开始自动提MR？发布单： {{ selectedProject }}</span>\n                      <template #footer>\n                        <span class=\"dialog-footer\">\n                          <el-button @click=\"centerDialogVisible = false\">取消</el-button>\n                          <el-button type=\"primary\" @click=\"startSingleAR(props)\">\n                            确认\n                          </el-button>\n                        </span>\n                      </template>\n                    </el-dialog>\n                    <el-table :data=\"props.row.merge_list\" border\n                              :header-cell-style=\"{background:'#def1ce',color:'#606266'}\"\n                    >\n                      <el-table-column label=\"仓库\" prop=\"repo_name\"/>\n                      <el-table-column label=\"分支\" prop=\"branch_name\"/>\n                      <el-table-column label=\"PIC\" prop=\"pic\"/>\n                      <el-table-column label=\"MR地址\" prop=\"web_url\">\n                        <template #default=\"{row}\">\n                          <a\n                              :href=\"row.web_url\"\n                              target=\"_blank\"\n                          >{{ row.web_url }}\n                          </a>\n                        </template>\n                      </el-table-column>\n                      <el-table-column label=\"MR状态\" prop=\"merge_status\"/>\n                      <el-table-column label=\"MR作者\" prop=\"author\"/>\n                    </el-table>\n                  </div>\n                </template>\n\n              </el-table-column>\n\n              <el-table-column prop=\"Code_Merged\" label=\"Code Merged\" header-align=\"center\" align=\"center\"\n                               min-width=\"40\"\n              >\n                <template #header slot-scope=\"scope\">\n                  Code<br>Merged\n                </template>\n                <template #default=\"{ row }\">\n                  <el-icon\n                      v-if=\"row.Code_Merged === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.Code_Merged === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.Code_Merged === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.Code_Merged === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n            </el-table-column>\n            <el-table-column label=\"周三\" header-align=\"center\" align=\"center\">\n              <el-table-column prop=\"config_center\" label=\"Config Changed\" header-align=\"center\" align=\"center\"\n                               min-width=\"43\"\n              >\n                <template #header slot-scope=\"scope\">\n                  Config<br>Changed\n                </template>\n                <template #default=\"{ row }\">\n                  <el-icon\n                      v-if=\"row.config_center === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.config_center === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.config_center === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.config_center === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"DB_Change\" label=\"DB Changed\" header-align=\"center\" align=\"center\" min-width=\"43\">\n                <template #default=\"{ row }\">\n                  <el-icon\n                      v-if=\"row.DB_Change === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.DB_Change === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.DB_Change === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.DB_Change === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n                <template #header slot-scope=\"scope\">\n                  DB<br>Changed\n                </template>\n              </el-table-column>\n              <el-table-column\n                  prop=\"services\"\n                  label=\"服务\"\n                  :min-width=\"150\"\n                  header-align=\"center\"\n                  align=\"center\"\n                  :style=\"{ 'white-space': 'pre-wrap' }\"\n              >\n                <template #default=\"{ row }\">\n                  <div v-if=\"row.services !== ''\">\n                    <template v-for=\"(service, index) in row.services.split('\\n')\">\n                      <div :style=\"{ color: !checkServiceGrouping(service) ? '#F56C67' : 'inherit' }\">\n                        {{ service }}\n                      </div>\n                    </template>\n                  </div>\n                  <el-icon\n                    v-if=\"row.services === ''\"\n                    :size=\"20\"\n                    :color=\"'#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n\n              <el-table-column prop=\"region\" label=\"Region\" header-align=\"center\" align=\"center\" min-width=\"37\">\n                <template #default=\"{ row }\">\n                  <el-text\n                      v-if=\"row.region !== '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.region === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    {{ row.region }}\n                  </el-text>\n                  <el-icon\n                      v-if=\"row.region === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.region === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n            </el-table-column>\n\n\n            <el-table-column prop=\"PM\" label=\"PM\" header-align=\"center\" align=\"center\" min-width=\"50\">\n              <template #default=\"{ row }\">\n                {{ row.PM }}\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"dev_pic\" label=\"DEV PIC\" header-align=\"center\" align=\"center\" min-width=\"40\">\n              <template #header slot-scope=\"scope\">\n                DEV<br>PIC\n              </template>\n              <template #default=\"{ row }\">\n                {{ row.dev_pic }}\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"qa_pic\" label=\"QA\" header-align=\"center\" align=\"center\" min-width=\"50\">\n              <template #default=\"{ row }\">\n                {{ row.qa_pic }}\n              </template>\n            </el-table-column>\n\n\n            <el-table-column prop=\"status\" label=\"Status\" header-align=\"center\" align=\"center\" min-width=\"60\">\n              <template #default=\"{ row }\">\n\n                <el-tag class=\"bold-text\"\n                        effect=\"dark\"\n                        :type=\"getStatusName(row.status)\"\n                        :color=\"getColorName(row.status)\"\n                >{{ getBigName(row.status) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n\n        </div>\n\n\n        <div style=\"display: flex;\">\n\n          <el-table\n\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#e78181',color:'#f8f7f7'}\"\n              :data=\"IN_pingGroupA\"\n              style=\"width: 100%\"\n\n              :empty-text=\"'暂无数据'\"\n          >\n\n            <el-table-column header-align=\"center\" label=\"平台BE1组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n          <el-table\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#819ee7',color:'#f8f7f7'}\"\n              header-align=\"center\"\n              :data=\"IN_pingGroupB\"\n              style=\"width: 100%\"\n\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column header-align=\"center\" label=\"平台BE2组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n          <el-table\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#81e7c8',color:'#f8f7f7'}\"\n              header-align=\"center\"\n              :data=\"IN_featureGroup\"\n              style=\"width: 100%\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column header-align=\"center\" label=\"功能BE组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n          <el-table\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#e7a881',color:'#f8f7f7'}\"\n              header-align=\"center\"\n              :data=\"IN_feGroup\"\n              style=\"width: 100%\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column header-align=\"center\" label=\"FE组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n\n\n        </div>\n\n      </el-card>\n    </transition>\n  </div>\n\n</template>\n\n<script lang=\"ts\" setup>\nimport {ref, onMounted, computed, watch, reactive, watchEffect, nextTick, toRef, toRaw, onUnmounted} from 'vue';\nimport type {ElTree} from 'element-plus'\nimport draggable from \"vuedraggable\";\nimport {copymsg} from '@/api/copymsg';\nimport CircularJSON from 'circular-json';\nimport Sortable from \"sortablejs\";\n\n\n\n\nimport {\n  ElPagination,\n  ElCard,\n  ElTable,\n  ElTableColumn,\n  ElTag,\n  ElSelect,\n  ElOption,\n  ElMessage,\n  ElMessageBox,\n  TableColumnCtx,\n  TableInstance,\n} from 'element-plus';\nimport {Eleme, Loading, Refresh, CirclePlus, ChatRound, Bell} from '@element-plus/icons-vue'\nimport axios from 'axios';\nimport {Edit, View as IconView} from '@element-plus/icons-vue';\nimport {ChatLineRound, Male} from '@element-plus/icons-vue';\nimport {read_json} from '@/api/read_json';\nimport {get_release_tag} from '@/api/get_release_tag';\nimport {send_title} from '@/api/send_title';\nimport {startAuto} from '@/api/startAuto';\nimport {autocheckdata} from '@/api/autocheckdata';\nimport {seatalk} from '@/api/seatalk';\nimport {callMRseatalk} from '@/api/callMRseatalk';\nimport {autochecknewdata} from '@/api/autochecknewdata';\nimport {start_single_ar} from '@/api/start_single_ar';\nimport {mr_seatalk_single_feature_msg} from '@/api/mr_seatalk_single_feature_msg';\nimport {get_key_jira_release_list} from '@/api/get_key_jira_release_list';\nimport {newMerge} from '@/api/newMerge';\nimport {ElButton, ElDrawer} from 'element-plus'\nimport {CircleCloseFilled} from '@element-plus/icons-vue'\nimport type {TabsPaneContext} from 'element-plus'\n//import {consoleLog} from \"echarts/types/src/util/log\";\nimport {signedOff} from '@/api//signedOff';\nconst dialogVisible = ref(false)\nimport {get_jira_release_list_finished} from '@/api/get_jira_release_list_finished';\n\n\nconst selectedProjectFinished = ref();\n\nconst fullscreenLoadingMR = ref(false);\n\nconst dialogTableVisible = ref(false)\nconst centerDialogVisible = ref(false)\nconst activeName = ref('first')\nconst handleClick = (tab: TabsPaneContext, event: Event) => {\n  console.log(tab, event)\n}\nconst fe_services = reactive([]);\nconst be_services = reactive([]);\nconst all_services = reactive([]);\n\n\nconst pingGroupA = reactive([\n  'shopee-chatbot-intent', 'shopee-chatbot-admin', 'shopee-chatbot-adminasynctask', \n  'shopee-chatbot-adminconfigservice', 'shopee-chatbot-adminservice', 'shopee-chatbot-agentcontrol',\n  'shopee-chatbot-asynctask', 'shopee-chatbot-auditlog', 'shopee-chatbot-botapi',\n  'shopee-chatbot-context', 'shopee-chatbot-dm', 'shopee-chatbot-featurecenter',\n  'shopee-chatbot-intentclarification', 'shopee-chatbot-messageasynctask', 'shopee-chatbot-messageservice',\n  'shopee-chatbot-messageverification', 'shopee-chatbot-nlu', 'shopee-chatbot-ordercard',\n  'shopee-chatbot-pilotapi', 'shopee-chatbotcommon-adminasynctask', 'shopee-chatbotcommon-adminconfigservice',\n  'shopee-chatbotcommon-adminservice', 'shopee-chatbotcommon-agentcontrol', 'shopee-chatbotcommon-asynctask',\n  'shopee-chatbotcommon-botapi', 'shopee-chatbotcommon-context', 'shopee-chatbotcommon-dm',\n  'shopee-chatbotcommon-featurecenter', 'shopee-chatbotcommon-nlu', 'shopee-chatbotcommon-productrecommend',\n  'shopee-chatbotcommon-rulebaseservice', 'shopee-chatbotcommon-shopconsole', 'shopee-chatbot-websocketgwy',\n  'shopee-chatbotcommon-logic','shopee-chatbotcommon-msgdetection'\n]);\n\nconst pingGroupB = reactive([\n  'shopee-chatbot-autotraining', 'shopee-annotation-admin', 'shopee-annotation-asynctask',\n  'shopee-agorithmservice-component', 'shopee-chatbot-experimentmanagement', 'shopee-chatbot-featureapiproxy',\n  'shopee-chatbot-modelgw', 'shopee-chatbot-realtime', 'shopee-chatbot-recallmanager',\n  'shopee-chatbot-recallservice', 'shopee-chatbot-recommendation', 'shopee-chatbotcommon-apadmin',\n  'shopee-chatbotcommon-apasynctask', 'shopee-chatbotcommon-component', 'shopee-chatbotcommon-experimentmanagement',\n  'shopee-chatbotcommon-featureapiproxy', 'shopee-chatbotcommon-intent', 'shopee-chatbotcommon-kbadmin',\n  'shopee-chatbotcommon-kbapi', 'shopee-chatbotcommon-kbasynctask', 'shopee-chatbotcommon-kblabelclarification',\n  'shopee-chatbotcommon-modelgw', 'shopee-knowledgebase-admin', 'shopee-knowledgebase-api',\n  'shopee-knowledgebase-asynctask', 'shopee-knowledgebase-labelclarification', 'shopee-chatbotcommon-promptmanagements',\n  'shopee-knowledgeplatform-offline','shopee-knowledgeplatform-admin','shopee-knowledgeplatform-api','shopee-knowledgeplatform-qa_tools'\n]);\n\nconst featureGroup = reactive([\n  'shopee-chatbot-api', 'shopee-chatbot-autotraining', 'shopee-chatbotcommon-tfapiproxy',\n  'shopee-chatbotcommon-tfeditor', 'shopee-chatbotcommon-tfserving', 'shopee-chatbotcommon-tfvariateserving',\n  'shopee-taskflow-apiproxy', 'shopee-taskflow-editor', 'shopee-taskflow-taskflowserving',\n  'shopee-taskflow-taskflowsop', 'shopee-taskflow-variateserving'\n]);\n\nconst feGroup = reactive([\n  'shopee-autotrainingportal-adminstatic', 'shopee-annotation-adminstatic', 'shopee-cbrcmdplt-rcmdpltstatic',\n  'shopee-chatbot-adminstatic', 'shopee-chatbot-chatbotcsatstatic', 'shopee-chatbot-chatbotrnstatic',\n  'shopee-chatbot-chatbotstatic', 'shopee-chatbot-csatstatic', 'shopee-chatbot-dashboardstatic',\n  'shopee-chatbot-tmcstatic', 'shopee-chatbotcommon-admincommonsaasstatic', 'shopee-chatbotcommon-adminsaasstatic',\n  'shopee-chatbotcommon-adminstatic', 'shopee-chatbotcommon-annotationadminstatic', 'shopee-chatbotcommon-apadminsaasstatic',\n  'shopee-chatbotcommon-csatstatic', 'shopee-chatbotcommon-kbadmincommonsaasstatic', 'shopee-chatbotcommon-kbadminsaasstatic',\n  'shopee-chatbotcommon-shopconsolestatic', 'shopee-chatbotcommon-static', 'shopee-chatbotcommon-tfeadmincommonsaasstatic',\n  'shopee-chatbotcommon-tfeadminsaasstatic', 'shopee-chatbotcommon-tmcsaasstatic', 'shopee-gec-gecstatic',\n  'shopee-knowledgebase-adminstatic', 'shopee-taskflow-adminstatic', 'shopee-cschat-h5',\n  'shopee-knowledgeplatform-adminstatic','shopee-knowledgeplatform-guidesstatic','shopee-knowledgeplatformnode-knowledgeplatformnode', \n  'shopee-chatbot-insights', 'shopee-chatbotcommon-insightssaasstatic-test',\n  'shopee-chatbot-mmfchatbotconsole'\n]);\n\n// 添加检查服务是否被正确分组的函数\nfunction checkServiceGrouping(service) {\n  if (fe_services.includes(service)) {\n    // 如果是前端服务，应该在FE组里\n    return feGroup.includes(service);\n  } else if (be_services.includes(service)) {\n    // 如果是后端服务，应该在平台BE1组、平台BE2组或功能BE组里\n    return pingGroupA.includes(service) || pingGroupB.includes(service) || featureGroup.includes(service);\n  }\n  return false; // 如果既不是前端也不是后端服务，则认为没有分组\n}\n\n// 添加一个数组存储未分组的服务\nconst ungrouped_services = ref([]);\n\nonMounted(async () => {\n  try {\n    // 获取发布单列表\n    const releaseList = await get_jira_release_list_finished();\n    console.log('API返回数据:', releaseList);\n    \n    // 确保响应数据存在且有数据\n    if (!releaseList || !releaseList.data || !releaseList.data.data) {\n      console.error('API返回数据格式不正确:', releaseList);\n      return;\n    }\n    \n    const releaseData = releaseList.data.data;\n    console.log('解析后的发布单数据:', releaseData);\n    \n    // 检查之前选择的发布单是否存在于当前获取的数据中\n    if (selectedProjectFinished.value) {\n      const projectExists = Array.isArray(releaseData) && releaseData.includes(selectedProjectFinished.value);\n      \n      if (!projectExists) {\n        console.log('之前选择的发布单不在当前列表中，清空选择');\n        selectedProjectFinished.value = '';\n      }\n    }\n    \n    // 如果没有选择发布单，自动选择最近的发布单\n    if (!selectedProjectFinished.value) {\n      const currentDate = new Date();\n      currentDate.setHours(0, 0, 0, 0);\n      let closestDate = null;\n      let minDayDiff = Infinity;\n      \n      if (Array.isArray(releaseData)) {\n        releaseData.forEach((projectTitle) => {\n          console.log('处理项目:', projectTitle);\n          \n          // 匹配日期格式: Bus-YYMMDD 或 Bus-YYYYMMDD\n          const match = projectTitle.match(/(bus|adhoc|hotfix)-(\\d{6,8})/i);\n          if (match) {\n            const dateString = match[2];\n            let year, month, day;\n            \n            // 处理6位和8位日期格式\n            if (dateString.length === 6) {\n              year = parseInt('20' + dateString.substring(0, 2));\n              month = parseInt(dateString.substring(2, 4)) - 1;\n              day = parseInt(dateString.substring(4, 6));\n            } else if (dateString.length === 8) {\n              year = parseInt(dateString.substring(0, 4));\n              month = parseInt(dateString.substring(4, 6)) - 1;\n              day = parseInt(dateString.substring(6, 8));\n            }\n            \n            const date = new Date(year, month, day);\n            console.log(`日期解析: ${projectTitle} => ${date.toISOString().split('T')[0]}`);\n            \n            // 计算与当前日期的差值（天数）\n            const timeDiff = currentDate.getTime() - date.getTime();\n            const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));\n            \n            // 选择过去最近的或今天的发布单\n            if (dayDiff >= 0 && dayDiff < minDayDiff) {\n              minDayDiff = dayDiff;\n              closestDate = date;\n              selectedProjectFinished.value = projectTitle;\n              console.log('自动选择历史发布单:', selectedProjectFinished.value, '天数差:', dayDiff);\n            }\n          }\n        });\n      }\n    }\n    \n    // 设置下拉列表数据\n    selectedRelease.value = releaseData;\n    \n    // 清空projects数组并填充对象格式的数据\n    projects.splice(0, projects.length);\n    \n    if (Array.isArray(releaseData)) {\n      releaseData.forEach((item) => {\n        // 创建具有title属性的对象\n        projects.push({ title: item });\n      });\n    }\n    \n    console.log('处理后的项目列表:', projects);\n    \n    // 初始化过滤的项目列表\n    filterProjects();\n    \n    // 如果选择了发布单，加载数据\n    if (selectedProjectFinished.value) {\n      getData(selectedProjectFinished.value);\n    }\n    \n    // 检查页面上是否有内容，隐藏空状态\n    if (document.body.innerHTML.trim().length > 0) {\n      const emptyElement = document.querySelector('.el-empty');\n      if (emptyElement) {\n        emptyElement.style.display = 'none';\n      }\n    }\n  } catch (error) {\n    console.error('获取发布单列表失败:', error);\n  }\n});\n\n\n\nconst IN_pingGroupA = computed(() => {\n  const result = {};\n  const pingGroupAKeys = pingGroupA.map(obj => Object.keys(obj)).flat();\n  for (const item of be_services) {\n    if (pingGroupAKeys.includes(item)) {\n      const link = pingGroupA.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\nconst IN_pingGroupB = computed(() => {\n  const result = {};\n  const pingGroupBKeys = pingGroupB.map(obj => Object.keys(obj)).flat();\n  for (const item of be_services) {\n    if (pingGroupBKeys.includes(item)) {\n      const link = pingGroupB.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\n\nconst IN_featureGroup = computed(() => {\n  const result = {};\n  const featureGroupKeys = featureGroup.map(obj => Object.keys(obj)).flat();\n  for (const item of be_services) {\n    if (featureGroupKeys.includes(item)) {\n      const link = featureGroup.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\n\nconst IN_feGroup = computed(() => {\n  const result = {};\n  const beGroupKeys = feGroup.map(obj => Object.keys(obj)).flat();\n  for (const item of fe_services) {\n    if (beGroupKeys.includes(item)) {\n      const link = feGroup.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\n\nlet services_pane = ref(false);\nlet visible = ref(false);\nlet show = ref(false);\nlet showfe = ref(false);\nlet showbe = ref(false);\nlet showresult = ref(false);\nlet active = ref(0);\nlet jiraStatus = ref(\"wait\");\nlet masterStatus = ref(\"wait\");\nconst tagType = [\"success\", \"info\", \"warning\", \"danger\"];\n// 发布类型过滤器，从本地存储获取或使用默认值\nconst projectTypeFilter = ref('SPCB'); // 添加项目类型过滤器，默认为SPCB\nconst releaseTypeFilter = ref(localStorage.getItem('releaseTypeFilter') || 'bus');\nconst filteredProjects = ref([]); // 存储过滤后的项目列表\n\n// 从本地存储获取上次使用的过滤类型\nonMounted(async () => {\n  const savedFilter = localStorage.getItem('releaseTypeFilter');\n  if (savedFilter) {\n    releaseTypeFilter.value = savedFilter;\n  }\n\n  try {\n    // 获取发布单列表\n    const response = await get_jira_release_list_finished();\n    const releaseData = response.data;\n    console.log('获取到的发布单列表:', releaseData);\n\n    // 清空projects数组并填充新数据\n    projects.splice(0, projects.length);\n    if (Array.isArray(releaseData)) {\n      releaseData.forEach((item) => {\n        projects.push(item);\n      });\n    }\n\n    // 初始化过滤项目列表\n    filterProjects();\n\n    // 自动选择最近的发布单\n    if (filteredProjects.value.length > 0 && !selectedProjectFinished.value) {\n      selectClosestRelease();\n    }\n\n    // 如果选择了发布单，加载数据\n    if (selectedProjectFinished.value) {\n      getData(selectedProjectFinished.value);\n    }\n\n    // 检查页面上是否有内容\n    if (document.body.innerHTML.trim().length > 0) {\n      const emptyElement = document.querySelector('.el-empty');\n      if (emptyElement) {\n        emptyElement.style.display = 'none';\n      }\n    }\n  } catch (error) {\n    console.error('获取发布单列表失败:', error);\n    ElMessage.error('获取发布单列表失败');\n  }\n});\n\n// 处理发布类型过滤变化\nconst handleReleaseTypeChange = () => {\n  // 保存过滤类型到本地存储\n  localStorage.setItem('releaseTypeFilter', releaseTypeFilter.value);\n  filterProjects();\n  \n  // 过滤后自动选择最近的发布单\n  if (filteredProjects.value.length > 0) {\n    selectClosestRelease();\n  } else {\n    // 如果没有过滤结果，确保清空选择和数据\n    selectedProjectFinished.value = '';\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n};\n\n// 过滤项目列表\nconst filterProjects = () => {\n  console.log('过滤项目列表，当前过滤类型:', releaseTypeFilter.value, '项目类型:', projectTypeFilter.value);\n  \n  if (!projects.length) return;\n  \n  filteredProjects.value = projects.filter(project => {\n    // 检查发布类型\n    const matchesType = releaseTypeFilter.value === 'all' || \n      project.release_type?.toLowerCase() === releaseTypeFilter.value.toLowerCase();\n    \n    // 检查项目类型（SPCB/SPCT）\n    const matchesProject = project.project_type === projectTypeFilter.value;\n    \n    return matchesType && matchesProject;\n  });\n  \n  console.log('过滤后项目数量:', filteredProjects.value.length);\n  \n  // 如果过滤后没有结果，清空选择的项目\n  if (filteredProjects.value.length === 0) {\n    selectedProjectFinished.value = '';\n    // 同时清空表格数据\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n};\n\nfunction getRandomElement() {\n  const randomIndex = Math.floor(Math.random() * tagType.length);\n  console.log(tagType[randomIndex]);\n  return tagType[randomIndex];\n}\n\nconst selectedProject = ref();\n// 每页显示条数\nconst fullscreenLoading = ref(false)\n\ninterface User {\n  release_title: string\n  jira_key: string\n  jira_title: string\n  jira_link: string\n  type: string\n}\n\nconst filterType = (value: string, row: User) => {\n  return row.type === value\n}\n\nconst selectedRelease = ref();\nconst projects = reactive([]);\n\nlet releaseTableData = reactive([]);\n\n\nconst callseatalk = (tab: TabsPaneContext, event: Event) => {\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n  seatalk(fin_data);\n  ElMessage({\n    message: '已进行checklist消息push，请耐心等待seatalk自动发送消息。',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nconst signedOffSeatalk = (tab: TabsPaneContext, event: Event) => {\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n  signedOff(fin_data);\n  ElMessage({\n    message: '已进行Signed off消息push，请耐心等待seatalk自动发送消息。',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nconst callMRseatalkFE = (tab: TabsPaneContext, event: Event) => {\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n\n  callMRseatalk(fin_data);\n  ElMessage({\n    message: '已进行MR消息push，请耐心等待seatalk自动发送消息。',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nasync function sendSingleFeatureToCT(row) {\n  let data_final = ref();\n  data_final = await mr_seatalk_single_feature_msg(row.row);\n  console.log(data_final)\n  navigator.clipboard.writeText(data_final.data)\n      .then(() => {\n        ElMessage({\n          message: '恭喜，MR信息已发送到seatalk！',\n          type: 'success',\n        })\n      })\n      .catch((error) => {\n        ElMessage.error('MR信息发送失败！')\n      });\n}\n\nasync function copyToClipboard(row) {\n  let data_final = ref();\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n  data_final = await copymsg(row.row);\n\n  console.log(data_final)\n  navigator.clipboard.writeText(data_final.data)\n      .then(() => {\n        ElMessage({\n          message: '恭喜，MR信息已复制到剪切板！',\n          type: 'success',\n        })\n        //alert('已复制到剪贴板');\n      })\n      .catch((error) => {\n        ElMessage.error('复制剪切板失败！')\n      });\n}\n\nasync function startSingleAR(props) {\n  //const rawRow = toRaw(props);\n  console.log(props.row);\n  //const jsonString = CircularJSON.stringify(props.toJSON());\n\n  let data_final = ref();\n  data_final = await start_single_ar(props.row);\n\n}\n\nasync function getreleaseData(value) {\n\n  console.log(releaseTableData);\n  const releaseTableDataIds = new Set(releaseTableData.map((item) =>\n      item.jira_key));\n  // 这里编写请求数据的异步操作\n// 将 releaseTableData 中每个对象的 id 属性存入 Set 对象中\n\n  all_services.splice(0, all_services.length);\n  be_services.splice(0, be_services.length);\n  fe_services.splice(0, fe_services.length);\n  console.log(`get data for ${value}`);\n  let fin_data = {\n    title: value,\n  };\n  let data_final = [];\n  let temp_data : any;\n  temp_data = await send_title(fin_data);\n  console.log(temp_data);\n  console.log(await send_title(fin_data))\n  console.log(temp_data);\n  if (temp_data.length === 0) {\n    releaseTableData.splice(0, releaseTableData.length);\n  } else {\n    releaseTableData.splice(0, releaseTableData.length);\n    temp_data.data.forEach((item) => {\n      console.log(item);\n      if (!releaseTableDataIds.has(item.jira_key)) {\n        releaseTableData.push(item);\n        releaseTableDataIds.add(item.jira_key);\n      }\n      // releaseTableData.push(item);\n      // releaseTableDataIds.add(item.jira_key);\n    });\n  }\n\n\n  console.log(releaseTableData);\n}\n\nasync function refreshData(value) {\n  showbe.value = false\n  showfe.value = false\n  console.log(value)\n\n  await getreleaseData(value);\n  fullscreenLoading.value = true\n\n  console.log(releaseTableData)\n  await getNewData(value)\n  fullscreenLoading.value = false\n\n  ElMessage({\n    message: '已更新状态',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nasync function getNewData(value) {\n  showbe.value = false\n  showfe.value = false\n  console.log(value)\n  await getreleaseData(value);\n  fullscreenLoading.value = true\n\n  let data_final = ref();\n  console.log(releaseTableData)\n  data_final.value = await autochecknewdata(releaseTableData);\n\n  fullscreenLoading.value = false\n\n  console.log(data_final);\n  const releaseDataLength = releaseTableData.length;\n  data_final.value.data.slice(0,releaseDataLength).forEach((item, index) => {\n    console.log(item.result_all);\n\n    if (!item.signoff_status) {\n      releaseTableData[index].sign_off = ''\n    } else {\n      releaseTableData[index].sign_off = item.signoff_status;\n    }\n    if (!item.config_center) {\n      releaseTableData[index].config_center = ''\n    } else {\n      releaseTableData[index].config_center = item.config_center;\n    }\n    if (!item.Code_Merged) {\n      releaseTableData[index].Code_Merged = ''\n    } else {\n      releaseTableData[index].Code_Merged = item.Code_Merged;\n    }\n    if (!item.shopee_region) {\n      releaseTableData[index].region = ''\n    } else {\n      releaseTableData[index].region = item.shopee_region;\n    }\n    if (!item.redis_check) {\n      releaseTableData[index].redis_change = ''\n    } else {\n      releaseTableData[index].redis_change = item.redis_check;\n    }\n    if (!item.DB_Change) {\n      releaseTableData[index].DB_Change = ''\n    } else {\n      releaseTableData[index].DB_Change = item.DB_Change;\n    }\n    if (!item.result) {\n      releaseTableData[index].result = ''\n    } else {\n      releaseTableData[index].result = item.result;\n    }\n\n    if (!item.merge_list) {\n      releaseTableData[index].merge_list = ''\n    } else {\n      releaseTableData[index].merge_list = item.merge_list;\n    }\n    if (!item.status) {\n      releaseTableData[index].status = ''\n    } else {\n      releaseTableData[index].status = item.status;\n    }\n    if (!item.dev_pic) {\n      releaseTableData[index].dev_pic = ''\n    } else {\n      releaseTableData[index].dev_pic = item.dev_pic;\n    }\n    if (!item.PM) {\n      releaseTableData[index].PM = ''\n    } else {\n      releaseTableData[index].PM = item.PM;\n    }\n    if (!item.qa_pic) {\n      releaseTableData[index].qa_pic = ''\n    } else {\n      releaseTableData[index].qa_pic = item.qa_pic;\n    }\n    console.log(item.redis_check)\n    console.log(releaseTableData)\n    releaseTableData[index].services = '';\n    item.services_list.services_list_be.forEach((service) => {\n      if (releaseTableData[index].services === '') {\n        releaseTableData[index].services += `${service}`;\n      } else {\n        releaseTableData[index].services += `\\n${service}`;\n      }\n      if (!be_services.includes(service)) {\n        be_services.push(service)\n        all_services.push(service)\n      }\n    })\n    item.services_list.services_list_fe.forEach((service) => {\n      if (releaseTableData[index].services === '') {\n        releaseTableData[index].services += `${service}`;\n      } else {\n        releaseTableData[index].services += `\\n${service}`;\n      }\n      if (!fe_services.includes(service)) {\n        fe_services.push(service)\n        all_services.push(service)\n      }\n    })\n  })\n\n  if (fe_services.length !== 0) {\n    showfe.value = true\n  }\n  if (be_services.length !== 0) {\n    showbe.value = true\n  }\n  console.log(fe_services)\n  console.log(be_services)\n  showresult.value = true\n  // let allTrue = data_final.data.every(item => item.result !== \"false\");\n  // console.log(allTrue);\n  // location.reload();\n}\n\n\nasync function getData(value) {\n  if (!value) {\n    console.log('没有选择发布单，不进行数据查询');\n    // 清空显示数据和状态\n    releaseTableData.splice(0, releaseTableData.length);\n    showbe.value = false;\n    showfe.value = false;\n    showresult.value = false;\n    be_services.splice(0, be_services.length);\n    fe_services.splice(0, fe_services.length);\n    all_services.splice(0, all_services.length);\n    return;\n  }\n  \n  showbe.value = false\n  showfe.value = false\n  console.log(`获取发布单数据: ${value}`)\n  await getreleaseData(value);\n  fullscreenLoading.value = true\n\n  let data_final = ref();\n  console.log('releaseTableData前:', releaseTableData)\n  data_final.value = await autocheckdata(releaseTableData);\n\n  fullscreenLoading.value = false\n  console.log('API响应:', data_final.value);\n  \n  if (!data_final.value || !data_final.value.data || data_final.value.data.length === 0) {\n    console.log('API返回数据为空或格式不正确');\n    // 清空表格数据\n    releaseTableData.splice(0, releaseTableData.length);\n    showresult.value = false;\n    return;\n  }\n  \n  const releaseDataLength = releaseTableData.length;\n  releaseTableData.forEach((itemA) => {\n    const matchedItemB = data_final.value.data.find((itemB) => itemA.jira_key === itemB.feature_key);\n    if (matchedItemB) {\n      console.log('匹配项数据:', matchedItemB);\n\n      itemA.type = matchedItemB.type;\n      itemA.jira_key = matchedItemB.feature_key;\n      itemA.jira_link = `https://jira.shopee.io/browse/${matchedItemB.feature_key}`;\n      itemA.jira_title = matchedItemB.feature_title;\n      \n      // 处理bug相关字段，确保转换为数字类型\n      itemA.bug_resolved = Number(matchedItemB.bug_resolved || 0);\n      itemA.bug_total = Number(matchedItemB.bug_total || 0);\n      console.log(`Bug数据: ${itemA.bug_resolved}/${itemA.bug_total}`);\n      \n      if (!matchedItemB.signoff_status) {\n        itemA.sign_off = ''\n      } else {\n        itemA.sign_off = matchedItemB.signoff_status;\n      }\n      if (!matchedItemB.config_center) {\n        itemA.config_center = ''\n      } else {\n        itemA.config_center = matchedItemB.config_center;\n      }\n      if (!matchedItemB.shopee_region) {\n        itemA.region = ''\n      } else {\n        itemA.region = matchedItemB.shopee_region;\n      }\n      if (!matchedItemB.redis_check) {\n        itemA.redis_change = ''\n      } else {\n        itemA.redis_change = matchedItemB.redis_check;\n      }\n      if (!matchedItemB.result) {\n        itemA.result = ''\n      } else {\n        itemA.result = matchedItemB.result;\n      }\n\n      if (!matchedItemB.merge_list) {\n        itemA.merge_list = ''\n      } else {\n        itemA.merge_list = matchedItemB.merge_list;\n      }\n      if (!matchedItemB.status) {\n        itemA.status = ''\n      } else {\n        itemA.status = matchedItemB.status;\n      }\n      if (!matchedItemB.Code_Merged) {\n        itemA.Code_Merged = ''\n      } else {\n        itemA.Code_Merged = matchedItemB.Code_Merged;\n      }\n      if (!matchedItemB.DB_Change) {\n        itemA.DB_Change = ''\n      } else {\n        itemA.DB_Change = matchedItemB.DB_Change;\n      }\n      if (!matchedItemB.dev_pic) {\n        itemA.dev_pic = ''\n      } else {\n        itemA.dev_pic = matchedItemB.dev_pic;\n      }\n      if (!matchedItemB.PM) {\n        itemA.PM = ''\n      } else {\n        itemA.PM = matchedItemB.PM;\n      }\n      if (!matchedItemB.qa_pic) {\n        itemA.qa_pic = ''\n      } else {\n        itemA.qa_pic = matchedItemB.qa_pic;\n      }\n      itemA.services = '';\n      matchedItemB.services_list.services_list_be.forEach((service) => {\n        if (itemA.services === '') {\n          itemA.services += `${service}`;\n        } else {\n          itemA.services += `\\n${service}`;\n        }\n        if (!be_services.includes(service)) {\n          be_services.push(service)\n          all_services.push(service)\n        }\n      })\n\n      matchedItemB.services_list.services_list_fe.forEach((service) => {\n        if (itemA.services === '') {\n          itemA.services += `${service}`;\n        } else {\n          itemA.services += `\\n${service}`;\n        }\n        if (!fe_services.includes(service)) {\n          fe_services.push(service)\n          all_services.push(service)\n        }\n      })\n    }\n  });\n\n  if (fe_services.length !== 0) {\n    showfe.value = true\n  }\n  if (be_services.length !== 0) {\n    showbe.value = true\n  }\n  console.log('fe_services:', fe_services)\n  console.log('be_services:', be_services)\n  console.log('最终表格数据:', releaseTableData)\n  showresult.value = true\n}\n\nfunction getIconName(type) {\n  if (type === 'Epic') {\n    return 'Epic-icon';\n  } else if (type === 'Sub-task') {\n    return 'ST-icon';\n  } else if (type === 'Task') {\n    return 'Task-icon';\n  } else if (type === 'Bug') {\n    return 'Bug-icon';\n  } else if (type === 'Story') {\n    return 'Story-icon';\n  }\n}\n\nfunction getColorName(status) {\n  if (status === 'TO DO') {\n    return '#42526e';\n  } else if (status === 'Done') {\n    return '#00875a';\n  } else if (status === 'Waiting') {\n    return '#42526e';\n  } else if (status === 'Icebox') {\n    return '#0052CC'\n  } else if (status === 'Doing') {\n    return '#0052CC'\n  } else if (status === 'UAT') {\n    return '#0052CC'\n  } else if (status === 'Delivering') {\n    return '#0052CC'\n  } else if (status === 'Developing') {\n    return '#0052CC'\n  } else if (status === 'Testing') {\n    return '#0052CC'\n  } else if (status === 'TECH DESIGN') {\n    return '#0052CC'\n  } else {\n    return '#0052CC'\n  }\n}\n\n\nfunction getBigName(status) {\n  if (status === 'TO DO') {\n    return 'TO DO';\n  } else if (status === 'Done') {\n    return 'DONE';\n  } else if (status === 'Waiting') {\n    return 'WAITING';\n  } else if (status === 'Icebox') {\n    return 'ICEBOX';\n  } else if (status === 'Doing') {\n    return 'DOING'\n  } else if (status === 'UAT') {\n    return 'UAT'\n  } else if (status === 'Delivering') {\n    return 'DELIVERING'\n  } else if (status === 'Developing') {\n    return 'DEVELOPING'\n  } else if (status === 'Testing') {\n    return 'TESTING'\n  } else {\n    return status\n  }\n}\n\n\nfunction getStatusName(status) {\n  if (status === 'TO DO') {\n    return 'info';\n  } else if (status === 'Done') {\n    return 'success';\n  } else if (status === 'Waiting') {\n    return 'info';\n  } else if (status === 'Icebox') {\n    return 'icebox';\n  } else if (status === 'Doing') {\n    return 'doing';\n  } else if (status === 'UAT') {\n    return 'uat';\n  } else if (status === 'Delivering') {\n    return 'delivering'\n  } else if (status === 'Developing') {\n    return 'developing'\n  } else if (status === 'Testing') {\n    return 'testing'\n  }\n}\n\n\nconst form = reactive({\n  name: '',\n  merge: true,\n});\n\nwatch(selectedProject, (newValue, oldValue) => {\n  if (newValue !== '' && newValue !== oldValue) {\n    releaseTableData.splice(0, releaseTableData.length);\n    getData(newValue);\n  }\n  form.name = removeReleasePrefix(newValue);\n});\n\n\nwatch(releaseTableData, (newValue, oldValue) => {\n  localStorage.setItem('releaseTableData', JSON.stringify(releaseTableData))\n  if (releaseTableData.length === 0) {\n    show.value = false\n  }\n  if (releaseTableData.length !== 0) {\n    show.value = true\n  }\n});\n\nwatch(all_services, (newValue, oldValue) => {\n  if (all_services.length === 0) {\n    services_pane.value = false\n  }\n  if (all_services.length !== 0) {\n    services_pane.value = true\n  }\n});\n\nwatchEffect(() => {\n  const savedOption = localStorage.getItem('selectedProject');\n  let saveactive = localStorage.getItem('active');\n\n  if (savedOption) {\n    selectedProject.value = savedOption\n  }\n  if (saveactive) {\n    active.value = saveactive\n  }\n})\n\n\nwatchEffect(() => {\n  localStorage.setItem('selectedProject', selectedProject.value);\n})\n\nfunction removeReleasePrefix(refParam) {\n  if (refParam) {\n    let modifiedString = refParam.replace(/【Release】|发布单/g, \"\").replace(/\\s/g, \"\");\n    console.log(modifiedString);\n    return modifiedString;\n  }\n  return \"\";\n}\n\n\n\n\n//平台1组\nconst selectedData1 = ref([]); // 保存选中的表格数据\n//平台2组\nconst selectedData2 = ref([]); // 保存选中的表格数据\n//功能组\nconst selectedData3 = ref([]); // 保存选中的表格数据\n//前端组\nconst selectedData4 = ref([]); // 保存选中的表格数据\n\n\nconst handleSelectionChange1 = (selection) => {\n  selectedData1.value = [];\n  selectedData1.value = selection;\n  console.log(processedData1.value);\n  console.log(selectedData1.value);\n};\nconst processedData1 = computed(() => {\n  return selectedData1.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\nconst getRowKey = (row: any) => row.id;\nconst defaultSort = {\n  prop: 'id',\n  order: 'ascending'\n};\nconst handleSortChange = ({ newIndex, oldIndex }: { newIndex: number, oldIndex: number }) => {\n  const movedItem = processedData1.value.splice(oldIndex, 1)[0];\n  processedData1.value.splice(newIndex, 0, movedItem);\n};\nconst handleSelectionChange2 = (selection) => {\n  selectedData2.value = []\n  selectedData2.value = selection;\n};\nconst processedData2 = computed(() => {\n  return selectedData2.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\nconst handleSelectionChange3 = (selection) => {\n  selectedData3.value = []\n  selectedData3.value = selection;\n};\nconst processedData3 = computed(() => {\n  return selectedData3.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\nconst handleSelectionChange4 = (selection) => {\n  selectedData4.value = []\n  selectedData4.value = selection;\n};\nconst processedData4 = computed(() => {\n  return selectedData4.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\n\n\nlet mrTableData = reactive([]);\nconst submitForm = () => {\n  dialogVisible.value = false;\n  fullscreenLoadingMR.value = true;\n  const mergedArray = selectedData1.value.concat(selectedData2.value, selectedData3.value, selectedData4.value);\n  const uniqueArray = Array.from(new Set(mergedArray));\n\n  const newArray = uniqueArray.map(item => item.name);\n  console.log(newArray);\n  const newData = {\n    \"services_list\": newArray,\n    \"title\": form.name,\n    \"if_merge\": form.merge\n  };\n  console.log(newData);\n  let data_fin = newMerge(newData);\n\n  data_fin.then((result) => {\n    console.log(result['repo']);\n    for (let i in result['repo']) {\n      console.log(i);\n      console.log(result['repo'][i])\n      mrTableData.push({\n        repo: result['repo'][i][\"repo\"],\n        url: result['repo'][i][\"url\"],\n        status: result['repo'][i][\"status\"],\n      });\n\n    }\n  });\n  fullscreenLoadingMR.value = false;\n  console.log(mrTableData)\n};\n\nwatch(selectedProjectFinished, (newValue, oldValue) => {\n  if (newValue !== '') {\n    releaseTableData.splice(0, releaseTableData.length);\n    getData(newValue);\n\n  }\n\n});\nwatchEffect(() => {\n  const savedOption = localStorage.getItem('selectedProjectFinished');\n  let saveactive = localStorage.getItem('active');\n\n  if (savedOption) {\n    selectedProjectFinished.value = savedOption\n  }\n  if (saveactive) {\n    active.value = saveactive\n  }\n})\nwatchEffect(() => {\n  localStorage.setItem('selectedProjectFinished', selectedProjectFinished.value);\n})\n\n// 监听过滤类型变化\nwatch([releaseTypeFilter, projectTypeFilter], () => {\n  // 保存过滤类型到本地存储\n  localStorage.setItem('releaseTypeFilter', releaseTypeFilter.value);\n  \n  // 更新过滤项目\n  filterProjects();\n  \n  // 过滤后自动选择最近的发布单\n  if (filteredProjects.value.length > 0) {\n    selectClosestRelease();\n  } else {\n    // 如果没有过滤结果，清空选择\n    selectedProjectFinished.value = '';\n    // 同时清空表格数据\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n});\n\n// 监听项目选择变化\nwatch(selectedProjectFinished, (newValue, oldValue) => {\n  if (newValue !== oldValue) {\n    releaseTableData.splice(0, releaseTableData.length);\n    getData(newValue);\n  }\n  form.name = removeReleasePrefix(newValue);\n});\n\n// 选择最近的发布单\nconst selectClosestRelease = () => {\n  const today = new Date();\n  let closestIndex = -1;\n  let minDiff = Infinity;\n  let matchedProject = null;\n  \n  console.log('开始寻找最近的历史发布单...');\n  \n  filteredProjects.value.forEach((project, index) => {\n    // 尝试用不同格式匹配日期\n    // 格式1: Bus-YYMMDD / Adhoc-YYMMDD / Hotfix-YYMMDD\n    const regex1 = /(?:Bus|Adhoc|Hotfix)-(\\d{6})/i;\n    // 格式2: Bus-YYYYMMDD / Adhoc-YYYYMMDD / Hotfix-YYYYMMDD\n    const regex2 = /(?:Bus|Adhoc|Hotfix)-(\\d{8})/i;\n    \n    let match = project.title.match(regex1);\n    let dateStr = null;\n    let projectDate = null;\n    \n    // 尝试匹配YYMMDD格式\n    if (match && match[1]) {\n      dateStr = match[1];\n      const year = parseInt('20' + dateStr.substring(0, 2));\n      const month = parseInt(dateStr.substring(2, 4)) - 1; // 月份从0开始\n      const day = parseInt(dateStr.substring(4, 6));\n      \n      projectDate = new Date(year, month, day);\n    } else {\n      // 尝试匹配YYYYMMDD格式\n      match = project.title.match(regex2);\n      if (match && match[1]) {\n        dateStr = match[1];\n        const year = parseInt(dateStr.substring(0, 4));\n        const month = parseInt(dateStr.substring(4, 6)) - 1; // 月份从0开始\n        const day = parseInt(dateStr.substring(6, 8));\n        \n        projectDate = new Date(year, month, day);\n      }\n    }\n    \n    if (projectDate && !isNaN(projectDate.getTime())) {\n      console.log(`项目: ${project.title}, 日期: ${projectDate.toISOString().split('T')[0]}`);\n      \n      // 计算与今天的差值（天数）\n      const timeDiff = today.getTime() - projectDate.getTime();\n      const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));\n      \n      // 优先选择过去最近的发布单或今天的发布单\n      if ((dayDiff >= 0 && dayDiff < minDiff) || \n          (dayDiff === 0 && closestIndex === -1)) {\n        minDiff = dayDiff;\n        closestIndex = index;\n        matchedProject = project;\n      }\n    }\n  });\n  \n  if (closestIndex !== -1 && matchedProject) {\n    console.log(`自动选择最近的历史发布单: ${matchedProject.title}`);\n    selectedProjectFinished.value = matchedProject.title;\n    \n    // 加载选中项目的数据\n    getData(selectedProjectFinished.value);\n  } else {\n    console.log('没有找到合适的历史发布单');\n    // 清空选择和表格数据\n    selectedProjectFinished.value = '';\n    releaseTableData.length = 0;\n  }\n};\n\n</script>\n\n<style scoped>\na {\n  text-decoration: none;\n}\n\n.index-container {\n  height: 100%;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n}\n\ndiv {\n  font-size: 12px;\n  margin: 5px;\n  border: 1px;\n  padding: 0;\n}\n\n</style>\n\n<style>\n.el-table .warning-row {\n  --el-table-tr-bg-color: var(--el-color-warning-light-9);\n}\n\n.el-table .success-row {\n  --el-table-tr-bg-color: var(--el-color-success-light-9);\n}\n\n.el-table .cell {\n  white-space: pre-wrap !important;\n}\n\n.table-header {\n  background-color: blue;\n  color: white;\n}\n</style>\n<style scoped>\n.n-gradient-text {\n  font-size: 24px;\n}\n</style>\n\n<style scoped>\n.scrollbar-fe-item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 30px;\n  margin: 10px; /* 删除默认外边距 */\n\n  text-align: center;\n  border-radius: 4px;\n  background: var(--el-color-success-light-9);\n  color: var(--el-color-success);\n}\n\n.scrollbar-be-item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 30px;\n  margin: 10px; /* 删除默认外边距 */\n  text-align: center;\n  border-radius: 4px;\n  background: var(--el-color-primary-light-9);\n  color: var(--el-color-primary);\n}\n\n.ml-2 {\n\n  margin: 10px; /* 添加10像素的间距 */\n\n}\n</style>\n\n\n<style scoped>\n.data-list {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.data-item {\n  width: calc(33.33% - 5px); /* 将元素宽度从原来的 calc(33.33% - 10px) 调整为 calc(33.33% - 5px) */\n  margin-bottom: 20px;\n  border-radius: 5px;\n  padding: 10px;\n  background-color: #f5f5f5;\n}\n\n.data-item__title {\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.data-item__content {\n  color: #666;\n}\n</style>\n\n<style scoped>\n.itxst {\n  width: 600px;\n  display: flex;\n}\n\n.itxst > div:nth-of-type(1) {\n  flex: 1;\n}\n\n.itxst > div:nth-of-type(2) {\n  width: 270px;\n  padding-left: 20px;\n}\n\n.item {\n  border: solid 1px #eee;\n  padding: 6px 10px;\n  text-align: left;\n}\n\n.item:hover {\n  cursor: move;\n}\n\n.item + .item {\n  margin-top: 10px;\n}\n\n.ghost {\n  border: solid 1px rgb(19, 41, 239);\n}\n\n.chosenClass {\n  background-color: #f1f1f1;\n}\n</style>\n\n<style>\n.el-popper.is-customized {\n  /* Set padding to ensure the height is 32px */\n  padding: 6px 12px;\n  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));\n}\n\n.el-popper.is-customized .el-popper__arrow::before {\n  background: linear-gradient(45deg, #b2e68d, #bce689);\n  right: 0;\n}\n\n.el-card + .el-card {\n  margin-top: 20px;\n}\n\n\n</style>\n<style>\n.ar-container {\n  display: flex;\n  align-items: center;\n\n}\n\n</style>\n\n<style>\n.Epic-icon {\n  background-image: url('@/icons/svg/epic.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.ST-icon {\n  background-image: url('@/icons/svg/sub-task.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.Bug-icon {\n  background-image: url('@/icons/svg/bug.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.Story-icon {\n  background-image: url('@/icons/svg/story.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.Task-icon {\n  background-image: url('@/icons/svg/task.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n</style>\n\n<!--<style>-->\n<!--//.el-table th {-->\n<!--//  background-color: #cacfd7 !important; -->\n<!--//  color: #333 !important; -->\n<!--//} -->\n\n<!--</style>-->\n<style>\n.to-do-text {\n  background-color: #42526e;\n  border-color: #42526e;\n  color: #fff;\n}\n\n.done-text {\n  background-color: #00875a;\n  border-color: #00875a;\n  color: #fff;\n}\n\n.doing-text {\n  background-color: #0052cc;\n  border-color: #0052cc;\n  color: #fff;\n}\n\n.delivering-text {\n  background-color: #0052cc;\n  border-color: #0052cc;\n  color: #fff;\n}\n\n.developing-text {\n  background-color: #0052cc;\n  border-color: #0052cc;\n  color: #fff;\n}\n\n.waiting-text {\n  background-color: #42526e;\n  border-color: #42526e;\n  color: #fff;\n}\n\n.bold-text {\n  font-weight: bold;\n}\n</style>\n\n<style scoped>\n/* ... existing code ... */\n\n.el-radio-group {\n  margin-bottom: 10px;\n}\n\n.release-type-filter {\n  height: 32px;\n}\n\n.release-type-filter :deep(.el-radio-button__inner) {\n  height: 32px;\n  line-height: 32px;\n  padding: 0 15px;\n}\n\n@media (min-width: 768px) {\n  .ar-container {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    gap: 10px;\n  }\n  \n  .el-radio-group {\n    margin-bottom: 0;\n  }\n}\n</style>\n\n"], "names": ["get_jira_release_list_finished", "axios", "get", "params", "project_type", "release_type", "then", "response", "catch", "error", "console", "log", "defaults", "timeout", "interceptors", "request", "use", "config", "Promise", "ref", "selectedProjectFinished", "centerDialogVisible", "fe_services", "reactive", "be_services", "all_services", "pingGroupA", "pingGroupB", "featureGroup", "feGroup", "checkServiceGrouping", "service", "includes", "onMounted", "async", "releaseList", "data", "releaseData", "value", "Array", "isArray", "currentDate", "Date", "setHours", "closestDate", "minDayDiff", "Infinity", "for<PERSON>ach", "projectTitle", "match", "dateString", "year", "month", "day", "length", "parseInt", "substring", "date", "toISOString", "split", "timeDiff", "getTime", "dayDiff", "Math", "floor", "selectedRelease", "projects", "splice", "item", "push", "title", "filterProjects", "getData", "document", "body", "innerHTML", "trim", "emptyElement", "querySelector", "style", "display", "IN_pingGroupA", "computed", "result", "pingGroupAKeys", "map", "obj", "Object", "keys", "flat", "link", "find", "name", "values", "IN_pingGroupB", "pingGroupBKeys", "IN_featureGroup", "featureGroupKeys", "IN_feGroup", "beGroupKeys", "services_pane", "visible", "show", "showfe", "showbe", "showresult", "active", "projectTypeFilter", "releaseTypeFilter", "localStorage", "getItem", "filteredProjects", "savedFilter", "selectClosestRelease", "ElMessage", "handleReleaseTypeChange", "setItem", "releaseTableData", "filter", "project", "matchesType", "toLowerCase", "matchesProject", "selectedProject", "fullscreenLoading", "filterType", "row", "type", "releaseTableDataIds", "Set", "jira_key", "temp_data", "fin_data", "send_title", "has", "add", "getreleaseData", "data_final", "autocheckdata", "itemA", "matchedItemB", "itemB", "feature_key", "jira_link", "jira_title", "feature_title", "bug_resolved", "Number", "bug_total", "signoff_status", "sign_off", "config_center", "shopee_region", "region", "redis_check", "redis_change", "merge_list", "status", "Code_Merged", "DB_Change", "dev_pic", "PM", "qa_pic", "services", "services_list", "services_list_be", "services_list_fe", "getColorName", "getBigName", "form", "merge", "removeReleasePrefix", "refParam", "modifiedString", "replace", "watch", "newValue", "oldValue", "JSON", "stringify", "watchEffect", "savedOption", "saveactive", "selectedData1", "selectedData2", "selectedData3", "selectedData4", "index", "id", "today", "closestIndex", "minDiff", "matchedProject", "regex2", "dateStr", "projectDate", "isNaN", "copymsg", "navigator", "clipboard", "writeText", "message", "mr_seatalk_single_feature_msg", "props", "start_single_ar"], "mappings": "woBAWA,SAASA,KACP,OAAOC,EAAMC,IAAI,2EAA4E,CAC3FC,OAAQ,CACNC,aAAc,YACdC,aAAc,sBAGfC,MAAK,SAAUC,GAGd,OAAOA,CACb,IACKC,OAAM,SAAUC,GACfC,QAAQC,IAAIF,EAClB,GACA,CAxBAR,EAAMW,SAASC,QAAU,IAEzBZ,EAAMa,aAAaC,QAAQC,KAAIC,GAEtBA,IACNR,GACMS,QAAQT,MAAMA,2cC2evBU,GAAA,GAIA,MAAAC,EAAAD,IAEAA,GAAA,GAEAA,GAAA,GACA,MAAAE,EAAAF,GAAA,GACAA,EAAA,SAIA,MAAAG,EAAAC,EAAA,IACAC,EAAAD,EAAA,IACAE,GAAAF,EAAA,IAGAG,GAAAH,EAAA,CAA4B,wBAC1B,uBAAyB,gCAAwB,oCACjD,8BAAqC,8BAA+B,2BACpE,0BAA4B,wBAA2B,yBACvD,oBAA0B,+BAAqB,qCAC/C,kCAAsC,gCAAmC,qCACzE,qBAAsC,2BAAsB,0BAC5D,sCAA2B,0CAAuC,oCAClE,oCAAqC,iCAAqC,8BAC1E,+BAA+B,0BAAgC,qCAC/D,2BAAsC,wCAA4B,uCAClE,mCAAwC,8BAAoC,6BAC5E,sCAGFI,GAAAJ,EAAA,CAA4B,8BAC1B,0BAA+B,8BAA2B,mCAC1D,sCAAoC,iCAAuC,yBAC3E,0BAA0B,+BAA2B,+BACrD,gCAAgC,+BAAiC,mCACjE,iCAAoC,4CAAkC,uCACtE,8BAAwC,+BAA+B,6BACvE,mCAA8B,4CAAoC,+BAClE,6BAAgC,2BAA8B,iCAC9D,0CAAkC,yCAA2C,mCAC7E,iCAAmC,+BAAiC,sCAGtEK,GAAAL,EAAA,CAA8B,qBAC5B,8BAAsB,kCAA+B,gCACrD,iCAAiC,wCAAkC,2BACnE,yBAA4B,kCAA0B,8BACtD,mCAGFM,GAAAN,EAAA,CAAyB,wCACvB,gCAAyC,iCAAiC,6BAC1E,mCAA8B,iCAAoC,+BAClE,4BAAgC,iCAA6B,2BAC7D,6CAA4B,uCAA8C,mCAC1E,6CAAoC,yCAA8C,kCAClF,+CAAmC,yCAAgD,yCACnF,8BAA0C,gDAA+B,0CACzE,qCAA2C,uBAAsC,mCACjF,8BAAoC,mBAA+B,uCACnE,wCAAuC,qDAAwC,0BAC/E,+CAA2B,qCAK7B,SAAAO,GAAAC,GACE,OAAAT,EAAAU,SAAAD,GAEEF,GAAAG,SAAAD,KAA+BP,EAAAQ,SAAAD,KAG/BL,GAAAM,SAAAD,IAAAJ,GAAAK,SAAAD,IAAAH,GAAAI,SAAAD,GAEK,CAITZ,EAAA,IAEAc,GAAAC,UACE,IAEE,MAAAC,QAAAnC,KAIA,GAHAU,QAAAC,IAAA,WAAAwB,IAGAA,IAAAA,EAAAC,OAAAD,EAAAC,KAAAA,KAEE,YADA1B,QAAAD,MAAA,gBAAA0B,GAIF,MAAAE,EAAAF,EAAAC,KAAAA,KAIA,GAHA1B,QAAAC,IAAA,aAAA0B,GAGAjB,EAAAkB,MAAA,CACEC,MAAAC,QAAAH,IAAAA,EAAAL,SAAAZ,EAAAkB,SAGE5B,QAAAC,IAAA,wBACAS,EAAAkB,MAAA,GACF,CAIF,IAAAlB,EAAAkB,MAAA,CACE,MAAAG,EAAA,IAAAC,KACAD,EAAAE,SAAA,EAAA,EAAA,EAAA,GACA,IAAAC,EAAA,KACAC,EAAAC,SAEAP,MAAAC,QAAAH,IACEA,EAAAU,SAAAC,IACEtC,QAAAC,IAAA,QAAAqC,GAGA,MAAAC,EAAAD,EAAAC,MAAA,iCACA,GAAAA,EAAA,CACE,MAAAC,EAAAD,EAAA,GACA,IAAAE,EAAAC,EAAAC,EAGA,IAAAH,EAAAI,QACEH,EAAAI,SAAA,KAAAL,EAAAM,UAAA,EAAA,IACAJ,EAAAG,SAAAL,EAAAM,UAAA,EAAA,IAAA,EACAH,EAAAE,SAAAL,EAAAM,UAAA,EAAA,KAAyC,IAAAN,EAAAI,SAEzCH,EAAAI,SAAAL,EAAAM,UAAA,EAAA,IACAJ,EAAAG,SAAAL,EAAAM,UAAA,EAAA,IAAA,EACAH,EAAAE,SAAAL,EAAAM,UAAA,EAAA,KAGF,MAAAC,EAAA,IAAAf,KAAAS,EAAAC,EAAAC,GACA3C,QAAAC,IAAA,SAAAqC,QAAAS,EAAAC,cAAAC,MAAA,KAAA,MAGA,MAAAC,EAAAnB,EAAAoB,UAAAJ,EAAAI,UACAC,EAAAC,KAAAC,MAAAJ,EAAA,OAGAE,GAAA,GAAAA,EAAAjB,IACEA,EAAAiB,EACAlB,EAAAa,EACArC,EAAAkB,MAAAU,EACAtC,QAAAC,IAAA,aAAAS,EAAAkB,MAAA,OAAAwB,GACF,IAGN,CA2BF,GAvBAG,GAAA3B,MAAAD,EAGA6B,GAAAC,OAAA,EAAAD,GAAAZ,QAEAf,MAAAC,QAAAH,IACEA,EAAAU,SAAAqB,IAEEF,GAAAG,KAAA,CAAAC,MAAAF,GAAA,IAIJ1D,QAAAC,IAAA,YAAAuD,IAGAK,KAGAnD,EAAAkB,OACEkC,GAAApD,EAAAkB,OAIFmC,SAAAC,KAAAC,UAAAC,OAAAtB,OAAA,EAAA,CACE,MAAAuB,EAAAJ,SAAAK,cAAA,aACAD,IACEA,EAAAE,MAAAC,QAAA,OACF,CACF,CAAA,MAAAvE,GAEAC,QAAAD,MAAA,aAAAA,EAAiC,KAMrC,MAAAwE,GAAAC,GAAA,KACE,MAAAC,EAAA,CAAA,EACAC,EAAA1D,GAAA2D,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAArB,KAAA5C,EACE,GAAA4D,EAAApD,SAAAoC,GAAA,CACE,MAAAsB,EAAAhE,GAAAiE,MAAAL,GAAAC,OAAAC,KAAAF,GAAAtD,SAAAoC,KAAAA,GACAe,EAAAf,GAAA,CAAewB,KAAAxB,EACPsB,OAER,CAGJ,OAAAH,OAAAM,OAAAV,EAAA,IAEFW,GAAAZ,GAAA,KACE,MAAAC,EAAA,CAAA,EACAY,EAAApE,GAAA0D,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAArB,KAAA5C,EACE,GAAAuE,EAAA/D,SAAAoC,GAAA,CACE,MAAAsB,EAAA/D,GAAAgE,MAAAL,GAAAC,OAAAC,KAAAF,GAAAtD,SAAAoC,KAAAA,GACAe,EAAAf,GAAA,CAAewB,KAAAxB,EACPsB,OAER,CAGJ,OAAAH,OAAAM,OAAAV,EAAA,IAGFa,GAAAd,GAAA,KACE,MAAAC,EAAA,CAAA,EACAc,EAAArE,GAAAyD,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAArB,KAAA5C,EACE,GAAAyE,EAAAjE,SAAAoC,GAAA,CACE,MAAAsB,EAAA9D,GAAA+D,MAAAL,GAAAC,OAAAC,KAAAF,GAAAtD,SAAAoC,KAAAA,GACAe,EAAAf,GAAA,CAAewB,KAAAxB,EACPsB,OAER,CAGJ,OAAAH,OAAAM,OAAAV,EAAA,IAGFe,GAAAhB,GAAA,KACE,MAAAC,EAAA,CAAA,EACAgB,EAAAtE,GAAAwD,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAArB,KAAA9C,EACE,GAAA6E,EAAAnE,SAAAoC,GAAA,CACE,MAAAsB,EAAA7D,GAAA8D,MAAAL,GAAAC,OAAAC,KAAAF,GAAAtD,SAAAoC,KAAAA,GACAe,EAAAf,GAAA,CAAewB,KAAAxB,EACPsB,OAER,CAGJ,OAAAH,OAAAM,OAAAV,EAAA,IAGF,IAAAiB,GAAAjF,GAAA,GACAkF,GAAAlF,GAAA,GACAmF,GAAAnF,GAAA,GACAoF,GAAApF,GAAA,GACAqF,GAAArF,GAAA,GACAsF,GAAAtF,GAAA,GACAuF,GAAAvF,EAAA,GACAA,EAAA,QACAA,EAAA,QAGA,MAAAwF,GAAAxF,EAAA,QACAyF,GAAAzF,EAAA0F,aAAAC,QAAA,sBAAA,OACAC,GAAA5F,EAAA,IAGAc,GAAAC,UACE,MAAA8E,EAAAH,aAAAC,QAAA,qBACAE,IACEJ,GAAAtE,MAAA0E,GAGF,IAEE,MACA3E,SADArC,MACAoC,KAyBA,GAxBA1B,QAAAC,IAAA,aAAA0B,GAGA6B,GAAAC,OAAA,EAAAD,GAAAZ,QACAf,MAAAC,QAAAH,IACEA,EAAAU,SAAAqB,IACEF,GAAAG,KAAAD,EAAA,IAKJG,KAGAwC,GAAAzE,MAAAgB,OAAA,IAAAlC,EAAAkB,OACE2E,KAIF7F,EAAAkB,OACEkC,GAAApD,EAAAkB,OAIFmC,SAAAC,KAAAC,UAAAC,OAAAtB,OAAA,EAAA,CACE,MAAAuB,EAAAJ,SAAAK,cAAA,aACAD,IACEA,EAAAE,MAAAC,QAAA,OACF,CACF,CAAA,MAAAvE,GAEAC,QAAAD,MAAA,aAAAA,GACAyG,EAAAzG,MAAA,YAA2B,KAK/B,MAAA0G,GAAA,KAEEN,aAAAO,QAAA,oBAAAR,GAAAtE,OACAiC,KAGAwC,GAAAzE,MAAAgB,OAAA,EACE2D,MAGA7F,EAAAkB,MAAA,GACA+E,GAAAlD,OAAA,EAAAkD,GAAA/D,QAAkD,EAKtDiB,GAAA,KACE7D,QAAAC,IAAA,iBAAAiG,GAAAtE,MAAA,QAAAqE,GAAArE,OAEA4B,GAAAZ,SAEAyD,GAAAzE,MAAA4B,GAAAoD,QAAAC,IAEE,MAAAC,EAAA,QAAAZ,GAAAtE,OAAAiF,EAAAlH,cAAAoH,gBAAAb,GAAAtE,MAAAmF,cAIAC,EAAAH,EAAAnH,eAAAuG,GAAArE,MAEA,OAAAkF,GAAAE,CAAA,IAGFhH,QAAAC,IAAA,WAAAoG,GAAAzE,MAAAgB,QAGA,IAAAyD,GAAAzE,MAAAgB,SACElC,EAAAkB,MAAA,GAEA+E,GAAAlD,OAAA,EAAAkD,GAAA/D,SAAkD,EAUtDqE,GAAAxG,IAEAyG,GAAAzG,GAAA,GAUA0G,GAAA,CAAAvF,EAAAwF,IACEA,EAAAC,OAAAzF,EAGF2B,GAAA9C,IACA+C,GAAA3C,EAAA,IAEA,IAAA8F,GAAA9F,EAAA,IA2QAW,eAAAsC,GAAAlC,GACE,IAAAA,EAUE,OATA5B,QAAAC,IAAA,mBAEA0G,GAAAlD,OAAA,EAAAkD,GAAA/D,QACAkD,GAAAlE,OAAA,EACAiE,GAAAjE,OAAA,EACAmE,GAAAnE,OAAA,EACAd,EAAA2C,OAAA,EAAA3C,EAAA8B,QACAhC,EAAA6C,OAAA,EAAA7C,EAAAgC,aACA7B,GAAA0C,OAAA,EAAA1C,GAAA6B,QAIFkD,GAAAlE,OAAA,EACAiE,GAAAjE,OAAA,EACA5B,QAAAC,IAAA,YAAA2B,WApMFJ,eAAAI,GAEE5B,QAAAC,IAAA0G,IACA,MAAAW,EAAA,IAAAC,IAAAZ,GAAAhC,KAAAjB,GAAAA,EAAA8D,YAKAzG,GAAA0C,OAAA,EAAA1C,GAAA6B,QACA9B,EAAA2C,OAAA,EAAA3C,EAAA8B,QACAhC,EAAA6C,OAAA,EAAA7C,EAAAgC,QACA5C,QAAAC,IAAA,gBAAA2B,KACA,IAIA6F,EAJAC,EAAA,CAAe9D,MAAAhC,GAKf6F,QAAAE,EAAAD,GACA1H,QAAAC,IAAAwH,GACAzH,QAAAC,UAAA0H,EAAAD,IACA1H,QAAAC,IAAAwH,GACA,IAAAA,EAAA7E,OACE+D,GAAAlD,OAAA,EAAAkD,GAAA/D,SAEA+D,GAAAlD,OAAA,EAAAkD,GAAA/D,QACA6E,EAAA/F,KAAAW,SAAAqB,IACE1D,QAAAC,IAAAyD,GACA4D,EAAAM,IAAAlE,EAAA8D,YACEb,GAAAhD,KAAAD,GACA4D,EAAAO,IAAAnE,EAAA8D,UAAqC,KAQ3CxH,QAAAC,IAAA0G,GAA4B,CAgK5BmB,CAAAlG,GACAsF,GAAAtF,OAAA,EAEA,IAAAmG,EAAAtH,IAOA,GANAT,QAAAC,IAAA,qBAAA0G,IACAoB,EAAAnG,YAAAoG,EAAArB,IAEAO,GAAAtF,OAAA,EACA5B,QAAAC,IAAA,SAAA8H,EAAAnG,QAEAmG,EAAAnG,QAAAmG,EAAAnG,MAAAF,MAAA,IAAAqG,EAAAnG,MAAAF,KAAAkB,OAKE,OAJA5C,QAAAC,IAAA,mBAEA0G,GAAAlD,OAAA,EAAAkD,GAAA/D,aACAmD,GAAAnE,OAAA,GAIF+E,GAAA/D,OACA+D,GAAAtE,SAAA4F,IACE,MAAAC,EAAAH,EAAAnG,MAAAF,KAAAuD,MAAAkD,GAAAF,EAAAT,WAAAW,EAAAC,cACAF,IACElI,QAAAC,IAAA,SAAAiI,GAEAD,EAAAZ,KAAAa,EAAAb,KACAY,EAAAT,SAAAU,EAAAE,YACAH,EAAAI,UAAA,iCAAAH,EAAAE,cACAH,EAAAK,WAAAJ,EAAAK,cAGAN,EAAAO,aAAAC,OAAAP,EAAAM,cAAA,GACAP,EAAAS,UAAAD,OAAAP,EAAAQ,WAAA,GACA1I,QAAAC,IAAA,UAAAgI,EAAAO,gBAAAP,EAAAS,aAEAR,EAAAS,eAGEV,EAAAW,SAAAV,EAAAS,eAFAV,EAAAW,SAAA,GAIFV,EAAAW,cAGEZ,EAAAY,cAAAX,EAAAW,cAFAZ,EAAAY,cAAA,GAIFX,EAAAY,cAGEb,EAAAc,OAAAb,EAAAY,cAFAb,EAAAc,OAAA,GAIFb,EAAAc,YAGEf,EAAAgB,aAAAf,EAAAc,YAFAf,EAAAgB,aAAA,GAIFf,EAAAzD,OAGEwD,EAAAxD,OAAAyD,EAAAzD,OAFAwD,EAAAxD,OAAA,GAKFyD,EAAAgB,WAGEjB,EAAAiB,WAAAhB,EAAAgB,WAFAjB,EAAAiB,WAAA,GAIFhB,EAAAiB,OAGElB,EAAAkB,OAAAjB,EAAAiB,OAFAlB,EAAAkB,OAAA,GAIFjB,EAAAkB,YAGEnB,EAAAmB,YAAAlB,EAAAkB,YAFAnB,EAAAmB,YAAA,GAIFlB,EAAAmB,UAGEpB,EAAAoB,UAAAnB,EAAAmB,UAFApB,EAAAoB,UAAA,GAIFnB,EAAAoB,QAGErB,EAAAqB,QAAApB,EAAAoB,QAFArB,EAAAqB,QAAA,GAIFpB,EAAAqB,GAGEtB,EAAAsB,GAAArB,EAAAqB,GAFAtB,EAAAsB,GAAA,GAIFrB,EAAAsB,OAGEvB,EAAAuB,OAAAtB,EAAAsB,OAFAvB,EAAAuB,OAAA,GAIFvB,EAAAwB,SAAA,GACAvB,EAAAwB,cAAAC,iBAAAtH,SAAAhB,IACE,KAAA4G,EAAAwB,SACExB,EAAAwB,UAAA,GAAApI,IAEA4G,EAAAwB,UAAA,KAAkBpI,IAEpBP,EAAAQ,SAAAD,KACEP,EAAA6C,KAAAtC,GACAN,GAAA4C,KAAAtC,GAAyB,IAI7B6G,EAAAwB,cAAAE,iBAAAvH,SAAAhB,IACE,KAAA4G,EAAAwB,SACExB,EAAAwB,UAAA,GAAApI,IAEA4G,EAAAwB,UAAA,KAAkBpI,IAEpBT,EAAAU,SAAAD,KACET,EAAA+C,KAAAtC,GACAN,GAAA4C,KAAAtC,GAAyB,IAE5B,IAIL,IAAAT,EAAAgC,SACEiD,GAAAjE,OAAA,GAEF,IAAAd,EAAA8B,SACEkD,GAAAlE,OAAA,GAEF5B,QAAAC,IAAA,eAAAW,GACAZ,QAAAC,IAAA,eAAAa,GACAd,QAAAC,IAAA,UAAA0G,IACAZ,GAAAnE,OAAA,CAAmB,CAiBrB,SAAAiI,GAAAV,GACE,MAAA,UAAAA,EACE,UAAO,SAAAA,EAEP,UAAO,YAAAA,EAEP,UAEA,SAeF,CAIF,SAAAW,GAAAX,GACE,MAAA,UAAAA,EACE,QAAO,SAAAA,EAEP,OAAO,YAAAA,EAEP,UAAO,WAAAA,EAEP,SAAO,UAAAA,EAEP,QAAO,QAAAA,EAEP,MAAO,eAAAA,EAEP,aAAO,eAAAA,EAEP,aAAO,YAAAA,EAEP,UAEAA,CACF,CA2BF,MAAAY,GAAAlJ,EAAA,CAAsBqE,KAAA,GACd8E,OAAA,IAiDR,SAAAC,GAAAC,GACE,GAAAA,EAAA,CACE,IAAAC,EAAAD,EAAAE,QAAA,iBAAA,IAAAA,QAAA,MAAA,IAEA,OADApK,QAAAC,IAAAkK,GACAA,CAAO,CAET,MAAA,EAAO,CAnDTE,EAAApD,IAAA,CAAAqD,EAAAC,KACE,KAAAD,GAAAA,IAAAC,IACE5D,GAAAlD,OAAA,EAAAkD,GAAA/D,QACAkB,GAAAwG,IAEFP,GAAA7E,KAAA+E,GAAAK,EAAA,IAIFD,EAAA1D,IAAA,CAAA2D,EAAAC,KACEpE,aAAAO,QAAA,mBAAA8D,KAAAC,UAAA9D,KACA,IAAAA,GAAA/D,SACEgD,GAAAhE,OAAA,GAEF,IAAA+E,GAAA/D,SACEgD,GAAAhE,OAAA,EAAa,IAIjByI,EAAAtJ,IAAA,CAAAuJ,EAAAC,KACE,IAAAxJ,GAAA6B,SACE8C,GAAA9D,OAAA,GAEF,IAAAb,GAAA6B,SACE8C,GAAA9D,OAAA,EAAsB,IAI1B8I,GAAA,KACE,MAAAC,EAAAxE,aAAAC,QAAA,mBACA,IAAAwE,EAAAzE,aAAAC,QAAA,UAEAuE,IACE1D,GAAArF,MAAA+I,GAEFC,IACE5E,GAAApE,MAAAgJ,EAAe,IAKnBF,GAAA,KACEvE,aAAAO,QAAA,kBAAAO,GAAArF,MAAA,IAgBF,MAAAiJ,GAAApK,EAAA,IAEAqK,GAAArK,EAAA,IAEAsK,GAAAtK,EAAA,IAEAuK,GAAAvK,EAAA,IASA+D,GAAA,IACEqG,GAAAjJ,MAAA+C,KAAA,CAAAjB,EAAAuH,KACE,IAAAvH,EAAAwH,GAAAD,EAAA,QAgBJzG,GAAA,IACEsG,GAAAlJ,MAAA+C,KAAA,CAAAjB,EAAAuH,KACE,IAAAvH,EAAAwH,GAAAD,EAAA,QAOJzG,GAAA,IACEuG,GAAAnJ,MAAA+C,KAAA,CAAAjB,EAAAuH,KACE,IAAAvH,EAAAwH,GAAAD,EAAA,QAOJzG,GAAA,IACEwG,GAAApJ,MAAA+C,KAAA,CAAAjB,EAAAuH,KACE,IAAAvH,EAAAwH,GAAAD,EAAA,QAKJpK,EAAA,IAkCAwJ,EAAA3J,GAAA,CAAA4J,EAAAC,KACE,KAAAD,IACE3D,GAAAlD,OAAA,EAAAkD,GAAA/D,QACAkB,GAAAwG,GAAgB,IAKpBI,GAAA,KACE,MAAAC,EAAAxE,aAAAC,QAAA,2BACA,IAAAwE,EAAAzE,aAAAC,QAAA,UAEAuE,IACEjK,EAAAkB,MAAA+I,GAEFC,IACE5E,GAAApE,MAAAgJ,EAAe,IAGnBF,GAAA,KACEvE,aAAAO,QAAA,0BAAAhG,EAAAkB,MAAA,IAIFyI,EAAA,CAAAnE,GAAAD,KAAA,KAEEE,aAAAO,QAAA,oBAAAR,GAAAtE,OAGAiC,KAGAwC,GAAAzE,MAAAgB,OAAA,EACE2D,MAGA7F,EAAAkB,MAAA,GAEA+E,GAAAlD,OAAA,EAAAkD,GAAA/D,QAAkD,IAKtDyH,EAAA3J,GAAA,CAAA4J,EAAAC,KACED,IAAAC,IACE5D,GAAAlD,OAAA,EAAAkD,GAAA/D,QACAkB,GAAAwG,IAEFP,GAAA7E,KAAA+E,GAAAK,EAAA,IAIF,MAAA/D,GAAA,KACE,MAAA4E,EAAA,IAAAnJ,KACA,IAAAoJ,GAAA,EACAC,EAAAjJ,SACAkJ,EAAA,KAEAtL,QAAAC,IAAA,mBAEAoG,GAAAzE,MAAAS,SAAA,CAAAwE,EAAAoE,KAGE,MAEAM,EAAA,gCAEA,IAAAhJ,EAAAsE,EAAAjD,MAAArB,MAJA,iCAKAiJ,EAAA,KACAC,EAAA,KAGA,GAAAlJ,GAAAA,EAAA,GAAA,CACEiJ,EAAAjJ,EAAA,GACA,MAAAE,EAAAI,SAAA,KAAA2I,EAAA1I,UAAA,EAAA,IACAJ,EAAAG,SAAA2I,EAAA1I,UAAA,EAAA,IAAA,EACAH,EAAAE,SAAA2I,EAAA1I,UAAA,EAAA,IAEA2I,EAAA,IAAAzJ,KAAAS,EAAAC,EAAAC,EAAuC,MAIvC,GADAJ,EAAAsE,EAAAjD,MAAArB,MAAAgJ,GACAhJ,GAAAA,EAAA,GAAA,CACEiJ,EAAAjJ,EAAA,GACA,MAAAE,EAAAI,SAAA2I,EAAA1I,UAAA,EAAA,IACAJ,EAAAG,SAAA2I,EAAA1I,UAAA,EAAA,IAAA,EACAH,EAAAE,SAAA2I,EAAA1I,UAAA,EAAA,IAEA2I,EAAA,IAAAzJ,KAAAS,EAAAC,EAAAC,EAAuC,CAI3C,GAAA8I,IAAAC,MAAAD,EAAAtI,WAAA,CACEnD,QAAAC,IAAA,OAAA4G,EAAAjD,cAAA6H,EAAAzI,cAAAC,MAAA,KAAA,MAGA,MAAAC,EAAAiI,EAAAhI,UAAAsI,EAAAtI,UACAC,EAAAC,KAAAC,MAAAJ,EAAA,QAGAE,GAAA,GAAAA,EAAAiI,GAAA,IAAAjI,IAAA,IAAAgI,KAEEC,EAAAjI,EACAgI,EAAAH,EACAK,EAAAzE,EACF,MAIJ,IAAAuE,GAAAE,GACEtL,QAAAC,IAAA,iBAAAqL,EAAA1H,SACAlD,EAAAkB,MAAA0J,EAAA1H,MAGAE,GAAApD,EAAAkB,SAEA5B,QAAAC,IAAA,gBAEAS,EAAAkB,MAAA,GACA+E,GAAA/D,OAAA,EAA0B,4+DA3W9ByE,SACE,SAAAA,EACE,YAAO,aAAAA,EAEP,UAAO,SAAAA,EAEP,YAAO,QAAAA,EAEP,WAAO,UAAAA,EAEP,kBAFO,wBARX,IAAAA,27DA1WA7F,eAAA4F,GACE,IAAAW,EAAAtH,IACewG,GAAArF,MAGfmG,QAAA4D,EAAAvE,EAAAA,KAEApH,QAAAC,IAAA8H,GACA6D,UAAAC,UAAAC,UAAA/D,EAAArG,MAAA9B,MAAA,KAEM4G,EAAA,CAAUuF,QAAA,kBACC1E,KAAA,WACH,IACPvH,OAAAC,IAIDyG,EAAAzG,MAAA,WAAA,GACD,wVAlCPyB,eAAA4F,GACE,IAAAW,EAAAtH,IACAsH,QAAAiE,EAAA5E,EAAAA,KACApH,QAAAC,IAAA8H,GACA6D,UAAAC,UAAAC,UAAA/D,EAAArG,MAAA9B,MAAA,KAEM4G,EAAA,CAAUuF,QAAA,sBACC1E,KAAA,WACH,IACPvH,OAAAC,IAGDyG,EAAAzG,MAAA,YAAA,GACD,2bAwBPyB,eAAAyK,GAEEjM,QAAAC,IAAAgM,EAAA7E,KAGA3G,UACAyL,EAAAD,EAAA7E,IAA4C,inHAiZ9C+B,WACE,UAAAA,EACE,OAAO,SAAAA,EAEP,UAAO,YAAAA,EAEP,OAAO,WAAAA,EAEP,SAAO,UAAAA,EAEP,QAAO,QAAAA,EAEP,MAAO,eAAAA,EAEP,aAAO,eAAAA,EAEP,aAAO,YAAAA,EAEP,eAFO,6FAhBX,IAAAA"}
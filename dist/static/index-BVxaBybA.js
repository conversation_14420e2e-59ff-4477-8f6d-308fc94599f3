import{H as e,K as a,R as l,S as s,p as o,J as r,T as d,M as n,V as i,o as t,C as u,w as c,v as m,W as g,l as p,x as f,t as h,j as v,X as w,Y as _,E as V,Z as b,a as y,h as F,$ as k,n as x,a0 as L,a1 as P,a2 as q,P as U,Q as j}from"./index-awKTxnvj.js";/* empty css                 */import{E as C}from"./index-TjDDNAcU.js";const K=""+new URL("login-8SbqLf3B.png",import.meta.url).href,R={class:"login-check"};const z=e({setup(){const{t:e}=a(),i=l(),t=s(),u=o(null),c=r({ruleForm:{username:"admin",password:"admin"},loading:!1,checkedPwd:!1,redirect:void 0,rules:{username:[{required:!0,message:e("login.rules.username"),trigger:"blur"}],password:[{required:!0,message:e("login.rules.password"),trigger:"blur"}]}});d((()=>t.currentRoute),(({_value:e})=>{const a=e;c.redirect=a.query&&a.query.redirect||"/"}),{immediate:!0});return{...n(c),validateForm:u,handleLogin:async()=>{const e=v(u);e&&await e.validate((e=>{e&&(c.valid=!0,c.loading=!0,i.dispatch("user/login",c.ruleForm).then((()=>{const e="/404"===c.redirect||"/401"===c.redirect?"/":c.redirect;t.push(e).catch((()=>{})),c.loading=!1})).catch((()=>{c.loading=!1})))}))},t:e}}},[["render",function(e,a,l,s,o,r){const d=i("icon-user"),n=w,v=_,y=i("icon-lock"),F=C,k=V,x=b;return t(),u(x,{model:e.ruleForm,rules:e.rules,ref:"validateForm",class:"login-ruleForm"},{default:c((()=>[m(v,{prop:"username"},{default:c((()=>[m(n,{placeholder:s.t("login.username"),modelValue:e.ruleForm.username,"onUpdate:modelValue":a[0]||(a[0]=a=>e.ruleForm.username=a)},{prefix:c((()=>[m(d,{theme:"outline",size:"16",fill:"#999"})])),_:1},8,["placeholder","modelValue"])])),_:1}),m(v,{prop:"password"},{default:c((()=>[m(n,{onKeyup:g(s.handleLogin,["enter"]),placeholder:s.t("login.password"),type:"password",modelValue:e.ruleForm.password,"onUpdate:modelValue":a[1]||(a[1]=a=>e.ruleForm.password=a)},{prefix:c((()=>[m(y,{theme:"outline",size:"16",fill:"#999"})])),_:1},8,["onKeyup","placeholder","modelValue"])])),_:1}),m(v,null,{default:c((()=>[p("div",R,[m(F,{modelValue:e.checkedPwd,"onUpdate:modelValue":a[2]||(a[2]=a=>e.checkedPwd=a)},{default:c((()=>[f(h(s.t("login.rememberPwd")),1)])),_:1},8,["modelValue"]),m(k,{text:"",type:"primary"},{default:c((()=>[f(h(s.t("login.forgotPwd")),1)])),_:1})])])),_:1}),m(v,null,{default:c((()=>[m(k,{type:"primary",loading:e.loading,class:"login-btn",round:"",onClick:s.handleLogin},{default:c((()=>[f(h(s.t("login.loginBtn")),1)])),_:1},8,["loading","onClick"])])),_:1})])),_:1},8,["model","rules"])}],["__scopeId","data-v-afe40f1e"]]),B={class:"login-wrapper"},E={class:"login-container"},I={class:"login-left hidden-sm-and-down"},M={class:"login-left-wrap"},S=(e=>(U("data-v-c09e1076"),e=e(),j(),e))((()=>p("img",{class:"img",src:K,alt:"login-bg"},null,-1))),T={class:"desc"},H={class:"tip"},J={class:"form-warp"},Q=e({__name:"index",setup(e){const s=l(),{t:r}=a(),d=o("first"),n=y((()=>s.getters["setting/isMobile"])),u=e=>{console.log(e)};return(e,a)=>{const l=i("Logo"),s=L,o=P,g=q;return t(),F("div",B,[m(s,{class:"header"},{default:c((()=>[m(l,{class:"logo"}),m(k,{class:"lang",color:"#fff"})])),_:1}),p("div",E,[p("div",I,[p("div",M,[S,p("h2",T,h(v(r)("login.desc")),1),p("p",H,h(v(r)("login.tip")),1)])]),p("div",{class:x(["login-form",{"is-mobile":n.value}])},[p("div",J,[m(g,{modelValue:d.value,"onUpdate:modelValue":a[0]||(a[0]=e=>d.value=e),onTabClick:u},{default:c((()=>[m(o,{label:v(r)("login.title"),name:"first"},{default:c((()=>[m(z)])),_:1},8,["label"])])),_:1},8,["modelValue"])])],2)])])}}},[["__scopeId","data-v-c09e1076"]]);export{Q as default};
//# sourceMappingURL=index-BVxaBybA.js.map

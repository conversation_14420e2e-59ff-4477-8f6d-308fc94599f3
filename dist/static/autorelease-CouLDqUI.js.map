{"version": 3, "file": "autorelease-CouLDqUI.js", "sources": ["../../src/views/autorelease/autorelease.vue"], "sourcesContent": ["<template>\n  <div class=\"index-conntainer\">\n    <transition name=\"el-fade-in-linear\">\n      <el-card class=\"card\" shadow=\"hover\" width=\"50px\" height=\"50px\">\n        <div class=\"ar-container\">\n          <ProjectTypeFilter v-model:projectType=\"projectTypeFilter\" />\n          <el-radio-group v-model=\"releaseTypeFilter\" class=\"release-type-filter\" @change=\"handleReleaseTypeChange\">\n            <el-radio-button label=\"bus\">Bus</el-radio-button>\n            <el-radio-button label=\"adhoc\">Adhoc</el-radio-button>\n            <el-radio-button label=\"hotfix\">Hotfix</el-radio-button>\n            <el-radio-button label=\"All\">全部</el-radio-button>\n          </el-radio-group>\n          \n          <el-select v-model=\"selectedProject\"\n                     filterable\n                     clearable\n                     placeholder=\"请选择发布单\"\n                     style=\"width: 380px; margin-left: 10px;\"\n          >\n            <el-option\n                v-for=\"project in filteredProjects\"\n                :key=\"project.title\"\n                :label=\"project.title\"\n                :value=\"project.title\"\n            >\n              <span style=\"\n          float: left;\n          font-size: 13px;\n        \">\n                <el-link :href=\"'https://jira.shopee.io/browse/' + project.key\" target=\"_blank\" :underline=\"false\">\n                            <el-tooltip\n                                class=\"box-item\"\n                                effect=\"customized\"\n                                content=\"点击跳转到JIRA\"\n                                placement=\"left-start\"\n\n                            >\n                <el-tag type=\"danger\">{{ project.key }}</el-tag>\n                            </el-tooltip>\n                </el-link>\n              </span>\n              <el-tooltip\n                  class=\"box-item\"\n                  effect=\"customized\"\n                  content=\"点击拉取数据并且展示发布详情\"\n                  placement=\"right-start\"\n\n              >\n              <span\n                  style=\"\n          float: left;\n        \"\n              >{{ project.title }}</span>\n              </el-tooltip>\n            </el-option>\n          </el-select>\n\n          <el-tooltip\n              class=\"box-item\"\n              effect=\"customized\"\n              content=\"点击进行后台数据更新\"\n              placement=\"top-start\"\n          >\n            <el-button\n\n                size=\"default\"\n                type=\"success\"\n\n                @click=\"refreshData(selectedProject)\"\n                :icon=\"Refresh\"\n                element-loading-text=\"AR正在进行后台数据更新，请耐心等待\"\n                v-loading.fullscreen.lock=\"fullscreenLoading\"\n            >更新\n            </el-button>\n\n          </el-tooltip>\n          <el-tooltip\n            class=\"box-item\"\n            effect=\"customized\"\n            content=\"点击发送Signed off字段提醒到seatalk\"\n            placement=\"top-start\"\n          >\n            <el-button type=\"primary\" size=\"default\" @click=\"signedOffSeatalk\" :icon=\"ChatRound\">\n              Signed off提醒\n            </el-button>\n\n          </el-tooltip>\n          <el-tooltip\n              class=\"box-item\"\n              effect=\"customized\"\n              content=\"点击发送MR提醒到seatalk\"\n              placement=\"top-start\"\n\n          >\n            <el-button type=\"primary\" size=\"default\" @click=\"callMRseatalkFE\" :icon=\"ChatRound\">\n              MR提醒\n            </el-button>\n\n          </el-tooltip>\n          <el-tooltip\n              class=\"box-item\"\n              effect=\"customized\"\n              content=\"点击发送checklist字段提醒到seatalk\"\n              placement=\"top-start\"\n          >\n            <el-button type=\"primary\" size=\"default\" @click=\"callseatalk\" :icon=\"ChatRound\">\n              Checklist提醒\n            </el-button>\n\n          </el-tooltip>\n\n          <el-tooltip\n              class=\"box-item\"\n              effect=\"customized\"\n              content=\"点击跳转monitor看板\"\n              placement=\"top-start\"\n          >\n            <el-button type=\"danger\" size=\"default\" @click=\"openKB\" :icon=\"ChatRound\">\n              monitor看板\n            </el-button>\n\n          </el-tooltip>\n          <el-drawer v-model=\"visible\" :show-close=\"false\">\n            <template #header=\"{ titleId, titleClass }\">\n              <h4 :id=\"titleId\" :class=\"titleClass\">{{ selectedProject }}</h4>\n            </template>\n            暂无失败信息。\n          </el-drawer>\n          <div class=\"ar-container\">\n            <el-icon :size=\"20\">\n              <Bell/>\n            </el-icon>\n            <el-text class=\"mx-1\" type=\"warning\" size=\"default\">后台会不断更新数据，请自行刷新页面</el-text>\n          </div>\n        </div>\n\n        <div class=\"ar-container\">\n          <el-table\n\n              :data=\"releaseTableData\"\n              stripe\n              border\n\n              highlight-current-row\n              fit\n              :header-cell-style=\"{background:'#cacfd7',color:'#606266'}\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column label=\"编号\" min-width=\"21\" header-align=\"center\" align=\"center\">\n              <template #default=\"scope\">\n                {{scope.$index+1}}\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"type\"\n                             label=\"类型\"\n                             header-align=\"center\" align=\"center\"\n                             min-width=\"30\"\n                             :filters=\"[\n                            { text: 'Epic', value: 'Epic' },\n                            { text: 'Bug', value: 'Bug' },\n                            { text: 'Task', value: 'Task' },\n                            { text: 'Sub-task', value: 'Sub-task' },\n                            { text: 'Story', value: 'Story' },\n                            ]\"\n                             :filter-method=\"filterType\"\n                             filter-placement=\"bottom-end\"\n            >\n\n              <template #default=\"{ row }\">\n\n                <el-icon :class=\"getIconName(row.type)\"></el-icon>\n              </template>\n\n            </el-table-column>\n\n            <el-table-column prop=\"jira_key\" label=\"单号\" :min-width=\"60\" header-align=\"center\" align=\"center\"\n            >\n              <template #default=\"{ row }\">\n                <el-link\n                    :underline=\"false\"\n                    v-bind:href=\"row.jira_link\"\n                    target=\"_blank\"\n                    type=\"primary\">\n                  {{ row.jira_key }}\n                </el-link>\n              </template>\n            </el-table-column>\n\n\n            <el-table-column prop=\"jira_title\" label=\"需求名\" :min-width=\"150\"\n            >\n              <template #default=\"{ row }\">\n                <el-link\n                    :underline=\"false\"\n                    v-bind:href=\"row.jira_link\" \n                    target=\"_blank\"\n                    type=\"primary\">\n                  {{ row.jira_title }}\n                </el-link>\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"bug_resolution_rate\" label=\"Bug解决率\" :min-width=\"80\" header-align=\"center\" align=\"center\">\n              <template #default=\"{ row }\">\n                <span :style=\"{ color: Number(row.bug_resolved || 0) === Number(row.bug_total || 0) ? '#67C23A' : '#F56C6C' }\">\n                  <template v-if=\"typeof row.bug_resolved === 'number' && typeof row.bug_total === 'number'\">\n                    {{ row.bug_resolved }}/{{ row.bug_total }}\n                  </template>\n                  <template v-else>\n                    {{ row.bug_resolved || 0 }}/{{ row.bug_total || 0 }}\n                  </template>\n                </span>\n              </template>\n            </el-table-column>\n\n            <el-table-column label=\"周一\" header-align=\"center\" align=\"center\">\n              <el-table-column prop=\"sign_off\" label=\"Signed off\" header-align=\"center\" align=\"center\" min-width=\"40\">\n\n\n                <template #header slot-scope=\"scope\">\n                  Signed<br>off\n                </template>\n                <template #default=\"{ row }\">\n\n                  <el-icon\n                      v-if=\"row.sign_off === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.sign_off === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.sign_off === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.sign_off === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n            </el-table-column>\n            <el-table-column label=\"周二\" header-align=\"center\" align=\"center\">\n              <el-table-column type=\"expand\" label=\"提MR\" min-width=\"32\">\n\n                <template #default=\"props\">\n                  <div>\n                    <div class=\"ar-container\">\n                      <el-tooltip\n                          class=\"box-item\"\n                          effect=\"customized\"\n                          content=\"点击创建MR\"\n                          placement=\"top-start\"\n                      >\n                        <el-button\n                            type=\"danger\"\n                            @click=\"centerDialogVisible = true\"\n                            size=\"small\"\n                            :icon=\"CirclePlus\"\n                            element-loading-text=\"AR正在创建MR, 请耐心等待...\"\n                            v-loading.fullscreen.lock=\"fullscreenLoading\">创建\n                        </el-button>\n                      </el-tooltip>\n\n                      <el-tooltip\n                          class=\"box-item\"\n                          effect=\"customized\"\n                          content=\"点击复制\"\n                          placement=\"top-start\"\n                      >\n                        <el-button\n                            type=\"primary\"\n                            size=\"small\"\n                            @click=\"copyToClipboard(props)\"\n                            :icon=\"ChatRound\"\n                            element-loading-text=\"AR正在处理数据，请耐心等待...\"\n                            v-loading.fullscreen.lock=\"fullscreenLoading\">复制\n                        </el-button>\n                      </el-tooltip>\n                      <el-tooltip\n                          class=\"box-item\"\n                          effect=\"customized\"\n                          content=\"点击发送MR提醒到seatalk\"\n                          placement=\"top-start\"\n                      >\n                        <el-button\n                            type=\"primary\"\n                            size=\"small\"\n                            @click=\"sendSingleFeatureToCT(props)\"\n                            :icon=\"ChatRound\"\n                            element-loading-text=\"AR正在处理数据，请耐心等待...\"\n                            v-loading.fullscreen.lock=\"fullscreenLoading\">发送\n                        </el-button>\n                      </el-tooltip>\n                    </div>\n                    <el-dialog\n                        v-model=\"centerDialogVisible\"\n                        title=\"Warning\"\n                        width=\"30%\"\n                        align-center\n                    >\n                      <span>请确认是否开始自动提MR？发布单： {{ selectedProject }}</span>\n                      <template #footer>\n                        <span class=\"dialog-footer\">\n                          <el-button @click=\"centerDialogVisible = false\">取消</el-button>\n                          <el-button type=\"primary\" @click=\"startSingleAR(props)\">\n                            确认\n                          </el-button>\n                        </span>\n                      </template>\n                    </el-dialog>\n                    <el-table :data=\"props.row.merge_list\" border\n                              :header-cell-style=\"{background:'#def1ce',color:'#606266'}\"\n                    >\n                      <el-table-column label=\"仓库\" prop=\"repo_name\"/>\n                      <el-table-column label=\"分支\" prop=\"branch_name\"/>\n                      <el-table-column label=\"PIC\" prop=\"pic\"/>\n                      <el-table-column label=\"MR地址\" prop=\"web_url\">\n                        <template #default=\"{row}\">\n                          <a\n                              :href=\"row.web_url\"\n                              target=\"_blank\"\n                          >{{ row.web_url }}\n                          </a>\n                        </template>\n                      </el-table-column>\n                      <el-table-column label=\"MR状态\" prop=\"merge_status\"/>\n                      <el-table-column label=\"MR作者\" prop=\"author\"/>\n                    </el-table>\n                  </div>\n                </template>\n\n              </el-table-column>\n\n              <el-table-column prop=\"Code_Merged\" label=\"Code Merged\" header-align=\"center\" align=\"center\"\n                               min-width=\"40\"\n              >\n                <template #header slot-scope=\"scope\">\n                  Code<br>Merged\n                </template>\n                <template #default=\"{ row }\">\n                  <el-icon\n                      v-if=\"row.Code_Merged === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.Code_Merged === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.Code_Merged === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.Code_Merged === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n            </el-table-column>\n            <el-table-column label=\"周三\" header-align=\"center\" align=\"center\">\n              <el-table-column prop=\"config_center\" label=\"Config Changed\" header-align=\"center\" align=\"center\"\n                               min-width=\"43\"\n              >\n                <template #header slot-scope=\"scope\">\n                  Config<br>Changed\n                </template>\n                <template #default=\"{ row }\">\n                  <el-icon\n                      v-if=\"row.config_center === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.config_center === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.config_center === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.config_center === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"DB_Change\" label=\"DB Changed\" header-align=\"center\" align=\"center\" min-width=\"43\">\n                <template #default=\"{ row }\">\n                  <el-icon\n                      v-if=\"row.DB_Change === 'Confirmed' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.DB_Change === 'Confirmed' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <SuccessFilled/>\n                  </el-icon>\n                  <el-icon\n                      v-if=\"row.DB_Change === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.DB_Change === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n                <template #header slot-scope=\"scope\">\n                  DB<br>Changed\n                </template>\n              </el-table-column>\n              <el-table-column\n                  prop=\"services\"\n                  label=\"services\"\n                  min-width=\"180\"\n                  header-align=\"center\"\n                  align=\"center\"\n                  :style=\"{ 'white-space': 'pre-wrap' }\"\n              >\n                <template #default=\"{ row }\">\n                    <div v-if=\"row.services && row.services !== ''\">\n                        <template v-for=\"(service, index) in row.services.split('\\n')\">\n                            <div :style=\"{ color: !checkServiceGrouping(service) ? '#F56C67' : 'inherit' }\">\n                                {{ service }}\n                            </div>\n                        </template>\n                    </div>\n                    <el-icon\n                        v-if=\"!row.services || row.services === ''\"\n                        :size=\"20\"\n                        :color=\"'#F56C67'\"\n                    >\n                        <CircleCloseFilled/>\n                    </el-icon>\n                </template>\n              </el-table-column>\n\n              <el-table-column prop=\"region\" label=\"Region\" header-align=\"center\" align=\"center\" min-width=\"37\">\n                <template #default=\"{ row }\">\n                  <el-text\n                      v-if=\"row.region !== '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.region === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    {{ row.region }}\n                  </el-text>\n                  <el-icon\n                      v-if=\"row.region === '' ? true : false\"\n                      :size=\"20\"\n                      :color=\"row.region === 'pass' ? '#67c23a' : '#F56C67'\"\n                  >\n                    <CircleCloseFilled/>\n                  </el-icon>\n                </template>\n              </el-table-column>\n            </el-table-column>\n\n\n            <el-table-column prop=\"PM\" label=\"PM\" header-align=\"center\" align=\"center\" min-width=\"50\">\n              <template #default=\"{ row }\">\n                {{ row.PM }}\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"dev_pic\" label=\"DEV PIC\" header-align=\"center\" align=\"center\" min-width=\"40\">\n              <template #header slot-scope=\"scope\">\n                DEV<br>PIC\n              </template>\n              <template #default=\"{ row }\">\n                {{ row.dev_pic }}\n              </template>\n            </el-table-column>\n\n            <el-table-column prop=\"qa_pic\" label=\"QA\" header-align=\"center\" align=\"center\" min-width=\"50\">\n              <template #default=\"{ row }\">\n                {{ row.qa_pic }}\n              </template>\n            </el-table-column>\n\n\n            <el-table-column prop=\"status\" label=\"Status\" header-align=\"center\" align=\"center\" min-width=\"60\">\n              <template #default=\"{ row }\">\n\n                <el-tag class=\"bold-text\"\n                        effect=\"dark\"\n                        :type=\"getStatusName(row.status)\"\n                        :color=\"getColorName(row.status)\"\n                >{{ getBigName(row.status) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n\n        </div>\n        <div class=\"ar-container\">\n        <el-text class=\"mx-1\" type=\"danger\">请勾选下面的服务，点击一键四连启动发布流程。前端4个仓库只支持打TAG，不支持合代码，请找bin.wang合代码，4个仓库为仓库为：</el-text>\n        <el-link href=\"https://git.garena.com/shopee/marketing/web-chatbot\"  :underline=\"false\" type=\"danger\" target=\"_blank\">web-chatbot、</el-link>\n        <el-link href=\"https://git.garena.com/shopee/seller-fe/cs-chat\"  :underline=\"false\" type=\"danger\" target=\"_blank\">cs-chat、</el-link>\n        <el-link href=\"https://git.garena.com/shopee/chatbot/web-chatbot-csat\"  :underline=\"false\" type=\"danger\" target=\"_blank\">web-chatbot-csat、</el-link>\n        <el-link href=\"https://git.garena.com/shopee/chatbot/web-csat-rn\"  :underline=\"false\" type=\"danger\" target=\"_blank\">web-csat-rn。</el-link>\n        </div>\n\n        <div style=\"display: flex;\">\n\n          <el-table\n\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#e78181',color:'#f8f7f7'}\"\n              :data=\"IN_pingGroupA\"\n              style=\"width: 100%\"\n              @selection-change=\"handleSelectionChange1\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"/>\n            <el-table-column header-align=\"center\" label=\"平台BE1组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n          <el-table\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#819ee7',color:'#f8f7f7'}\"\n              header-align=\"center\"\n              :data=\"IN_pingGroupB\"\n              style=\"width: 100%\"\n              @selection-change=\"handleSelectionChange2\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"/>\n            <el-table-column header-align=\"center\" label=\"平台BE2组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n          <el-table\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#81e7c8',color:'#f8f7f7'}\"\n              header-align=\"center\"\n              :data=\"IN_featureGroup\"\n              style=\"width: 100%\"\n              @selection-change=\"handleSelectionChange3\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"/>\n            <el-table-column header-align=\"center\" label=\"功能BE组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n          <el-table\n              ref=\"multipleTableRef\"\n              border\n              :header-cell-style=\"{background:'#e7a881',color:'#f8f7f7'}\"\n              header-align=\"center\"\n              :data=\"IN_feGroup\"\n              style=\"width: 100%\"\n              @selection-change=\"handleSelectionChange4\"\n              :empty-text=\"'暂无数据'\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"/>\n            <el-table-column header-align=\"center\" label=\"FE组\">\n              <template #default=\"scope\">\n                <el-link :href=\"scope.row.link\" target=\"_blank\" :underline=\"false\">{{ scope.row.name }}</el-link>\n              </template>\n            </el-table-column>\n          </el-table>\n\n\n        </div>\n\n      </el-card>\n    </transition>\n    <el-card class=\"card\" shadow=\"hover\" width=\"50px\" height=\"50px\">\n\n\n      <el-form\n        :model=\"form\"\n        label-width=\"auto\"\n\n\n      >\n        <div style=\"display: flex; justify-content: center;\">\n          <el-dialog\n            v-model=\"dialogVisible\"\n            title=\"live-发布确认\"\n            width=\"30%\"\n            :before-close=\"handleClose\"\n          >\n            <span>您正在进行live自动发布流程，请确认您的操作是否要继续。</span>\n            <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">\n          确认发布\n        </el-button>\n      </span>\n            </template>\n          </el-dialog>\n        </div>\n        <div style=\"display: flex;\">\n        <el-form-item\n          label=\"标题\"\n          style=\"max-width: 460px\"\n        >\n          <el-input\n            v-model=\"form.name\"\n          />\n\n\n        </el-form-item>\n                      <el-tag class=\"ml-2\" size=\"large\">标题规范：bus/adhoc/bugfix-YYYYMMDD。若当日多次同类型发布，在标题后面自增号码以区分，示例：adhoc-20230831-1、adhoc-20230831-2。</el-tag>\n\n        </div>\n        <div style=\"display: flex;\">\n        <el-form-item label=\"是否合代码\">\n          <el-tooltip\n              class=\"box-item\"\n              effect=\"customized\"\n              content=\"勾选则从master合代码到release，不勾选则默认只打TAG\"\n              placement=\"top-start\"\n\n          >\n            <el-switch v-model=\"form.merge\"/>\n          </el-tooltip>\n        </el-form-item>\n\n        <el-form-item>\n\n          <el-tooltip\n              class=\"box-item\"\n              effect=\"customized\"\n              content=\"创建MR、合代码到release、打TAG、预编译\"\n              placement=\"top-start\"\n          >\n            <el-button  type=\"success\"\n                        v-loading.fullscreen.lock=\"fullscreenLoadingMR\"\n                        element-loading-text=\"正在处理，辛苦等待一下，中途请不要关闭此页面...\"\n                        @click=\"dialogVisible = true\"\n                        >一键四连</el-button>\n          </el-tooltip>\n\n        </el-form-item>\n        </div>\n      </el-form>\n      <el-table :data=\"mrTableData\"\n                border style=\"width: 100%\"\n                :header-cell-style=\"{background:'#cacfd7',color:'#606266'}\"\n                :empty-text=\"'暂无数据'\"\n      >\n        <el-table-column prop=\"repo\" label=\"仓库\"  header-align=\"center\" width=\"180\" />\n        <el-table-column prop=\"url\" label=\"MR地址\" header-align=\"center\" width=\"500\">\n          <template #default=\"{row}\">\n            <a\n              :href=\"row.url\"\n              target=\"_blank\"\n            >{{ row.url }}\n            </a>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"status\" label=\"状态\" header-align=\"center\" />\n      </el-table>\n    </el-card>\n  </div>\n\n\n</template>\n\n<script lang=\"ts\" setup>\nimport {ref, onMounted, computed, watch, reactive, watchEffect, nextTick, toRef, toRaw, onUnmounted} from 'vue';\nimport type {ElTree} from 'element-plus'\nimport draggable from \"vuedraggable\";\nimport {copymsg} from '@/api/copymsg';\nimport CircularJSON from 'circular-json';\nimport Sortable from \"sortablejs\";\n\n\n\n\nimport {\n  ElPagination,\n  ElCard,\n  ElTable,\n  ElTableColumn,\n  ElTag,\n  ElSelect,\n  ElOption,\n  ElMessage,\n  ElMessageBox,\n  TableColumnCtx,\n  TableInstance,\n} from 'element-plus';\nimport {Eleme, Loading, Refresh, CirclePlus, ChatRound, Bell} from '@element-plus/icons-vue'\nimport axios from 'axios';\nimport {Edit, View as IconView} from '@element-plus/icons-vue';\nimport {ChatLineRound, Male} from '@element-plus/icons-vue';\nimport {read_json} from '@/api/read_json';\nimport {get_release_tag} from '@/api/get_release_tag';\nimport {send_title} from '@/api/send_title';\nimport {startAuto} from '@/api/startAuto';\nimport {autocheckdata} from '@/api/autocheckdata';\nimport {seatalk} from '@/api/seatalk';\nimport {callMRseatalk} from '@/api/callMRseatalk';\nimport {autochecknewdata} from '@/api/autochecknewdata';\nimport {start_single_ar} from '@/api/start_single_ar';\nimport {mr_seatalk_single_feature_msg} from '@/api/mr_seatalk_single_feature_msg';\nimport {get_key_jira_release_list} from '@/api/get_key_jira_release_list';\nimport {get_unreleased_versions} from '@/api/get_unreleased_versions';\nimport {newMerge} from '@/api/newMerge';\nimport {ElButton, ElDrawer} from 'element-plus'\nimport {CircleCloseFilled} from '@element-plus/icons-vue'\nimport type {TabsPaneContext} from 'element-plus'\n//import {consoleLog} from \"echarts/types/src/util/log\";\nimport {signedOff} from '@/api//signedOff';\nconst dialogVisible = ref(false)\n\nconst fullscreenLoadingMR = ref(false);\n\nconst dialogTableVisible = ref(false)\nconst centerDialogVisible = ref(false)\nconst activeName = ref('first')\nconst handleClick = (tab: TabsPaneContext, event: Event) => {\n  console.log(tab, event)\n}\nconst fe_services = reactive([]);\nconst be_services = reactive([]);\nconst all_services = reactive([]);\n\n\nconst pingGroupA = reactive([\n  {\n    'shopee-chatbot-intent': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intent',\n    'shopee-chatbot-admin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.admin',\n    'shopee-chatbot-adminasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminasynctask',\n    'shopee-chatbot-adminconfigservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminconfigservice',\n    'shopee-chatbot-adminservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminservice',\n    'shopee-chatbot-agentcontrol': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.agentcontrol',\n    'shopee-chatbot-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.asynctask',\n    'shopee-chatbot-auditlog': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.auditlog',\n    'shopee-chatbot-botapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi',\n    'shopee-chatbot-context': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.context',\n    'shopee-chatbot-dm': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.dm',\n    'shopee-chatbot-featurecenter': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featurecenter',\n    'shopee-chatbot-intentclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intentclarification',\n    'shopee-chatbot-messageasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageasynctask',\n    'shopee-chatbot-messageservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageservice',\n    'shopee-chatbot-messageverification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageverification',\n    'shopee-chatbot-nlu': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.nlu',\n    'shopee-chatbot-ordercard': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.ordercard',\n    'shopee-chatbot-pilotapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.pilotapi',\n    'shopee-chatbotcommon-adminasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminasynctask',\n    'shopee-chatbotcommon-adminconfigservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminconfigservice',\n    'shopee-chatbotcommon-adminservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminservice',\n    'shopee-chatbotcommon-agentcontrol': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.agentcontrol',\n    'shopee-chatbotcommon-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.asynctask',\n    'shopee-chatbotcommon-botapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.botapi',\n    'shopee-chatbotcommon-context': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.context',\n    'shopee-chatbotcommon-dm': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.dm',\n    'shopee-chatbotcommon-featurecenter': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featurecenter',\n    'shopee-chatbotcommon-nlu': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu',\n    'shopee-chatbotcommon-productrecommend': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.productrecommend',\n    'shopee-chatbotcommon-rulebaseservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.rulebaseservice',\n    'shopee-chatbotcommon-shopconsole': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsole',\n    'shopee-chatbotcommon-intentclarification':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intentclarification',\n    'shopee-chatbot-websocketgwy':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.websocketgwy',\n    'shopee-csdata-metricservice':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.data.metricservice',\n    'shopee-chatbotcommon-logic':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.ai_video_chatbot.engineer.logic',\n    'shopee-chatbotcommon-msgdetection':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.ai_video_chatbot.engineer.msgdetection'\n\n  }]);\n\nconst pingGroupB = reactive([{\n  'shopee-chatbot-autotraining': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining',\n  'shopee-annotation-admin':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.admin',\n  'shopee-annotation-asynctask':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.asynctask',\n  'shopee-annotation-timetask':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.timetask',\n  'shopee-annotation-dataproxy':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.dataproxy',\n  'shopee-agorithmservice-component': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.ai_engineering.nlu_component',\n  'shopee-chatbot-experimentmanagement': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.experimentmanagement',\n  'shopee-chatbot-featureapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featureapiproxy',\n  'shopee-chatbot-modelgw': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.modelgw',\n  'shopee-chatbot-realtime': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.realtime',\n  'shopee-chatbot-recallmanager': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallmanager',\n  'shopee-chatbot-recallservice': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallservice',\n  'shopee-chatbot-recommendation': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recommendation',\n  'shopee-chatbotcommon-apadmin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadmin',\n  'shopee-chatbotcommon-apasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apasynctask',\n  'shopee-chatbotcommon-apdataproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apdataproxy',\n  'shopee-chatbotcommon-component': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu_component',\n  'shopee-chatbotcommon-experimentmanagement': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.experimentmanagement',\n  'shopee-chatbotcommon-featureapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featureapiproxy',\n  'shopee-chatbotcommon-intent': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intent',\n  'shopee-chatbotcommon-kbadmin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmin',\n  'shopee-chatbotcommon-kbapi': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbapi',\n  'shopee-chatbotcommon-kbasynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbasynctask',\n  'shopee-chatbotcommon-kblabelclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kblabelclarification',\n  'shopee-chatbotcommon-modelgw': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.modelgw',\n  'shopee-knowledgebase-admin': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.admin',\n  'shopee-knowledgebase-api': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.api',\n  'shopee-knowledgebase-asynctask': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.asynctask',\n  'shopee-knowledgebase-labelclarification': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.labelclarification',\n  'shopee-chatbotcommon-promptmanagements':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.promptmanagements',\n  'shopee-knowledgeplatform-admin':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin',\n  'shopee-knowledgeplatform-api':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.api',\n  'shopee-knowledgeplatform-qa_tools':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.qa_tools',\n  'shopee-knowledgeplatform-offline':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.offline',\n  'shopee-chatbot-apiadmin':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.apistore.apiadmin',\n  'shopee-chatbot-apiflowserving':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.apistore.apiflowserving'\n\n\n}]);\nconst featureGroup = reactive([{\n  'shopee-chatbot-api': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.api',\n  'shopee-chatbot-autotraining': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining',\n  'shopee-chatbotcommon-tfapiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfapiproxy',\n  'shopee-chatbotcommon-tfeditor': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeditor',\n  'shopee-chatbotcommon-tfserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfserving',\n  'shopee-chatbotcommon-tfvariateserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfvariateserving',\n  'shopee-taskflow-apiproxy': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.apiproxy',\n  'shopee-taskflow-editor': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.editor',\n  'shopee-taskflow-taskflowserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowserving',\n  'shopee-taskflow-taskflowsop': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowsop',\n  'shopee-taskflow-variateserving': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.variateserving'\n}]);\n\nconst feGroup = reactive([\n  {\n    'shopee-autotrainingportal-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.autotrainingportal.adminstatic',\n    'shopee-annotation-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.adminstatic',\n    'shopee-cbrcmdplt-rcmdpltstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.cbrcmdplt.rcmdpltstatic',\n    'shopee-chatbot-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminstatic',\n    'shopee-chatbot-chatbotcsatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotcsatstatic',\n    'shopee-chatbot-chatbotrnstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotrnstatic',\n    'shopee-chatbot-chatbotstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotstatic',\n    'shopee-chatbot-csatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.csatstatic',\n    'shopee-chatbot-dashboardstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.dashboardstatic',\n    'shopee-chatbotcommon-admincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.admincommonsaasstatic',\n    'shopee-chatbotcommon-adminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminsaasstatic',\n    'shopee-chatbotcommon-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminstatic',\n    'shopee-chatbotcommon-annotationadminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.annotationadminstatic',\n    'shopee-chatbotcommon-apadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadminsaasstatic',\n    'shopee-chatbotcommon-csatstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.csatstatic',\n    'shopee-chatbotcommon-kbadmincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmincommonsaasstatic',\n    'shopee-chatbotcommon-kbadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadminsaasstatic',\n    'shopee-chatbotcommon-shopconsolestatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsolestatic',\n    'shopee-chatbotcommon-static': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.static',\n    'shopee-chatbotcommon-tfeadmincommonsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadmincommonsaasstatic',\n    'shopee-chatbotcommon-tfeadminsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadminsaasstatic',\n    'shopee-chatbotcommon-tmcsaasstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tmcsaasstatic',\n    'shopee-gec-gecstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.gecstatic',\n    'shopee-knowledgebase-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.adminstatic',\n    'shopee-taskflow-adminstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.adminstatic',\n    'shopee-cschat-h5':'https://space.shopee.io/console/cmdb/overview/detail/shopee.customer_service_and_chatbot.customer_service.cschannel.cschat.h5',\n    'shopee-knowledgeplatform-adminstatic':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.adminstatic',\n    'shopee-knowledgeplatformnode-knowledgeplatformnode':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin_portal',\n    'shopee-knowledgeplatform-guidesstatic':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.guidesstatic',\n    'shopee-chatbot-insights':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.adminportal.insightstatic',\n    'shopee-chatbotcommon-insightssaasstatic-test':'https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.chatbotcommon.insightssaasstatic',\n    'shopee-chatbot-mmfchatbotconsole':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.mmfchatbotconsole',\n    'shopee-chatbot-h5mmfchatbotsharedrcstatic':'https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.h5mmfchatbotsharedrcstatic',\n    'shopee-chatbot-tmcstatic': 'https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.tmcstatic',\n\n  }\n\n])\n\nconst openKB = () => {\n  window.open('https://monitoring.infra.sz.shopee.io/grafana/d/kj1f3huVk/chatbot-fa-ban-kan-ban?orgId=10&from=now-1h&to=now&refresh=30s', '_blank');\n}\n\nconst IN_pingGroupA = computed(() => {\n  const result = {};\n  const pingGroupAKeys = pingGroupA.map(obj => Object.keys(obj)).flat();\n  for (const item of be_services) {\n    if (pingGroupAKeys.includes(item)) {\n      const link = pingGroupA.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\nconst IN_pingGroupB = computed(() => {\n  const result = {};\n  const pingGroupBKeys = pingGroupB.map(obj => Object.keys(obj)).flat();\n  for (const item of be_services) {\n    if (pingGroupBKeys.includes(item)) {\n      const link = pingGroupB.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\n\nconst IN_featureGroup = computed(() => {\n  const result = {};\n  const featureGroupKeys = featureGroup.map(obj => Object.keys(obj)).flat();\n  for (const item of be_services) {\n    if (featureGroupKeys.includes(item)) {\n      const link = featureGroup.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\n\nconst IN_feGroup = computed(() => {\n  const result = {};\n  const beGroupKeys = feGroup.map(obj => Object.keys(obj)).flat();\n  for (const item of fe_services) {\n    if (beGroupKeys.includes(item)) {\n      const link = feGroup.find(obj => Object.keys(obj).includes(item))[item];\n      result[item] = {\n        name: item,\n        link: link,\n      };\n    }\n  }\n  return Object.values(result);\n});\n\nlet services_pane = ref(false);\nlet visible = ref(false);\nlet show = ref(false);\nlet showfe = ref(false);\nlet showbe = ref(false);\nlet showresult = ref(false);\nlet active = ref(0);\nlet jiraStatus = ref(\"wait\");\nlet masterStatus = ref(\"wait\");\nconst tagType = [\"success\", \"info\", \"warning\", \"danger\"];\n// 发布类型过滤器\nconst releaseTypeFilter = ref(localStorage.getItem('releaseTypeFilter') || 'bus');\nconst filteredProjects = ref([]); // 存储过滤后的项目列表\n\n// 从本地存储获取上次使用的过滤类型\nonMounted(() => {\n  const savedFilter = localStorage.getItem('releaseTypeFilter');\n  if (savedFilter) {\n    releaseTypeFilter.value = savedFilter;\n  }\n});\n\n// 处理发布类型过滤变化\nconst handleReleaseTypeChange = () => {\n  // 保存过滤类型到本地存储\n  localStorage.setItem('releaseTypeFilter', releaseTypeFilter.value);\n  filterProjects();\n  \n  // 过滤后自动选择最近的发布单\n  if (filteredProjects.value.length > 0) {\n    selectClosestRelease();\n  } else {\n    // 如果没有过滤结果，确保清空选择和数据\n    selectedProject.value = '';\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n};\n\n// 选择最近的发布单\nconst selectClosestRelease = () => {\n  const currentDate = new Date();\n  currentDate.setHours(0, 0, 0, 0);\n  let closestDate = null;\n  let closestProject = null;\n  \n  console.log('尝试选择最近发布单，过滤后项目数量:', filteredProjects.value.length);\n  \n  filteredProjects.value.forEach((project) => {\n    // 尝试匹配多种可能的格式\n    const title = project.title || '';\n    // 尝试不同的日期格式匹配\n    \n    // 匹配格式1: bus-230428或adhoc-230428 (6位日期，YYMMDD格式)\n    let match = title.match(/(bus|adhoc|hotfix)[_-](\\d{6})/i);\n    \n    // 匹配格式2: bus-20230428或adhoc-20230428 (8位日期，YYYYMMDD格式)\n    if (!match) {\n      match = title.match(/(bus|adhoc|hotfix)[_-](\\d{8})/i);\n    }\n    \n    if (match) {\n      // 提取日期部分\n      const dateString = match[2];\n      let year, month, day;\n      \n      // 根据日期长度确定解析方式\n      if (dateString.length === 6) {\n        // 6位日期格式 (YYMMDD)\n        const twoDigitYear = parseInt(dateString.slice(0, 2));\n        year = twoDigitYear < 50 ? 2000 + twoDigitYear : 1900 + twoDigitYear;\n        month = parseInt(dateString.slice(2, 4)) - 1;\n        day = parseInt(dateString.slice(4, 6));\n      } else if (dateString.length === 8) {\n        // 8位日期格式 (YYYYMMDD)\n        year = parseInt(dateString.slice(0, 4));\n        month = parseInt(dateString.slice(4, 6)) - 1;\n        day = parseInt(dateString.slice(6, 8));\n      } else {\n        console.log(`无法识别的日期格式: ${dateString}`);\n        return; // 跳过无法解析的日期\n      }\n      \n      try {\n        const date = new Date(year, month, day, 0, 0, 0);\n        \n        // 只有有效日期才进行比较\n        if (!isNaN(date.getTime())) {\n          console.log(`解析发布单 ${project.title}: ${year}-${month+1}-${day}, date=${date}, 今天=${currentDate}`);\n          \n          // 只选择未来的发布单，或者今天的发布单\n          if (date >= currentDate && (!closestDate || date < closestDate)) {\n            closestDate = date;\n            closestProject = project;\n            console.log(`找到更近的发布单: ${project.title}, 日期: ${date}`);\n          }\n        } else {\n          console.log(`日期无效: ${year}-${month+1}-${day}`);\n        }\n      } catch (e) {\n        console.log(`日期解析错误: ${e.message}`);\n      }\n    } else {\n      console.log(`无法解析日期格式: ${project.title}`);\n    }\n  });\n  \n  if (closestProject) {\n    selectedProject.value = closestProject.title;\n    console.log('自动选择最近发布单:', selectedProject.value);\n    \n    // 移除这行，避免重复调用getData\n    // getData(selectedProject.value);\n  } else {\n    console.log('未找到合适的发布单自动选择');\n  }\n};\n\n// 过滤项目列表\nconst projectTypeFilter = ref(localStorage.getItem('projectTypeFilter') || 'SPCB');\n\nconst filterProjects = () => {\n  console.log('过滤项目列表，当前过滤类型:', releaseTypeFilter.value, '项目类型:', projectTypeFilter.value);\n  \n  if (!projects.length) return;\n  \n  filteredProjects.value = projects.filter(project => {\n    // 检查发布类型\n    const matchesType = releaseTypeFilter.value === 'All' || \n      project.title?.toLowerCase().includes(releaseTypeFilter.value.toLowerCase());\n    \n    // 检查项目类型（SPCB/SPCT）\n    const matchesProject = project.key && project.key.startsWith(projectTypeFilter.value);\n    \n    return matchesType && matchesProject;\n  });\n  \n  console.log('过滤后项目数量:', filteredProjects.value.length);\n  \n  // 如果过滤后没有结果，清空选择的项目\n  if (filteredProjects.value.length === 0) {\n    selectedProject.value = '';\n    // 同时清空表格数据\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n};\n\nfunction getRandomElement() {\n  const randomIndex = Math.floor(Math.random() * tagType.length);\n  console.log(tagType[randomIndex]);\n  return tagType[randomIndex];\n}\n\nconst selectedProject = ref();\n// 每页显示条数\nconst fullscreenLoading = ref(false)\n\ninterface User {\n  release_title: string\n  jira_key: string\n  jira_title: string\n  jira_link: string\n  type: string\n}\n\nconst filterType = (value: string, row: User) => {\n  return row.type === value\n}\n\nconst selectedRelease = ref();\nconst projects = reactive([]);\n\nlet releaseTableData = reactive([]);\n\n\nconst callseatalk = (tab: TabsPaneContext, event: Event) => {\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n  seatalk(fin_data);\n  ElMessage({\n    message: '已进行checklist消息push，请耐心等待seatalk自动发送消息。',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nconst signedOffSeatalk = (tab: TabsPaneContext, event: Event) => {\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n  signedOff(fin_data);\n  ElMessage({\n    message: '已进行Signed off消息push，请耐心等待seatalk自动发送消息。',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nconst callMRseatalkFE = (tab: TabsPaneContext, event: Event) => {\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n\n  callMRseatalk(fin_data);\n  ElMessage({\n    message: '已进行MR消息push，请耐心等待seatalk自动发送消息。',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nasync function sendSingleFeatureToCT(row) {\n  let data_final = ref();\n  data_final = await mr_seatalk_single_feature_msg(row.row);\n  console.log(data_final)\n  navigator.clipboard.writeText(data_final.data)\n      .then(() => {\n        ElMessage({\n          message: '恭喜，MR信息已发送到seatalk！',\n          type: 'success',\n        })\n      })\n      .catch((error) => {\n        ElMessage.error('MR信息发送失败！')\n      });\n}\n\nasync function copyToClipboard(row) {\n  let data_final = ref();\n  let fin_data = {\n    jira_title: selectedProject.value,\n  };\n  data_final = await copymsg(row.row);\n\n  console.log(data_final)\n  navigator.clipboard.writeText(data_final.data)\n      .then(() => {\n        ElMessage({\n          message: '恭喜，MR信息已复制到剪切板！',\n          type: 'success',\n        })\n        //alert('已复制到剪贴板');\n      })\n      .catch((error) => {\n        ElMessage.error('复制剪切板失败！')\n      });\n}\n\nasync function startSingleAR(props) {\n  //const rawRow = toRaw(props);\n  console.log(props.row);\n  //const jsonString = CircularJSON.stringify(props.toJSON());\n\n  let data_final = ref();\n  data_final = await start_single_ar(props.row);\n\n}\n\nasync function getreleaseData(value) {\n\n  console.log(releaseTableData);\n  const releaseTableDataIds = new Set(releaseTableData.map((item) =>\n      item.jira_key));\n  // 这里编写请求数据的异步操作\n// 将 releaseTableData 中每个对象的 id 属性存入 Set 对象中\n\n  all_services.splice(0, all_services.length);\n  be_services.splice(0, be_services.length);\n  fe_services.splice(0, fe_services.length);\n  console.log(`get data for ${value}`);\n  let fin_data = {\n    title: value,\n  };\n  let data_final = [];\n  let temp_data : any;\n  temp_data = await send_title(fin_data);\n  // console.log(temp_data);\n  // console.log(await send_title(fin_data))\n  // console.log(temp_data);\n  if (temp_data.length === 0) {\n    releaseTableData.splice(0, releaseTableData.length);\n  } else {\n    releaseTableData.splice(0, releaseTableData.length);\n    temp_data.data.forEach((item) => {\n      console.log(item);\n      if (!releaseTableDataIds.has(item.jira_key)) {\n        releaseTableData.push(item);\n        releaseTableDataIds.add(item.jira_key);\n      }\n      // releaseTableData.push(item);\n      // releaseTableDataIds.add(item.jira_key);\n    });\n  }\n\n\n  console.log(releaseTableData);\n}\n\nasync function refreshData(value) {\n  showbe.value = false\n  showfe.value = false\n  console.log(value)\n\n  await getreleaseData(value);\n  fullscreenLoading.value = true\n\n  console.log(releaseTableData)\n  await getNewData(value)\n  fullscreenLoading.value = false\n\n  ElMessage({\n    message: '已更新状态',\n    type: 'success',\n    duration: 5000,\n  })\n}\n\nasync function getNewData(value) {\n  showbe.value = false\n  showfe.value = false\n  console.log(value)\n  await getreleaseData(value);\n  fullscreenLoading.value = true\n\n  let data_final = ref();\n  console.log(releaseTableData)\n  data_final.value = await autochecknewdata(releaseTableData);\n\n  fullscreenLoading.value = false\n\n  console.log(data_final);\n  const releaseDataLength = releaseTableData.length;\n  data_final.value.data.slice(0,releaseDataLength).forEach((item, index) => {\n    console.log(item.result_all);\n\n    if (!item.signoff_status) {\n      releaseTableData[index].sign_off = ''\n    } else {\n      releaseTableData[index].sign_off = item.signoff_status;\n    }\n    if (!item.config_center) {\n      releaseTableData[index].config_center = ''\n    } else {\n      releaseTableData[index].config_center = item.config_center;\n    }\n    if (!item.Code_Merged) {\n      releaseTableData[index].Code_Merged = ''\n    } else {\n      releaseTableData[index].Code_Merged = item.Code_Merged;\n    }\n    if (!item.shopee_region) {\n      releaseTableData[index].region = ''\n    } else {\n      releaseTableData[index].region = item.shopee_region;\n    }\n    if (!item.redis_check) {\n      releaseTableData[index].redis_change = ''\n    } else {\n      releaseTableData[index].redis_change = item.redis_check;\n    }\n    if (!item.DB_Change) {\n      releaseTableData[index].DB_Change = ''\n    } else {\n      releaseTableData[index].DB_Change = item.DB_Change;\n    }\n    if (!item.result) {\n      releaseTableData[index].result = ''\n    } else {\n      releaseTableData[index].result = item.result;\n    }\n\n    if (!item.merge_list) {\n      releaseTableData[index].merge_list = ''\n    } else {\n      releaseTableData[index].merge_list = item.merge_list;\n    }\n    if (!item.status) {\n      releaseTableData[index].status = ''\n    } else {\n      releaseTableData[index].status = item.status;\n    }\n    if (!item.dev_pic) {\n      releaseTableData[index].dev_pic = ''\n    } else {\n      releaseTableData[index].dev_pic = item.dev_pic;\n    }\n    if (!item.PM) {\n      releaseTableData[index].PM = ''\n    } else {\n      releaseTableData[index].PM = item.PM;\n    }\n    if (!item.qa_pic) {\n      releaseTableData[index].qa_pic = ''\n    } else {\n      releaseTableData[index].qa_pic = item.qa_pic;\n    }\n    console.log(item.redis_check)\n    console.log(releaseTableData)\n    releaseTableData[index].services = '';\n    item.services_list.services_list_be.forEach((service) => {\n      if (releaseTableData[index].services === '') {\n        releaseTableData[index].services += `${service}`;\n      } else {\n        releaseTableData[index].services += `\\n${service}`;\n      }\n      if (!be_services.includes(service)) {\n        be_services.push(service)\n        all_services.push(service)\n      }\n    })\n    item.services_list.services_list_fe.forEach((service) => {\n      if (releaseTableData[index].services === '') {\n        releaseTableData[index].services += `${service}`;\n      } else {\n        releaseTableData[index].services += `\\n${service}`;\n      }\n      if (!fe_services.includes(service)) {\n        fe_services.push(service)\n        all_services.push(service)\n      }\n    })\n  })\n\n  if (fe_services.length !== 0) {\n    showfe.value = true\n  }\n  if (be_services.length !== 0) {\n    showbe.value = true\n  }\n  console.log(fe_services)\n  console.log(be_services)\n  showresult.value = true\n  // let allTrue = data_final.data.every(item => item.result !== \"false\");\n  // console.log(allTrue);\n  // location.reload();\n}\n\n\nasync function getData(value) {\n  if (!value) {\n    console.log('没有选择发布单，不进行数据查询');\n    releaseTableData.splice(0, releaseTableData.length);\n    return;\n  }\n  \n  showbe.value = false\n  showfe.value = false\n  console.log(`获取发布单数据: ${value}`)\n  await getreleaseData(value);\n  fullscreenLoading.value = true\n\n  let data_final = ref();\n  console.log('releaseTableData前:', releaseTableData)\n  data_final.value = await autocheckdata(releaseTableData);\n  console.log('API响应:', data_final.value);\n  \n  if (!data_final.value || !data_final.value.data) {\n    console.log('API返回数据为空或格式不正确');\n    fullscreenLoading.value = false;\n    return;\n  }\n\n  fullscreenLoading.value = false\n  const releaseDataLength = releaseTableData.length;\n  releaseTableData.forEach((itemA) => {\n    const matchedItemB = data_final.value.data.find((itemB) => itemA.jira_key === itemB.feature_key);\n    if (matchedItemB) {\n      console.log('匹配项数据:', matchedItemB);\n      \n      // 处理bug相关字段，确保转换为数字类型\n      itemA.bug_resolved = Number(matchedItemB.bug_resolved || 0);\n      itemA.bug_total = Number(matchedItemB.bug_total || 0);\n      console.log(`Bug数据: ${itemA.bug_resolved}/${itemA.bug_total}`);\n      \n      itemA.type = matchedItemB.type;\n      itemA.jira_key = matchedItemB.feature_key;\n      itemA.jira_link = `https://jira.shopee.io/browse/${matchedItemB.feature_key}`;\n      itemA.jira_title = matchedItemB.feature_title;\n      if (!matchedItemB.signoff_status) {\n        itemA.sign_off = ''\n      } else {\n        itemA.sign_off = matchedItemB.signoff_status;\n      }\n      if (!matchedItemB.config_center) {\n        itemA.config_center = ''\n      } else {\n        itemA.config_center = matchedItemB.config_center;\n      }\n      if (!matchedItemB.shopee_region) {\n        itemA.region = ''\n      } else {\n        itemA.region = matchedItemB.shopee_region;\n      }\n      if (!matchedItemB.redis_check) {\n        itemA.redis_change = ''\n      } else {\n        itemA.redis_change = matchedItemB.redis_check;\n      }\n      if (!matchedItemB.result) {\n        itemA.result = ''\n      } else {\n        itemA.result = matchedItemB.result;\n      }\n\n      if (!matchedItemB.merge_list) {\n        itemA.merge_list = ''\n      } else {\n        itemA.merge_list = matchedItemB.merge_list;\n      }\n      if (!matchedItemB.status) {\n        itemA.status = ''\n      } else {\n        itemA.status = matchedItemB.status;\n      }\n      if (!matchedItemB.Code_Merged) {\n        itemA.Code_Merged = ''\n      } else {\n        itemA.Code_Merged = matchedItemB.Code_Merged;\n      }\n      if (!matchedItemB.DB_Change) {\n        itemA.DB_Change = ''\n      } else {\n        itemA.DB_Change = matchedItemB.DB_Change;\n      }\n      if (!matchedItemB.dev_pic) {\n        itemA.dev_pic = ''\n      } else {\n        itemA.dev_pic = matchedItemB.dev_pic;\n      }\n      if (!matchedItemB.PM) {\n        itemA.PM = ''\n      } else {\n        itemA.PM = matchedItemB.PM;\n      }\n      if (!matchedItemB.qa_pic) {\n        itemA.qa_pic = ''\n      } else {\n        itemA.qa_pic = matchedItemB.qa_pic;\n      }\n      itemA.services = '';\n      matchedItemB.services_list.services_list_be.forEach((service) => {\n        if (itemA.services === '') {\n          itemA.services += `${service}`;\n        } else {\n          itemA.services += `\\n${service}`;\n        }\n        if (!be_services.includes(service)) {\n          be_services.push(service)\n          all_services.push(service)\n        }\n      })\n\n      matchedItemB.services_list.services_list_fe.forEach((service) => {\n        if (itemA.services === '') {\n          itemA.services += `${service}`;\n        } else {\n          itemA.services += `\\n${service}`;\n        }\n        if (!fe_services.includes(service)) {\n          fe_services.push(service)\n          all_services.push(service)\n        }\n      })\n    }\n  });\n\n  if (fe_services.length !== 0) {\n    showfe.value = true\n  }\n  if (be_services.length !== 0) {\n    showbe.value = true\n  }\n  console.log('fe_services:', fe_services)\n  console.log('be_services:', be_services)\n  console.log('最终表格数据:', releaseTableData)\n  showresult.value = true\n}\n\nfunction getIconName(type) {\n  if (type === 'Epic') {\n    return 'Epic-icon';\n  } else if (type === 'Sub-task') {\n    return 'ST-icon';\n  } else if (type === 'Task') {\n    return 'Task-icon';\n  } else if (type === 'Bug') {\n    return 'Bug-icon';\n  } else if (type === 'Story') {\n    return 'Story-icon';\n  }\n}\n\nfunction getColorName(status) {\n  if (status === 'TO DO') {\n    return '#42526e';\n  } else if (status === 'Done') {\n    return '#00875a';\n  } else if (status === 'Waiting') {\n    return '#42526e';\n  } else if (status === 'Icebox') {\n    return '#0052CC'\n  } else if (status === 'Doing') {\n    return '#0052CC'\n  } else if (status === 'UAT') {\n    return '#0052CC'\n  } else if (status === 'Delivering') {\n    return '#0052CC'\n  } else if (status === 'Developing') {\n    return '#0052CC'\n  } else if (status === 'Testing') {\n    return '#0052CC'\n  } else if (status === 'TECH DESIGN') {\n    return '#0052CC'\n  } else {\n    return '#0052CC'\n  }\n}\n\n\nfunction getBigName(status) {\n  if (status === 'TO DO') {\n    return 'TO DO';\n  } else if (status === 'Done') {\n    return 'DONE';\n  } else if (status === 'Waiting') {\n    return 'WAITING';\n  } else if (status === 'Icebox') {\n    return 'ICEBOX';\n  } else if (status === 'Doing') {\n    return 'DOING'\n  } else if (status === 'UAT') {\n    return 'UAT'\n  } else if (status === 'Delivering') {\n    return 'DELIVERING'\n  } else if (status === 'Developing') {\n    return 'DEVELOPING'\n  } else if (status === 'Testing') {\n    return 'TESTING'\n  } else {\n    return status\n  }\n}\n\n\nfunction getStatusName(status) {\n  if (status === 'TO DO') {\n    return 'info';\n  } else if (status === 'Done') {\n    return 'success';\n  } else if (status === 'Waiting') {\n    return 'info';\n  } else if (status === 'Icebox') {\n    return 'icebox';\n  } else if (status === 'Doing') {\n    return 'doing';\n  } else if (status === 'UAT') {\n    return 'uat';\n  } else if (status === 'Delivering') {\n    return 'delivering'\n  } else if (status === 'Developing') {\n    return 'developing'\n  } else if (status === 'Testing') {\n    return 'testing'\n  }\n}\n\n\n\nonMounted(async () => {\n  // 从 localStorage 获取一些基本设置\n  const savedReleaseType = localStorage.getItem('releaseTypeFilter');\n  if (savedReleaseType) {\n    releaseTypeFilter.value = savedReleaseType;\n  }\n  \n  const savedProjectType = localStorage.getItem('projectTypeFilter');\n  if (savedProjectType) {\n    projectTypeFilter.value = savedProjectType;\n  }\n  \n  // 获取发布标签列表\n  const new_releaseList = await get_key_jira_release_list();\n  const releaseData = new_releaseList.data.data;\n\n  // 填充项目数据\n  selectedRelease.value = releaseData;\n  releaseData.forEach((item) => {\n    projects.push(item);\n  });\n  \n  // 应用过滤器\n  filterProjects();\n  \n  // 如果localStorage中有选中的项目，则使用它\n  const savedOption = localStorage.getItem('selectedProject');\n  if (savedOption) {\n    console.log('从localStorage恢复选中项目:', savedOption);\n    selectedProject.value = savedOption;\n    // 注意：由于watch会处理selectedProject的变化，这里无需调用getData\n  } else {\n    // 自动选择最近的发布单，但不触发数据加载\n    selectClosestRelease();\n  }\n  \n  // 恢复活动标签\n  const saveactive = localStorage.getItem('active');\n  if (saveactive) {\n    active.value = parseInt(saveactive);\n  }\n  \n  // 检查页面上是否有内容\n  if (document.body.innerHTML.trim().length > 0) {\n    // 选择具有\"el-empty\"类的元素\n    const emptyElement = document.querySelector('.el-empty');\n\n    // 如果有内容，隐藏具有\"el-empty\"类的元素\n    if (emptyElement) {\n      emptyElement.style.display = 'none';\n    }\n  }\n});\n\nconst form = reactive({\n  name: '',\n  merge: true,\n});\n\nwatch(selectedProject, (newValue, oldValue) => {\n  // 确保只有在值真正变化且非空时才执行getData\n  if (newValue && newValue !== oldValue) {\n    console.log(`选择项目变化: ${oldValue} -> ${newValue}, 重新加载数据`);\n    releaseTableData.splice(0, releaseTableData.length);\n    getData(newValue);\n  }\n  \n  // 保存到localStorage\n  if (newValue) {\n    localStorage.setItem('selectedProject', newValue);\n  }\n  \n  // 更新表单名称\n  form.name = removeReleasePrefix(newValue);\n}, { flush: 'post' }); // 添加flush: 'post'确保在DOM更新后执行\n\nwatch(releaseTableData, (newValue, oldValue) => {\n  localStorage.setItem('releaseTableData', JSON.stringify(releaseTableData))\n  if (releaseTableData.length === 0) {\n    show.value = false\n  }\n  if (releaseTableData.length !== 0) {\n    show.value = true\n  }\n});\n\n// 监听项目类型变化\nwatch(projectTypeFilter, (newValue) => {\n  // 保存过滤类型到本地存储\n  localStorage.setItem('projectTypeFilter', newValue);\n  \n  // 更新过滤项目\n  filterProjects();\n  \n  // 过滤后自动选择最近的发布单\n  if (filteredProjects.value.length > 0) {\n    selectClosestRelease();\n  } else {\n    // 如果没有过滤结果，清空选择和表格数据\n    selectedProject.value = '';\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n});\n\nwatch(all_services, (newValue, oldValue) => {\n  if (all_services.length === 0) {\n    services_pane.value = false\n  }\n  if (all_services.length !== 0) {\n    services_pane.value = true\n  }\n});\n\n// 监听过滤类型变化\nwatch(releaseTypeFilter, (newValue) => {\n  // 保存过滤类型到本地存储\n  localStorage.setItem('releaseTypeFilter', releaseTypeFilter.value);\n  \n  // 更新过滤项目\n  filterProjects();\n  \n  // 过滤后自动选择最近的发布单\n  if (filteredProjects.value.length > 0) {\n    selectClosestRelease();\n  } else {\n    // 如果没有过滤结果，清空选择和表格数据\n    selectedProject.value = '';\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n  \n  // 如果当前选中的项目不在过滤结果中，则清空选择\n  if (selectedProject.value && \n      !filteredProjects.value.some(project => project.title === selectedProject.value)) {\n    selectedProject.value = '';\n    // 同时清空表格数据\n    releaseTableData.splice(0, releaseTableData.length);\n  }\n});\n\nfunction removeReleasePrefix(refParam) {\n  if (refParam) {\n    let modifiedString = refParam.replace(/【Release】|发布单/g, \"\").replace(/\\s/g, \"\");\n    console.log(modifiedString);\n    return modifiedString;\n  }\n  return \"\";\n}\n\n\n\n\n//平台1组\nconst selectedData1 = ref([]); // 保存选中的表格数据\n//平台2组\nconst selectedData2 = ref([]); // 保存选中的表格数据\n//功能组\nconst selectedData3 = ref([]); // 保存选中的表格数据\n//前端组\nconst selectedData4 = ref([]); // 保存选中的表格数据\n\n\nconst handleSelectionChange1 = (selection) => {\n  selectedData1.value = [];\n  selectedData1.value = selection;\n  console.log(processedData1.value);\n  console.log(selectedData1.value);\n};\nconst processedData1 = computed(() => {\n  return selectedData1.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\nconst getRowKey = (row: any) => row.id;\nconst defaultSort = {\n  prop: 'id',\n  order: 'ascending'\n};\nconst handleSortChange = ({ newIndex, oldIndex }: { newIndex: number, oldIndex: number }) => {\n  const movedItem = processedData1.value.splice(oldIndex, 1)[0];\n  processedData1.value.splice(newIndex, 0, movedItem);\n};\nconst handleSelectionChange2 = (selection) => {\n  selectedData2.value = []\n  selectedData2.value = selection;\n};\nconst processedData2 = computed(() => {\n  return selectedData2.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\nconst handleSelectionChange3 = (selection) => {\n  selectedData3.value = []\n  selectedData3.value = selection;\n};\nconst processedData3 = computed(() => {\n  return selectedData3.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\nconst handleSelectionChange4 = (selection) => {\n  selectedData4.value = []\n  selectedData4.value = selection;\n};\nconst processedData4 = computed(() => {\n  return selectedData4.value.map((item, index) => {\n    return { ...item, id: index + 1 };\n  });\n});\n\n\nlet mrTableData = reactive([]);\nconst submitForm = () => {\n  dialogVisible.value = false;\n  fullscreenLoadingMR.value = true;\n  const mergedArray = selectedData1.value.concat(selectedData2.value, selectedData3.value, selectedData4.value);\n  const uniqueArray = Array.from(new Set(mergedArray));\n\n  const newArray = uniqueArray.map(item => item.name);\n  console.log(newArray);\n  const newData = {\n    \"services_list\": newArray,\n    \"title\": form.name,\n    \"if_merge\": form.merge\n  };\n  console.log(newData);\n  let data_fin = newMerge(newData);\n\n  data_fin.then((result) => {\n    console.log(result['repo']);\n    for (let i in result['repo']) {\n      console.log(i);\n      console.log(result['repo'][i])\n      mrTableData.push({\n        repo: result['repo'][i][\"repo\"],\n        url: result['repo'][i][\"url\"],\n        status: result['repo'][i][\"status\"],\n      });\n\n    }\n  });\n  fullscreenLoadingMR.value = false;\n  console.log(mrTableData)\n};\n\n// 添加检查服务是否被正确分组的函数\nfunction checkServiceGrouping(service) {\n  if (fe_services.includes(service)) {\n    // 如果是前端服务，应该在FE组里\n    const feGroupKeys = feGroup.map(obj => Object.keys(obj)).flat();\n    return feGroupKeys.includes(service);\n  } else if (be_services.includes(service)) {\n    // 如果是后端服务，应该在平台BE1组、平台BE2组或功能BE组里\n    const pingGroupAKeys = pingGroupA.map(obj => Object.keys(obj)).flat();\n    const pingGroupBKeys = pingGroupB.map(obj => Object.keys(obj)).flat();\n    const featureGroupKeys = featureGroup.map(obj => Object.keys(obj)).flat();\n    return pingGroupAKeys.includes(service) || pingGroupBKeys.includes(service) || featureGroupKeys.includes(service);\n  }\n  return false; // 如果既不是前端也不是后端服务，则认为没有分组\n}\n\n// 在处理服务数据后，添加计算未分组服务的代码\nif (fe_services.length !== 0) {\n  showfe.value = true\n}\nif (be_services.length !== 0) {\n  showbe.value = true\n}\n// 计算未被正确分组的服务\nconst ungroupedServices = [];\nall_services.forEach(service => {\n  if (!checkServiceGrouping(service)) {\n    ungroupedServices.push(service);\n  }\n});\nconsole.log(\"未正确分组的服务:\", ungroupedServices);\nconsole.log(fe_services);\nconsole.log(be_services);\n\n</script>\n\n<style scoped>\na {\n  text-decoration: none;\n}\n\n.index-container {\n  height: 100%;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n}\n\ndiv {\n  font-size: 12px;\n  margin: 5px;\n  border: 1px;\n  padding: 0;\n}\n\n</style>\n\n<style>\n.el-table .warning-row {\n  --el-table-tr-bg-color: var(--el-color-warning-light-9);\n}\n\n.el-table .success-row {\n  --el-table-tr-bg-color: var(--el-color-success-light-9);\n}\n\n.el-table .cell {\n  white-space: pre-wrap !important;\n}\n\n.table-header {\n  background-color: blue;\n  color: white;\n}\n</style>\n<style scoped>\n.n-gradient-text {\n  font-size: 24px;\n}\n</style>\n\n<style scoped>\n.scrollbar-fe-item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 30px;\n  margin: 10px; /* 删除默认外边距 */\n\n  text-align: center;\n  border-radius: 4px;\n  background: var(--el-color-success-light-9);\n  color: var(--el-color-success);\n}\n\n.scrollbar-be-item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 30px;\n  margin: 10px; /* 删除默认外边距 */\n  text-align: center;\n  border-radius: 4px;\n  background: var(--el-color-primary-light-9);\n  color: var(--el-color-primary);\n}\n\n.ml-2 {\n\n  margin: 10px; /* 添加10像素的间距 */\n\n}\n</style>\n\n\n<style scoped>\n.data-list {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.data-item {\n  width: calc(33.33% - 5px); /* 将元素宽度从原来的 calc(33.33% - 10px) 调整为 calc(33.33% - 5px) */\n  margin-bottom: 20px;\n  border-radius: 5px;\n  padding: 10px;\n  background-color: #f5f5f5;\n}\n\n.data-item__title {\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.data-item__content {\n  color: #666;\n}\n</style>\n\n<style scoped>\n.itxst {\n  width: 600px;\n  display: flex;\n}\n\n.itxst > div:nth-of-type(1) {\n  flex: 1;\n}\n\n.itxst > div:nth-of-type(2) {\n  width: 270px;\n  padding-left: 20px;\n}\n\n.item {\n  border: solid 1px #eee;\n  padding: 6px 10px;\n  text-align: left;\n}\n\n.item:hover {\n  cursor: move;\n}\n\n.item + .item {\n  margin-top: 10px;\n}\n\n.ghost {\n  border: solid 1px rgb(19, 41, 239);\n}\n\n.chosenClass {\n  background-color: #f1f1f1;\n}\n</style>\n\n<style>\n.el-popper.is-customized {\n  /* Set padding to ensure the height is 32px */\n  padding: 6px 12px;\n  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));\n}\n\n.el-popper.is-customized .el-popper__arrow::before {\n  background: linear-gradient(45deg, #b2e68d, #bce689);\n  right: 0;\n}\n\n.el-card + .el-card {\n  margin-top: 20px;\n}\n\n\n</style>\n<style>\n.ar-container {\n  display: flex;\n  align-items: center;\n\n}\n\n</style>\n\n<style>\n.Epic-icon {\n  background-image: url('@/icons/svg/epic.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.ST-icon {\n  background-image: url('@/icons/svg/sub-task.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.Bug-icon {\n  background-image: url('@/icons/svg/bug.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.Story-icon {\n  background-image: url('@/icons/svg/story.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n\n.Task-icon {\n  background-image: url('@/icons/svg/task.svg');\n  width: 16px !important;\n  height: 16px !important;\n}\n</style>\n\n<!--<style>-->\n<!--//.el-table th {-->\n<!--//  background-color: #cacfd7 !important; /* 设置表头背景色 */-->\n<!--//  color: #333 !important; /* 设置表头字体颜色 */-->\n<!--//}-->\n\n<!--</style>-->\n<style>\n.to-do-text {\n  background-color: #42526e;\n  border-color: #42526e;\n  color: #fff;\n}\n\n.done-text {\n  background-color: #00875a;\n  border-color: #00875a;\n  color: #fff;\n}\n\n.doing-text {\n  background-color: #0052cc;\n  border-color: #0052cc;\n  color: #fff;\n}\n\n.delivering-text {\n  background-color: #0052cc;\n  border-color: #0052cc;\n  color: #fff;\n}\n\n.developing-text {\n  background-color: #0052cc;\n  border-color: #0052cc;\n  color: #fff;\n}\n\n.waiting-text {\n  background-color: #42526e;\n  border-color: #42526e;\n  color: #fff;\n}\n\n.bold-text {\n  font-weight: bold;\n}\n</style>\n\n<style scoped>\n/* ... existing code ... */\n\n.el-radio-group {\n  margin-bottom: 10px;\n}\n\n@media (min-width: 768px) {\n  .ar-container {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    gap: 10px;\n  }\n  \n  .el-radio-group {\n    margin-bottom: 0;\n  }\n}\n</style>\n\n"], "names": ["dialogVisible", "ref", "fullscreenLoadingMR", "centerDialogVisible", "fe_services", "reactive", "be_services", "all_services", "pingGroupA", "pingGroupB", "featureGroup", "feGroup", "openKB", "window", "open", "IN_pingGroupA", "computed", "result", "pingGroupAKeys", "map", "obj", "Object", "keys", "flat", "item", "includes", "link", "find", "name", "values", "IN_pingGroupB", "pingGroupBKeys", "IN_featureGroup", "featureGroupKeys", "IN_feGroup", "beGroupKeys", "services_pane", "visible", "show", "showfe", "showbe", "showresult", "active", "releaseTypeFilter", "localStorage", "getItem", "filteredProjects", "onMounted", "savedFilter", "value", "handleReleaseTypeChange", "setItem", "filterProjects", "length", "selectClosestRelease", "selectedProject", "releaseTableData", "splice", "currentDate", "Date", "setHours", "closestDate", "closestProject", "console", "log", "for<PERSON>ach", "project", "title", "match", "dateString", "year", "month", "day", "twoDigitYear", "parseInt", "slice", "date", "isNaN", "getTime", "e", "message", "projectTypeFilter", "projects", "filter", "matchesType", "toLowerCase", "matchesProject", "key", "startsWith", "fullscreenLoading", "filterType", "row", "type", "selectedRelease", "callseatalk", "tab", "event", "fin_data", "jira_title", "seatalk", "ElMessage", "duration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "callMRseatalkFE", "callMRseatalk", "async", "getreleaseData", "releaseTableDataIds", "Set", "jira_key", "temp_data", "send_title", "data", "has", "push", "add", "refreshData", "data_final", "autochecknewdata", "releaseDataLength", "index", "result_all", "signoff_status", "sign_off", "config_center", "Code_Merged", "shopee_region", "region", "redis_check", "redis_change", "DB_Change", "merge_list", "status", "dev_pic", "PM", "qa_pic", "services", "services_list", "services_list_be", "service", "services_list_fe", "getNewData", "getColorName", "getBigName", "savedReleaseType", "savedProjectType", "releaseData", "get_key_jira_release_list", "savedOption", "saveactive", "document", "body", "innerHTML", "trim", "emptyElement", "querySelector", "style", "display", "form", "merge", "watch", "newValue", "oldValue", "autocheckdata", "itemA", "matchedItemB", "itemB", "feature_key", "bug_resolved", "Number", "bug_total", "jira_link", "feature_title", "getData", "refParam", "modifiedString", "replace", "removeReleasePrefix", "flush", "JSON", "stringify", "some", "selectedData1", "selectedData2", "selectedData3", "selectedData4", "handleSelectionChange1", "selection", "processedData1", "id", "handleSelectionChange2", "handleSelectionChange3", "handleSelectionChange4", "mrTableData", "submitForm", "mergedArray", "concat", "newArray", "Array", "from", "newData", "if_merge", "newMerge", "then", "i", "repo", "url", "checkServiceGrouping", "ungroupedServices", "copymsg", "navigator", "clipboard", "writeText", "catch", "error", "mr_seatalk_single_feature_msg", "props", "start_single_ar"], "mappings": "m/CAssBA,MAAAA,EAAAC,GAAA,GAEAC,EAAAD,GAAA,GAEAA,GAAA,GACA,MAAAE,EAAAF,GAAA,GACAA,EAAA,SAIA,MAAAG,GAAAC,EAAA,IACAC,GAAAD,EAAA,IACAE,GAAAF,EAAA,IAGAG,GAAAH,EAAA,CAA4B,CAC1B,wBAAA,qHAC2B,uBAAA,uHACD,gCAAA,gIACS,oCAAA,iIACI,8BAAA,2HACN,8BAAA,2HACA,2BAAA,wHACH,0BAAA,uHACD,wBAAA,qHACF,yBAAA,sHACC,oBAAA,iHACL,+BAAA,4HACW,qCAAA,kIACM,kCAAA,sIACH,gCAAA,oIACF,qCAAA,yIACK,qBAAA,kHAChB,2BAAA,wHACM,0BAAA,uHACD,sCAAA,oIACY,0CAAA,wIACI,oCAAA,kIACN,oCAAA,kIACA,iCAAA,+HACH,8BAAA,4HACH,+BA<PERSON>,6HACC,0BAAA,wHACL,qCAAA,mIACW,2BAAA,yHACV,wCAAA,sIACa,uCAAA,qIACD,mCAAA,iIACJ,2CAAA,yIACO,8BAAA,2HACb,8BAAA,wHACA,6BAAA,sHACD,oCAAA,gIAKjCI,GAAAJ,EAAA,CAAA,CAA6B,8BAAA,2HACI,0BAAA,sHACL,8BAAA,0HACI,6BAAA,yHACD,8BAAA,0HACC,mCAAA,kIACM,sCAAA,mIACG,iCAAA,8HACL,yBAAA,sHACR,0BAAA,uHACC,+BAAA,4HACK,+BAAA,4HACA,gCAAA,6HACC,+BAAA,6HACD,mCAAA,iIACI,mCAAA,iIACA,iCAAA,mIACF,4CAAA,0IACW,uCAAA,qIACL,8BAAA,4HACT,+BAAA,6HACC,6BAAA,2HACF,mCAAA,iIACM,4CAAA,0IACS,+BAAA,6HACb,6BAAA,yHACF,2BAAA,uHACF,iCAAA,6HACM,0CAAA,sIACS,yCAAA,uIACF,iCAAA,+HACR,+BAAA,6HACF,oCAAA,kIACK,mCAAA,iIACD,0BAAA,uHACT,gCAAA,gIAK5BK,GAAAL,EAAA,CAAA,CAA+B,qBAAA,kHACP,8BAAA,2HACS,kCAAA,gIACI,gCAAA,8HACF,iCAAA,+HACC,wCAAA,sIACO,2BAAA,uHACb,yBAAA,qHACF,kCAAA,8HACS,8BAAA,0HACJ,iCAAA,gIAIjCM,GAAAN,EAAA,CAAyB,CACvB,wCAAA,oIAC2C,gCAAA,4HACR,iCAAA,6HACC,6BAAA,6HACJ,mCAAA,gIACM,iCAAA,8HACF,+BAAA,4HACF,4BAAA,yHACH,iCAAA,8HACK,6CAAA,2IACY,uCAAA,qIACN,mCAAA,iIACJ,6CAAA,2IACU,yCAAA,uIACJ,kCAAA,gIACP,+CAAA,6IACa,yCAAA,uIACN,yCAAA,uIACA,8BAAA,4HACX,gDAAA,8IACkB,0CAAA,wIACN,qCAAA,mIACL,uBAAA,wHACd,mCAAA,+HACY,8BAAA,0HACL,mBAAA,gIACZ,uCAAA,0IACoB,qDAAA,2IACc,wCAAA,2IACb,0BAAA,4IACd,+CAAA,mJACqB,mCAAA,qIACZ,4CAAA,qJACS,2BAAA,8HAOhDO,GAAA,KACEC,OAAAC,KAAA,2HAAA,SAAA,EAGFC,GAAAC,GAAA,KACE,MAAAC,EAAA,CAAA,EACAC,EAAAV,GAAAW,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAAC,KAAAlB,GACE,GAAAY,EAAAO,SAAAD,GAAA,CACE,MAAAE,EAAAlB,GAAAmB,MAAAP,GAAAC,OAAAC,KAAAF,GAAAK,SAAAD,KAAAA,GACAP,EAAAO,GAAA,CAAeI,KAAAJ,EACPE,OAER,CAGJ,OAAAL,OAAAQ,OAAAZ,EAAA,IAEFa,GAAAd,GAAA,KACE,MAAAC,EAAA,CAAA,EACAc,EAAAtB,GAAAU,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAAC,KAAAlB,GACE,GAAAyB,EAAAN,SAAAD,GAAA,CACE,MAAAE,EAAAjB,GAAAkB,MAAAP,GAAAC,OAAAC,KAAAF,GAAAK,SAAAD,KAAAA,GACAP,EAAAO,GAAA,CAAeI,KAAAJ,EACPE,OAER,CAGJ,OAAAL,OAAAQ,OAAAZ,EAAA,IAGFe,GAAAhB,GAAA,KACE,MAAAC,EAAA,CAAA,EACAgB,EAAAvB,GAAAS,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAAC,KAAAlB,GACE,GAAA2B,EAAAR,SAAAD,GAAA,CACE,MAAAE,EAAAhB,GAAAiB,MAAAP,GAAAC,OAAAC,KAAAF,GAAAK,SAAAD,KAAAA,GACAP,EAAAO,GAAA,CAAeI,KAAAJ,EACPE,OAER,CAGJ,OAAAL,OAAAQ,OAAAZ,EAAA,IAGFiB,GAAAlB,GAAA,KACE,MAAAC,EAAA,CAAA,EACAkB,EAAAxB,GAAAQ,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,IAAA,MAAAC,KAAApB,GACE,GAAA+B,EAAAV,SAAAD,GAAA,CACE,MAAAE,EAAAf,GAAAgB,MAAAP,GAAAC,OAAAC,KAAAF,GAAAK,SAAAD,KAAAA,GACAP,EAAAO,GAAA,CAAeI,KAAAJ,EACPE,OAER,CAGJ,OAAAL,OAAAQ,OAAAZ,EAAA,IAGF,IAAAmB,GAAAnC,GAAA,GACAoC,GAAApC,GAAA,GACAqC,GAAArC,GAAA,GACAsC,GAAAtC,GAAA,GACAuC,GAAAvC,GAAA,GACAwC,GAAAxC,GAAA,GACAyC,GAAAzC,EAAA,GACAA,EAAA,QACAA,EAAA,QAGA,MAAA0C,GAAA1C,EAAA2C,aAAAC,QAAA,sBAAA,OACAC,GAAA7C,EAAA,IAGA8C,GAAA,KACE,MAAAC,EAAAJ,aAAAC,QAAA,qBACAG,IACEL,GAAAM,MAAAD,EAA0B,IAK9B,MAAAE,GAAA,KAEEN,aAAAO,QAAA,oBAAAR,GAAAM,OACAG,KAGAN,GAAAG,MAAAI,OAAA,EACEC,MAGAC,GAAAN,MAAA,GACAO,GAAAC,OAAA,EAAAD,GAAAH,QAAkD,EAKtDC,GAAA,KACE,MAAAI,EAAA,IAAAC,KACAD,EAAAE,SAAA,EAAA,EAAA,EAAA,GACA,IAAAC,EAAA,KACAC,EAAA,KAEAC,QAAAC,IAAA,qBAAAlB,GAAAG,MAAAI,QAEAP,GAAAG,MAAAgB,SAAAC,IAEE,MAAAC,EAAAD,EAAAC,OAAA,GAIA,IAAAC,EAAAD,EAAAC,MAAA,kCAOA,GAJAA,IACEA,EAAAD,EAAAC,MAAA,mCAGFA,EAAA,CAEE,MAAAC,EAAAD,EAAA,GACA,IAAAE,EAAAC,EAAAC,EAGA,GAAA,IAAAH,EAAAhB,OAAA,CAEE,MAAAoB,EAAAC,SAAAL,EAAAM,MAAA,EAAA,IACAL,EAAAG,EAAA,GAAA,IAAAA,EAAA,KAAAA,EACAF,EAAAG,SAAAL,EAAAM,MAAA,EAAA,IAAA,EACAH,EAAAE,SAAAL,EAAAM,MAAA,EAAA,GAAqC,KAAA,IAAA,IAAAN,EAAAhB,OAQrC,YADAU,QAAAC,IAAA,cAAAK,KAJAC,EAAAI,SAAAL,EAAAM,MAAA,EAAA,IACAJ,EAAAG,SAAAL,EAAAM,MAAA,EAAA,IAAA,EACAH,EAAAE,SAAAL,EAAAM,MAAA,EAAA,GAGA,CAGF,IACE,MAAAC,EAAA,IAAAjB,KAAAW,EAAAC,EAAAC,EAAA,EAAA,EAAA,GAGAK,MAAAD,EAAAE,WAUEf,QAAAC,IAAA,SAAAM,KAAAC,EAAA,KAAAC,MATAT,QAAAC,IAAA,SAAAE,EAAAC,UAAAG,KAAAC,EAAA,KAAAC,WAAAI,SAAAlB,KAGAkB,GAAAlB,KAAAG,GAAAe,EAAAf,KACEA,EAAAe,EACAd,EAAAI,EACAH,QAAAC,IAAA,aAAAE,EAAAC,cAAAS,MAIJ,CAAA,MAAAG,GAEAhB,QAAAC,IAAA,WAAAe,EAAAC,UAAkC,CACpC,MAEAjB,QAAAC,IAAA,aAAAE,EAAAC,QAAwC,IAI5CL,GACEP,GAAAN,MAAAa,EAAAK,MACAJ,QAAAC,IAAA,aAAAT,GAAAN,QAKAc,QAAAC,IAAA,gBAA2B,EAK/BiB,GAAAhF,EAAA2C,aAAAC,QAAA,sBAAA,QAEAO,GAAA,KACEW,QAAAC,IAAA,iBAAArB,GAAAM,MAAA,QAAAgC,GAAAhC,OAEAiC,GAAA7B,SAEAP,GAAAG,MAAAiC,GAAAC,QAAAjB,IAEE,MAAAkB,EAAA,QAAAzC,GAAAM,OAAAiB,EAAAC,OAAAkB,cAAA5D,SAAAkB,GAAAM,MAAAoC,eAIAC,EAAApB,EAAAqB,KAAArB,EAAAqB,IAAAC,WAAAP,GAAAhC,OAEA,OAAAmC,GAAAE,CAAA,IAGFvB,QAAAC,IAAA,WAAAlB,GAAAG,MAAAI,QAGA,IAAAP,GAAAG,MAAAI,SACEE,GAAAN,MAAA,GAEAO,GAAAC,OAAA,EAAAD,GAAAH,SAAkD,EAUtDE,GAAAtD,IAEAwF,GAAAxF,GAAA,GAUAyF,GAAA,CAAAzC,EAAA0C,IACEA,EAAAC,OAAA3C,EAGF4C,GAAA5F,IACAiF,GAAA7E,EAAA,IAEA,IAAAmD,GAAAnD,EAAA,IAGA,MAAAyF,GAAA,CAAAC,EAAAC,KACE,IAAAC,EAAA,CAAeC,WAAA3C,GAAAN,OAGfkD,EAAAF,GACAG,EAAA,CAAUpB,QAAA,yCACCY,KAAA,UACHS,SAAA,KACI,EAIdC,GAAA,CAAAP,EAAAC,KACE,IAAAC,EAAA,CAAeC,WAAA3C,GAAAN,OAGfsD,EAAAN,GACAG,EAAA,CAAUpB,QAAA,0CACCY,KAAA,UACHS,SAAA,KACI,EAIdG,GAAA,CAAAT,EAAAC,KACE,IAAAC,EAAA,CAAeC,WAAA3C,GAAAN,OAIfwD,EAAAR,GACAG,EAAA,CAAUpB,QAAA,kCACCY,KAAA,UACHS,SAAA,KACI,EAmDdK,eAAAC,GAAA1D,GAEEc,QAAAC,IAAAR,IACA,MAAAoD,EAAA,IAAAC,IAAArD,GAAArC,KAAAK,GAAAA,EAAAsF,YAKAvG,GAAAkD,OAAA,EAAAlD,GAAA8C,QACA/C,GAAAmD,OAAA,EAAAnD,GAAA+C,QACAjD,GAAAqD,OAAA,EAAArD,GAAAiD,QACAU,QAAAC,IAAA,gBAAAf,KACA,IAIA8D,EAJAd,EAAA,CAAe9B,MAAAlB,GAKf8D,QAAAC,GAAAf,GAIA,IAAAc,EAAA1D,OACEG,GAAAC,OAAA,EAAAD,GAAAH,SAEAG,GAAAC,OAAA,EAAAD,GAAAH,QACA0D,EAAAE,KAAAhD,SAAAzC,IACEuC,QAAAC,IAAAxC,GACAoF,EAAAM,IAAA1F,EAAAsF,YACEtD,GAAA2D,KAAA3F,GACAoF,EAAAQ,IAAA5F,EAAAsF,UAAqC,KAQ3C/C,QAAAC,IAAAR,GAA4B,CAG9BkD,eAAAW,GAAApE,GACET,GAAAS,OAAA,EACAV,GAAAU,OAAA,EACAc,QAAAC,IAAAf,SAEA0D,GAAA1D,GACAwC,GAAAxC,OAAA,EAEAc,QAAAC,IAAAR,UAWFkD,eAAAzD,GACET,GAAAS,OAAA,EACAV,GAAAU,OAAA,EACAc,QAAAC,IAAAf,SACA0D,GAAA1D,GACAwC,GAAAxC,OAAA,EAEA,IAAAqE,EAAArH,IACA8D,QAAAC,IAAAR,IACA8D,EAAArE,YAAAsE,EAAA/D,IAEAiC,GAAAxC,OAAA,EAEAc,QAAAC,IAAAsD,GACA,MAAAE,EAAAhE,GAAAH,OACAiE,EAAArE,MAAAgE,KAAAtC,MAAA,EAAA6C,GAAAvD,SAAA,CAAAzC,EAAAiG,KACE1D,QAAAC,IAAAxC,EAAAkG,YAEAlG,EAAAmG,eAGEnE,GAAAiE,GAAAG,SAAApG,EAAAmG,eAFAnE,GAAAiE,GAAAG,SAAA,GAIFpG,EAAAqG,cAGErE,GAAAiE,GAAAI,cAAArG,EAAAqG,cAFArE,GAAAiE,GAAAI,cAAA,GAIFrG,EAAAsG,YAGEtE,GAAAiE,GAAAK,YAAAtG,EAAAsG,YAFAtE,GAAAiE,GAAAK,YAAA,GAIFtG,EAAAuG,cAGEvE,GAAAiE,GAAAO,OAAAxG,EAAAuG,cAFAvE,GAAAiE,GAAAO,OAAA,GAIFxG,EAAAyG,YAGEzE,GAAAiE,GAAAS,aAAA1G,EAAAyG,YAFAzE,GAAAiE,GAAAS,aAAA,GAIF1G,EAAA2G,UAGE3E,GAAAiE,GAAAU,UAAA3G,EAAA2G,UAFA3E,GAAAiE,GAAAU,UAAA,GAIF3G,EAAAP,OAGEuC,GAAAiE,GAAAxG,OAAAO,EAAAP,OAFAuC,GAAAiE,GAAAxG,OAAA,GAKFO,EAAA4G,WAGE5E,GAAAiE,GAAAW,WAAA5G,EAAA4G,WAFA5E,GAAAiE,GAAAW,WAAA,GAIF5G,EAAA6G,OAGE7E,GAAAiE,GAAAY,OAAA7G,EAAA6G,OAFA7E,GAAAiE,GAAAY,OAAA,GAIF7G,EAAA8G,QAGE9E,GAAAiE,GAAAa,QAAA9G,EAAA8G,QAFA9E,GAAAiE,GAAAa,QAAA,GAIF9G,EAAA+G,GAGE/E,GAAAiE,GAAAc,GAAA/G,EAAA+G,GAFA/E,GAAAiE,GAAAc,GAAA,GAIF/G,EAAAgH,OAGEhF,GAAAiE,GAAAe,OAAAhH,EAAAgH,OAFAhF,GAAAiE,GAAAe,OAAA,GAIFzE,QAAAC,IAAAxC,EAAAyG,aACAlE,QAAAC,IAAAR,IACAA,GAAAiE,GAAAgB,SAAA,GACAjH,EAAAkH,cAAAC,iBAAA1E,SAAA2E,IACE,KAAApF,GAAAiE,GAAAgB,SACEjF,GAAAiE,GAAAgB,UAAA,GAAAG,IAEApF,GAAAiE,GAAAgB,UAAA,KAAoCG,IAEtCtI,GAAAmB,SAAAmH,KACEtI,GAAA6G,KAAAyB,GACArI,GAAA4G,KAAAyB,GAAyB,IAG7BpH,EAAAkH,cAAAG,iBAAA5E,SAAA2E,IACE,KAAApF,GAAAiE,GAAAgB,SACEjF,GAAAiE,GAAAgB,UAAA,GAAAG,IAEApF,GAAAiE,GAAAgB,UAAA,KAAoCG,IAEtCxI,GAAAqB,SAAAmH,KACExI,GAAA+G,KAAAyB,GACArI,GAAA4G,KAAAyB,GAAyB,GAC3B,IAIJ,IAAAxI,GAAAiD,SACEd,GAAAU,OAAA,GAEF,IAAA3C,GAAA+C,SACEb,GAAAS,OAAA,GAEFc,QAAAC,IAAA5D,IACA2D,QAAAC,IAAA1D,IACAmC,GAAAQ,OAAA,CAAmB,CA5HnB6F,CAAA7F,GACAwC,GAAAxC,OAAA,EAEAmD,EAAA,CAAUpB,QAAA,QACCY,KAAA,UACHS,SAAA,KAEP,CAsRH,SAAA0C,GAAAV,GACE,MAAA,UAAAA,EACE,UAAO,SAAAA,EAEP,UAAO,YAAAA,EAEP,UAEA,SAeF,CAIF,SAAAW,GAAAX,GACE,MAAA,UAAAA,EACE,QAAO,SAAAA,EAEP,OAAO,YAAAA,EAEP,UAAO,WAAAA,EAEP,SAAO,UAAAA,EAEP,QAAO,QAAAA,EAEP,MAAO,eAAAA,EAEP,aAAO,eAAAA,EAEP,aAAO,YAAAA,EAEP,UAEAA,CACF,CA4BFtF,GAAA2D,UAEE,MAAAuC,EAAArG,aAAAC,QAAA,qBACAoG,IACEtG,GAAAM,MAAAgG,GAGF,MAAAC,EAAAtG,aAAAC,QAAA,qBACAqG,IACEjE,GAAAhC,MAAAiG,GAIF,MACAC,SADAC,KACAnC,KAAAA,KAGApB,GAAA5C,MAAAkG,EACAA,EAAAlF,SAAAzC,IACE0D,GAAAiC,KAAA3F,EAAA,IAIF4B,KAGA,MAAAiG,EAAAzG,aAAAC,QAAA,mBACAwG,GACEtF,QAAAC,IAAA,uBAAAqF,GACA9F,GAAAN,MAAAoG,GAIA/F,KAIF,MAAAgG,EAAA1G,aAAAC,QAAA,UAMA,GALAyG,IACE5G,GAAAO,MAAAyB,SAAA4E,IAIFC,SAAAC,KAAAC,UAAAC,OAAArG,OAAA,EAAA,CAEE,MAAAsG,EAAAJ,SAAAK,cAAA,aAGAD,IACEA,EAAAE,MAAAC,QAAA,OACF,KAIJ,MAAAC,GAAA1J,EAAA,CAAsBuB,KAAA,GACdoI,OAAA,IAIRC,EAAA1G,IAAA,CAAA2G,EAAAC,KAEED,GAAAA,IAAAC,IACEpG,QAAAC,IAAA,WAAAmG,QAAAD,aACA1G,GAAAC,OAAA,EAAAD,GAAAH,QArSJqD,eAAAzD,GACE,IAAAA,EAGE,OAFAc,QAAAC,IAAA,wBACAR,GAAAC,OAAA,EAAAD,GAAAH,QAIFb,GAAAS,OAAA,EACAV,GAAAU,OAAA,EACAc,QAAAC,IAAA,YAAAf,WACA0D,GAAA1D,GACAwC,GAAAxC,OAAA,EAEA,IAAAqE,EAAArH,IAKA,GAJA8D,QAAAC,IAAA,qBAAAR,IACA8D,EAAArE,YAAAmH,EAAA5G,IACAO,QAAAC,IAAA,SAAAsD,EAAArE,QAEAqE,EAAArE,QAAAqE,EAAArE,MAAAgE,KAGE,OAFAlD,QAAAC,IAAA,wBACAyB,GAAAxC,OAAA,GAIFwC,GAAAxC,OAAA,EACAO,GAAAH,OACAG,GAAAS,SAAAoG,IACE,MAAAC,EAAAhD,EAAArE,MAAAgE,KAAAtF,MAAA4I,GAAAF,EAAAvD,WAAAyD,EAAAC,cACAF,IACEvG,QAAAC,IAAA,SAAAsG,GAGAD,EAAAI,aAAAC,OAAAJ,EAAAG,cAAA,GACAJ,EAAAM,UAAAD,OAAAJ,EAAAK,WAAA,GACA5G,QAAAC,IAAA,UAAAqG,EAAAI,gBAAAJ,EAAAM,aAEAN,EAAAzE,KAAA0E,EAAA1E,KACAyE,EAAAvD,SAAAwD,EAAAE,YACAH,EAAAO,UAAA,iCAAAN,EAAAE,cACAH,EAAAnE,WAAAoE,EAAAO,cACAP,EAAA3C,eAGE0C,EAAAzC,SAAA0C,EAAA3C,eAFA0C,EAAAzC,SAAA,GAIF0C,EAAAzC,cAGEwC,EAAAxC,cAAAyC,EAAAzC,cAFAwC,EAAAxC,cAAA,GAIFyC,EAAAvC,cAGEsC,EAAArC,OAAAsC,EAAAvC,cAFAsC,EAAArC,OAAA,GAIFsC,EAAArC,YAGEoC,EAAAnC,aAAAoC,EAAArC,YAFAoC,EAAAnC,aAAA,GAIFoC,EAAArJ,OAGEoJ,EAAApJ,OAAAqJ,EAAArJ,OAFAoJ,EAAApJ,OAAA,GAKFqJ,EAAAlC,WAGEiC,EAAAjC,WAAAkC,EAAAlC,WAFAiC,EAAAjC,WAAA,GAIFkC,EAAAjC,OAGEgC,EAAAhC,OAAAiC,EAAAjC,OAFAgC,EAAAhC,OAAA,GAIFiC,EAAAxC,YAGEuC,EAAAvC,YAAAwC,EAAAxC,YAFAuC,EAAAvC,YAAA,GAIFwC,EAAAnC,UAGEkC,EAAAlC,UAAAmC,EAAAnC,UAFAkC,EAAAlC,UAAA,GAIFmC,EAAAhC,QAGE+B,EAAA/B,QAAAgC,EAAAhC,QAFA+B,EAAA/B,QAAA,GAIFgC,EAAA/B,GAGE8B,EAAA9B,GAAA+B,EAAA/B,GAFA8B,EAAA9B,GAAA,GAIF+B,EAAA9B,OAGE6B,EAAA7B,OAAA8B,EAAA9B,OAFA6B,EAAA7B,OAAA,GAIF6B,EAAA5B,SAAA,GACA6B,EAAA5B,cAAAC,iBAAA1E,SAAA2E,IACE,KAAAyB,EAAA5B,SACE4B,EAAA5B,UAAA,GAAAG,IAEAyB,EAAA5B,UAAA,KAAkBG,IAEpBtI,GAAAmB,SAAAmH,KACEtI,GAAA6G,KAAAyB,GACArI,GAAA4G,KAAAyB,GAAyB,IAI7B0B,EAAA5B,cAAAG,iBAAA5E,SAAA2E,IACE,KAAAyB,EAAA5B,SACE4B,EAAA5B,UAAA,GAAAG,IAEAyB,EAAA5B,UAAA,KAAkBG,IAEpBxI,GAAAqB,SAAAmH,KACExI,GAAA+G,KAAAyB,GACArI,GAAA4G,KAAAyB,GAAyB,IAE5B,IAIL,IAAAxI,GAAAiD,SACEd,GAAAU,OAAA,GAEF,IAAA3C,GAAA+C,SACEb,GAAAS,OAAA,GAEFc,QAAAC,IAAA,eAAA5D,IACA2D,QAAAC,IAAA,eAAA1D,IACAyD,QAAAC,IAAA,UAAAR,IACAf,GAAAQ,OAAA,CAAmB,CA6JjB6H,CAAAZ,IAIFA,GACEtH,aAAAO,QAAA,kBAAA+G,GAIFH,GAAAnI,KAkEF,SAAAmJ,GACE,GAAAA,EAAA,CACE,IAAAC,EAAAD,EAAAE,QAAA,iBAAA,IAAAA,QAAA,MAAA,IAEA,OADAlH,QAAAC,IAAAgH,GACAA,CAAO,CAET,MAAA,EAAO,CAxEPE,CAAAhB,EAAA,GAAwC,CAAAiB,MAAA,SAG1ClB,EAAAzG,IAAA,CAAA0G,EAAAC,KACEvH,aAAAO,QAAA,mBAAAiI,KAAAC,UAAA7H,KACA,IAAAA,GAAAH,SACEf,GAAAW,OAAA,GAEF,IAAAO,GAAAH,SACEf,GAAAW,OAAA,EAAa,IAKjBgH,EAAAhF,IAAAiF,IAEEtH,aAAAO,QAAA,oBAAA+G,GAGA9G,KAGAN,GAAAG,MAAAI,OAAA,EACEC,MAGAC,GAAAN,MAAA,GACAO,GAAAC,OAAA,EAAAD,GAAAH,QAAkD,IAItD4G,EAAA1J,IAAA,CAAA2J,EAAAC,KACE,IAAA5J,GAAA8C,SACEjB,GAAAa,OAAA,GAEF,IAAA1C,GAAA8C,SACEjB,GAAAa,OAAA,EAAsB,IAK1BgH,EAAAtH,IAAAuH,IAEEtH,aAAAO,QAAA,oBAAAR,GAAAM,OAGAG,KAGAN,GAAAG,MAAAI,OAAA,EACEC,MAGAC,GAAAN,MAAA,GACAO,GAAAC,OAAA,EAAAD,GAAAH,SAIFE,GAAAN,QAAAH,GAAAG,MAAAqI,MAAApH,GAAAA,EAAAC,QAAAZ,GAAAN,UAEEM,GAAAN,MAAA,GAEAO,GAAAC,OAAA,EAAAD,GAAAH,QAAkD,IAiBtD,MAAAkI,GAAAtL,EAAA,IAEAuL,GAAAvL,EAAA,IAEAwL,GAAAxL,EAAA,IAEAyL,GAAAzL,EAAA,IAGA0L,GAAAC,IACEL,GAAAtI,MAAA,GACAsI,GAAAtI,MAAA2I,EACA7H,QAAAC,IAAA6H,GAAA5I,OACAc,QAAAC,IAAAuH,GAAAtI,MAAA,EAEF4I,GAAA7K,GAAA,IACEuK,GAAAtI,MAAA9B,KAAA,CAAAK,EAAAiG,KACE,IAAAjG,EAAAsK,GAAArE,EAAA,QAYJsE,GAAAH,IACEJ,GAAAvI,MAAA,GACAuI,GAAAvI,MAAA2I,CAAA,EAEF5K,GAAA,IACEwK,GAAAvI,MAAA9B,KAAA,CAAAK,EAAAiG,KACE,IAAAjG,EAAAsK,GAAArE,EAAA,QAGJ,MAAAuE,GAAAJ,IACEH,GAAAxI,MAAA,GACAwI,GAAAxI,MAAA2I,CAAA,EAEF5K,GAAA,IACEyK,GAAAxI,MAAA9B,KAAA,CAAAK,EAAAiG,KACE,IAAAjG,EAAAsK,GAAArE,EAAA,QAGJ,MAAAwE,GAAAL,IACEF,GAAAzI,MAAA,GACAyI,GAAAzI,MAAA2I,CAAA,EAEF5K,GAAA,IACE0K,GAAAzI,MAAA9B,KAAA,CAAAK,EAAAiG,KACE,IAAAjG,EAAAsK,GAAArE,EAAA,QAKJ,IAAAyE,GAAA7L,EAAA,IACA,MAAA8L,GAAA,KACEnM,EAAAiD,OAAA,EACA/C,EAAA+C,OAAA,EACA,MAAAmJ,EAAAb,GAAAtI,MAAAoJ,OAAAb,GAAAvI,MAAAwI,GAAAxI,MAAAyI,GAAAzI,OAGAqJ,EAFAC,MAAAC,KAAA,IAAA3F,IAAAuF,IAEAjL,KAAAK,GAAAA,EAAAI,OACAmC,QAAAC,IAAAsI,GACA,MAAAG,EAAA,CAAgB/D,cAAA4D,EACGnI,MAAA4F,GAAAnI,KACH8K,SAAA3C,GAAAC,OAGhBjG,QAAAC,IAAAyI,GACAE,EAAAF,GAEAG,MAAA3L,IACE8C,QAAAC,IAAA/C,EAAA,MACA,IAAA,IAAA4L,KAAA5L,EAAA,KACE8C,QAAAC,IAAA6I,GACA9I,QAAAC,IAAA/C,EAAA,KAAA4L,IACAX,GAAA/E,KAAA,CAAiB2F,KAAA7L,EAAA,KAAA4L,GAAA,KACeE,IAAA9L,EAAA,KAAA4L,GAAA,IACFxE,OAAApH,EAAA,KAAA4L,GAAA,QAE7B,IAIL3M,EAAA+C,OAAA,EACAc,QAAAC,IAAAkI,GAAA,EAIF,SAAAc,GAAApE,GACE,GAAAxI,GAAAqB,SAAAmH,GAAA,CAGE,OADAjI,GAAAQ,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACAE,SAAAmH,EAAmC,CAAA,GAAAtI,GAAAmB,SAAAmH,GAAA,CAGnC,MAAA1H,EAAAV,GAAAW,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACAQ,EAAAtB,GAAAU,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACAU,EAAAvB,GAAAS,KAAAC,GAAAC,OAAAC,KAAAF,KAAAG,OACA,OAAAL,EAAAO,SAAAmH,IAAA7G,EAAAN,SAAAmH,IAAA3G,EAAAR,SAAAmH,EAAgH,CAElH,OAAA,CAAO,CAIT,IAAAxI,GAAAiD,SACEd,GAAAU,OAAA,GAEF,IAAA3C,GAAA+C,SACEb,GAAAS,OAAA,GAGF,MAAAgK,GAAA,UACA1M,GAAA0D,SAAA2E,IACEoE,GAAApE,IACEqE,GAAA9F,KAAAyB,EAA8B,IAGlC7E,QAAAC,IAAA,YAAAiJ,IACAlJ,QAAAC,IAAA5D,IACA2D,QAAAC,IAAA1D,m0HA9WAsF,SACE,SAAAA,EACE,YAAO,aAAAA,EAEP,UAAO,SAAAA,EAEP,YAAO,QAAAA,EAEP,WAAO,UAAAA,EAEP,kBAFO,wBARX,IAAAA,kmEA/VAc,eAAAf,GACE,IAAA2B,EAAArH,IACesD,GAAAN,MAGfqE,QAAA4F,EAAAvH,EAAAA,KAEA5B,QAAAC,IAAAsD,GACA6F,UAAAC,UAAAC,UAAA/F,EAAAL,MAAA2F,MAAA,KAEMxG,EAAA,CAAUpB,QAAA,kBACCY,KAAA,WACH,IACP0H,OAAAC,IAIDnH,EAAAmH,MAAA,WAAA,GACD,wVAlCP7G,eAAAf,GACE,IAAA2B,EAAArH,IACAqH,QAAAkG,EAAA7H,EAAAA,KACA5B,QAAAC,IAAAsD,GACA6F,UAAAC,UAAAC,UAAA/F,EAAAL,MAAA2F,MAAA,KAEMxG,EAAA,CAAUpB,QAAA,sBACCY,KAAA,WACH,IACP0H,OAAAC,IAGDnH,EAAAmH,MAAA,YAAA,GACD,2bAwBP7G,eAAA+G,GAEE1J,QAAAC,IAAAyJ,EAAA9H,KAGA1F,UACAyN,EAAAD,EAAA9H,IAA4C,iqHAsY9C0C,WACE,UAAAA,EACE,OAAO,SAAAA,EAEP,UAAO,YAAAA,EAEP,OAAO,WAAAA,EAEP,SAAO,UAAAA,EAEP,QAAO,QAAAA,EAEP,MAAO,eAAAA,EAEP,aAAO,eAAAA,EAEP,aAAO,YAAAA,EAEP,eAFO,6FAhBX,IAAAA"}
import{H as e,e as a,p as t,J as l,a as r,L as s,a3 as n,o as d,C as o,w as i,v as u,l as p,j as c,x as f,t as _,a4 as m,a5 as h,h as b,F as w,r as y,a6 as g,a7 as A,a8 as T,a9 as v,a1 as G,a2 as x}from"./index-awKTxnvj.js";import{E as k,b as C,a as I}from"./index-ENIpyTzl.js";import{E as D}from"./index-BWOrXwLB.js";import{E}from"./index-DeelQlL1.js";import"./index-TjDDNAcU.js";const V={class:"index-conntainer"},M={class:"ar-container"},S=e(a({__name:"monitor",props:{newTableData:{type:Array,required:!0}},setup(e){const a=t("first"),S=(e,a)=>{console.log(e,a)};t(1),t("");const j=t("");t(0),t(10),t(!0),t([]),t("chatbot");const U=l([]),R=l([]);l(["chatbot","inhouse","channel","data"]);const F=new Set,L=l([]),$=t(),z=l([]),q=l([]),H=r((()=>j.value?z.filter((e=>e.TAG===j.value)):z)),J=(e,a,t)=>a[t.property]===e,B=({row:e})=>"FAILURE"===e.build_result?"warning-row":"";const K=async()=>{const e=(await T()).data.data;q.splice(0,q.length),z.splice(0,z.length),L.splice(0,L.length),L.push(...e);const a=function(e){const a=l([]),t=new Map;for(let l=0;l<e.length;l++){const a=e[l],{pipeline_name:r,end_time:s,build_result:n}=a,d=t.get(r);(!d||new Date(s)>new Date(d))&&t.set(r,s)}for(let l=0;l<e.length;l++){const r=e[l],{pipeline_name:s,end_time:n,build_result:d}=r;"FAILURE"===d&&n===t.get(s)&&a.push(r)}return a}(L);q.push(...a),L.forEach(((e,a)=>{e.TAG&&!F.has(e.TAG)&&(F.add(e.TAG),U.push({text:e.TAG,value:e.TAG}),R.push(e.TAG));const t={pipeline_name:e.pipeline_name,build_type:e.build_type,duration:e.duration,CID:e.CID,start_time:e.start_time,end_time:e.end_time,TAG:e.TAG,space_link:e.space_link,executor:e.executor,build_result:e.build_result,err_msg:e.err_msg,index:e.index};e.TAG&&!F.has(e.TAG)&&(F.add(e.TAG),U.push({text:e.TAG,value:e.TAG}),R.push(e.TAG)),z.push(t)}))},N=t(null);s((async()=>{K();const e=setInterval(K,6e4);N.value=e})),n((()=>{clearInterval(N.value)}));const O=e;function P(){return`hsl(${Math.floor(360*Math.random())}, ${Math.floor(30*Math.random())+70}%, ${Math.floor(20*Math.random())+70}%)`}t({}),r((()=>{const e={};for(const a of O.newTableData){const t=a.start_time.split(" ")[0];if(console.log(t),!e[t]){const a=P();e[t]=a}}return e}));const Q=({row:e})=>parseInt(e.index)%2==0?"success-row":"primary-row";return(e,t)=>{const l=D,r=v,s=E,n=G,T=x;return d(),o(T,{modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=e=>a.value=e),class:"demo-tabs",onTabClick:S,type:"border-card"},{default:i((()=>[u(n,{label:"服务",name:"first"},{default:i((()=>[p("div",V,[u(c(k),{shadow:"hover"},{default:i((()=>[u(c(C),{ref_key:"tableRef",ref:$,data:q,columns:e.tableColumns,"row-class-name":B,style:{width:"auto",height:"100%"},border:"","header-cell-style":{background:"#ee4d2d",color:"#f3f1f6"}},{default:i((()=>[u(c(I),{prop:"start_time",label:"部署生效时间",sortable:"","default-sort":{prop:"start_time",order:"descending"}}),u(c(I),{prop:"end_time",label:"任务结束时间",sortable:"","default-sort":{prop:"end_time",order:"descending"}}),u(c(I),{prop:"pipeline_name",label:"服务","min-width":200,style:{"white-space":"nowrap","min-width":0}},{default:i((({row:e})=>[u(l,{underline:!1,href:e.space_link,target:"_blank",type:"primary"},{default:i((()=>[f(_(e.pipeline_name),1)])),_:2},1032,["href"])])),_:1}),u(c(I),{prop:"duration",label:"部署间隔","min-width":60,"header-":""}),u(c(I),{prop:"build_type",label:"类型","min-width":55,"header-":""},{default:i((({row:e})=>[u(c(m),{type:"灰度发布"===e.build_type?"info":"全量发布"===e.build_type?"success":"warning"},{default:i((()=>[f(_(e.build_type),1)])),_:2},1032,["type"])])),_:1}),u(c(I),{prop:"build_result",label:"部署结果","min-width":50,"header-":""},{default:i((({row:e})=>[u(c(m),{type:"SUCCESS"===e.build_result?"success":"danger"},{default:i((()=>[f(_(e.build_result),1)])),_:2},1032,["type"])])),_:1}),u(c(I),{prop:"CID",label:"部署地区","header-":""}),u(c(I),{prop:"TAG",label:"TAG",filters:U,"filter-method":J,"filtered-value":e.filteredValue,"filter-placement":"bottom-end","header-":""},{default:i((({row:e})=>[u(c(m),{key:"string",type:"warning",class:"mx-1",effect:"light"},{default:i((()=>[f(_(e.TAG),1)])),_:2},1024)])),_:1},8,["filters","filtered-value"]),u(c(I),{prop:"executor",label:"部署触发人","min-width":65,"header-":""})])),_:1},8,["data","columns"]),p("div",M,[u(c(h),{modelValue:j.value,"onUpdate:modelValue":t[0]||(t[0]=e=>j.value=e),filterable:"",placeholder:"请选择TAG来进行筛选",clearable:""},{default:i((()=>[(d(!0),b(w,null,y(R,(e=>(d(),o(c(g),{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),u(r,{size:20},{default:i((()=>[u(c(A))])),_:1}),u(s,{class:"mx-1",type:"danger",size:"default"},{default:i((()=>[f("注意，同一天发布的服务背景色是统一的")])),_:1})]),u(c(C),{ref_key:"tableRef",ref:$,data:H.value,columns:e.tableColumns,"row-class-name":Q,style:{width:"auto",height:"100%"},border:"","header-cell-style":{background:"#eef1f6",color:"#606266"}},{default:i((()=>[u(c(I),{prop:"start_time",label:"部署生效时间",sortable:"","default-sort":{prop:"start_time",order:"descending"}}),u(c(I),{prop:"end_time",label:"任务结束时间",sortable:"","default-sort":{prop:"end_time",order:"descending"}}),u(c(I),{prop:"pipeline_name",label:"服务","min-width":200,style:{"white-space":"nowrap","min-width":0}},{default:i((({row:e})=>[u(l,{underline:!1,href:e.space_link,target:"_blank",type:"primary"},{default:i((()=>[f(_(e.pipeline_name),1)])),_:2},1032,["href"])])),_:1}),u(c(I),{prop:"duration",label:"部署间隔","min-width":60,"header-":""}),u(c(I),{prop:"build_type",label:"类型","min-width":55,"header-":""},{default:i((({row:e})=>[u(c(m),{type:"灰度发布"===e.build_type?"info":"全量发布"===e.build_type?"success":"warning"},{default:i((()=>[f(_(e.build_type),1)])),_:2},1032,["type"])])),_:1}),u(c(I),{prop:"build_result",label:"部署结果","min-width":50,"header-":""},{default:i((({row:e})=>[u(c(m),{type:"SUCCESS"===e.build_result?"success":"danger"},{default:i((()=>[f(_(e.build_result),1)])),_:2},1032,["type"])])),_:1}),u(c(I),{prop:"CID",label:"部署地区","header-":""}),u(c(I),{prop:"TAG",label:"TAG",filters:U,"filter-method":J,"filtered-value":e.filteredValue,"filter-placement":"bottom-end","header-":""},{default:i((({row:e})=>[u(c(m),{key:"string",type:"warning",class:"mx-1",effect:"light"},{default:i((()=>[f(_(e.TAG),1)])),_:2},1024)])),_:1},8,["filters","filtered-value"]),u(c(I),{prop:"executor",label:"部署触发人","min-width":65,"header-":""})])),_:1},8,["data","columns"])])),_:1})])])),_:1}),u(n,{label:"配置",name:"second"})])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-58091246"]]);export{S as default};
//# sourceMappingURL=monitor-CuRbzCSb.js.map

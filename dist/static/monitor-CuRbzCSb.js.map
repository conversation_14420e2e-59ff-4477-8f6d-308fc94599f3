{"version": 3, "file": "monitor-CuRbzCSb.js", "sources": ["../../src/views/monitor/monitor.vue"], "sourcesContent": ["<template>\n  <el-tabs v-model=\"activeName\" class=\"demo-tabs\" @tab-click=\"handleClick\" type=\"border-card\">\n    <el-tab-pane label=\"服务\" name=\"first\">\n      <div class=\"index-conntainer\">\n\n        <!--        <el-card class=\"card\" shadow=\"hover\" width=\"50px\" height=\"50px\">-->\n\n        <!--&lt;!&ndash;          <el-select v-model=\"selectedProject\" placeholder=\"请选择项目组\">&ndash;&gt;-->\n        <!--&lt;!&ndash;            <el-option&ndash;&gt;-->\n        <!--&lt;!&ndash;                v-for=\"project in projects\"&ndash;&gt;-->\n        <!--&lt;!&ndash;                :key=\"project\"&ndash;&gt;-->\n        <!--&lt;!&ndash;                :label=\"project\"&ndash;&gt;-->\n        <!--&lt;!&ndash;                :value=\"project\"&ndash;&gt;-->\n        <!--&lt;!&ndash;            ></el-option>&ndash;&gt;-->\n        <!--&lt;!&ndash;          </el-select>&ndash;&gt;-->\n        <!--          <el-table-->\n        <!--              ref=\"tableRef\"-->\n        <!--              :data=\"failTableData\"-->\n        <!--              :columns=\"tableColumns\"-->\n        <!--              :row-class-name=\"tableRowClassName\"-->\n        <!--              style=\"width: auto; height: 100%\"-->\n        <!--              border-->\n        <!--              :header-cell-style=\"{background:'#ee4d2d',color:'#f3f1f6'}\"-->\n        <!--          >-->\n        <!--            <el-table-column-->\n        <!--                prop=\"start_time\"-->\n        <!--                label=\"部署生效时间\"-->\n        <!--                sortable-->\n\n        <!--                :default-sort=\"{ prop: 'start_time', order: 'descending' }\"-->\n        <!--            >-->\n        <!--            </el-table-column>-->\n        <!--            <el-table-column-->\n        <!--                prop=\"end_time\"-->\n        <!--                label=\"任务结束时间\"-->\n        <!--                sortable-->\n        <!--                :default-sort=\"{ prop: 'end_time', order: 'descending' }\"-->\n        <!--            >-->\n        <!--            </el-table-column>-->\n        <!--            <el-table-column-->\n        <!--                prop=\"pipeline_name\"-->\n        <!--                label=\"服务\"-->\n        <!--                :min-width=\"200\"-->\n        <!--                :style=\"{ 'white-space': 'nowrap', 'min-width': 0 }\"-->\n        <!--            >-->\n        <!--              <template #default=\"{ row }\">-->\n        <!--                <el-link-->\n        <!--                    :underline=\"false\"-->\n        <!--                    v-bind:href=\"row.space_link\"-->\n        <!--                    target=\"_blank\"-->\n        <!--                    type=\"primary\">-->\n        <!--                  {{ row.pipeline_name }}-->\n        <!--                </el-link>-->\n        <!--              </template>-->\n        <!--            </el-table-column>-->\n        <!--            <el-table-column prop=\"duration\" label=\"部署间隔\" :min-width=\"60\" header-></el-table-column>-->\n        <!--            <el-table-column prop=\"build_type\" label=\"类型\" :min-width=\"55\" header->-->\n        <!--              <template #default=\"{ row }\">-->\n        <!--                <el-tag-->\n        <!--                    :type=\"row.build_type === '灰度发布' ? 'info' : row.build_type === '全量发布' ? 'success' : 'warning'\">-->\n        <!--                  {{ row.build_type }}-->\n        <!--                </el-tag>-->\n        <!--              </template>-->\n        <!--            </el-table-column>-->\n        <!--            <el-table-column prop=\"build_result\" label=\"部署结果\" :min-width=\"50\" header->-->\n        <!--              <template #default=\"{ row }\">-->\n        <!--                <el-tag :type=\"row.build_result === 'SUCCESS' ? 'success' : 'danger'\">-->\n        <!--                  {{ row.build_result }}-->\n        <!--                </el-tag>-->\n        <!--              </template>-->\n        <!--            </el-table-column>-->\n        <!--            <el-table-column prop=\"CID\" label=\"部署地区\" header-></el-table-column>-->\n        <!--            <el-table-column-->\n        <!--                prop=\"TAG\"-->\n        <!--                label=\"TAG\"-->\n        <!--                :filters=\"filters\"-->\n        <!--                :filter-method=\"filterHandler\"-->\n        <!--                :filtered-value=\"filteredValue\"-->\n        <!--                filter-placement=\"bottom-end\"-->\n        <!--                header- -->\n        <!--            >-->\n        <!--              <template #default=\"{ row }\">-->\n        <!--                <el-tag key=\"string\" type=\"warning\" class=\"mx-1\" effect=\"light\">-->\n        <!--                  {{ row.TAG }}-->\n        <!--                </el-tag>-->\n        <!--              </template>-->\n        <!--            </el-table-column>-->\n        <!--            <el-table-column prop=\"executor\" label=\"部署触发人\" :min-width=\"65\" header-></el-table-column>-->\n\n        <!--          </el-table>-->\n\n        <!--          &lt;!&ndash;      <el-table&ndash;&gt;-->\n        <!--          &lt;!&ndash;          :data=\"failTableData\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;          :filter-method=\"handleFilter\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;          border&ndash;&gt;-->\n        <!--          &lt;!&ndash;          max-height=\"200\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;          :empty-text=\"'No Fail Services'\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;          :header-cell-style=\"{background:'#ee4d2d',color:'#f3f1f6'}\"&ndash;&gt;-->\n\n        <!--          &lt;!&ndash;      >&ndash;&gt;-->\n        <!--          &lt;!&ndash;        <el-table-column prop=\"pipeline_name\" label=\"Fail Services\" :min-width=\"150\"></el-table-column>&ndash;&gt;-->\n        <!--          &lt;!&ndash;        <el-table-column prop=\"TAG\" label=\"TAG\" :min-width=\"150\" header->&ndash;&gt;-->\n        <!--          &lt;!&ndash;          <template #default=\"{ row }\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;            <el-tag key=\"string\" type=\"warning\" class=\"mx-1\" effect=\"light\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;              {{ row.TAG }}&ndash;&gt;-->\n        <!--          &lt;!&ndash;            </el-tag>&ndash;&gt;-->\n        <!--          &lt;!&ndash;          </template>&ndash;&gt;-->\n        <!--          &lt;!&ndash;        </el-table-column>&ndash;&gt;-->\n        <!--          &lt;!&ndash;        <el-table-column prop=\"err_msg\" label=\"Fail Reasion\" :min-width=\"150\" header-></el-table-column>&ndash;&gt;-->\n        <!--          &lt;!&ndash;        <el-table-column prop=\"space_link\" label=\"SPACE\" :min-width=\"40\" header->&ndash;&gt;-->\n        <!--          &lt;!&ndash;          <template #default=\"{ row }\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;            <el-link&ndash;&gt;-->\n        <!--          &lt;!&ndash;                v-bind:href=\"row.space_link\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;                target=\"_blank\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;                :underline=\"false\"&ndash;&gt;-->\n        <!--          &lt;!&ndash;                type=\"primary\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;              <el-icon class=\"el-icon&#45;&#45;right\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;                <icon-view/>&ndash;&gt;-->\n        <!--          &lt;!&ndash;              </el-icon>&ndash;&gt;-->\n        <!--          &lt;!&ndash;              link&ndash;&gt;-->\n        <!--          &lt;!&ndash;            </el-link>&ndash;&gt;-->\n        <!--          &lt;!&ndash;          </template>&ndash;&gt;-->\n        <!--          &lt;!&ndash;        </el-table-column>&ndash;&gt;-->\n        <!--          &lt;!&ndash;        <el-table-column prop=\"build_result\" label=\"Status\" header->&ndash;&gt;-->\n        <!--          &lt;!&ndash;          <template #default=\"{ row }\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;            <el-tag :type=\"'danger'\">&ndash;&gt;-->\n        <!--          &lt;!&ndash;              {{ row.build_result }}&ndash;&gt;-->\n        <!--          &lt;!&ndash;            </el-tag>&ndash;&gt;-->\n        <!--          &lt;!&ndash;          </template>&ndash;&gt;-->\n\n        <!--          &lt;!&ndash;        </el-table-column>&ndash;&gt;-->\n        <!--          &lt;!&ndash;      </el-table>&ndash;&gt;-->\n\n        <!--        </el-card>-->\n\n        <el-card shadow=\"hover\">\n          <!--      <h3>TAG Filters</h3>-->\n          <el-table\n              ref=\"tableRef\"\n              :data=\"failTableData\"\n              :columns=\"tableColumns\"\n              :row-class-name=\"tableRowClassName\"\n              style=\"width: auto; height: 100%\"\n              border\n              :header-cell-style=\"{background:'#ee4d2d',color:'#f3f1f6'}\"\n          >\n            <el-table-column\n                prop=\"start_time\"\n                label=\"部署生效时间\"\n                sortable\n\n                :default-sort=\"{ prop: 'start_time', order: 'descending' }\"\n            >\n            </el-table-column>\n            <el-table-column\n                prop=\"end_time\"\n                label=\"任务结束时间\"\n                sortable\n                :default-sort=\"{ prop: 'end_time', order: 'descending' }\"\n            >\n            </el-table-column>\n            <el-table-column\n                prop=\"pipeline_name\"\n                label=\"服务\"\n                :min-width=\"200\"\n                :style=\"{ 'white-space': 'nowrap', 'min-width': 0 }\"\n            >\n              <template #default=\"{ row }\">\n                <el-link\n                    :underline=\"false\"\n                    v-bind:href=\"row.space_link\"\n                    target=\"_blank\"\n                    type=\"primary\">\n                  {{ row.pipeline_name }}\n                </el-link>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"duration\" label=\"部署间隔\" :min-width=\"60\" header-></el-table-column>\n            <el-table-column prop=\"build_type\" label=\"类型\" :min-width=\"55\" header->\n              <template #default=\"{ row }\">\n                <el-tag\n                    :type=\"row.build_type === '灰度发布' ? 'info' : row.build_type === '全量发布' ? 'success' : 'warning'\">\n                  {{ row.build_type }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"build_result\" label=\"部署结果\" :min-width=\"50\" header->\n              <template #default=\"{ row }\">\n                <el-tag :type=\"row.build_result === 'SUCCESS' ? 'success' : 'danger'\">\n                  {{ row.build_result }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"CID\" label=\"部署地区\" header-></el-table-column>\n            <el-table-column\n                prop=\"TAG\"\n                label=\"TAG\"\n                :filters=\"filters\"\n                :filter-method=\"filterHandler\"\n                :filtered-value=\"filteredValue\"\n                filter-placement=\"bottom-end\"\n                header-\n            >\n              <template #default=\"{ row }\">\n                <el-tag key=\"string\" type=\"warning\" class=\"mx-1\" effect=\"light\">\n                  {{ row.TAG }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"executor\" label=\"部署触发人\" :min-width=\"65\" header-></el-table-column>\n\n          </el-table>\n          <div class=\"ar-container\">\n          <el-select v-model=\"selected\"\n                     filterable\n                     placeholder=\"请选择TAG来进行筛选\"\n                     clearable\n          >\n            <el-option\n                v-for=\"tag in searchList\"\n                :key=\"tag\"\n                :label=\"tag\"\n                :value=\"tag\"\n            />\n          </el-select>\n          <!--          <el-button type=\"primary\" @click=\"newclearFilter\">Reset Filters</el-button>-->\n          <!--          <el-button type=\"success\" @click=\"refreshPage\">Refresh Page</el-button>-->\n\n            <el-icon :size=\"20\">\n              <Bell/>\n            </el-icon>\n            <el-text class=\"mx-1\" type=\"danger\" size=\"default\">注意，同一天发布的服务背景色是统一的</el-text>\n          </div>\n\n          <el-table\n              ref=\"tableRef\"\n\n              :data=\"filteredData\"\n              :columns=\"tableColumns\"\n              :row-class-name=\"tableRowFullClassName\"\n              style=\"width: auto; height: 100%\"\n\n              border\n              :header-cell-style=\"{background:'#eef1f6',color:'#606266'}\"\n          >\n            <el-table-column\n                prop=\"start_time\"\n                label=\"部署生效时间\"\n                sortable\n\n                :default-sort=\"{ prop: 'start_time', order: 'descending' }\"\n            >\n              <!--              <template #default=\"{ row }\">-->\n              <!--                <div :style=\"{ backgroundColor: getDateColor(row.start_time), padding: '5px' }\">-->\n              <!--                  {{ row.start_time }}-->\n              <!--                </div>-->\n              <!--              </template>-->\n            </el-table-column>\n            <el-table-column\n                prop=\"end_time\"\n                label=\"任务结束时间\"\n                sortable\n                :default-sort=\"{ prop: 'end_time', order: 'descending' }\"\n            >\n            </el-table-column>\n            <el-table-column\n                prop=\"pipeline_name\"\n                label=\"服务\"\n                :min-width=\"200\"\n                :style=\"{ 'white-space': 'nowrap', 'min-width': 0 }\"\n            >\n              <template #default=\"{ row }\">\n                <el-link\n                    :underline=\"false\"\n                    v-bind:href=\"row.space_link\"\n                    target=\"_blank\"\n                    type=\"primary\">\n                  {{ row.pipeline_name }}\n                </el-link>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"duration\" label=\"部署间隔\" :min-width=\"60\" header-></el-table-column>\n            <el-table-column prop=\"build_type\" label=\"类型\" :min-width=\"55\" header->\n              <template #default=\"{ row }\">\n                <el-tag\n                    :type=\"row.build_type === '灰度发布' ? 'info' : row.build_type === '全量发布' ? 'success' : 'warning'\">\n                  {{ row.build_type }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"build_result\" label=\"部署结果\" :min-width=\"50\" header->\n              <template #default=\"{ row }\">\n                <el-tag :type=\"row.build_result === 'SUCCESS' ? 'success' : 'danger'\">\n                  {{ row.build_result }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"CID\" label=\"部署地区\" header-></el-table-column>\n            <el-table-column\n                prop=\"TAG\"\n                label=\"TAG\"\n                :filters=\"filters\"\n                :filter-method=\"filterHandler\"\n                :filtered-value=\"filteredValue\"\n                filter-placement=\"bottom-end\"\n                header-\n            >\n              <template #default=\"{ row }\">\n                <el-tag key=\"string\" type=\"warning\" class=\"mx-1\" effect=\"light\">\n                  {{ row.TAG }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"executor\" label=\"部署触发人\" :min-width=\"65\" header-></el-table-column>\n\n          </el-table>\n        </el-card>\n\n      </div>\n    </el-tab-pane>\n\n    <el-tab-pane label=\"配置\" name=\"second\"></el-tab-pane>\n  </el-tabs>\n</template>\n\n<script lang=\"ts\" setup>\nimport {ref, onMounted, computed, watch, reactive, watchEffect, nextTick, onUnmounted} from 'vue';\nimport type {ElTree, TabsPaneContext} from 'element-plus'\nimport {\n  ElPagination,\n  ElCard,\n  ElTable,\n  ElTableColumn,\n  ElTag,\n  ElSelect,\n  ElOption,\n  TableColumnCtx,\n  TableInstance,\n} from 'element-plus';\nimport axios from 'axios';\nimport {Bell, Edit, View as IconView} from '@element-plus/icons-vue';\nimport {ChatLineRound, Male} from '@element-plus/icons-vue';\nimport {get_data_json} from '@/api/get_data_json';\n\nconst activeName = ref('first');\nconst handleClick = (tab: TabsPaneContext, event: Event) => {\n  console.log(tab, event)\n}\nconst currentPage = ref(1);\nconst value = ref('')\nconst selected = ref('')\nlet failCount = ref(0);\n// 每页显示条数\nconst pageSize = ref(10);\n\n// 是否显示分页器\nconst pagination = ref(true);\nconst deployments = ref([]);\nconst selectedProject = ref('chatbot');\n// const tableData = reactive([]);\nconst filters = reactive([]);\nconst searchList = reactive([]);\nconst projects = reactive([\n  \"chatbot\", \"inhouse\", \"channel\", \"data\",\n]);\nconst tagSet = new Set(); // 用来存储已经添加过的 TAG 值\ninterface User {\n  pipeline_name: string\n  build_type: string\n  duration: string\n  CID: string\n  start_time: string\n  end_time: string\n  TAG: string\n  space_link: string\n  executor: string\n  build_result: string\n  err_msg: string\n  index: string\n}\n\nconst tableData: User[] = reactive([]);\nconst tableRef = ref<TableInstance>()\nconst newTableData: User[] = reactive([]);\nconst failTableData = reactive([]);\nconst refreshPage = () => {\n  location.reload();\n}\n// TODO: improvement typing when refactor table\nconst clearFilter = () => {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-expect-error\n  tableRef.value!.clearFilter()\n}\n\nconst newclearFilter = () => {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-expect-error\n  selected.value = null\n}\nconst formatter = (row: User, column: TableColumnCtx<User>) => {\n  return row.pipeline_name\n}\nconst filterTag = (value: string, row: User) => {\n  return row.TAG === value\n}\nconst filteredData = computed(() => {\n  return selected.value ? newTableData.filter(item => item.TAG === selected.value) : newTableData\n})\nconst filterHandler = (\n    value: string,\n    row: User,\n    column: TableColumnCtx<User>\n) => {\n  const property = column['property']\n\n  return row[property] === value\n}\n\nconst warningRow = () => {\n  return 'warning-row'\n}\n\nconst tableRowClassName = ({\n                             row,\n                           }: {\n  row: User\n}) => {\n  if (row.build_result === 'FAILURE') {\n    return 'warning-row'\n  }\n  return ''\n}\n\n\n// const getRandomClass = () => {\n//   const classes = ['warning', 'success'];\n//   const randomIndex = Math.floor(Math.random() * classes.length);\n//   return classes[randomIndex];\n// };\n// const props = defineProps({\n//   tableData: {\n//     type: Array,\n//     default: () => []\n//   },\n//   newtableData: {\n//     type: Array,\n//     default: () => []\n//   }\n// })\n\n\nfunction processArray(array) {\n  const new_data = reactive([]);\n  const latestEndTimeMap = new Map();\n  for (let i = 0; i < array.length; i++) {\n    const item = array[i];\n    const {pipeline_name, end_time, build_result} = item;\n    const latestEndTime = latestEndTimeMap.get(pipeline_name);\n    if (!latestEndTime || new Date(end_time) > new Date(latestEndTime)) {\n      latestEndTimeMap.set(pipeline_name, end_time);\n    }\n  }\n\n  for (let i = 0; i < array.length; i++) {\n    const item = array[i];\n    const {pipeline_name, end_time, build_result} = item;\n    if (build_result === 'FAILURE' && end_time === latestEndTimeMap.get(pipeline_name)) {\n      new_data.push(item);\n    }\n  }\n  return new_data;\n}\n\nconst fetchData = async () => {\n  const response = await get_data_json();\n  const data = response.data.data;\n  failTableData.splice(0, failTableData.length);\n  newTableData.splice(0, newTableData.length);\n  tableData.splice(0, tableData.length);\n  tableData.push(...data);\n  const temp_fail_data = processArray(tableData);\n  failTableData.push(...temp_fail_data);\n  tableData.forEach((item, index) => {\n\n    if (item.TAG && !tagSet.has(item.TAG)) {\n      tagSet.add(item.TAG);\n      filters.push({\n        text: item.TAG,\n        value: item.TAG,\n      });\n      searchList.push(item.TAG);\n    }\n    // console.log(index)\n    // if (item.build_result === \"FAILURE\") {\n    //   const latestEndTime = latestEndTimeMap.get(pipeline_name);\n    //   if (!latestEndTime || new Date(end_time) > new Date(latestEndTime)) {\n    //     latestEndTimeMap.set(pipeline_name, end_time);\n    //     failTableData.push(\n    //         {\n    //           pipeline_name: item.pipeline_name,\n    //           TAG: item.TAG,\n    //           space_link: item.space_link,\n    //           build_result: item.build_result,\n    //           err_msg: item.err_msg,\n    //           executor: item.executor,\n    //           end_time: item.end_time,\n    //           build_type: item.build_type,\n    //           duration: item.duration,\n    //           start_time: item.start_time,\n    //           cid: item.CID,\n    //         }\n    //     );\n    //   }\n    //   //   failTableData.push(\n    //   //     {\n    //   //       pipeline_name: item.pipeline_name,\n    //   //       TAG: item.TAG,\n    //   //       space_link: item.space_link,\n    //   //       build_result: item.build_result,\n    //   //       err_msg: item.err_msg,\n    //   //       executor: item.executor,\n    //   //       end_time: item.end_time,\n    //   //       build_type: item.build_type,\n    //   //       duration: item.duration,\n    //   //       start_time: item.start_time,\n    //   //       cid: item.CID,\n    //   //     }\n    //   // )\n    // }\n    const everyone: User = {\n      pipeline_name: item.pipeline_name,\n      build_type: item.build_type,\n      duration: item.duration,\n      CID: item.CID,\n      start_time: item.start_time,\n      end_time: item.end_time,\n      TAG: item.TAG,\n      space_link: item.space_link,\n      executor: item.executor,\n      build_result: item.build_result,\n      err_msg: item.err_msg,\n      index: item.index,\n    };\n    if (item.TAG && !tagSet.has(item.TAG)) {\n      tagSet.add(item.TAG);\n      filters.push({\n        text: item.TAG,\n        value: item.TAG,\n      });\n      searchList.push(item.TAG);\n    }\n    newTableData.push(everyone)\n  });\n  // 发送请求到后台获取数据   // ...\n}\n// 模拟获取到的数据\n//  const responseData = '这是从后台获取到的数据';\n\n// 更新响应式数据\n//  data.value = responseData;\n// };\nconst intervalRef = ref(null);\n\nonMounted(async () => {\n  fetchData();\n  const intervalId = setInterval(fetchData, 60000); // 每隔一分钟执行一次\n  intervalRef.value = intervalId;\n});\nonUnmounted(() => {\n      clearInterval(intervalRef.value);\n    }\n);\n\nconst props = defineProps({\n  newTableData: {\n    type: Array as () => User[],\n    required: true\n  }\n});\n\nconst dateColors = ref<Record<string, string>>({});\n\n\nconst generateDateColors = computed(() => {\n  const colors: Record<string, string> = {};\n  for (const item of props.newTableData) {\n    const date = item.start_time.split(' ')[0]; // 提取日期部分\n    console.log(date);\n    if (!colors[date]) {\n      const randomColor = getRandomLightColor();\n      colors[date] = randomColor;\n    }\n  }\n  return colors;\n});\n\nfunction getRandomLightColor() {\n  const hue = Math.floor(Math.random() * 360);\n  const saturation = Math.floor(Math.random() * 30) + 70;\n  const lightness = Math.floor(Math.random() * 20) + 70;\n  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;\n}\n\nfunction getDateColor(start_time: string) {\n  const date = start_time.split(' ')[0];\n  return dateColors.value[date];\n}\n\n// watch(() => props.newTableData, () => {\n//   dateColors.value = generateDateColors.value;\n// });\n\n\nconst tableRowFullClassName = ({\n                                 row,\n                               }: {\n  row: User\n}) => {\n  const number = parseInt(row.index);\n  if (number % 2 === 0) {\n    return 'success-row'\n  } else {\n    return 'primary-row'\n  }\n  // if (row.index === \"0\") {\n  //   return 'success-row'\n  // }\n  // if (row.index === \"1\") {\n  //   return 'primary-row'\n  // }\n  // if (row.index === \"2\") {\n  //   return 'danger-row'\n  // }\n  // return ''\n\n}\n\n\n\n\n</script>\n\n<style scoped>\n.index-container {\n  height: 100%;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\ndiv {\n  font-size: 12px;\n  margin: 5px;\n  border: 1px;\n  padding: 0;\n}\n\n</style>\n\n<style>\n.el-table .warning-row {\n  --el-table-tr-bg-color: var(--el-color-warning-light-9);\n}\n.el-table .danger-row {\n  --el-table-tr-bg-color: var(--el-color-danger-light-9);\n}\n.el-table .success-row {\n  --el-table-tr-bg-color: var(--el-color-success-light-9);\n}\n.el-table .primary-row {\n  --el-table-tr-bg-color: var(--el-color-primary-light-9);\n}\n.el-table .warning-row {\n  --el-table-tr-bg-color: var(--el-color-warning-light-9);\n}\n.el-table .info-row {\n  --el-table-tr-bg-color: var(--el-color-info-light-9);\n}\n.table-header {\n  background-color: blue;\n  color: white;\n}\n\n\n</style>\n\n<style lang=\"scss\" scoped>\n\n.index-conntainer {\n  //width: $base-width;\n  .head-card {\n    display: flex;\n    align-items: center;\n    padding: $base-main-padding;\n    background-color: $base-color-white;\n\n    &-content {\n      padding-left: 15px;\n\n      .desc {\n        color: $base-font-color;\n      }\n    }\n  }\n\n  .content {\n    margin: 5px 10px;\n    display: flex;\n    flex-direction: row; // 指定子元素在一行上排列\n    justify-content: space-between; // 指定子元素之间的间距和位置\n    .count-box {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .item {\n        display: flex;\n        flex-direction: column;\n        text-align: center;\n\n        .label {\n          padding: 10px 0;\n          font-size: $base-font-size-big;\n        }\n\n        .count {\n          font-size: $base-font-size-max;\n          font-weight: bolder;\n          color: $base-color-primary;\n\n          &.error {\n            color: var(--el-color-danger);\n          }\n\n          &.success {\n            color: var(--el-color-success);\n          }\n        }\n      }\n    }\n\n    .title {\n      margin: 0;\n    }\n\n    .skill-title {\n      padding: 10px 0;\n      font-weight: 500;\n    }\n\n    .card {\n      margin-bottom: 15px;\n      flex: 1;\n\n      &-body {\n        display: flex;\n        flex-direction: row;\n        //display: grid;\n        grid-template-columns: repeat(4, 1fr);\n\n        &.mobile {\n          grid-template-columns: repeat(1, 1fr);\n        }\n\n        .item {\n          box-sizing: border-box;\n          padding: 10px 20px;\n          margin-top: -1px;\n          margin-left: -1px;\n          overflow: hidden;\n          cursor: pointer;\n          border: 1px solid black;\n          border: 1px solid #eee;\n          transition: box-shadow 0.5;\n\n          .lf {\n            display: flex;\n            align-items: center;\n            max-width: 140px;\n\n            .img {\n              width: auto;\n              max-width: 120px;\n              height: auto;\n              max-height: 40px;\n            }\n          }\n\n          &:hover {\n            box-shadow: $base-box-shadow;\n          }\n\n          .title {\n            padding-left: 5px;\n            font-size: 12px;\n            font-weight: bold;\n          }\n\n          .desc {\n            padding: 5px 0;\n            font-size: 12px;\n            line-height: 1.5;\n            color: $base-font-color;\n          }\n        }\n      }\n    }\n  }\n\n}\n\n.el-card + .el-card {\n  margin-top: 20px;\n}\n</style>\n"], "names": ["activeName", "ref", "handleClick", "tab", "event", "console", "log", "selected", "filters", "reactive", "searchList", "tagSet", "Set", "tableData", "tableRef", "newTableData", "failTableData", "filteredData", "computed", "value", "filter", "item", "TAG", "<PERSON><PERSON><PERSON><PERSON>", "value2", "row", "column", "tableRowClassName", "build_result", "fetchData", "async", "data", "get_data_json", "splice", "length", "push", "temp_fail_data", "array", "new_data", "latestEndTimeMap", "Map", "i", "pipeline_name", "end_time", "latestEndTime", "get", "Date", "set", "processArray", "for<PERSON>ach", "index", "has", "add", "text", "everyone", "build_type", "duration", "CID", "start_time", "space_link", "executor", "err_msg", "intervalRef", "onMounted", "intervalId", "setInterval", "onUnmounted", "clearInterval", "props", "__props", "getRandomLightColor", "Math", "floor", "random", "colors", "date", "split", "randomColor", "tableRowFullClassName", "parseInt"], "mappings": "wgBAwVA,MAAAA,EAAAC,EAAA,SACAC,EAAA,CAAAC,EAAAC,KACEC,QAAAC,IAAAH,EAAAC,EAAA,EAEFH,EAAA,GACAA,EAAA,IACA,MAAAM,EAAAN,EAAA,IACAA,EAAA,GAEAA,EAAA,IAGAA,GAAA,GACAA,EAAA,IACAA,EAAA,WAEA,MAAAO,EAAAC,EAAA,IACAC,EAAAD,EAAA,IACAA,EAAA,CAA0B,UACxB,UAAW,UAAW,SAExB,MAAAE,EAAA,IAAAC,IAgBAC,EAAAJ,EAAA,IACAK,EAAAb,IACAc,EAAAN,EAAA,IACAO,EAAAP,EAAA,IAsBAQ,EAAAC,GAAA,IACEX,EAAAY,MAAAJ,EAAAK,QAAAC,GAAAA,EAAAC,MAAAf,EAAAY,QAAAJ,IAEFQ,EAAA,CAAAC,EAAAC,EAAAC,IAOED,EAFAC,EAAA,YAEAF,EAOFG,EAAA,EAA2BF,SAKzB,YAAAA,EAAAG,aACE,cAEF,GA2CF,MAAAC,EAAAC,UACE,MACAC,SADAC,KACAD,KAAAA,KACAf,EAAAiB,OAAA,EAAAjB,EAAAkB,QACAnB,EAAAkB,OAAA,EAAAlB,EAAAmB,QACArB,EAAAoB,OAAA,EAAApB,EAAAqB,QACArB,EAAAsB,QAAAJ,GACA,MAAAK,EA7BF,SAAAC,GACE,MAAAC,EAAA7B,EAAA,IACA8B,EAAA,IAAAC,IACA,IAAA,IAAAC,EAAA,EAAAA,EAAAJ,EAAAH,OAAAO,IAAA,CACE,MAAApB,EAAAgB,EAAAI,IACAC,cAAAA,EAAAC,SAAAA,EAAAf,aAAAA,GAAAP,EACAuB,EAAAL,EAAAM,IAAAH,KACAE,GAAA,IAAAE,KAAAH,GAAA,IAAAG,KAAAF,KACEL,EAAAQ,IAAAL,EAAAC,EACF,CAGF,IAAA,IAAAF,EAAA,EAAAA,EAAAJ,EAAAH,OAAAO,IAAA,CACE,MAAApB,EAAAgB,EAAAI,IACAC,cAAAA,EAAAC,SAAAA,EAAAf,aAAAA,GAAAP,EACA,YAAAO,GAAAe,IAAAJ,EAAAM,IAAAH,IACEJ,EAAAH,KAAAd,EACF,CAEF,OAAAiB,CAAO,CAUPU,CAAAnC,GACAG,EAAAmB,QAAAC,GACAvB,EAAAoC,SAAA,CAAA5B,EAAA6B,KAEE7B,EAAAC,MAAAX,EAAAwC,IAAA9B,EAAAC,OACEX,EAAAyC,IAAA/B,EAAAC,KACAd,EAAA2B,KAAA,CAAakB,KAAAhC,EAAAC,IACAH,MAAAE,EAAAC,MAGbZ,EAAAyB,KAAAd,EAAAC,MAuCF,MAAAgC,EAAA,CAAuBZ,cAAArB,EAAAqB,cACDa,WAAAlC,EAAAkC,WACHC,SAAAnC,EAAAmC,SACFC,IAAApC,EAAAoC,IACLC,WAAArC,EAAAqC,WACOf,SAAAtB,EAAAsB,SACFrB,IAAAD,EAAAC,IACLqC,WAAAtC,EAAAsC,WACOC,SAAAvC,EAAAuC,SACFhC,aAAAP,EAAAO,aACIiC,QAAAxC,EAAAwC,QACLX,MAAA7B,EAAA6B,OAGhB7B,EAAAC,MAAAX,EAAAwC,IAAA9B,EAAAC,OACEX,EAAAyC,IAAA/B,EAAAC,KACAd,EAAA2B,KAAA,CAAakB,KAAAhC,EAAAC,IACAH,MAAAE,EAAAC,MAGbZ,EAAAyB,KAAAd,EAAAC,MAEFP,EAAAoB,KAAAmB,EAAA,GAA0B,EAW9BQ,EAAA7D,EAAA,MAEA8D,GAAAjC,UACED,IACA,MAAAmC,EAAAC,YAAApC,EAAA,KACAiC,EAAA3C,MAAA6C,CAAA,IAEFE,GAAA,KACKC,cAAAL,EAAA3C,MAAA,IASL,MAAAiD,EAAAC,EAkBA,SAAAC,IAIC,MAAA,OAHCC,KAAAC,MAAA,IAAAD,KAAAE,cACAF,KAAAC,MAAA,GAAAD,KAAAE,UAAA,QACAF,KAAAC,MAAA,GAAAD,KAAAE,UAAA,MACD,CApBDxE,EAAA,CAAA,GAGAiB,GAAA,KACE,MAAAwD,EAAA,CAAA,EACA,IAAA,MAAArD,KAAA+C,EAAArD,aAAA,CACE,MAAA4D,EAAAtD,EAAAqC,WAAAkB,MAAA,KAAA,GAEA,GADAvE,QAAAC,IAAAqE,IACAD,EAAAC,GAAA,CACE,MAAAE,EAAAP,IACDI,EAAAC,GAAAE,CAAA,CACF,CAED,OAAAH,CAAA,IAoBF,MAAAI,EAAA,EAA+BrD,SAK7BsD,SAAAtD,EAAAyB,OACA,GAAA,EACE,cAED"}
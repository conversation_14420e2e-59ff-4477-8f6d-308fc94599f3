import{b as e,ay as a,e as s,f as t,a as i,o as n,h as l,C as d,w as o,az as r,j as f,a9 as u,k as c,n as p,m as y,_ as k,D as b}from"./index-awKTxnvj.js";const g=e({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:a}}),m={click:e=>e instanceof MouseEvent},v=["href","target"],h=s({name:"ElLink"});const _=b(k(s({...h,props:g,emits:m,setup(e,{emit:a}){const s=e,k=t("link"),b=i((()=>[k.b(),k.m(s.type),k.is("disabled",s.disabled),k.is("underline",s.underline&&!s.disabled)]));function g(e){s.disabled||a("click",e)}return(e,a)=>(n(),l("a",{class:p(f(b)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:g},[e.icon?(n(),d(f(u),{key:0},{default:o((()=>[(n(),d(r(e.icon)))])),_:1})):c("v-if",!0),e.$slots.default?(n(),l("span",{key:1,class:p(f(k).e("inner"))},[y(e.$slots,"default")],2)):c("v-if",!0),e.$slots.icon?y(e.$slots,"icon",{key:2}):c("v-if",!0)],10,v))}}),[["__file","link.vue"]]));export{_ as E};
//# sourceMappingURL=index-BWOrXwLB.js.map

System.register(["./index-legacy.C52nWfoy.js"],(function(e,t){"use strict";var i,n,s,a,l,d,r,f,o,u,c,y,p,g,k,b,v;return{setters:[e=>{i=e.b,n=e.ay,s=e.e,a=e.f,l=e.a,d=e.o,r=e.h,f=e.C,o=e.w,u=e.az,c=e.j,y=e.a9,p=e.k,g=e.n,k=e.m,b=e._,v=e.D}],execute:function(){const t=i({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:n}}),h={click:e=>e instanceof MouseEvent},m=["href","target"],_=s({name:"ElLink"}),S=s({..._,props:t,emits:h,setup(e,{emit:t}){const i=e,n=a("link"),s=l((()=>[n.b(),n.m(i.type),n.is("disabled",i.disabled),n.is("underline",i.underline&&!i.disabled)]));function b(e){i.disabled||t("click",e)}return(e,t)=>(d(),r("a",{class:g(c(s)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:b},[e.icon?(d(),f(c(y),{key:0},{default:o((()=>[(d(),f(u(e.icon)))])),_:1})):p("v-if",!0),e.$slots.default?(d(),r("span",{key:1,class:g(c(n).e("inner"))},[k(e.$slots,"default")],2)):p("v-if",!0),e.$slots.icon?k(e.$slots,"icon",{key:2}):p("v-if",!0)],10,m))}});e("E",v(b(S,[["__file","link.vue"]])))}}}));
//# sourceMappingURL=index-legacy.Co5M3uHU.js.map

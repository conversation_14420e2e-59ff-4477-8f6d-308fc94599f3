var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=function(t){return t&&t.Math===Math&&t},n=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof t&&t)||e("object"==typeof t&&t)||function(){return this}()||Function("return this")(),r={},o=function(t){try{return!!t()}catch(e){return!0}},i=!o((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),u=!o((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),c=u,f=Function.prototype.call,a=c?f.bind(f):function(){return f.apply(f,arguments)},s={},l={}.propertyIsEnumerable,p=Object.getOwnPropertyDescriptor,h=p&&!l.call({1:2},1);s.f=h?function(t){var e=p(this,t);return!!e&&e.enumerable}:l;var v,d,y=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},m=u,g=Function.prototype,b=g.call,w=m&&g.bind.bind(b,b),O=m?w:function(t){return function(){return b.apply(t,arguments)}},S=O,j=S({}.toString),E=S("".slice),P=function(t){return E(j(t),8,-1)},x=o,T=P,I=Object,C=O("".split),L=x((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"===T(t)?C(t,""):I(t)}:I,M=function(t){return null==t},F=M,R=TypeError,_=function(t){if(F(t))throw new R("Can't call method on "+t);return t},A=L,k=_,N=function(t){return A(k(t))},z="object"==typeof document&&document.all,D=void 0===z&&void 0!==z?function(t){return"function"==typeof t||t===z}:function(t){return"function"==typeof t},W=D,G=function(t){return"object"==typeof t?null!==t:W(t)},U=n,q=D,J=function(t,e){return arguments.length<2?(n=U[t],q(n)?n:void 0):U[t]&&U[t][e];var n},B=O({}.isPrototypeOf),$=n,H="undefined"!=typeof navigator&&String(navigator.userAgent)||"",K=$.process,V=$.Deno,X=K&&K.versions||V&&V.version,Y=X&&X.v8;Y&&(d=(v=Y.split("."))[0]>0&&v[0]<4?1:+(v[0]+v[1])),!d&&H&&(!(v=H.match(/Edge\/(\d+)/))||v[1]>=74)&&(v=H.match(/Chrome\/(\d+)/))&&(d=+v[1]);var Q=d,Z=o,tt=n.String,et=!!Object.getOwnPropertySymbols&&!Z((function(){var t=Symbol("symbol detection");return!tt(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Q&&Q<41})),nt=et&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,rt=J,ot=D,it=B,ut=Object,ct=nt?function(t){return"symbol"==typeof t}:function(t){var e=rt("Symbol");return ot(e)&&it(e.prototype,ut(t))},ft=String,at=function(t){try{return ft(t)}catch(e){return"Object"}},st=D,lt=at,pt=TypeError,ht=function(t){if(st(t))return t;throw new pt(lt(t)+" is not a function")},vt=ht,dt=M,yt=a,mt=D,gt=G,bt=TypeError,wt={exports:{}},Ot=n,St=Object.defineProperty,jt=function(t,e){try{St(Ot,t,{value:e,configurable:!0,writable:!0})}catch(n){Ot[t]=e}return e},Et=n,Pt=jt,xt="__core-js_shared__",Tt=wt.exports=Et[xt]||Pt(xt,{});(Tt.versions||(Tt.versions=[])).push({version:"3.37.1",mode:"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"});var It=wt.exports,Ct=It,Lt=function(t,e){return Ct[t]||(Ct[t]=e||{})},Mt=_,Ft=Object,Rt=function(t){return Ft(Mt(t))},_t=O({}.hasOwnProperty),At=Object.hasOwn||function(t,e){return _t(Rt(t),e)},kt=O,Nt=0,zt=Math.random(),Dt=kt(1..toString),Wt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Dt(++Nt+zt,36)},Gt=Lt,Ut=At,qt=Wt,Jt=et,Bt=nt,$t=n.Symbol,Ht=Gt("wks"),Kt=Bt?$t.for||$t:$t&&$t.withoutSetter||qt,Vt=function(t){return Ut(Ht,t)||(Ht[t]=Jt&&Ut($t,t)?$t[t]:Kt("Symbol."+t)),Ht[t]},Xt=a,Yt=G,Qt=ct,Zt=function(t,e){var n=t[e];return dt(n)?void 0:vt(n)},te=function(t,e){var n,r;if("string"===e&&mt(n=t.toString)&&!gt(r=yt(n,t)))return r;if(mt(n=t.valueOf)&&!gt(r=yt(n,t)))return r;if("string"!==e&&mt(n=t.toString)&&!gt(r=yt(n,t)))return r;throw new bt("Can't convert object to primitive value")},ee=TypeError,ne=Vt("toPrimitive"),re=function(t,e){if(!Yt(t)||Qt(t))return t;var n,r=Zt(t,ne);if(r){if(void 0===e&&(e="default"),n=Xt(r,t,e),!Yt(n)||Qt(n))return n;throw new ee("Can't convert object to primitive value")}return void 0===e&&(e="number"),te(t,e)},oe=ct,ie=function(t){var e=re(t,"string");return oe(e)?e:e+""},ue=G,ce=n.document,fe=ue(ce)&&ue(ce.createElement),ae=function(t){return fe?ce.createElement(t):{}},se=!i&&!o((function(){return 7!==Object.defineProperty(ae("div"),"a",{get:function(){return 7}}).a})),le=i,pe=a,he=s,ve=y,de=N,ye=ie,me=At,ge=se,be=Object.getOwnPropertyDescriptor;r.f=le?be:function(t,e){if(t=de(t),e=ye(e),ge)try{return be(t,e)}catch(n){}if(me(t,e))return ve(!pe(he.f,t,e),t[e])};var we={},Oe=i&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Se=G,je=String,Ee=TypeError,Pe=function(t){if(Se(t))return t;throw new Ee(je(t)+" is not an object")},xe=i,Te=se,Ie=Oe,Ce=Pe,Le=ie,Me=TypeError,Fe=Object.defineProperty,Re=Object.getOwnPropertyDescriptor,_e="enumerable",Ae="configurable",ke="writable";we.f=xe?Ie?function(t,e,n){if(Ce(t),e=Le(e),Ce(n),"function"==typeof t&&"prototype"===e&&"value"in n&&ke in n&&!n[ke]){var r=Re(t,e);r&&r[ke]&&(t[e]=n.value,n={configurable:Ae in n?n[Ae]:r[Ae],enumerable:_e in n?n[_e]:r[_e],writable:!1})}return Fe(t,e,n)}:Fe:function(t,e,n){if(Ce(t),e=Le(e),Ce(n),Te)try{return Fe(t,e,n)}catch(r){}if("get"in n||"set"in n)throw new Me("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var Ne=we,ze=y,De=i?function(t,e,n){return Ne.f(t,e,ze(1,n))}:function(t,e,n){return t[e]=n,t},We={exports:{}},Ge=i,Ue=At,qe=Function.prototype,Je=Ge&&Object.getOwnPropertyDescriptor,Be=Ue(qe,"name"),$e={EXISTS:Be,PROPER:Be&&"something"===function(){}.name,CONFIGURABLE:Be&&(!Ge||Ge&&Je(qe,"name").configurable)},He=D,Ke=It,Ve=O(Function.toString);He(Ke.inspectSource)||(Ke.inspectSource=function(t){return Ve(t)});var Xe,Ye,Qe,Ze=Ke.inspectSource,tn=D,en=n.WeakMap,nn=tn(en)&&/native code/.test(String(en)),rn=Wt,on=Lt("keys"),un={},cn=nn,fn=n,an=G,sn=De,ln=At,pn=It,hn=function(t){return on[t]||(on[t]=rn(t))},vn=un,dn="Object already initialized",yn=fn.TypeError,mn=fn.WeakMap;if(cn||pn.state){var gn=pn.state||(pn.state=new mn);gn.get=gn.get,gn.has=gn.has,gn.set=gn.set,Xe=function(t,e){if(gn.has(t))throw new yn(dn);return e.facade=t,gn.set(t,e),e},Ye=function(t){return gn.get(t)||{}},Qe=function(t){return gn.has(t)}}else{var bn=hn("state");vn[bn]=!0,Xe=function(t,e){if(ln(t,bn))throw new yn(dn);return e.facade=t,sn(t,bn,e),e},Ye=function(t){return ln(t,bn)?t[bn]:{}},Qe=function(t){return ln(t,bn)}}var wn={set:Xe,get:Ye,has:Qe,enforce:function(t){return Qe(t)?Ye(t):Xe(t,{})},getterFor:function(t){return function(e){var n;if(!an(e)||(n=Ye(e)).type!==t)throw new yn("Incompatible receiver, "+t+" required");return n}}},On=O,Sn=o,jn=D,En=At,Pn=i,xn=$e.CONFIGURABLE,Tn=Ze,In=wn.enforce,Cn=wn.get,Ln=String,Mn=Object.defineProperty,Fn=On("".slice),Rn=On("".replace),_n=On([].join),An=Pn&&!Sn((function(){return 8!==Mn((function(){}),"length",{value:8}).length})),kn=String(String).split("String"),Nn=We.exports=function(t,e,n){"Symbol("===Fn(Ln(e),0,7)&&(e="["+Rn(Ln(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!En(t,"name")||xn&&t.name!==e)&&(Pn?Mn(t,"name",{value:e,configurable:!0}):t.name=e),An&&n&&En(n,"arity")&&t.length!==n.arity&&Mn(t,"length",{value:n.arity});try{n&&En(n,"constructor")&&n.constructor?Pn&&Mn(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(o){}var r=In(t);return En(r,"source")||(r.source=_n(kn,"string"==typeof e?e:"")),t};Function.prototype.toString=Nn((function(){return jn(this)&&Cn(this).source||Tn(this)}),"toString");var zn=We.exports,Dn=D,Wn=we,Gn=zn,Un=jt,qn=function(t,e,n,r){r||(r={});var o=r.enumerable,i=void 0!==r.name?r.name:e;if(Dn(n)&&Gn(n,i,r),r.global)o?t[e]=n:Un(e,n);else{try{r.unsafe?t[e]&&(o=!0):delete t[e]}catch(u){}o?t[e]=n:Wn.f(t,e,{value:n,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return t},Jn={},Bn=Math.ceil,$n=Math.floor,Hn=Math.trunc||function(t){var e=+t;return(e>0?$n:Bn)(e)},Kn=function(t){var e=+t;return e!=e||0===e?0:Hn(e)},Vn=Kn,Xn=Math.max,Yn=Math.min,Qn=Kn,Zn=Math.min,tr=function(t){var e=Qn(t);return e>0?Zn(e,9007199254740991):0},er=N,nr=function(t,e){var n=Vn(t);return n<0?Xn(n+e,0):Yn(n,e)},rr=function(t){return tr(t.length)},or=function(t){return function(e,n,r){var o=er(e),i=rr(o);if(0===i)return!t&&-1;var u,c=nr(r,i);if(t&&n!=n){for(;i>c;)if((u=o[c++])!=u)return!0}else for(;i>c;c++)if((t||c in o)&&o[c]===n)return t||c||0;return!t&&-1}},ir={includes:or(!0),indexOf:or(!1)},ur=At,cr=N,fr=ir.indexOf,ar=un,sr=O([].push),lr=function(t,e){var n,r=cr(t),o=0,i=[];for(n in r)!ur(ar,n)&&ur(r,n)&&sr(i,n);for(;e.length>o;)ur(r,n=e[o++])&&(~fr(i,n)||sr(i,n));return i},pr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype");Jn.f=Object.getOwnPropertyNames||function(t){return lr(t,pr)};var hr={};hr.f=Object.getOwnPropertySymbols;var vr=J,dr=Jn,yr=hr,mr=Pe,gr=O([].concat),br=vr("Reflect","ownKeys")||function(t){var e=dr.f(mr(t)),n=yr.f;return n?gr(e,n(t)):e},wr=At,Or=br,Sr=r,jr=we,Er=o,Pr=D,xr=/#|\.prototype\./,Tr=function(t,e){var n=Cr[Ir(t)];return n===Mr||n!==Lr&&(Pr(e)?Er(e):!!e)},Ir=Tr.normalize=function(t){return String(t).replace(xr,".").toLowerCase()},Cr=Tr.data={},Lr=Tr.NATIVE="N",Mr=Tr.POLYFILL="P",Fr=Tr,Rr=n,_r=r.f,Ar=De,kr=qn,Nr=jt,zr=function(t,e,n){for(var r=Or(e),o=jr.f,i=Sr.f,u=0;u<r.length;u++){var c=r[u];wr(t,c)||n&&wr(n,c)||o(t,c,i(e,c))}},Dr=Fr,Wr=n.Promise,Gr={};Gr[Vt("toStringTag")]="z";var Ur="[object z]"===String(Gr),qr=D,Jr=P,Br=Vt("toStringTag"),$r=Object,Hr="Arguments"===Jr(function(){return arguments}()),Kr=O,Vr=o,Xr=D,Yr=Ur?Jr:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(n){}}(e=$r(t),Br))?n:Hr?Jr(e):"Object"===(r=Jr(e))&&qr(e.callee)?"Arguments":r},Qr=Ze,Zr=function(){},to=J("Reflect","construct"),eo=/^\s*(?:class|function)\b/,no=Kr(eo.exec),ro=!eo.test(Zr),oo=function(t){if(!Xr(t))return!1;try{return to(Zr,[],t),!0}catch(e){return!1}},io=function(t){if(!Xr(t))return!1;switch(Yr(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ro||!!no(eo,Qr(t))}catch(e){return!0}};io.sham=!0;var uo=!to||Vr((function(){var t;return oo(oo.call)||!oo(Object)||!oo((function(){t=!0}))||t}))?io:oo,co=at,fo=TypeError,ao=Pe,so=function(t){if(uo(t))return t;throw new fo(co(t)+" is not a constructor")},lo=M,po=Vt("species"),ho={},vo=ht,yo=TypeError,mo=function(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw new yo("Bad Promise constructor");e=t,n=r})),this.resolve=vo(e),this.reject=vo(n)};ho.f=function(t){return new mo(t)};var go=Pe,bo=G,wo=ho,Oo=function(t,e){var n,r,o,i,u,c=t.target,f=t.global,a=t.stat;if(n=f?Rr:a?Rr[c]||Nr(c,{}):Rr[c]&&Rr[c].prototype)for(r in e){if(i=e[r],o=t.dontCallGetSet?(u=_r(n,r))&&u.value:n[r],!Dr(f?r:c+(a?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;zr(i,o)}(t.sham||o&&o.sham)&&Ar(i,"sham",!0),kr(n,r,i,t)}},So=Wr,jo=o,Eo=J,Po=D,xo=function(t,e){var n,r=ao(t).constructor;return void 0===r||lo(n=ao(r)[po])?e:so(n)},To=function(t,e){if(go(t),bo(e)&&e.constructor===t)return e;var n=wo.f(t);return(0,n.resolve)(e),n.promise},Io=qn,Co=So&&So.prototype;if(Oo({target:"Promise",proto:!0,real:!0,forced:!!So&&jo((function(){Co.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=xo(this,Eo("Promise")),n=Po(t);return this.then(n?function(n){return To(e,t()).then((function(){return n}))}:t,n?function(n){return To(e,t()).then((function(){throw n}))}:t)}}),Po(So)){var Lo=Eo("Promise").prototype.finally;Co.finally!==Lo&&Io(Co,"finally",Lo,{unsafe:!0})}
/*!
 * SJS 6.15.1
 */!function(){function e(t,e){return(e||"")+" (SystemJS https://github.com/systemjs/systemjs/blob/main/docs/errors.md#"+t+")"}function n(t,e){if(-1!==t.indexOf("\\")&&(t=t.replace(E,"/")),"/"===t[0]&&"/"===t[1])return e.slice(0,e.indexOf(":")+1)+t;if("."===t[0]&&("/"===t[1]||"."===t[1]&&("/"===t[2]||2===t.length&&(t+="/"))||1===t.length&&(t+="/"))||"/"===t[0]){var n,r=e.slice(0,e.indexOf(":")+1);if(n="/"===e[r.length+1]?"file:"!==r?(n=e.slice(r.length+2)).slice(n.indexOf("/")+1):e.slice(8):e.slice(r.length+("/"===e[r.length])),"/"===t[0])return e.slice(0,e.length-n.length-1)+t;for(var o=n.slice(0,n.lastIndexOf("/")+1)+t,i=[],u=-1,c=0;c<o.length;c++)-1!==u?"/"===o[c]&&(i.push(o.slice(u,c+1)),u=-1):"."===o[c]?"."!==o[c+1]||"/"!==o[c+2]&&c+2!==o.length?"/"===o[c+1]||c+1===o.length?c+=1:u=c:(i.pop(),c+=2):u=c;return-1!==u&&i.push(o.slice(u)),e.slice(0,e.length-n.length)+i.join("")}}function r(t,e){return n(t,e)||(-1!==t.indexOf(":")?t:n("./"+t,e))}function o(t,e,r,o,i){for(var u in t){var c=n(u,r)||u,s=t[u];if("string"==typeof s){var l=a(o,n(s,r)||s,i);l?e[c]=l:f("W1",u,s)}}}function i(t,e,n){var i;for(i in t.imports&&o(t.imports,n.imports,e,n,null),t.scopes||{}){var u=r(i,e);o(t.scopes[i],n.scopes[u]||(n.scopes[u]={}),e,n,u)}for(i in t.depcache||{})n.depcache[r(i,e)]=t.depcache[i];for(i in t.integrity||{})n.integrity[r(i,e)]=t.integrity[i]}function u(t,e){if(e[t])return t;var n=t.length;do{var r=t.slice(0,n+1);if(r in e)return r}while(-1!==(n=t.lastIndexOf("/",n-1)))}function c(t,e){var n=u(t,e);if(n){var r=e[n];if(null===r)return;if(!(t.length>n.length&&"/"!==r[r.length-1]))return r+t.slice(n.length);f("W2",n,r)}}function f(t,n,r){console.warn(e(t,[r,n].join(", ")))}function a(t,e,n){for(var r=t.scopes,o=n&&u(n,r);o;){var i=c(e,r[o]);if(i)return i;o=u(o.slice(0,o.lastIndexOf("/")),r)}return c(e,t.imports)||-1!==e.indexOf(":")&&e}function s(){this[x]={}}function l(t,n,r,o){var i=t[x][n];if(i)return i;var u=[],c=Object.create(null);P&&Object.defineProperty(c,P,{value:"Module"});var f=Promise.resolve().then((function(){return t.instantiate(n,r,o)})).then((function(r){if(!r)throw Error(e(2,n));var o=r[1]((function(t,e){i.h=!0;var n=!1;if("string"==typeof t)t in c&&c[t]===e||(c[t]=e,n=!0);else{for(var r in t)e=t[r],r in c&&c[r]===e||(c[r]=e,n=!0);t&&t.__esModule&&(c.__esModule=t.__esModule)}if(n)for(var o=0;o<u.length;o++){var f=u[o];f&&f(c)}return e}),2===r[1].length?{import:function(e,r){return t.import(e,n,r)},meta:t.createContext(n)}:void 0);return i.e=o.execute||function(){},[r[0],o.setters||[],r[2]||[]]}),(function(t){throw i.e=null,i.er=t,t})),a=f.then((function(e){return Promise.all(e[0].map((function(r,o){var i=e[1][o],u=e[2][o];return Promise.resolve(t.resolve(r,n)).then((function(e){var r=l(t,e,n,u);return Promise.resolve(r.I).then((function(){return i&&(r.i.push(i),!r.h&&r.I||i(r.n)),r}))}))}))).then((function(t){i.d=t}))}));return i=t[x][n]={id:n,i:u,n:c,m:o,I:f,L:a,h:!1,d:void 0,e:void 0,er:void 0,E:void 0,C:void 0,p:void 0}}function p(t,e,n,r){if(!r[e.id])return r[e.id]=!0,Promise.resolve(e.L).then((function(){return e.p&&null!==e.p.e||(e.p=n),Promise.all(e.d.map((function(e){return p(t,e,n,r)})))})).catch((function(t){if(e.er)throw t;throw e.e=null,t}))}function h(t,e){return e.C=p(t,e,e,{}).then((function(){return v(t,e,{})})).then((function(){return e.n}))}function v(t,e,n){function r(){try{var t=i.call(I);if(t)return t=t.then((function(){e.C=e.n,e.E=null}),(function(t){throw e.er=t,e.E=null,t})),e.E=t;e.C=e.n,e.L=e.I=void 0}catch(n){throw e.er=n,n}}if(!n[e.id]){if(n[e.id]=!0,!e.e){if(e.er)throw e.er;return e.E?e.E:void 0}var o,i=e.e;return e.e=null,e.d.forEach((function(r){try{var i=v(t,r,n);i&&(o=o||[]).push(i)}catch(c){throw e.er=c,c}})),o?Promise.all(o).then(r):r()}}function d(){[].forEach.call(document.querySelectorAll("script"),(function(t){if(!t.sp)if("systemjs-module"===t.type){if(t.sp=!0,!t.src)return;System.import("import:"===t.src.slice(0,7)?t.src.slice(7):r(t.src,y)).catch((function(e){if(e.message.indexOf("https://github.com/systemjs/systemjs/blob/main/docs/errors.md#3")>-1){var n=document.createEvent("Event");n.initEvent("error",!1,!1),t.dispatchEvent(n)}return Promise.reject(e)}))}else if("systemjs-importmap"===t.type){t.sp=!0;var n=t.src?(System.fetch||fetch)(t.src,{integrity:t.integrity,priority:t.fetchPriority,passThrough:!0}).then((function(t){if(!t.ok)throw Error(t.status);return t.text()})).catch((function(n){return n.message=e("W4",t.src)+"\n"+n.message,console.warn(n),"function"==typeof t.onerror&&t.onerror(),"{}"})):t.innerHTML;M=M.then((function(){return n})).then((function(n){!function(t,n,r){var o={};try{o=JSON.parse(n)}catch(c){console.warn(Error(e("W5")))}i(o,r,t)}(F,n,t.src||y)}))}}))}var y,m="undefined"!=typeof Symbol,g="undefined"!=typeof self,b="undefined"!=typeof document,w=g?self:t;if(b){var O=document.querySelector("base[href]");O&&(y=O.href)}if(!y&&"undefined"!=typeof location){var S=(y=location.href.split("#")[0].split("?")[0]).lastIndexOf("/");-1!==S&&(y=y.slice(0,S+1))}var j,E=/\\/g,P=m&&Symbol.toStringTag,x=m?Symbol():"@",T=s.prototype;T.import=function(t,e,n){var r=this;return e&&"object"==typeof e&&(n=e,e=void 0),Promise.resolve(r.prepareImport()).then((function(){return r.resolve(t,e,n)})).then((function(t){var e=l(r,t,void 0,n);return e.C||h(r,e)}))},T.createContext=function(t){var e=this;return{url:t,resolve:function(n,r){return Promise.resolve(e.resolve(n,r||t))}}},T.register=function(t,e,n){j=[t,e,n]},T.getRegister=function(){var t=j;return j=void 0,t};var I=Object.freeze(Object.create(null));w.System=new s;var C,L,M=Promise.resolve(),F={imports:{},scopes:{},depcache:{},integrity:{}},R=b;if(T.prepareImport=function(t){return(R||t)&&(d(),R=!1),M},T.getImportMap=function(){return JSON.parse(JSON.stringify(F))},b&&(d(),window.addEventListener("DOMContentLoaded",d)),T.addImportMap=function(t,e){i(t,e||y,F)},b){window.addEventListener("error",(function(t){A=t.filename,k=t.error}));var _=location.origin}T.createScript=function(t){var e=document.createElement("script");e.async=!0,t.indexOf(_+"/")&&(e.crossOrigin="anonymous");var n=F.integrity[t];return n&&(e.integrity=n),e.src=t,e};var A,k,N={},z=T.register;T.register=function(t,e){if(b&&"loading"===document.readyState&&"string"!=typeof t){var n=document.querySelectorAll("script[src]"),r=n[n.length-1];if(r){C=t;var o=this;L=setTimeout((function(){N[r.src]=[t,e],o.import(r.src)}))}}else C=void 0;return z.call(this,t,e)},T.instantiate=function(t,n){var r=N[t];if(r)return delete N[t],r;var o=this;return Promise.resolve(T.createScript(t)).then((function(r){return new Promise((function(i,u){r.addEventListener("error",(function(){u(Error(e(3,[t,n].join(", "))))})),r.addEventListener("load",(function(){if(document.head.removeChild(r),A===t)u(k);else{var e=o.getRegister(t);e&&e[0]===C&&clearTimeout(L),i(e)}})),document.head.appendChild(r)}))}))},T.shouldFetch=function(){return!1},"undefined"!=typeof fetch&&(T.fetch=fetch);var D=T.instantiate,W=/^(text|application)\/(x-)?javascript(;|$)/;T.instantiate=function(t,n,r){var o=this;return this.shouldFetch(t,n,r)?this.fetch(t,{credentials:"same-origin",integrity:F.integrity[t],meta:r}).then((function(r){if(!r.ok)throw Error(e(7,[r.status,r.statusText,t,n].join(", ")));var i=r.headers.get("content-type");if(!i||!W.test(i))throw Error(e(4,i));return r.text().then((function(e){return e.indexOf("//# sourceURL=")<0&&(e+="\n//# sourceURL="+t),(0,eval)(e),o.getRegister(t)}))})):D.apply(this,arguments)},T.resolve=function(t,r){return a(F,n(t,r=r||y)||t,r)||function(t,n){throw Error(e(8,[t,n].join(", ")))}(t,r)};var G=T.instantiate;T.instantiate=function(t,e,n){var r=F.depcache[t];if(r)for(var o=0;o<r.length;o++)l(this,this.resolve(r[o],t),t);return G.call(this,t,e,n)},g&&"function"==typeof importScripts&&(T.instantiate=function(t){var e=this;return Promise.resolve().then((function(){return importScripts(t),e.getRegister(t)}))})}();

import{H as t,K as e,S as s,o as r,h as a,v as l,w as c,l as n,k as o,t as i,x as d,N as p,E as g,O as m}from"./index-awKTxnvj.js";const u=""+new URL("401-636EFLNQ.svg",import.meta.url).href,f=""+new URL("404-DHovWM90.svg",import.meta.url).href,y={class:"errorPage-container"},v={class:"error-img"},h=["alt"],k=["alt"],x={class:"error-content"},_={class:"error-title"},S={class:"desc"},w={class:"btn"};const E=t({props:{src:{type:String,default:""},type:{type:String,default:""},title:{type:String,default:""},msg:{type:String,default:""}},setup(){const{t:t}=e(),r=s();return{t:t,handleBack:()=>{r.replace("/")}}}},[["render",function(t,e,s,E,L,P){const b=p,B=g,C=m;return r(),a("div",y,[l(C,{gutter:10,class:"error-row"},{default:c((()=>[l(b,{class:"error-col",xs:24,sm:24,md:10,lg:10,xl:10},{default:c((()=>[n("div",v,["401"===s.type?(r(),a("img",{key:0,src:u,class:"img",alt:s.type},null,8,h)):o("",!0),"404"===s.type?(r(),a("img",{key:1,src:f,class:"img",alt:s.type},null,8,k)):o("",!0)])])),_:1}),l(b,{xs:24,sm:24,md:10,lg:10,xl:10},{default:c((()=>[n("div",x,[n("h2",_,i(E.t("errorPages.title")),1),n("h3",null,i(s.title),1),n("p",S,i(s.msg),1),n("div",w,[l(B,{type:"primary",onClick:E.handleBack},{default:c((()=>[d(i(E.t("errorPages.btn")),1)])),_:1},8,["onClick"])])])])),_:1})])),_:1})])}],["__scopeId","data-v-1c6c718f"]]);export{E};
//# sourceMappingURL=index-BYqWM-yL.js.map

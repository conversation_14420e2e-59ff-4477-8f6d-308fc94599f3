import{G as e,H as t,e as a,p as o,J as l,L as s,a as n,al as c,as as i,T as r,ax as d,V as h,o as u,h as p,v as g,w as m,j as f,l as b,x as _,a5 as v,F as y,r as k,C as w,a6 as C,ae as x,af as I,n as j,t as T,ag as D,k as S,ah as M,ab as E,E as B,ai as O,ad as A,a4 as P,aj as F,ao as R,ap as z,aq as V,aw as $,a9 as N,au as q,P as H,Q as U}from"./index-awKTxnvj.js";import{_ as L}from"./ProjectTypeFilter--v8GjLSk.js";import{E as G,b as W,a as J}from"./index-ENIpyTzl.js";import{E as Q,a as X,b as K,v as Y}from"./directive-CeALrXM5.js";import{E as Z}from"./index-BWOrXwLB.js";import{E as ee}from"./index-DeelQlL1.js";import"./index-TjDDNAcU.js";function te(){return e.get("https://autorelease.chatbot.shopee.io/api/get_jira_release_list_finished",{params:{project_type:"SPCB,SPCT",release_type:"Bus,Adhoc,Hotfix"}}).then((function(e){return e})).catch((function(e){console.log(e)}))}e.defaults.timeout=5e4,e.interceptors.request.use((e=>e),(e=>Promise.error(e)));const ae=e=>(H("data-v-d8982ff0"),e=e(),U(),e),oe={class:"index-conntainer"},le={class:"ar-container"},se=["id"],ne={class:"ar-container"},ce=ae((()=>b("br",null,null,-1))),ie={class:"ar-container"},re={class:"dialog-footer"},de=["href"],he=ae((()=>b("br",null,null,-1))),ue=ae((()=>b("br",null,null,-1))),pe=ae((()=>b("br",null,null,-1))),ge={key:0},me=ae((()=>b("br",null,null,-1))),fe={style:{display:"flex"}},be=t(a({__name:"releaseHistory",setup(e){o(!1);const t=o();o(!1),o(!1);const a=o(!1);o("first");const H=l([]),U=l([]),ae=l([]),be=l(["shopee-chatbot-intent","shopee-chatbot-admin","shopee-chatbot-adminasynctask","shopee-chatbot-adminconfigservice","shopee-chatbot-adminservice","shopee-chatbot-agentcontrol","shopee-chatbot-asynctask","shopee-chatbot-auditlog","shopee-chatbot-botapi","shopee-chatbot-context","shopee-chatbot-dm","shopee-chatbot-featurecenter","shopee-chatbot-intentclarification","shopee-chatbot-messageasynctask","shopee-chatbot-messageservice","shopee-chatbot-messageverification","shopee-chatbot-nlu","shopee-chatbot-ordercard","shopee-chatbot-pilotapi","shopee-chatbotcommon-adminasynctask","shopee-chatbotcommon-adminconfigservice","shopee-chatbotcommon-adminservice","shopee-chatbotcommon-agentcontrol","shopee-chatbotcommon-asynctask","shopee-chatbotcommon-botapi","shopee-chatbotcommon-context","shopee-chatbotcommon-dm","shopee-chatbotcommon-featurecenter","shopee-chatbotcommon-nlu","shopee-chatbotcommon-productrecommend","shopee-chatbotcommon-rulebaseservice","shopee-chatbotcommon-shopconsole","shopee-chatbot-websocketgwy","shopee-chatbotcommon-logic","shopee-chatbotcommon-msgdetection"]),_e=l(["shopee-chatbot-autotraining","shopee-annotation-admin","shopee-annotation-asynctask","shopee-agorithmservice-component","shopee-chatbot-experimentmanagement","shopee-chatbot-featureapiproxy","shopee-chatbot-modelgw","shopee-chatbot-realtime","shopee-chatbot-recallmanager","shopee-chatbot-recallservice","shopee-chatbot-recommendation","shopee-chatbotcommon-apadmin","shopee-chatbotcommon-apasynctask","shopee-chatbotcommon-component","shopee-chatbotcommon-experimentmanagement","shopee-chatbotcommon-featureapiproxy","shopee-chatbotcommon-intent","shopee-chatbotcommon-kbadmin","shopee-chatbotcommon-kbapi","shopee-chatbotcommon-kbasynctask","shopee-chatbotcommon-kblabelclarification","shopee-chatbotcommon-modelgw","shopee-knowledgebase-admin","shopee-knowledgebase-api","shopee-knowledgebase-asynctask","shopee-knowledgebase-labelclarification","shopee-chatbotcommon-promptmanagements","shopee-knowledgeplatform-offline","shopee-knowledgeplatform-admin","shopee-knowledgeplatform-api","shopee-knowledgeplatform-qa_tools"]),ve=l(["shopee-chatbot-api","shopee-chatbot-autotraining","shopee-chatbotcommon-tfapiproxy","shopee-chatbotcommon-tfeditor","shopee-chatbotcommon-tfserving","shopee-chatbotcommon-tfvariateserving","shopee-taskflow-apiproxy","shopee-taskflow-editor","shopee-taskflow-taskflowserving","shopee-taskflow-taskflowsop","shopee-taskflow-variateserving"]),ye=l(["shopee-autotrainingportal-adminstatic","shopee-annotation-adminstatic","shopee-cbrcmdplt-rcmdpltstatic","shopee-chatbot-adminstatic","shopee-chatbot-chatbotcsatstatic","shopee-chatbot-chatbotrnstatic","shopee-chatbot-chatbotstatic","shopee-chatbot-csatstatic","shopee-chatbot-dashboardstatic","shopee-chatbot-tmcstatic","shopee-chatbotcommon-admincommonsaasstatic","shopee-chatbotcommon-adminsaasstatic","shopee-chatbotcommon-adminstatic","shopee-chatbotcommon-annotationadminstatic","shopee-chatbotcommon-apadminsaasstatic","shopee-chatbotcommon-csatstatic","shopee-chatbotcommon-kbadmincommonsaasstatic","shopee-chatbotcommon-kbadminsaasstatic","shopee-chatbotcommon-shopconsolestatic","shopee-chatbotcommon-static","shopee-chatbotcommon-tfeadmincommonsaasstatic","shopee-chatbotcommon-tfeadminsaasstatic","shopee-chatbotcommon-tmcsaasstatic","shopee-gec-gecstatic","shopee-knowledgebase-adminstatic","shopee-taskflow-adminstatic","shopee-cschat-h5","shopee-knowledgeplatform-adminstatic","shopee-knowledgeplatform-guidesstatic","shopee-knowledgeplatformnode-knowledgeplatformnode","shopee-chatbot-insights","shopee-chatbotcommon-insightssaasstatic-test","shopee-chatbot-mmfchatbotconsole"]);function ke(e){return H.includes(e)?ye.includes(e):!!U.includes(e)&&(be.includes(e)||_e.includes(e)||ve.includes(e))}o([]),s((async()=>{try{const e=await te();if(console.log("API返回数据:",e),!e||!e.data||!e.data.data)return void console.error("API返回数据格式不正确:",e);const a=e.data.data;if(console.log("解析后的发布单数据:",a),t.value){Array.isArray(a)&&a.includes(t.value)||(console.log("之前选择的发布单不在当前列表中，清空选择"),t.value="")}if(!t.value){const e=new Date;e.setHours(0,0,0,0);let o=null,l=Infinity;Array.isArray(a)&&a.forEach((a=>{console.log("处理项目:",a);const s=a.match(/(bus|adhoc|hotfix)-(\d{6,8})/i);if(s){const n=s[2];let c,i,r;6===n.length?(c=parseInt("20"+n.substring(0,2)),i=parseInt(n.substring(2,4))-1,r=parseInt(n.substring(4,6))):8===n.length&&(c=parseInt(n.substring(0,4)),i=parseInt(n.substring(4,6))-1,r=parseInt(n.substring(6,8)));const d=new Date(c,i,r);console.log(`日期解析: ${a} => ${d.toISOString().split("T")[0]}`);const h=e.getTime()-d.getTime(),u=Math.floor(h/864e5);u>=0&&u<l&&(l=u,o=d,t.value=a,console.log("自动选择历史发布单:",t.value,"天数差:",u))}}))}if(Ne.value=a,qe.splice(0,qe.length),Array.isArray(a)&&a.forEach((e=>{qe.push({title:e})})),console.log("处理后的项目列表:",qe),Re(),t.value&&Ue(t.value),document.body.innerHTML.trim().length>0){const e=document.querySelector(".el-empty");e&&(e.style.display="none")}}catch(e){console.error("获取发布单列表失败:",e)}}));const we=n((()=>{const e={},t=be.map((e=>Object.keys(e))).flat();for(const a of U)if(t.includes(a)){const t=be.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)})),Ce=n((()=>{const e={},t=_e.map((e=>Object.keys(e))).flat();for(const a of U)if(t.includes(a)){const t=_e.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)})),xe=n((()=>{const e={},t=ve.map((e=>Object.keys(e))).flat();for(const a of U)if(t.includes(a)){const t=ve.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)})),Ie=n((()=>{const e={},t=ye.map((e=>Object.keys(e))).flat();for(const a of H)if(t.includes(a)){const t=ye.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)}));let je=o(!1),Te=o(!1),De=o(!1),Se=o(!1),Me=o(!1),Ee=o(!1),Be=o(0);o("wait"),o("wait");const Oe=o("SPCB"),Ae=o(localStorage.getItem("releaseTypeFilter")||"bus"),Pe=o([]);s((async()=>{const e=localStorage.getItem("releaseTypeFilter");e&&(Ae.value=e);try{const e=(await te()).data;if(console.log("获取到的发布单列表:",e),qe.splice(0,qe.length),Array.isArray(e)&&e.forEach((e=>{qe.push(e)})),Re(),Pe.value.length>0&&!t.value&&Ze(),t.value&&Ue(t.value),document.body.innerHTML.trim().length>0){const e=document.querySelector(".el-empty");e&&(e.style.display="none")}}catch(a){console.error("获取发布单列表失败:",a),c.error("获取发布单列表失败")}}));const Fe=()=>{localStorage.setItem("releaseTypeFilter",Ae.value),Re(),Pe.value.length>0?Ze():(t.value="",He.splice(0,He.length))},Re=()=>{console.log("过滤项目列表，当前过滤类型:",Ae.value,"项目类型:",Oe.value),qe.length&&(Pe.value=qe.filter((e=>{const t="all"===Ae.value||e.release_type?.toLowerCase()===Ae.value.toLowerCase(),a=e.project_type===Oe.value;return t&&a})),console.log("过滤后项目数量:",Pe.value.length),0===Pe.value.length&&(t.value="",He.splice(0,He.length)))},ze=o(),Ve=o(!1),$e=(e,t)=>t.type===e,Ne=o(),qe=l([]);let He=l([]);async function Ue(e){if(!e)return console.log("没有选择发布单，不进行数据查询"),He.splice(0,He.length),Me.value=!1,Se.value=!1,Ee.value=!1,U.splice(0,U.length),H.splice(0,H.length),void ae.splice(0,ae.length);Me.value=!1,Se.value=!1,console.log(`获取发布单数据: ${e}`),await async function(e){console.log(He);const t=new Set(He.map((e=>e.jira_key)));ae.splice(0,ae.length),U.splice(0,U.length),H.splice(0,H.length),console.log(`get data for ${e}`);let a,o={title:e};a=await $(o),console.log(a),console.log(await $(o)),console.log(a),0===a.length?He.splice(0,He.length):(He.splice(0,He.length),a.data.forEach((e=>{console.log(e),t.has(e.jira_key)||(He.push(e),t.add(e.jira_key))}))),console.log(He)}(e),Ve.value=!0;let t=o();if(console.log("releaseTableData前:",He),t.value=await i(He),Ve.value=!1,console.log("API响应:",t.value),!t.value||!t.value.data||0===t.value.data.length)return console.log("API返回数据为空或格式不正确"),He.splice(0,He.length),void(Ee.value=!1);He.length,He.forEach((e=>{const a=t.value.data.find((t=>e.jira_key===t.feature_key));a&&(console.log("匹配项数据:",a),e.type=a.type,e.jira_key=a.feature_key,e.jira_link=`https://jira.shopee.io/browse/${a.feature_key}`,e.jira_title=a.feature_title,e.bug_resolved=Number(a.bug_resolved||0),e.bug_total=Number(a.bug_total||0),console.log(`Bug数据: ${e.bug_resolved}/${e.bug_total}`),a.signoff_status?e.sign_off=a.signoff_status:e.sign_off="",a.config_center?e.config_center=a.config_center:e.config_center="",a.shopee_region?e.region=a.shopee_region:e.region="",a.redis_check?e.redis_change=a.redis_check:e.redis_change="",a.result?e.result=a.result:e.result="",a.merge_list?e.merge_list=a.merge_list:e.merge_list="",a.status?e.status=a.status:e.status="",a.Code_Merged?e.Code_Merged=a.Code_Merged:e.Code_Merged="",a.DB_Change?e.DB_Change=a.DB_Change:e.DB_Change="",a.dev_pic?e.dev_pic=a.dev_pic:e.dev_pic="",a.PM?e.PM=a.PM:e.PM="",a.qa_pic?e.qa_pic=a.qa_pic:e.qa_pic="",e.services="",a.services_list.services_list_be.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,U.includes(t)||(U.push(t),ae.push(t))})),a.services_list.services_list_fe.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,H.includes(t)||(H.push(t),ae.push(t))})))})),0!==H.length&&(Se.value=!0),0!==U.length&&(Me.value=!0),console.log("fe_services:",H),console.log("be_services:",U),console.log("最终表格数据:",He),Ee.value=!0}function Le(e){return"TO DO"===e?"#42526e":"Done"===e?"#00875a":"Waiting"===e?"#42526e":"#0052CC"}function Ge(e){return"TO DO"===e?"TO DO":"Done"===e?"DONE":"Waiting"===e?"WAITING":"Icebox"===e?"ICEBOX":"Doing"===e?"DOING":"UAT"===e?"UAT":"Delivering"===e?"DELIVERING":"Developing"===e?"DEVELOPING":"Testing"===e?"TESTING":e}const We=l({name:"",merge:!0});function Je(e){if(e){let t=e.replace(/【Release】|发布单/g,"").replace(/\s/g,"");return console.log(t),t}return""}r(ze,((e,t)=>{""!==e&&e!==t&&(He.splice(0,He.length),Ue(e)),We.name=Je(e)})),r(He,((e,t)=>{localStorage.setItem("releaseTableData",JSON.stringify(He)),0===He.length&&(De.value=!1),0!==He.length&&(De.value=!0)})),r(ae,((e,t)=>{0===ae.length&&(je.value=!1),0!==ae.length&&(je.value=!0)})),d((()=>{const e=localStorage.getItem("selectedProject");let t=localStorage.getItem("active");e&&(ze.value=e),t&&(Be.value=t)})),d((()=>{localStorage.setItem("selectedProject",ze.value)}));const Qe=o([]),Xe=o([]),Ke=o([]),Ye=o([]);n((()=>Qe.value.map(((e,t)=>({...e,id:t+1}))))),n((()=>Xe.value.map(((e,t)=>({...e,id:t+1}))))),n((()=>Ke.value.map(((e,t)=>({...e,id:t+1}))))),n((()=>Ye.value.map(((e,t)=>({...e,id:t+1}))))),l([]),r(t,((e,t)=>{""!==e&&(He.splice(0,He.length),Ue(e))})),d((()=>{const e=localStorage.getItem("selectedProjectFinished");let a=localStorage.getItem("active");e&&(t.value=e),a&&(Be.value=a)})),d((()=>{localStorage.setItem("selectedProjectFinished",t.value)})),r([Ae,Oe],(()=>{localStorage.setItem("releaseTypeFilter",Ae.value),Re(),Pe.value.length>0?Ze():(t.value="",He.splice(0,He.length))})),r(t,((e,t)=>{e!==t&&(He.splice(0,He.length),Ue(e)),We.name=Je(e)}));const Ze=()=>{const e=new Date;let a=-1,o=Infinity,l=null;console.log("开始寻找最近的历史发布单..."),Pe.value.forEach(((t,s)=>{const n=/(?:Bus|Adhoc|Hotfix)-(\d{8})/i;let c=t.title.match(/(?:Bus|Adhoc|Hotfix)-(\d{6})/i),i=null,r=null;if(c&&c[1]){i=c[1];const e=parseInt("20"+i.substring(0,2)),t=parseInt(i.substring(2,4))-1,a=parseInt(i.substring(4,6));r=new Date(e,t,a)}else if(c=t.title.match(n),c&&c[1]){i=c[1];const e=parseInt(i.substring(0,4)),t=parseInt(i.substring(4,6))-1,a=parseInt(i.substring(6,8));r=new Date(e,t,a)}if(r&&!isNaN(r.getTime())){console.log(`项目: ${t.title}, 日期: ${r.toISOString().split("T")[0]}`);const n=e.getTime()-r.getTime(),c=Math.floor(n/864e5);(c>=0&&c<o||0===c&&-1===a)&&(o=c,a=s,l=t)}})),-1!==a&&l?(console.log(`自动选择最近的历史发布单: ${l.title}`),t.value=l.title,Ue(t.value)):(console.log("没有找到合适的历史发布单"),t.value="",He.length=0)};return(e,l)=>{const s=L,n=Q,i=X,r=N,d=Z,$=h("SuccessFilled"),H=q,U=K,te=ee,ae=Y;return u(),p("div",oe,[g(F,{name:"el-fade-in-linear"},{default:m((()=>[g(f(G),{class:"card",shadow:"hover",width:"50px",height:"50px"},{default:m((()=>[b("div",le,[g(s,{projectType:Oe.value,"onUpdate:projectType":l[0]||(l[0]=e=>Oe.value=e)},null,8,["projectType"]),g(i,{modelValue:Ae.value,"onUpdate:modelValue":l[1]||(l[1]=e=>Ae.value=e),class:"release-type-filter",onChange:Fe},{default:m((()=>[g(n,{label:"bus"},{default:m((()=>[_("Bus")])),_:1}),g(n,{label:"adhoc"},{default:m((()=>[_("Adhoc")])),_:1}),g(n,{label:"hotfix"},{default:m((()=>[_("Hotfix")])),_:1}),g(n,{label:"all"},{default:m((()=>[_("全部")])),_:1})])),_:1},8,["modelValue"]),g(f(v),{modelValue:t.value,"onUpdate:modelValue":l[2]||(l[2]=e=>t.value=e),filterable:"",clearable:"",placeholder:"please select release ticket",style:{width:"380px","margin-left":"10px"}},{default:m((()=>[(u(!0),p(y,null,k(Pe.value,(e=>(u(),w(f(C),{key:"string"==typeof e?e:e.title,label:"string"==typeof e?e:e.title,value:"string"==typeof e?e:e.title},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),g(f(x),{modelValue:f(Te),"onUpdate:modelValue":l[3]||(l[3]=e=>I(Te)?Te.value=e:Te=e),"show-close":!1},{header:m((({titleId:e,titleClass:t})=>[b("h4",{id:e,class:j(t)},T(ze.value),11,se)])),default:m((()=>[_(" 暂无失败信息。 ")])),_:1},8,["modelValue"])]),b("div",ne,[g(f(W),{data:f(He),stripe:"",border:"","highlight-current-row":"",fit:"","header-cell-style":{background:"#cacfd7",color:"#606266"},"empty-text":"暂无数据"},{default:m((()=>[g(f(J),{label:"编号","min-width":"21","header-align":"center",align:"center"},{default:m((e=>[_(T(e.$index+1),1)])),_:1}),g(f(J),{prop:"type",label:"类型","header-align":"center",align:"center","min-width":"30",filters:[{text:"Epic",value:"Epic"},{text:"Bug",value:"Bug"},{text:"Task",value:"Task"},{text:"Sub-task",value:"Sub-task"},{text:"Story",value:"Story"}],"filter-method":$e,"filter-placement":"bottom-end"},{default:m((({row:e})=>{return[g(r,{class:j((t=e.type,"Epic"===t?"Epic-icon":"Sub-task"===t?"ST-icon":"Task"===t?"Task-icon":"Bug"===t?"Bug-icon":"Story"===t?"Story-icon":void 0))},null,8,["class"])];var t})),_:1}),g(f(J),{prop:"jira_key",label:"单号","min-width":60,"header-align":"center",align:"center"},{default:m((({row:e})=>[g(d,{underline:!1,href:e.jira_link,target:"_blank",type:"primary"},{default:m((()=>[_(T(e.jira_key),1)])),_:2},1032,["href"])])),_:1}),g(f(J),{prop:"jira_title",label:"需求名","min-width":150}),g(f(J),{prop:"bug_resolution_rate",label:"Bug解决率","min-width":80,"header-align":"center",align:"center"},{default:m((({row:e})=>[b("span",{style:D({color:Number(e.bug_resolved||0)===Number(e.bug_total||0)?"#67C23A":"#F56C6C"})},["number"==typeof e.bug_resolved&&"number"==typeof e.bug_total?(u(),p(y,{key:0},[_(T(e.bug_resolved)+"/"+T(e.bug_total),1)],64)):(u(),p(y,{key:1},[_(T(e.bug_resolved||0)+"/"+T(e.bug_total||0),1)],64))],4)])),_:1}),g(f(J),{label:"周一","header-align":"center",align:"center"},{default:m((()=>[g(f(J),{prop:"sign_off",label:"Signed off","header-align":"center",align:"center","min-width":"40"},{header:m((()=>[_(" Signed"),ce,_("off ")])),default:m((({row:e})=>["Confirmed"===e.sign_off?(u(),w(r,{key:0,size:20,color:"Confirmed"===e.sign_off?"#67c23a":"#F56C67"},{default:m((()=>[g($)])),_:2},1032,["color"])):S("",!0),""===e.sign_off?(u(),w(r,{key:1,size:20,color:"pass"===e.sign_off?"#67c23a":"#F56C67"},{default:m((()=>[g(f(M))])),_:2},1032,["color"])):S("",!0)])),_:1})])),_:1}),g(f(J),{label:"周二","header-align":"center",align:"center"},{default:m((()=>[g(f(J),{type:"expand",label:"提MR","min-width":"32"},{default:m((e=>[b("div",null,[b("div",ie,[g(H,{class:"box-item",effect:"customized",content:"点击创建MR",placement:"top-start"},{default:m((()=>[E((u(),w(f(B),{type:"danger",onClick:l[4]||(l[4]=e=>a.value=!0),size:"small",icon:f(O),"element-loading-text":"AR正在创建MR, 请耐心等待..."},{default:m((()=>[_("创建 ")])),_:1},8,["icon"])),[[ae,Ve.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1}),g(H,{class:"box-item",effect:"customized",content:"点击复制",placement:"top-start"},{default:m((()=>[E((u(),w(f(B),{type:"primary",size:"small",onClick:t=>async function(e){let t=o();ze.value,t=await z(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{c({message:"恭喜，MR信息已复制到剪切板！",type:"success"})})).catch((e=>{c.error("复制剪切板失败！")}))}(e),icon:f(A),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:m((()=>[_("复制 ")])),_:2},1032,["onClick","icon"])),[[ae,Ve.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024),g(H,{class:"box-item",effect:"customized",content:"点击发送MR提醒到seatalk",placement:"top-start"},{default:m((()=>[E((u(),w(f(B),{type:"primary",size:"small",onClick:t=>async function(e){let t=o();t=await R(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{c({message:"恭喜，MR信息已发送到seatalk！",type:"success"})})).catch((e=>{c.error("MR信息发送失败！")}))}(e),icon:f(A),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:m((()=>[_("发送 ")])),_:2},1032,["onClick","icon"])),[[ae,Ve.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024)]),g(U,{modelValue:a.value,"onUpdate:modelValue":l[6]||(l[6]=e=>a.value=e),title:"Warning",width:"30%","align-center":""},{footer:m((()=>[b("span",re,[g(f(B),{onClick:l[5]||(l[5]=e=>a.value=!1)},{default:m((()=>[_("取消")])),_:1}),g(f(B),{type:"primary",onClick:t=>async function(e){console.log(e.row),o(),await V(e.row)}(e)},{default:m((()=>[_(" 确认 ")])),_:2},1032,["onClick"])])])),default:m((()=>[b("span",null,"请确认是否开始自动提MR？发布单： "+T(ze.value),1)])),_:2},1032,["modelValue"]),g(f(W),{data:e.row.merge_list,border:"","header-cell-style":{background:"#def1ce",color:"#606266"}},{default:m((()=>[g(f(J),{label:"仓库",prop:"repo_name"}),g(f(J),{label:"分支",prop:"branch_name"}),g(f(J),{label:"PIC",prop:"pic"}),g(f(J),{label:"MR地址",prop:"web_url"},{default:m((({row:e})=>[b("a",{href:e.web_url,target:"_blank"},T(e.web_url),9,de)])),_:1}),g(f(J),{label:"MR状态",prop:"merge_status"}),g(f(J),{label:"MR作者",prop:"author"})])),_:2},1032,["data"])])])),_:1}),g(f(J),{prop:"Code_Merged",label:"Code Merged","header-align":"center",align:"center","min-width":"40"},{header:m((()=>[_(" Code"),he,_("Merged ")])),default:m((({row:e})=>["Confirmed"===e.Code_Merged?(u(),w(r,{key:0,size:20,color:"Confirmed"===e.Code_Merged?"#67c23a":"#F56C67"},{default:m((()=>[g($)])),_:2},1032,["color"])):S("",!0),""===e.Code_Merged?(u(),w(r,{key:1,size:20,color:"pass"===e.Code_Merged?"#67c23a":"#F56C67"},{default:m((()=>[g(f(M))])),_:2},1032,["color"])):S("",!0)])),_:1})])),_:1}),g(f(J),{label:"周三","header-align":"center",align:"center"},{default:m((()=>[g(f(J),{prop:"config_center",label:"Config Changed","header-align":"center",align:"center","min-width":"43"},{header:m((()=>[_(" Config"),ue,_("Changed ")])),default:m((({row:e})=>["Confirmed"===e.config_center?(u(),w(r,{key:0,size:20,color:"Confirmed"===e.config_center?"#67c23a":"#F56C67"},{default:m((()=>[g($)])),_:2},1032,["color"])):S("",!0),""===e.config_center?(u(),w(r,{key:1,size:20,color:"pass"===e.config_center?"#67c23a":"#F56C67"},{default:m((()=>[g(f(M))])),_:2},1032,["color"])):S("",!0)])),_:1}),g(f(J),{prop:"DB_Change",label:"DB Changed","header-align":"center",align:"center","min-width":"43"},{default:m((({row:e})=>["Confirmed"===e.DB_Change?(u(),w(r,{key:0,size:20,color:"Confirmed"===e.DB_Change?"#67c23a":"#F56C67"},{default:m((()=>[g($)])),_:2},1032,["color"])):S("",!0),""===e.DB_Change?(u(),w(r,{key:1,size:20,color:"pass"===e.DB_Change?"#67c23a":"#F56C67"},{default:m((()=>[g(f(M))])),_:2},1032,["color"])):S("",!0)])),header:m((()=>[_(" DB"),pe,_("Changed ")])),_:1}),g(f(J),{prop:"services",label:"服务","min-width":150,"header-align":"center",align:"center",style:{"white-space":"pre-wrap"}},{default:m((({row:e})=>[""!==e.services?(u(),p("div",ge,[(u(!0),p(y,null,k(e.services.split("\n"),((e,t)=>(u(),p("div",{style:D({color:ke(e)?"inherit":"#F56C67"})},T(e),5)))),256))])):S("",!0),""===e.services?(u(),w(r,{key:1,size:20,color:"#F56C67"},{default:m((()=>[g(f(M))])),_:1})):S("",!0)])),_:1}),g(f(J),{prop:"region",label:"Region","header-align":"center",align:"center","min-width":"37"},{default:m((({row:e})=>[""!==e.region?(u(),w(te,{key:0,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:m((()=>[_(T(e.region),1)])),_:2},1032,["color"])):S("",!0),""===e.region?(u(),w(r,{key:1,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:m((()=>[g(f(M))])),_:2},1032,["color"])):S("",!0)])),_:1})])),_:1}),g(f(J),{prop:"PM",label:"PM","header-align":"center",align:"center","min-width":"50"},{default:m((({row:e})=>[_(T(e.PM),1)])),_:1}),g(f(J),{prop:"dev_pic",label:"DEV PIC","header-align":"center",align:"center","min-width":"40"},{header:m((()=>[_(" DEV"),me,_("PIC ")])),default:m((({row:e})=>[_(T(e.dev_pic),1)])),_:1}),g(f(J),{prop:"qa_pic",label:"QA","header-align":"center",align:"center","min-width":"50"},{default:m((({row:e})=>[_(T(e.qa_pic),1)])),_:1}),g(f(J),{prop:"status",label:"Status","header-align":"center",align:"center","min-width":"60"},{default:m((({row:e})=>{return[g(f(P),{class:"bold-text",effect:"dark",type:(t=e.status,"TO DO"===t?"info":"Done"===t?"success":"Waiting"===t?"info":"Icebox"===t?"icebox":"Doing"===t?"doing":"UAT"===t?"uat":"Delivering"===t?"delivering":"Developing"===t?"developing":"Testing"===t?"testing":void 0),color:Le(e.status)},{default:m((()=>[_(T(Ge(e.status)),1)])),_:2},1032,["type","color"])];var t})),_:1})])),_:1},8,["data"])]),b("div",fe,[g(f(W),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e78181",color:"#f8f7f7"},data:we.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:m((()=>[g(f(J),{"header-align":"center",label:"平台BE1组"},{default:m((e=>[g(d,{href:e.row.link,target:"_blank",underline:!1},{default:m((()=>[_(T(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),g(f(W),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#819ee7",color:"#f8f7f7"},"header-align":"center",data:Ce.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:m((()=>[g(f(J),{"header-align":"center",label:"平台BE2组"},{default:m((e=>[g(d,{href:e.row.link,target:"_blank",underline:!1},{default:m((()=>[_(T(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),g(f(W),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#81e7c8",color:"#f8f7f7"},"header-align":"center",data:xe.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:m((()=>[g(f(J),{"header-align":"center",label:"功能BE组"},{default:m((e=>[g(d,{href:e.row.link,target:"_blank",underline:!1},{default:m((()=>[_(T(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),g(f(W),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e7a881",color:"#f8f7f7"},"header-align":"center",data:Ie.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:m((()=>[g(f(J),{"header-align":"center",label:"FE组"},{default:m((e=>[g(d,{href:e.row.link,target:"_blank",underline:!1},{default:m((()=>[_(T(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"])])])),_:1})])),_:1})])}}}),[["__scopeId","data-v-d8982ff0"]]);export{be as default};
//# sourceMappingURL=releaseHistory--Pd21q9h.js.map

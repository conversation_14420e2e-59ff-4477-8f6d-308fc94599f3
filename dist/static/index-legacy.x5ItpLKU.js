System.register(["./index-legacy.C52nWfoy.js"],(function(e,t){"use strict";var a,n,s,l,i,r,u,p,c,m,y,g,d,f,o,b,v;return{setters:[e=>{a=e.b,n=e.bP,s=e.e,l=e.b7,i=e.f,r=e.a,u=e.aN,p=e.o,c=e.C,m=e.w,y=e.m,g=e.n,d=e.j,f=e.ag,o=e.az,b=e._,v=e.D}],execute:function(){const t=a({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:n,default:""},truncated:{type:Boolean},lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),x=s({name:"ElText"}),S=s({...x,props:t,setup(e){const t=e,a=l(),n=i("text"),s=r((()=>[n.b(),n.m(t.type),n.m(a.value),n.is("truncated",t.truncated),n.is("line-clamp",!u(t.lineClamp))]));return(e,t)=>(p(),c(o(e.tag),{class:g(d(s)),style:f({"-webkit-line-clamp":e.lineClamp})},{default:m((()=>[y(e.$slots,"default")])),_:3},8,["class","style"]))}});e("E",v(b(S,[["__file","text.vue"]])))}}}));
//# sourceMappingURL=index-legacy.x5ItpLKU.js.map

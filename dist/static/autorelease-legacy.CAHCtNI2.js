System.register(["./index-legacy.C52nWfoy.js","./ProjectTypeFilter-legacy.wfFkpZSQ.js","./index-legacy.CAqey3Xi.js","./directive-legacy.xes9WOkH.js","./index-legacy.Co5M3uHU.js","./index-legacy.x5ItpLKU.js","./index-legacy.CNmEMj-H.js"],(function(e,t){"use strict";var o,a,c,s,n,l,h,i,p,d,r,m,b,_,g,u,f,y,v,k,w,x,C,j,M,T,D,S,I,z,E,$,O,R,B,V,F,P,A,q,N,U,G,L,W,Y,H,J,Q,X,Z,K,ee,te,oe,ae,ce,se,ne,le,he,ie,pe,de,re;return{setters:[e=>{o=e.H,a=e.e,c=e.p,s=e.J,n=e.a,l=e.L,h=e.aa,i=e.T,p=e.V,d=e.o,r=e.h,m=e.v,b=e.w,_=e.j,g=e.l,u=e.x,f=e.a5,y=e.F,v=e.r,k=e.C,w=e.a6,x=e.a4,C=e.t,j=e.ab,M=e.E,T=e.ac,D=e.ad,S=e.ae,I=e.af,z=e.n,E=e.a7,$=e.ag,O=e.k,R=e.ah,B=e.ai,V=e.aj,F=e.ak,P=e.al,A=e.am,q=e.an,N=e.ao,U=e.ap,G=e.aq,L=e.ar,W=e.as,Y=e.at,H=e.au,J=e.a9,Q=e.X,X=e.Y,Z=e.av,K=e.Z,ee=e.P,te=e.Q,oe=e.aw},e=>{ae=e._},e=>{ce=e.E,se=e.b,ne=e.a},e=>{le=e.E,he=e.a,ie=e.b,pe=e.v},e=>{de=e.E},e=>{re=e.E},null],execute:function(){const t=e=>(ee("data-v-341ca16e"),e=e(),te(),e),me={class:"index-conntainer"},be={class:"ar-container"},_e={style:{float:"left","font-size":"13px"}},ge={style:{float:"left"}},ue=["id"],fe={class:"ar-container"},ye={class:"ar-container"},ve=t((()=>g("br",null,null,-1))),ke={class:"ar-container"},we={class:"dialog-footer"},xe=["href"],Ce=t((()=>g("br",null,null,-1))),je=t((()=>g("br",null,null,-1))),Me=t((()=>g("br",null,null,-1))),Te={key:0},De=t((()=>g("br",null,null,-1))),Se={class:"ar-container"},Ie={style:{display:"flex"}},ze={style:{display:"flex","justify-content":"center"}},Ee=t((()=>g("span",null,"您正在进行live自动发布流程，请确认您的操作是否要继续。",-1))),$e={class:"dialog-footer"},Oe={style:{display:"flex"}},Re={style:{display:"flex"}},Be=["href"],Ve=a({__name:"autorelease",setup(e){const t=c(!1),o=c(!1);c(!1);const a=c(!1);c("first");const ee=s([]),te=s([]),Ve=s([]),Fe=s([{"shopee-chatbot-intent":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intent","shopee-chatbot-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.admin","shopee-chatbot-adminasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminasynctask","shopee-chatbot-adminconfigservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminconfigservice","shopee-chatbot-adminservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminservice","shopee-chatbot-agentcontrol":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.agentcontrol","shopee-chatbot-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.asynctask","shopee-chatbot-auditlog":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.auditlog","shopee-chatbot-botapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi","shopee-chatbot-context":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.context","shopee-chatbot-dm":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.dm","shopee-chatbot-featurecenter":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featurecenter","shopee-chatbot-intentclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intentclarification","shopee-chatbot-messageasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageasynctask","shopee-chatbot-messageservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageservice","shopee-chatbot-messageverification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageverification","shopee-chatbot-nlu":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.nlu","shopee-chatbot-ordercard":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.ordercard","shopee-chatbot-pilotapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.pilotapi","shopee-chatbotcommon-adminasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminasynctask","shopee-chatbotcommon-adminconfigservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminconfigservice","shopee-chatbotcommon-adminservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminservice","shopee-chatbotcommon-agentcontrol":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.agentcontrol","shopee-chatbotcommon-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.asynctask","shopee-chatbotcommon-botapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.botapi","shopee-chatbotcommon-context":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.context","shopee-chatbotcommon-dm":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.dm","shopee-chatbotcommon-featurecenter":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featurecenter","shopee-chatbotcommon-nlu":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu","shopee-chatbotcommon-productrecommend":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.productrecommend","shopee-chatbotcommon-rulebaseservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.rulebaseservice","shopee-chatbotcommon-shopconsole":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsole","shopee-chatbotcommon-intentclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intentclarification","shopee-chatbot-websocketgwy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.websocketgwy","shopee-csdata-metricservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.data.metricservice","shopee-chatbotcommon-logic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.ai_video_chatbot.engineer.logic","shopee-chatbotcommon-msgdetection":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.ai_video_chatbot.engineer.msgdetection"}]),Pe=s([{"shopee-chatbot-autotraining":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining","shopee-annotation-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.admin","shopee-annotation-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.asynctask","shopee-annotation-timetask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.timetask","shopee-annotation-dataproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.dataproxy","shopee-agorithmservice-component":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.ai_engineering.nlu_component","shopee-chatbot-experimentmanagement":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.experimentmanagement","shopee-chatbot-featureapiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featureapiproxy","shopee-chatbot-modelgw":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.modelgw","shopee-chatbot-realtime":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.realtime","shopee-chatbot-recallmanager":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallmanager","shopee-chatbot-recallservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallservice","shopee-chatbot-recommendation":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recommendation","shopee-chatbotcommon-apadmin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadmin","shopee-chatbotcommon-apasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apasynctask","shopee-chatbotcommon-apdataproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apdataproxy","shopee-chatbotcommon-component":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu_component","shopee-chatbotcommon-experimentmanagement":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.experimentmanagement","shopee-chatbotcommon-featureapiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featureapiproxy","shopee-chatbotcommon-intent":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intent","shopee-chatbotcommon-kbadmin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmin","shopee-chatbotcommon-kbapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbapi","shopee-chatbotcommon-kbasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbasynctask","shopee-chatbotcommon-kblabelclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kblabelclarification","shopee-chatbotcommon-modelgw":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.modelgw","shopee-knowledgebase-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.admin","shopee-knowledgebase-api":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.api","shopee-knowledgebase-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.asynctask","shopee-knowledgebase-labelclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.labelclarification","shopee-chatbotcommon-promptmanagements":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.promptmanagements","shopee-knowledgeplatform-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin","shopee-knowledgeplatform-api":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.api","shopee-knowledgeplatform-qa_tools":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.qa_tools","shopee-knowledgeplatform-offline":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.offline","shopee-chatbot-apiadmin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.apistore.apiadmin","shopee-chatbot-apiflowserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.apistore.apiflowserving"}]),Ae=s([{"shopee-chatbot-api":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.api","shopee-chatbot-autotraining":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining","shopee-chatbotcommon-tfapiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfapiproxy","shopee-chatbotcommon-tfeditor":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeditor","shopee-chatbotcommon-tfserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfserving","shopee-chatbotcommon-tfvariateserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfvariateserving","shopee-taskflow-apiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.apiproxy","shopee-taskflow-editor":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.editor","shopee-taskflow-taskflowserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowserving","shopee-taskflow-taskflowsop":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowsop","shopee-taskflow-variateserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.variateserving"}]),qe=s([{"shopee-autotrainingportal-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.autotrainingportal.adminstatic","shopee-annotation-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.adminstatic","shopee-cbrcmdplt-rcmdpltstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.cbrcmdplt.rcmdpltstatic","shopee-chatbot-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminstatic","shopee-chatbot-chatbotcsatstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotcsatstatic","shopee-chatbot-chatbotrnstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotrnstatic","shopee-chatbot-chatbotstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotstatic","shopee-chatbot-csatstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.csatstatic","shopee-chatbot-dashboardstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.dashboardstatic","shopee-chatbotcommon-admincommonsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.admincommonsaasstatic","shopee-chatbotcommon-adminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminsaasstatic","shopee-chatbotcommon-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminstatic","shopee-chatbotcommon-annotationadminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.annotationadminstatic","shopee-chatbotcommon-apadminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadminsaasstatic","shopee-chatbotcommon-csatstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.csatstatic","shopee-chatbotcommon-kbadmincommonsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmincommonsaasstatic","shopee-chatbotcommon-kbadminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadminsaasstatic","shopee-chatbotcommon-shopconsolestatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsolestatic","shopee-chatbotcommon-static":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.static","shopee-chatbotcommon-tfeadmincommonsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadmincommonsaasstatic","shopee-chatbotcommon-tfeadminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadminsaasstatic","shopee-chatbotcommon-tmcsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tmcsaasstatic","shopee-gec-gecstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.gecstatic","shopee-knowledgebase-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.adminstatic","shopee-taskflow-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.adminstatic","shopee-cschat-h5":"https://space.shopee.io/console/cmdb/overview/detail/shopee.customer_service_and_chatbot.customer_service.cschannel.cschat.h5","shopee-knowledgeplatform-adminstatic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.adminstatic","shopee-knowledgeplatformnode-knowledgeplatformnode":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin_portal","shopee-knowledgeplatform-guidesstatic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.guidesstatic","shopee-chatbot-insights":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.adminportal.insightstatic","shopee-chatbotcommon-insightssaasstatic-test":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.chatbotcommon.insightssaasstatic","shopee-chatbot-mmfchatbotconsole":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.mmfchatbotconsole","shopee-chatbot-h5mmfchatbotsharedrcstatic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.h5mmfchatbotsharedrcstatic","shopee-chatbot-tmcstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.tmcstatic"}]),Ne=()=>{window.open("https://monitoring.infra.sz.shopee.io/grafana/d/kj1f3huVk/chatbot-fa-ban-kan-ban?orgId=10&from=now-1h&to=now&refresh=30s","_blank")},Ue=n((()=>{const e={},t=Fe.map((e=>Object.keys(e))).flat();for(const o of te)if(t.includes(o)){const t=Fe.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)})),Ge=n((()=>{const e={},t=Pe.map((e=>Object.keys(e))).flat();for(const o of te)if(t.includes(o)){const t=Pe.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)})),Le=n((()=>{const e={},t=Ae.map((e=>Object.keys(e))).flat();for(const o of te)if(t.includes(o)){const t=Ae.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)})),We=n((()=>{const e={},t=qe.map((e=>Object.keys(e))).flat();for(const o of ee)if(t.includes(o)){const t=qe.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)}));let Ye=c(!1),He=c(!1),Je=c(!1),Qe=c(!1),Xe=c(!1),Ze=c(!1),Ke=c(0);c("wait"),c("wait");const et=c(localStorage.getItem("releaseTypeFilter")||"bus"),tt=c([]);l((()=>{const e=localStorage.getItem("releaseTypeFilter");e&&(et.value=e)}));const ot=()=>{localStorage.setItem("releaseTypeFilter",et.value),st(),tt.value.length>0?at():(nt.value="",dt.splice(0,dt.length))},at=()=>{const e=new Date;e.setHours(0,0,0,0);let t=null,o=null;console.log("尝试选择最近发布单，过滤后项目数量:",tt.value.length),tt.value.forEach((a=>{const c=a.title||"";let s=c.match(/(bus|adhoc|hotfix)[_-](\d{6})/i);if(s||(s=c.match(/(bus|adhoc|hotfix)[_-](\d{8})/i)),s){const c=s[2];let l,h,i;if(6===c.length){const e=parseInt(c.slice(0,2));l=e<50?2e3+e:1900+e,h=parseInt(c.slice(2,4))-1,i=parseInt(c.slice(4,6))}else{if(8!==c.length)return void console.log(`无法识别的日期格式: ${c}`);l=parseInt(c.slice(0,4)),h=parseInt(c.slice(4,6))-1,i=parseInt(c.slice(6,8))}try{const c=new Date(l,h,i,0,0,0);isNaN(c.getTime())?console.log(`日期无效: ${l}-${h+1}-${i}`):(console.log(`解析发布单 ${a.title}: ${l}-${h+1}-${i}, date=${c}, 今天=${e}`),c>=e&&(!t||c<t)&&(t=c,o=a,console.log(`找到更近的发布单: ${a.title}, 日期: ${c}`)))}catch(n){console.log(`日期解析错误: ${n.message}`)}}else console.log(`无法解析日期格式: ${a.title}`)})),o?(nt.value=o.title,console.log("自动选择最近发布单:",nt.value)):console.log("未找到合适的发布单自动选择")},ct=c(localStorage.getItem("projectTypeFilter")||"SPCB"),st=()=>{console.log("过滤项目列表，当前过滤类型:",et.value,"项目类型:",ct.value),pt.length&&(tt.value=pt.filter((e=>{const t="All"===et.value||e.title?.toLowerCase().includes(et.value.toLowerCase()),o=e.key&&e.key.startsWith(ct.value);return t&&o})),console.log("过滤后项目数量:",tt.value.length),0===tt.value.length&&(nt.value="",dt.splice(0,dt.length)))},nt=c(),lt=c(!1),ht=(e,t)=>t.type===e,it=c(),pt=s([]);let dt=s([]);const rt=(e,t)=>{let o={jira_title:nt.value};F(o),P({message:"已进行checklist消息push，请耐心等待seatalk自动发送消息。",type:"success",duration:5e3})},mt=(e,t)=>{let o={jira_title:nt.value};A(o),P({message:"已进行Signed off消息push，请耐心等待seatalk自动发送消息。",type:"success",duration:5e3})},bt=(e,t)=>{let o={jira_title:nt.value};q(o),P({message:"已进行MR消息push，请耐心等待seatalk自动发送消息。",type:"success",duration:5e3})};async function _t(e){console.log(dt);const t=new Set(dt.map((e=>e.jira_key)));Ve.splice(0,Ve.length),te.splice(0,te.length),ee.splice(0,ee.length),console.log(`get data for ${e}`);let o,a={title:e};o=await oe(a),0===o.length?dt.splice(0,dt.length):(dt.splice(0,dt.length),o.data.forEach((e=>{console.log(e),t.has(e.jira_key)||(dt.push(e),t.add(e.jira_key))}))),console.log(dt)}async function gt(e){Xe.value=!1,Qe.value=!1,console.log(e),await _t(e),lt.value=!0,console.log(dt),await async function(e){Xe.value=!1,Qe.value=!1,console.log(e),await _t(e),lt.value=!0;let t=c();console.log(dt),t.value=await L(dt),lt.value=!1,console.log(t);const o=dt.length;t.value.data.slice(0,o).forEach(((e,t)=>{console.log(e.result_all),e.signoff_status?dt[t].sign_off=e.signoff_status:dt[t].sign_off="",e.config_center?dt[t].config_center=e.config_center:dt[t].config_center="",e.Code_Merged?dt[t].Code_Merged=e.Code_Merged:dt[t].Code_Merged="",e.shopee_region?dt[t].region=e.shopee_region:dt[t].region="",e.redis_check?dt[t].redis_change=e.redis_check:dt[t].redis_change="",e.DB_Change?dt[t].DB_Change=e.DB_Change:dt[t].DB_Change="",e.result?dt[t].result=e.result:dt[t].result="",e.merge_list?dt[t].merge_list=e.merge_list:dt[t].merge_list="",e.status?dt[t].status=e.status:dt[t].status="",e.dev_pic?dt[t].dev_pic=e.dev_pic:dt[t].dev_pic="",e.PM?dt[t].PM=e.PM:dt[t].PM="",e.qa_pic?dt[t].qa_pic=e.qa_pic:dt[t].qa_pic="",console.log(e.redis_check),console.log(dt),dt[t].services="",e.services_list.services_list_be.forEach((e=>{""===dt[t].services?dt[t].services+=`${e}`:dt[t].services+=`\n${e}`,te.includes(e)||(te.push(e),Ve.push(e))})),e.services_list.services_list_fe.forEach((e=>{""===dt[t].services?dt[t].services+=`${e}`:dt[t].services+=`\n${e}`,ee.includes(e)||(ee.push(e),Ve.push(e))}))})),0!==ee.length&&(Qe.value=!0),0!==te.length&&(Xe.value=!0),console.log(ee),console.log(te),Ze.value=!0}(e),lt.value=!1,P({message:"已更新状态",type:"success",duration:5e3})}function ut(e){return"TO DO"===e?"#42526e":"Done"===e?"#00875a":"Waiting"===e?"#42526e":"#0052CC"}function ft(e){return"TO DO"===e?"TO DO":"Done"===e?"DONE":"Waiting"===e?"WAITING":"Icebox"===e?"ICEBOX":"Doing"===e?"DOING":"UAT"===e?"UAT":"Delivering"===e?"DELIVERING":"Developing"===e?"DEVELOPING":"Testing"===e?"TESTING":e}l((async()=>{const e=localStorage.getItem("releaseTypeFilter");e&&(et.value=e);const t=localStorage.getItem("projectTypeFilter");t&&(ct.value=t);const o=(await h()).data.data;it.value=o,o.forEach((e=>{pt.push(e)})),st();const a=localStorage.getItem("selectedProject");a?(console.log("从localStorage恢复选中项目:",a),nt.value=a):at();const c=localStorage.getItem("active");if(c&&(Ke.value=parseInt(c)),document.body.innerHTML.trim().length>0){const e=document.querySelector(".el-empty");e&&(e.style.display="none")}}));const yt=s({name:"",merge:!0});i(nt,((e,t)=>{e&&e!==t&&(console.log(`选择项目变化: ${t} -> ${e}, 重新加载数据`),dt.splice(0,dt.length),async function(e){if(!e)return console.log("没有选择发布单，不进行数据查询"),void dt.splice(0,dt.length);Xe.value=!1,Qe.value=!1,console.log(`获取发布单数据: ${e}`),await _t(e),lt.value=!0;let t=c();if(console.log("releaseTableData前:",dt),t.value=await W(dt),console.log("API响应:",t.value),!t.value||!t.value.data)return console.log("API返回数据为空或格式不正确"),void(lt.value=!1);lt.value=!1,dt.length,dt.forEach((e=>{const o=t.value.data.find((t=>e.jira_key===t.feature_key));o&&(console.log("匹配项数据:",o),e.bug_resolved=Number(o.bug_resolved||0),e.bug_total=Number(o.bug_total||0),console.log(`Bug数据: ${e.bug_resolved}/${e.bug_total}`),e.type=o.type,e.jira_key=o.feature_key,e.jira_link=`https://jira.shopee.io/browse/${o.feature_key}`,e.jira_title=o.feature_title,o.signoff_status?e.sign_off=o.signoff_status:e.sign_off="",o.config_center?e.config_center=o.config_center:e.config_center="",o.shopee_region?e.region=o.shopee_region:e.region="",o.redis_check?e.redis_change=o.redis_check:e.redis_change="",o.result?e.result=o.result:e.result="",o.merge_list?e.merge_list=o.merge_list:e.merge_list="",o.status?e.status=o.status:e.status="",o.Code_Merged?e.Code_Merged=o.Code_Merged:e.Code_Merged="",o.DB_Change?e.DB_Change=o.DB_Change:e.DB_Change="",o.dev_pic?e.dev_pic=o.dev_pic:e.dev_pic="",o.PM?e.PM=o.PM:e.PM="",o.qa_pic?e.qa_pic=o.qa_pic:e.qa_pic="",e.services="",o.services_list.services_list_be.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,te.includes(t)||(te.push(t),Ve.push(t))})),o.services_list.services_list_fe.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,ee.includes(t)||(ee.push(t),Ve.push(t))})))})),0!==ee.length&&(Qe.value=!0),0!==te.length&&(Xe.value=!0),console.log("fe_services:",ee),console.log("be_services:",te),console.log("最终表格数据:",dt),Ze.value=!0}(e)),e&&localStorage.setItem("selectedProject",e),yt.name=function(e){if(e){let t=e.replace(/【Release】|发布单/g,"").replace(/\s/g,"");return console.log(t),t}return""}(e)}),{flush:"post"}),i(dt,((e,t)=>{localStorage.setItem("releaseTableData",JSON.stringify(dt)),0===dt.length&&(Je.value=!1),0!==dt.length&&(Je.value=!0)})),i(ct,(e=>{localStorage.setItem("projectTypeFilter",e),st(),tt.value.length>0?at():(nt.value="",dt.splice(0,dt.length))})),i(Ve,((e,t)=>{0===Ve.length&&(Ye.value=!1),0!==Ve.length&&(Ye.value=!0)})),i(et,(e=>{localStorage.setItem("releaseTypeFilter",et.value),st(),tt.value.length>0?at():(nt.value="",dt.splice(0,dt.length)),nt.value&&!tt.value.some((e=>e.title===nt.value))&&(nt.value="",dt.splice(0,dt.length))}));const vt=c([]),kt=c([]),wt=c([]),xt=c([]),Ct=e=>{vt.value=[],vt.value=e,console.log(jt.value),console.log(vt.value)},jt=n((()=>vt.value.map(((e,t)=>({...e,id:t+1}))))),Mt=e=>{kt.value=[],kt.value=e};n((()=>kt.value.map(((e,t)=>({...e,id:t+1})))));const Tt=e=>{wt.value=[],wt.value=e};n((()=>wt.value.map(((e,t)=>({...e,id:t+1})))));const Dt=e=>{xt.value=[],xt.value=e};n((()=>xt.value.map(((e,t)=>({...e,id:t+1})))));let St=s([]);const It=()=>{t.value=!1,o.value=!0;const e=vt.value.concat(kt.value,wt.value,xt.value),a=Array.from(new Set(e)).map((e=>e.name));console.log(a);const c={services_list:a,title:yt.name,if_merge:yt.merge};console.log(c),Y(c).then((e=>{console.log(e.repo);for(let t in e.repo)console.log(t),console.log(e.repo[t]),St.push({repo:e.repo[t].repo,url:e.repo[t].url,status:e.repo[t].status})})),o.value=!1,console.log(St)};function zt(e){if(ee.includes(e))return qe.map((e=>Object.keys(e))).flat().includes(e);if(te.includes(e)){const t=Fe.map((e=>Object.keys(e))).flat(),o=Pe.map((e=>Object.keys(e))).flat(),a=Ae.map((e=>Object.keys(e))).flat();return t.includes(e)||o.includes(e)||a.includes(e)}return!1}0!==ee.length&&(Qe.value=!0),0!==te.length&&(Xe.value=!0);const Et=[];return Ve.forEach((e=>{zt(e)||Et.push(e)})),console.log("未正确分组的服务:",Et),console.log(ee),console.log(te),(e,s)=>{const n=ae,l=le,h=he,i=H,F=de,A=J,q=re,L=p("SuccessFilled"),W=ie,Y=Q,ee=X,te=Z,oe=K,Ve=pe;return d(),r("div",me,[m(V,{name:"el-fade-in-linear"},{default:b((()=>[m(_(ce),{class:"card",shadow:"hover",width:"50px",height:"50px"},{default:b((()=>[g("div",be,[m(n,{projectType:ct.value,"onUpdate:projectType":s[0]||(s[0]=e=>ct.value=e)},null,8,["projectType"]),m(h,{modelValue:et.value,"onUpdate:modelValue":s[1]||(s[1]=e=>et.value=e),class:"release-type-filter",onChange:ot},{default:b((()=>[m(l,{label:"bus"},{default:b((()=>[u("Bus")])),_:1}),m(l,{label:"adhoc"},{default:b((()=>[u("Adhoc")])),_:1}),m(l,{label:"hotfix"},{default:b((()=>[u("Hotfix")])),_:1}),m(l,{label:"All"},{default:b((()=>[u("全部")])),_:1})])),_:1},8,["modelValue"]),m(_(f),{modelValue:nt.value,"onUpdate:modelValue":s[2]||(s[2]=e=>nt.value=e),filterable:"",clearable:"",placeholder:"请选择发布单",style:{width:"380px","margin-left":"10px"}},{default:b((()=>[(d(!0),r(y,null,v(tt.value,(e=>(d(),k(_(w),{key:e.title,label:e.title,value:e.title},{default:b((()=>[g("span",_e,[m(F,{href:"https://jira.shopee.io/browse/"+e.key,target:"_blank",underline:!1},{default:b((()=>[m(i,{class:"box-item",effect:"customized",content:"点击跳转到JIRA",placement:"left-start"},{default:b((()=>[m(_(x),{type:"danger"},{default:b((()=>[u(C(e.key),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["href"])]),m(i,{class:"box-item",effect:"customized",content:"点击拉取数据并且展示发布详情",placement:"right-start"},{default:b((()=>[g("span",ge,C(e.title),1)])),_:2},1024)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),m(i,{class:"box-item",effect:"customized",content:"点击进行后台数据更新",placement:"top-start"},{default:b((()=>[j((d(),k(_(M),{size:"default",type:"success",onClick:s[3]||(s[3]=e=>gt(nt.value)),icon:_(T),"element-loading-text":"AR正在进行后台数据更新，请耐心等待"},{default:b((()=>[u("更新 ")])),_:1},8,["icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1}),m(i,{class:"box-item",effect:"customized",content:"点击发送Signed off字段提醒到seatalk",placement:"top-start"},{default:b((()=>[m(_(M),{type:"primary",size:"default",onClick:mt,icon:_(D)},{default:b((()=>[u(" Signed off提醒 ")])),_:1},8,["icon"])])),_:1}),m(i,{class:"box-item",effect:"customized",content:"点击发送MR提醒到seatalk",placement:"top-start"},{default:b((()=>[m(_(M),{type:"primary",size:"default",onClick:bt,icon:_(D)},{default:b((()=>[u(" MR提醒 ")])),_:1},8,["icon"])])),_:1}),m(i,{class:"box-item",effect:"customized",content:"点击发送checklist字段提醒到seatalk",placement:"top-start"},{default:b((()=>[m(_(M),{type:"primary",size:"default",onClick:rt,icon:_(D)},{default:b((()=>[u(" Checklist提醒 ")])),_:1},8,["icon"])])),_:1}),m(i,{class:"box-item",effect:"customized",content:"点击跳转monitor看板",placement:"top-start"},{default:b((()=>[m(_(M),{type:"danger",size:"default",onClick:Ne,icon:_(D)},{default:b((()=>[u(" monitor看板 ")])),_:1},8,["icon"])])),_:1}),m(_(S),{modelValue:_(He),"onUpdate:modelValue":s[4]||(s[4]=e=>I(He)?He.value=e:He=e),"show-close":!1},{header:b((({titleId:e,titleClass:t})=>[g("h4",{id:e,class:z(t)},C(nt.value),11,ue)])),default:b((()=>[u(" 暂无失败信息。 ")])),_:1},8,["modelValue"]),g("div",fe,[m(A,{size:20},{default:b((()=>[m(_(E))])),_:1}),m(q,{class:"mx-1",type:"warning",size:"default"},{default:b((()=>[u("后台会不断更新数据，请自行刷新页面")])),_:1})])]),g("div",ye,[m(_(se),{data:_(dt),stripe:"",border:"","highlight-current-row":"",fit:"","header-cell-style":{background:"#cacfd7",color:"#606266"},"empty-text":"暂无数据"},{default:b((()=>[m(_(ne),{label:"编号","min-width":"21","header-align":"center",align:"center"},{default:b((e=>[u(C(e.$index+1),1)])),_:1}),m(_(ne),{prop:"type",label:"类型","header-align":"center",align:"center","min-width":"30",filters:[{text:"Epic",value:"Epic"},{text:"Bug",value:"Bug"},{text:"Task",value:"Task"},{text:"Sub-task",value:"Sub-task"},{text:"Story",value:"Story"}],"filter-method":ht,"filter-placement":"bottom-end"},{default:b((({row:e})=>{return[m(A,{class:z((t=e.type,"Epic"===t?"Epic-icon":"Sub-task"===t?"ST-icon":"Task"===t?"Task-icon":"Bug"===t?"Bug-icon":"Story"===t?"Story-icon":void 0))},null,8,["class"])];var t})),_:1}),m(_(ne),{prop:"jira_key",label:"单号","min-width":60,"header-align":"center",align:"center"},{default:b((({row:e})=>[m(F,{underline:!1,href:e.jira_link,target:"_blank",type:"primary"},{default:b((()=>[u(C(e.jira_key),1)])),_:2},1032,["href"])])),_:1}),m(_(ne),{prop:"jira_title",label:"需求名","min-width":150},{default:b((({row:e})=>[m(F,{underline:!1,href:e.jira_link,target:"_blank",type:"primary"},{default:b((()=>[u(C(e.jira_title),1)])),_:2},1032,["href"])])),_:1}),m(_(ne),{prop:"bug_resolution_rate",label:"Bug解决率","min-width":80,"header-align":"center",align:"center"},{default:b((({row:e})=>[g("span",{style:$({color:Number(e.bug_resolved||0)===Number(e.bug_total||0)?"#67C23A":"#F56C6C"})},["number"==typeof e.bug_resolved&&"number"==typeof e.bug_total?(d(),r(y,{key:0},[u(C(e.bug_resolved)+"/"+C(e.bug_total),1)],64)):(d(),r(y,{key:1},[u(C(e.bug_resolved||0)+"/"+C(e.bug_total||0),1)],64))],4)])),_:1}),m(_(ne),{label:"周一","header-align":"center",align:"center"},{default:b((()=>[m(_(ne),{prop:"sign_off",label:"Signed off","header-align":"center",align:"center","min-width":"40"},{header:b((()=>[u(" Signed"),ve,u("off ")])),default:b((({row:e})=>["Confirmed"===e.sign_off?(d(),k(A,{key:0,size:20,color:"Confirmed"===e.sign_off?"#67c23a":"#F56C67"},{default:b((()=>[m(L)])),_:2},1032,["color"])):O("",!0),""===e.sign_off?(d(),k(A,{key:1,size:20,color:"pass"===e.sign_off?"#67c23a":"#F56C67"},{default:b((()=>[m(_(R))])),_:2},1032,["color"])):O("",!0)])),_:1})])),_:1}),m(_(ne),{label:"周二","header-align":"center",align:"center"},{default:b((()=>[m(_(ne),{type:"expand",label:"提MR","min-width":"32"},{default:b((e=>[g("div",null,[g("div",ke,[m(i,{class:"box-item",effect:"customized",content:"点击创建MR",placement:"top-start"},{default:b((()=>[j((d(),k(_(M),{type:"danger",onClick:s[5]||(s[5]=e=>a.value=!0),size:"small",icon:_(B),"element-loading-text":"AR正在创建MR, 请耐心等待..."},{default:b((()=>[u("创建 ")])),_:1},8,["icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1}),m(i,{class:"box-item",effect:"customized",content:"点击复制",placement:"top-start"},{default:b((()=>[j((d(),k(_(M),{type:"primary",size:"small",onClick:t=>async function(e){let t=c();nt.value,t=await U(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{P({message:"恭喜，MR信息已复制到剪切板！",type:"success"})})).catch((e=>{P.error("复制剪切板失败！")}))}(e),icon:_(D),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:b((()=>[u("复制 ")])),_:2},1032,["onClick","icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024),m(i,{class:"box-item",effect:"customized",content:"点击发送MR提醒到seatalk",placement:"top-start"},{default:b((()=>[j((d(),k(_(M),{type:"primary",size:"small",onClick:t=>async function(e){let t=c();t=await N(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{P({message:"恭喜，MR信息已发送到seatalk！",type:"success"})})).catch((e=>{P.error("MR信息发送失败！")}))}(e),icon:_(D),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:b((()=>[u("发送 ")])),_:2},1032,["onClick","icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024)]),m(W,{modelValue:a.value,"onUpdate:modelValue":s[7]||(s[7]=e=>a.value=e),title:"Warning",width:"30%","align-center":""},{footer:b((()=>[g("span",we,[m(_(M),{onClick:s[6]||(s[6]=e=>a.value=!1)},{default:b((()=>[u("取消")])),_:1}),m(_(M),{type:"primary",onClick:t=>async function(e){console.log(e.row),c(),await G(e.row)}(e)},{default:b((()=>[u(" 确认 ")])),_:2},1032,["onClick"])])])),default:b((()=>[g("span",null,"请确认是否开始自动提MR？发布单： "+C(nt.value),1)])),_:2},1032,["modelValue"]),m(_(se),{data:e.row.merge_list,border:"","header-cell-style":{background:"#def1ce",color:"#606266"}},{default:b((()=>[m(_(ne),{label:"仓库",prop:"repo_name"}),m(_(ne),{label:"分支",prop:"branch_name"}),m(_(ne),{label:"PIC",prop:"pic"}),m(_(ne),{label:"MR地址",prop:"web_url"},{default:b((({row:e})=>[g("a",{href:e.web_url,target:"_blank"},C(e.web_url),9,xe)])),_:1}),m(_(ne),{label:"MR状态",prop:"merge_status"}),m(_(ne),{label:"MR作者",prop:"author"})])),_:2},1032,["data"])])])),_:1}),m(_(ne),{prop:"Code_Merged",label:"Code Merged","header-align":"center",align:"center","min-width":"40"},{header:b((()=>[u(" Code"),Ce,u("Merged ")])),default:b((({row:e})=>["Confirmed"===e.Code_Merged?(d(),k(A,{key:0,size:20,color:"Confirmed"===e.Code_Merged?"#67c23a":"#F56C67"},{default:b((()=>[m(L)])),_:2},1032,["color"])):O("",!0),""===e.Code_Merged?(d(),k(A,{key:1,size:20,color:"pass"===e.Code_Merged?"#67c23a":"#F56C67"},{default:b((()=>[m(_(R))])),_:2},1032,["color"])):O("",!0)])),_:1})])),_:1}),m(_(ne),{label:"周三","header-align":"center",align:"center"},{default:b((()=>[m(_(ne),{prop:"config_center",label:"Config Changed","header-align":"center",align:"center","min-width":"43"},{header:b((()=>[u(" Config"),je,u("Changed ")])),default:b((({row:e})=>["Confirmed"===e.config_center?(d(),k(A,{key:0,size:20,color:"Confirmed"===e.config_center?"#67c23a":"#F56C67"},{default:b((()=>[m(L)])),_:2},1032,["color"])):O("",!0),""===e.config_center?(d(),k(A,{key:1,size:20,color:"pass"===e.config_center?"#67c23a":"#F56C67"},{default:b((()=>[m(_(R))])),_:2},1032,["color"])):O("",!0)])),_:1}),m(_(ne),{prop:"DB_Change",label:"DB Changed","header-align":"center",align:"center","min-width":"43"},{default:b((({row:e})=>["Confirmed"===e.DB_Change?(d(),k(A,{key:0,size:20,color:"Confirmed"===e.DB_Change?"#67c23a":"#F56C67"},{default:b((()=>[m(L)])),_:2},1032,["color"])):O("",!0),""===e.DB_Change?(d(),k(A,{key:1,size:20,color:"pass"===e.DB_Change?"#67c23a":"#F56C67"},{default:b((()=>[m(_(R))])),_:2},1032,["color"])):O("",!0)])),header:b((()=>[u(" DB"),Me,u("Changed ")])),_:1}),m(_(ne),{prop:"services",label:"services","min-width":"180","header-align":"center",align:"center",style:{"white-space":"pre-wrap"}},{default:b((({row:e})=>[e.services&&""!==e.services?(d(),r("div",Te,[(d(!0),r(y,null,v(e.services.split("\n"),((e,t)=>(d(),r("div",{style:$({color:zt(e)?"inherit":"#F56C67"})},C(e),5)))),256))])):O("",!0),e.services&&""!==e.services?O("",!0):(d(),k(A,{key:1,size:20,color:"#F56C67"},{default:b((()=>[m(_(R))])),_:1}))])),_:1}),m(_(ne),{prop:"region",label:"Region","header-align":"center",align:"center","min-width":"37"},{default:b((({row:e})=>[""!==e.region?(d(),k(q,{key:0,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:b((()=>[u(C(e.region),1)])),_:2},1032,["color"])):O("",!0),""===e.region?(d(),k(A,{key:1,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:b((()=>[m(_(R))])),_:2},1032,["color"])):O("",!0)])),_:1})])),_:1}),m(_(ne),{prop:"PM",label:"PM","header-align":"center",align:"center","min-width":"50"},{default:b((({row:e})=>[u(C(e.PM),1)])),_:1}),m(_(ne),{prop:"dev_pic",label:"DEV PIC","header-align":"center",align:"center","min-width":"40"},{header:b((()=>[u(" DEV"),De,u("PIC ")])),default:b((({row:e})=>[u(C(e.dev_pic),1)])),_:1}),m(_(ne),{prop:"qa_pic",label:"QA","header-align":"center",align:"center","min-width":"50"},{default:b((({row:e})=>[u(C(e.qa_pic),1)])),_:1}),m(_(ne),{prop:"status",label:"Status","header-align":"center",align:"center","min-width":"60"},{default:b((({row:e})=>{return[m(_(x),{class:"bold-text",effect:"dark",type:(t=e.status,"TO DO"===t?"info":"Done"===t?"success":"Waiting"===t?"info":"Icebox"===t?"icebox":"Doing"===t?"doing":"UAT"===t?"uat":"Delivering"===t?"delivering":"Developing"===t?"developing":"Testing"===t?"testing":void 0),color:ut(e.status)},{default:b((()=>[u(C(ft(e.status)),1)])),_:2},1032,["type","color"])];var t})),_:1})])),_:1},8,["data"])]),g("div",Se,[m(q,{class:"mx-1",type:"danger"},{default:b((()=>[u("请勾选下面的服务，点击一键四连启动发布流程。前端4个仓库只支持打TAG，不支持合代码，请找bin.wang合代码，4个仓库为仓库为：")])),_:1}),m(F,{href:"https://git.garena.com/shopee/marketing/web-chatbot",underline:!1,type:"danger",target:"_blank"},{default:b((()=>[u("web-chatbot、")])),_:1}),m(F,{href:"https://git.garena.com/shopee/seller-fe/cs-chat",underline:!1,type:"danger",target:"_blank"},{default:b((()=>[u("cs-chat、")])),_:1}),m(F,{href:"https://git.garena.com/shopee/chatbot/web-chatbot-csat",underline:!1,type:"danger",target:"_blank"},{default:b((()=>[u("web-chatbot-csat、")])),_:1}),m(F,{href:"https://git.garena.com/shopee/chatbot/web-csat-rn",underline:!1,type:"danger",target:"_blank"},{default:b((()=>[u("web-csat-rn。")])),_:1})]),g("div",Ie,[m(_(se),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e78181",color:"#f8f7f7"},data:Ue.value,style:{width:"100%"},onSelectionChange:Ct,"empty-text":"暂无数据"},{default:b((()=>[m(_(ne),{type:"selection",width:"55"}),m(_(ne),{"header-align":"center",label:"平台BE1组"},{default:b((e=>[m(F,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[u(C(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),m(_(se),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#819ee7",color:"#f8f7f7"},"header-align":"center",data:Ge.value,style:{width:"100%"},onSelectionChange:Mt,"empty-text":"暂无数据"},{default:b((()=>[m(_(ne),{type:"selection",width:"55"}),m(_(ne),{"header-align":"center",label:"平台BE2组"},{default:b((e=>[m(F,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[u(C(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),m(_(se),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#81e7c8",color:"#f8f7f7"},"header-align":"center",data:Le.value,style:{width:"100%"},onSelectionChange:Tt,"empty-text":"暂无数据"},{default:b((()=>[m(_(ne),{type:"selection",width:"55"}),m(_(ne),{"header-align":"center",label:"功能BE组"},{default:b((e=>[m(F,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[u(C(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),m(_(se),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e7a881",color:"#f8f7f7"},"header-align":"center",data:We.value,style:{width:"100%"},onSelectionChange:Dt,"empty-text":"暂无数据"},{default:b((()=>[m(_(ne),{type:"selection",width:"55"}),m(_(ne),{"header-align":"center",label:"FE组"},{default:b((e=>[m(F,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[u(C(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"])])])),_:1})])),_:1}),m(_(ce),{class:"card",shadow:"hover",width:"50px",height:"50px"},{default:b((()=>[m(oe,{model:yt,"label-width":"auto"},{default:b((()=>[g("div",ze,[m(W,{modelValue:t.value,"onUpdate:modelValue":s[9]||(s[9]=e=>t.value=e),title:"live-发布确认",width:"30%","before-close":e.handleClose},{footer:b((()=>[g("span",$e,[m(_(M),{onClick:s[8]||(s[8]=e=>t.value=!1)},{default:b((()=>[u("取消")])),_:1}),m(_(M),{type:"primary",onClick:It},{default:b((()=>[u(" 确认发布 ")])),_:1})])])),default:b((()=>[Ee])),_:1},8,["modelValue","before-close"])]),g("div",Oe,[m(ee,{label:"标题",style:{"max-width":"460px"}},{default:b((()=>[m(Y,{modelValue:yt.name,"onUpdate:modelValue":s[10]||(s[10]=e=>yt.name=e)},null,8,["modelValue"])])),_:1}),m(_(x),{class:"ml-2",size:"large"},{default:b((()=>[u("标题规范：bus/adhoc/bugfix-YYYYMMDD。若当日多次同类型发布，在标题后面自增号码以区分，示例：adhoc-20230831-1、adhoc-20230831-2。")])),_:1})]),g("div",Re,[m(ee,{label:"是否合代码"},{default:b((()=>[m(i,{class:"box-item",effect:"customized",content:"勾选则从master合代码到release，不勾选则默认只打TAG",placement:"top-start"},{default:b((()=>[m(te,{modelValue:yt.merge,"onUpdate:modelValue":s[11]||(s[11]=e=>yt.merge=e)},null,8,["modelValue"])])),_:1})])),_:1}),m(ee,null,{default:b((()=>[m(i,{class:"box-item",effect:"customized",content:"创建MR、合代码到release、打TAG、预编译",placement:"top-start"},{default:b((()=>[j((d(),k(_(M),{type:"success","element-loading-text":"正在处理，辛苦等待一下，中途请不要关闭此页面...",onClick:s[12]||(s[12]=e=>t.value=!0)},{default:b((()=>[u("一键四连")])),_:1})),[[Ve,o.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1})])),_:1})])])),_:1},8,["model"]),m(_(se),{data:_(St),border:"",style:{width:"100%"},"header-cell-style":{background:"#cacfd7",color:"#606266"},"empty-text":"暂无数据"},{default:b((()=>[m(_(ne),{prop:"repo",label:"仓库","header-align":"center",width:"180"}),m(_(ne),{prop:"url",label:"MR地址","header-align":"center",width:"500"},{default:b((({row:e})=>[g("a",{href:e.url,target:"_blank"},C(e.url),9,Be)])),_:1}),m(_(ne),{prop:"status",label:"状态","header-align":"center"})])),_:1},8,["data"])])),_:1})])}}});e("default",o(Ve,[["__scopeId","data-v-341ca16e"]]))}}}));
//# sourceMappingURL=autorelease-legacy.CAHCtNI2.js.map

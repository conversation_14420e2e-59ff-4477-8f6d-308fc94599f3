{"version": 3, "file": "index-BYqWM-yL.js", "sources": ["../../src/assets/error/401.svg", "../../src/assets/error/404.svg", "../../src/components/ErrorPage/index.vue"], "sourcesContent": ["export default \"__VITE_ASSET__DD$OftX5__\"", "export default \"__VITE_ASSET__DYwGXyup__\"", "<template>\n  <div class=\"errorPage-container\">\n    <el-row :gutter=\"10\" class=\"error-row\">\n      <el-col class=\"error-col\" :xs=\"24\" :sm=\"24\" :md=\"10\" :lg=\"10\" :xl=\"10\">\n        <div class=\"error-img\">\n          <img v-if=\"type === '401'\" src=\"@/assets/error/401.svg\" class=\"img\" :alt=\"type\" />\n          <img v-if=\"type === '404'\" src=\"@/assets/error/404.svg\" class=\"img\" :alt=\"type\" />\n        </div>\n      </el-col>\n      <el-col :xs=\"24\" :sm=\"24\" :md=\"10\" :lg=\"10\" :xl=\"10\">\n        <div class=\"error-content\">\n          <h2 class=\"error-title\">{{ t('errorPages.title') }}</h2>\n          <h3>{{ title }}</h3>\n          <p class=\"desc\"> {{ msg }} </p>\n          <div class=\"btn\">\n            <el-button type=\"primary\" @click=\"handleBack\">{{ t('errorPages.btn') }}</el-button>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n  import { useRouter } from 'vue-router';\n  import { useI18n } from 'vue-i18n';\n  export default {\n    props: {\n      src: {\n        type: String,\n        default: '',\n      },\n      type: {\n        type: String,\n        default: '',\n      },\n      title: {\n        type: String,\n        default: '',\n      },\n      msg: {\n        type: String,\n        default: '',\n      },\n    },\n    setup() {\n      const { t } = useI18n();\n      const router = useRouter();\n      const handleBack = () => {\n        router.replace('/');\n      };\n      return {\n        t,\n        handleBack,\n      };\n    },\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  .errorPage-container {\n    display: flex;\n    align-items: center;\n    padding: $base-main-padding;\n    background-color: $base-color-white;\n    .error-row {\n      flex: 1;\n      justify-content: center;\n      .error-col {\n        text-align: center;\n      }\n      .error-img {\n        width: 100%;\n        .img {\n          width: 80%;\n        }\n      }\n      .error-content {\n        padding: 40px 20px;\n        .error-title {\n          font-size: 36px;\n          color: $base-color-primary;\n        }\n        .desc {\n          width: 290px;\n          line-height: 1.5;\n        }\n        .btn {\n          margin: 20px 0;\n        }\n      }\n    }\n  }\n</style>\n"], "names": ["_imports_0", "URL", "url", "href", "_imports_1", "_hoisted_1", "class", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "props", "title", "setup", "t", "useI18n", "router", "replace", "_createVNode", "_component_el_row", "gutter", "_component_el_col", "xs", "sm", "md", "lg", "default", "_withCtx", "key", "src", "_createElementVNode", "_toDisplayString", "$setup", "$props", "msg", "_component_el_button", "type"], "mappings": "mIAAA,MAAeA,EAAA,GAAA,IAAAC,IAAA,+BAAAC,KAAAC,KCAAC,EAAA,GAAA,IAAAH,IAAA,+BAAAC,KAAAC,KCIFE,EAAA,CAAAC,MAAA,uDAMAC,EAAA,CAAA,OACCC,EAAA,CAAAF,MAAA,iBAEDG,EAAA,CAAAH,MAAA,eACEI,EAAA,CAAAJ,MAAA,mCAaXK,MAAA,4DASEC,MAAA,uDASF,KAAAC,GACE,MAAAC,EAAAA,GAAAC,UAKA,MAAA,gBAHA,KACEC,EAAAC,QAAA,IAAA,8EA/CKC,EAAAC,EAAA,CAAYC,OAAA,uCACXF,EAAAG,EAAA,CAAmBf,MAAA,YAASgB,GAAA,GAASC,GAAA,GAASC,GAAA,GAASC,GAAA,WAC7DC,QAAAC,GAAA,IAAA,yCAC6BC,IAAA,EAA6BC,IAAA7B,EAAaM,MAAA,mEAC1CsB,IAAA,EAA6BC,IAAAzB,EAAaE,MAAA,iDAGhEY,EAAAG,EAAA,CAASC,GAAA,GAASC,GAAA,GAASC,GAAA,GAASC,GAAA,WAC3CC,QAAAC,GAAA,IAAA,YAEEG,EAAA,KAAArB,EAAAsB,EAAAC,EAAAlB,EAAA,qBAAA,6BAEAgB,EAAA,IAAApB,EAAAqB,EAAAE,EAAAC,KAAA,cACahB,EAAAiB,EAAA,CAAgBC,KAAA"}
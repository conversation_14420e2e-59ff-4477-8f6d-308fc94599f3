{"version": 3, "file": "jiratools-BDX84GcO.js", "sources": ["../../src/views/jiratools/jiratools.vue"], "sourcesContent": ["<template>\n  <div>\n    <button @click=\"showDialog\">New</button>\n    <div v-if=\"dialogVisible\">\n      <input v-model=\"description\" placeholder=\"Description\">\n      <input v-model=\"jql\" placeholder=\"JQL\">\n      <input v-model=\"cron\" placeholder=\"Cron Expression\">\n      <input v-model=\"groupId\" placeholder=\"Seatalk Group ID\">\n      <button @click=\"save\">Save</button>\n    </div>\n    <div v-for=\"item in items\" :key=\"item.id\">\n      <div>{{ item.description }}</div>\n      <div>{{ item.jql }}</div>\n      <div>{{ item.cron }}</div>\n      <div>{{ item.groupId }}</div>\n      <input type=\"checkbox\" v-model=\"item.switch\">\n      <button @click=\"execute(item.id)\">立即执行</button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      dialogVisible: false,\n      description: '',\n      jql: '',\n      cron: '',\n      groupId: '',\n      items: []\n    }\n  },\n  methods: {\n    showDialog() {\n      this.dialogVisible = true;\n    },\n    save() {\n      // 调用后端接口保存数据\n      // 示例：this.$http.post('/api/saveData', { description: this.description, jql: this.jql, cron: this.cron, groupId: this.groupId })\n      // 保存成功后将数据添加到items数组中\n      this.items.push({ description: this.description, jql: this.jql, cron: this.cron, groupId: this.groupId, switch: false });\n      this.dialogVisible = false;\n    },\n    execute(id) {\n      // 调用后端接口执行计划任务\n      // 示例：this.$http.post('/api/executeTask', { id: id })\n    }\n  }\n}\n</script>\n"], "names": ["data", "dialogVisible", "description", "jql", "cron", "items", "methods", "save", "_createElementVNode", "_cache", "$event", "$data", "_withDirectives", "type", "_hoisted_2"], "mappings": "iHAsBA,kEAAA,CACEA,KAAA,KACE,CACEC,eAAA,EACAC,YAAA,GACAC,IAAA,GACAC,KAAA,cAEAC,MAAA,KAGJC,QAAA,qCAIE,IAAAC,gJAUA,8DA7CSC,EAAA,SAAA,oHAEsB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAAAC,EAAA,YAAAD,0EACR,sBAAAD,EAAA,KAAAA,EAAA,GAAAC,GAAAC,EAAA,IAAAD,0DACC,sBAAAD,EAAA,KAAAA,EAAA,GAAAC,GAAAC,EAAA,KAAAD,uEACG,sBAAAD,EAAA,KAAAA,EAAA,GAAAC,GAAAC,EAAA,QAAAD,8DAChBF,EAAA,SAAA,gPAOFI,EAAAJ,EAAA,QAAA,CAAyBK,KAAA,gDAAA,KAAA,EAAAC,GAAA,gBACvBN,EAAA,SAAA"}
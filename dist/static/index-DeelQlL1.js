import{b as a,bP as e,e as s,b7 as t,f as l,a as n,aN as i,o as p,C as r,w as u,m,n as c,j as o,ag as d,az as y,_ as f,D as g}from"./index-awKTxnvj.js";const b=a({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:e,default:""},truncated:{type:Boolean},lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),x=s({name:"ElText"});const v=g(f(s({...x,props:b,setup(a){const e=a,s=t(),f=l("text"),g=n((()=>[f.b(),f.m(e.type),f.m(s.value),f.is("truncated",e.truncated),f.is("line-clamp",!i(e.lineClamp))]));return(a,e)=>(p(),r(y(a.tag),{class:c(o(g)),style:d({"-webkit-line-clamp":a.lineClamp})},{default:u((()=>[m(a.$slots,"default")])),_:3},8,["class","style"]))}}),[["__file","text.vue"]]));export{v as E};
//# sourceMappingURL=index-DeelQlL1.js.map

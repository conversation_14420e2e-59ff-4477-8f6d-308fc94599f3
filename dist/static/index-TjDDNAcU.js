import{a$ as e,br as l,U as a,bl as t,bm as n,b3 as o,aG as u,a as s,aN as i,bo as d,b5 as r,aJ as c,T as b,b8 as v,aV as m,p as h,s as p,bn as f,i as x,cs as k,aP as g,b7 as C,bs as y,bp as L,e as V,bC as S,f as B,o as I,C as E,w as F,l as z,n as N,j as w,ab as U,h as D,af as _,aH as O,ct as G,m as $,F as j,x as R,t as A,k as M,az as P,_ as T,ag as H,b as J,d as K,aT as q,cu as Q,M as W,D as X,bt as Y}from"./index-awKTxnvj.js";const Z={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:e,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0},...l(["ariaControls"])},ee={[a]:e=>t(e)||n(e)||o(e),change:e=>t(e)||n(e)||o(e)},le=Symbol("checkboxGroupContextKey"),ae=(e,{model:l,isLimitExceeded:a,hasOwnLabel:t,isDisabled:n,isLabeledByFormItem:o})=>{const i=u(le,void 0),{formItem:d}=r(),{emit:h}=c();function p(l){var a,t,n,o;return[!0,e.trueValue,e.trueLabel].includes(l)?null==(t=null!=(a=e.trueValue)?a:e.trueLabel)||t:null!=(o=null!=(n=e.falseValue)?n:e.falseLabel)&&o}const f=s((()=>(null==i?void 0:i.validateEvent)||e.validateEvent));return b((()=>e.modelValue),(()=>{f.value&&(null==d||d.validate("change").catch((e=>v())))})),{handleChange:function(e){if(a.value)return;const l=e.target;h("change",p(l.checked),e)},onClickRoot:async function(u){if(!a.value&&!t.value&&!n.value&&o.value){u.composedPath().some((e=>"LABEL"===e.tagName))||(l.value=p([!1,e.falseValue,e.falseLabel].includes(l.value)),await m(),function(e,l){h("change",p(e),l)}(l.value,u))}}}},te=(e,l)=>{const{formItem:t}=r(),{model:n,isGroup:b,isLimitExceeded:v}=(e=>{const l=h(!1),{emit:t}=c(),n=u(le,void 0),o=s((()=>!1===i(n))),d=h(!1),r=s({get(){var a,t;return o.value?null==(a=null==n?void 0:n.modelValue)?void 0:a.value:null!=(t=e.modelValue)?t:l.value},set(e){var u,s;o.value&&p(e)?(d.value=void 0!==(null==(u=null==n?void 0:n.max)?void 0:u.value)&&e.length>(null==n?void 0:n.max.value)&&e.length>r.value.length,!1===d.value&&(null==(s=null==n?void 0:n.changeEvent)||s.call(n,e))):(t(a,e),l.value=e)}});return{model:r,isGroup:o,isLimitExceeded:d}})(e),{isFocused:m,isChecked:V,checkboxButtonSize:S,checkboxSize:B,hasOwnLabel:I,actualValue:E}=((e,l,{model:a})=>{const t=u(le,void 0),n=h(!1),i=s((()=>f(e.value)?e.label:e.value)),d=s((()=>{const l=a.value;return o(l)?l:p(l)?x(i.value)?l.map(k).some((e=>g(e,i.value))):l.map(k).includes(i.value):null!=l?l===e.trueValue||l===e.trueLabel:!!l}));return{checkboxButtonSize:C(s((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value})),{prop:!0}),isChecked:d,isFocused:n,checkboxSize:C(s((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value}))),hasOwnLabel:s((()=>!!l.default||!f(i.value))),actualValue:i}})(e,l,{model:n}),{isDisabled:F}=(({model:e,isChecked:l})=>{const a=u(le,void 0),t=s((()=>{var t,n;const o=null==(t=null==a?void 0:a.max)?void 0:t.value,u=null==(n=null==a?void 0:a.min)?void 0:n.value;return!i(o)&&e.value.length>=o&&!l.value||!i(u)&&e.value.length<=u&&l.value}));return{isDisabled:d(s((()=>(null==a?void 0:a.disabled.value)||t.value))),isLimitDisabled:t}})({model:n,isChecked:V}),{inputId:z,isLabeledByFormItem:N}=y(e,{formItemContext:t,disableIdGeneration:I,disableIdManagement:b}),{handleChange:w,onClickRoot:U}=ae(e,{model:n,isLimitExceeded:v,hasOwnLabel:I,isDisabled:F,isLabeledByFormItem:N});var D,_;return e.checked&&(p(n.value)&&!n.value.includes(E.value)?n.value.push(E.value):n.value=null==(_=null!=(D=e.trueValue)?D:e.trueLabel)||_),L({from:"controls",replacement:"aria-controls",version:"2.8.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},s((()=>!!e.controls))),L({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},s((()=>b.value&&f(e.value)))),L({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},s((()=>!!e.trueLabel))),L({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},s((()=>!!e.falseLabel))),{inputId:z,isLabeledByFormItem:N,isChecked:V,isDisabled:F,isFocused:m,checkboxButtonSize:S,checkboxSize:B,hasOwnLabel:I,model:n,actualValue:E,handleChange:w,onClickRoot:U}},ne=["id","indeterminate","name","tabindex","disabled","true-value","false-value"],oe=["id","indeterminate","disabled","value","name","tabindex"],ue=V({name:"ElCheckbox"});var se=T(V({...ue,props:Z,emits:ee,setup(e){const l=e,a=S(),{inputId:t,isLabeledByFormItem:n,isChecked:o,isDisabled:u,isFocused:i,checkboxSize:d,hasOwnLabel:r,model:c,actualValue:b,handleChange:v,onClickRoot:m}=te(l,a),h=B("checkbox"),p=s((()=>[h.b(),h.m(d.value),h.is("disabled",u.value),h.is("bordered",l.border),h.is("checked",o.value)])),f=s((()=>[h.e("input"),h.is("disabled",u.value),h.is("checked",o.value),h.is("indeterminate",l.indeterminate),h.is("focus",i.value)]));return(e,l)=>(I(),E(P(!w(r)&&w(n)?"span":"label"),{class:N(w(p)),"aria-controls":e.indeterminate?e.controls||e.ariaControls:null,onClick:w(m)},{default:F((()=>{var a,n;return[z("span",{class:N(w(f))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?U((I(),D("input",{key:0,id:w(t),"onUpdate:modelValue":l[0]||(l[0]=e=>_(c)?c.value=e:null),class:N(w(h).e("original")),type:"checkbox",indeterminate:e.indeterminate,name:e.name,tabindex:e.tabindex,disabled:w(u),"true-value":null!=(a=e.trueValue)?a:e.trueLabel,"false-value":null!=(n=e.falseValue)?n:e.falseLabel,onChange:l[1]||(l[1]=(...e)=>w(v)&&w(v)(...e)),onFocus:l[2]||(l[2]=e=>i.value=!0),onBlur:l[3]||(l[3]=e=>i.value=!1),onClick:l[4]||(l[4]=O((()=>{}),["stop"]))},null,42,ne)),[[G,w(c)]]):U((I(),D("input",{key:1,id:w(t),"onUpdate:modelValue":l[5]||(l[5]=e=>_(c)?c.value=e:null),class:N(w(h).e("original")),type:"checkbox",indeterminate:e.indeterminate,disabled:w(u),value:w(b),name:e.name,tabindex:e.tabindex,onChange:l[6]||(l[6]=(...e)=>w(v)&&w(v)(...e)),onFocus:l[7]||(l[7]=e=>i.value=!0),onBlur:l[8]||(l[8]=e=>i.value=!1),onClick:l[9]||(l[9]=O((()=>{}),["stop"]))},null,42,oe)),[[G,w(c)]]),z("span",{class:N(w(h).e("inner"))},null,2)],2),w(r)?(I(),D("span",{key:0,class:N(w(h).e("label"))},[$(e.$slots,"default"),e.$slots.default?M("v-if",!0):(I(),D(j,{key:0},[R(A(e.label),1)],64))],2)):M("v-if",!0)]})),_:3},8,["class","aria-controls","onClick"]))}}),[["__file","checkbox.vue"]]);const ie=["name","tabindex","disabled","true-value","false-value"],de=["name","tabindex","disabled","value"],re=V({name:"ElCheckboxButton"});var ce=T(V({...re,props:Z,emits:ee,setup(e){const l=e,a=S(),{isFocused:t,isChecked:n,isDisabled:o,checkboxButtonSize:i,model:d,actualValue:r,handleChange:c}=te(l,a),b=u(le,void 0),v=B("checkbox"),m=s((()=>{var e,l,a,t;const n=null!=(l=null==(e=null==b?void 0:b.fill)?void 0:e.value)?l:"";return{backgroundColor:n,borderColor:n,color:null!=(t=null==(a=null==b?void 0:b.textColor)?void 0:a.value)?t:"",boxShadow:n?`-1px 0 0 0 ${n}`:void 0}})),h=s((()=>[v.b("button"),v.bm("button",i.value),v.is("disabled",o.value),v.is("checked",n.value),v.is("focus",t.value)]));return(e,l)=>{var a,u;return I(),D("label",{class:N(w(h))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?U((I(),D("input",{key:0,"onUpdate:modelValue":l[0]||(l[0]=e=>_(d)?d.value=e:null),class:N(w(v).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:w(o),"true-value":null!=(a=e.trueValue)?a:e.trueLabel,"false-value":null!=(u=e.falseValue)?u:e.falseLabel,onChange:l[1]||(l[1]=(...e)=>w(c)&&w(c)(...e)),onFocus:l[2]||(l[2]=e=>t.value=!0),onBlur:l[3]||(l[3]=e=>t.value=!1),onClick:l[4]||(l[4]=O((()=>{}),["stop"]))},null,42,ie)),[[G,w(d)]]):U((I(),D("input",{key:1,"onUpdate:modelValue":l[5]||(l[5]=e=>_(d)?d.value=e:null),class:N(w(v).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:w(o),value:w(r),onChange:l[6]||(l[6]=(...e)=>w(c)&&w(c)(...e)),onFocus:l[7]||(l[7]=e=>t.value=!0),onBlur:l[8]||(l[8]=e=>t.value=!1),onClick:l[9]||(l[9]=O((()=>{}),["stop"]))},null,42,de)),[[G,w(d)]]),e.$slots.default||e.label?(I(),D("span",{key:2,class:N(w(v).be("button","inner")),style:H(w(n)?w(m):void 0)},[$(e.$slots,"default",{},(()=>[R(A(e.label),1)]))],6)):M("v-if",!0)],2)}}}),[["__file","checkbox-button.vue"]]);const be=J({modelValue:{type:K(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:e,label:String,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0},...l(["ariaLabel"])}),ve={[a]:e=>p(e),change:e=>p(e)},me=V({name:"ElCheckboxGroup"});var he=T(V({...me,props:be,emits:ve,setup(e,{emit:l}){const t=e,n=B("checkbox"),{formItem:o}=r(),{inputId:u,isLabeledByFormItem:i}=y(t,{formItemContext:o}),d=async e=>{l(a,e),await m(),l("change",e)},c=s({get:()=>t.modelValue,set(e){d(e)}});return q(le,{...Q(W(t),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:c,changeEvent:d}),L({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-checkbox-group",ref:"https://element-plus.org/en-US/component/checkbox.html"},s((()=>!!t.label))),b((()=>t.modelValue),(()=>{t.validateEvent&&(null==o||o.validate("change").catch((e=>v())))})),(e,l)=>{var a;return I(),E(P(e.tag),{id:w(u),class:N(w(n).b("group")),role:"group","aria-label":w(i)?void 0:e.label||e.ariaLabel||"checkbox-group","aria-labelledby":w(i)?null==(a=w(o))?void 0:a.labelId:void 0},{default:F((()=>[$(e.$slots,"default")])),_:3},8,["id","class","aria-label","aria-labelledby"])}}}),[["__file","checkbox-group.vue"]]);const pe=X(se,{CheckboxButton:ce,CheckboxGroup:he});Y(ce),Y(he);export{pe as E};
//# sourceMappingURL=index-TjDDNAcU.js.map

{"version": 3, "file": "ProjectTypeFilter--v8GjLSk.js", "sources": ["../../src/components/ProjectTypeFilter/ProjectTypeFilter.vue"], "sourcesContent": ["<template>\n  <el-radio-group v-model=\"localProjectType\" class=\"project-type-filter\" @change=\"handleProjectTypeChange\">\n    <el-radio-button label=\"SPCB\">SPCB</el-radio-button>\n    <el-radio-button label=\"SPCT\">SPCT</el-radio-button>\n  </el-radio-group>\n</template>\n\n<script setup>\nimport { ref, watch } from 'vue';\n\nconst props = defineProps({\n  projectType: {\n    type: String,\n    default: 'SPCB'\n  }\n});\n\nconst emit = defineEmits(['update:projectType']);\n\nconst localProjectType = ref(props.projectType);\n\nconst handleProjectTypeChange = (value) => {\n  emit('update:projectType', value);\n};\n\nwatch(() => props.projectType, (newValue) => {\n  localProjectType.value = newValue;\n});\n</script>\n\n<style scoped>\n.project-type-filter {\n  margin-right: 10px;\n}\n</style>"], "names": ["props", "__props", "emit", "__emit", "localProjectType", "ref", "projectType", "handleProjectTypeChange", "value", "watch", "newValue"], "mappings": "kRAUA,MAAAA,EAAAC,EAOAC,EAAAC,EAEAC,EAAAC,EAAAL,EAAAM,aAEAC,EAAAC,IACAN,EAAA,qBAAAM,EAAA,SAGAC,GAAA,IAAAT,EAAAM,cAAAI,IACAN,EAAAI,MAAAE,CAAA"}
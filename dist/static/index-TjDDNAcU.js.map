{"version": 3, "file": "index-TjDDNAcU.js", "sources": ["../../node_modules/element-plus/es/components/checkbox/src/checkbox.mjs", "../../node_modules/element-plus/es/components/checkbox/src/constants.mjs", "../../node_modules/element-plus/es/components/checkbox/src/composables/use-checkbox-event.mjs", "../../node_modules/element-plus/es/components/checkbox/src/composables/use-checkbox.mjs", "../../node_modules/element-plus/es/components/checkbox/src/composables/use-checkbox-model.mjs", "../../node_modules/element-plus/es/components/checkbox/src/composables/use-checkbox-status.mjs", "../../node_modules/element-plus/es/components/checkbox/src/composables/use-checkbox-disabled.mjs", "../../node_modules/element-plus/es/components/checkbox/src/checkbox2.mjs", "../../node_modules/element-plus/es/components/checkbox/src/checkbox-button.mjs", "../../node_modules/element-plus/es/components/checkbox/src/checkbox-group.mjs", "../../node_modules/element-plus/es/components/checkbox/src/checkbox-group2.mjs", "../../node_modules/element-plus/es/components/checkbox/index.mjs"], "sourcesContent": ["import '../../../constants/index.mjs';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isString } from '@vue/shared';\nimport { isNumber, isBoolean } from '../../../utils/types.mjs';\n\nconst checkboxProps = {\n  modelValue: {\n    type: [Number, String, Boolean],\n    default: void 0\n  },\n  label: {\n    type: [String, Boolean, Number, Object],\n    default: void 0\n  },\n  value: {\n    type: [String, Boolean, Number, Object],\n    default: void 0\n  },\n  indeterminate: Boolean,\n  disabled: Boolean,\n  checked: <PERSON>ole<PERSON>,\n  name: {\n    type: String,\n    default: void 0\n  },\n  trueValue: {\n    type: [String, Number],\n    default: void 0\n  },\n  falseValue: {\n    type: [String, Number],\n    default: void 0\n  },\n  trueLabel: {\n    type: [String, Number],\n    default: void 0\n  },\n  falseLabel: {\n    type: [String, Number],\n    default: void 0\n  },\n  id: {\n    type: String,\n    default: void 0\n  },\n  controls: {\n    type: String,\n    default: void 0\n  },\n  border: Boolean,\n  size: useSizeProp,\n  tabindex: [String, Number],\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaControls\"])\n};\nconst checkboxEmits = {\n  [UPDATE_MODEL_EVENT]: (val) => isString(val) || isNumber(val) || isBoolean(val),\n  change: (val) => isString(val) || isNumber(val) || isBoolean(val)\n};\n\nexport { checkboxEmits, checkboxProps };\n//# sourceMappingURL=checkbox.mjs.map\n", "const checkboxGroupContextKey = Symbol(\"checkboxGroupContextKey\");\n\nexport { checkboxGroupContextKey };\n//# sourceMappingURL=constants.mjs.map\n", "import { inject, getCurrentInstance, nextTick, computed, watch } from 'vue';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\n\nconst useCheckboxEvent = (props, {\n  model,\n  isLimitExceeded,\n  hasOwnLabel,\n  isDisabled,\n  isLabeledByFormItem\n}) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const { formItem } = useFormItem();\n  const { emit } = getCurrentInstance();\n  function getLabeledValue(value) {\n    var _a, _b, _c, _d;\n    return [true, props.trueValue, props.trueLabel].includes(value) ? (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true : (_d = (_c = props.falseValue) != null ? _c : props.falseLabel) != null ? _d : false;\n  }\n  function emitChangeEvent(checked, e) {\n    emit(\"change\", getLabeledValue(checked), e);\n  }\n  function handleChange(e) {\n    if (isLimitExceeded.value)\n      return;\n    const target = e.target;\n    emit(\"change\", getLabeledValue(target.checked), e);\n  }\n  async function onClickRoot(e) {\n    if (isLimitExceeded.value)\n      return;\n    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {\n      const eventTargets = e.composedPath();\n      const hasLabel = eventTargets.some((item) => item.tagName === \"LABEL\");\n      if (!hasLabel) {\n        model.value = getLabeledValue([false, props.falseValue, props.falseLabel].includes(model.value));\n        await nextTick();\n        emitChangeEvent(model.value, e);\n      }\n    }\n  }\n  const validateEvent = computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.validateEvent) || props.validateEvent);\n  watch(() => props.modelValue, () => {\n    if (validateEvent.value) {\n      formItem == null ? void 0 : formItem.validate(\"change\").catch((err) => debugWarn(err));\n    }\n  });\n  return {\n    handleChange,\n    onClickRoot\n  };\n};\n\nexport { useCheckboxEvent };\n//# sourceMappingURL=use-checkbox-event.mjs.map\n", "import { computed } from 'vue';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport '../../../../hooks/index.mjs';\nimport { useCheckboxDisabled } from './use-checkbox-disabled.mjs';\nimport { useCheckboxEvent } from './use-checkbox-event.mjs';\nimport { useCheckboxModel } from './use-checkbox-model.mjs';\nimport { useCheckboxStatus } from './use-checkbox-status.mjs';\nimport { useFormItem, useFormItemInputId } from '../../../form/src/hooks/use-form-item.mjs';\nimport { isArray } from '@vue/shared';\nimport { useDeprecated } from '../../../../hooks/use-deprecated/index.mjs';\nimport { isPropAbsent } from '../../../../utils/types.mjs';\n\nconst useCheckbox = (props, slots) => {\n  const { formItem: elFormItem } = useFormItem();\n  const { model, isGroup, isLimitExceeded } = useCheckboxModel(props);\n  const {\n    isFocused,\n    isChecked,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue\n  } = useCheckboxStatus(props, slots, { model });\n  const { isDisabled } = useCheckboxDisabled({ model, isChecked });\n  const { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n    disableIdGeneration: hasOwnLabel,\n    disableIdManagement: isGroup\n  });\n  const { handleChange, onClickRoot } = useCheckboxEvent(props, {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem\n  });\n  const setStoreValue = () => {\n    function addToStore() {\n      var _a, _b;\n      if (isArray(model.value) && !model.value.includes(actualValue.value)) {\n        model.value.push(actualValue.value);\n      } else {\n        model.value = (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true;\n      }\n    }\n    props.checked && addToStore();\n  };\n  setStoreValue();\n  useDeprecated({\n    from: \"controls\",\n    replacement: \"aria-controls\",\n    version: \"2.8.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.controls));\n  useDeprecated({\n    from: \"label act as value\",\n    replacement: \"value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => isGroup.value && isPropAbsent(props.value)));\n  useDeprecated({\n    from: \"true-label\",\n    replacement: \"true-value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.trueLabel));\n  useDeprecated({\n    from: \"false-label\",\n    replacement: \"false-value\",\n    version: \"3.0.0\",\n    scope: \"el-checkbox\",\n    ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n  }, computed(() => !!props.falseLabel));\n  return {\n    inputId,\n    isLabeledByFormItem,\n    isChecked,\n    isDisabled,\n    isFocused,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    model,\n    actualValue,\n    handleChange,\n    onClickRoot\n  };\n};\n\nexport { useCheckbox };\n//# sourceMappingURL=use-checkbox.mjs.map\n", "import { ref, getCurrentInstance, inject, computed } from 'vue';\nimport '../../../../utils/index.mjs';\nimport '../../../../constants/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nimport { UPDATE_MODEL_EVENT } from '../../../../constants/event.mjs';\n\nconst useCheckboxModel = (props) => {\n  const selfModel = ref(false);\n  const { emit } = getCurrentInstance();\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isGroup = computed(() => isUndefined(checkboxGroup) === false);\n  const isLimitExceeded = ref(false);\n  const model = computed({\n    get() {\n      var _a, _b;\n      return isGroup.value ? (_a = checkboxGroup == null ? void 0 : checkboxGroup.modelValue) == null ? void 0 : _a.value : (_b = props.modelValue) != null ? _b : selfModel.value;\n    },\n    set(val) {\n      var _a, _b;\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value = ((_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value) !== void 0 && val.length > (checkboxGroup == null ? void 0 : checkboxGroup.max.value) && val.length > model.value.length;\n        isLimitExceeded.value === false && ((_b = checkboxGroup == null ? void 0 : checkboxGroup.changeEvent) == null ? void 0 : _b.call(checkboxGroup, val));\n      } else {\n        emit(UPDATE_MODEL_EVENT, val);\n        selfModel.value = val;\n      }\n    }\n  });\n  return {\n    model,\n    isGroup,\n    isLimitExceeded\n  };\n};\n\nexport { useCheckboxModel };\n//# sourceMappingURL=use-checkbox-model.mjs.map\n", "import { inject, ref, computed, toRaw } from 'vue';\nimport { isEqual } from 'lodash-unified';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isPropAbsent, isBoolean } from '../../../../utils/types.mjs';\nimport { isArray, isObject } from '@vue/shared';\nimport { useFormSize } from '../../../form/src/hooks/use-form-common-props.mjs';\n\nconst useCheckboxStatus = (props, slots, { model }) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isFocused = ref(false);\n  const actualValue = computed(() => {\n    if (!isPropAbsent(props.value)) {\n      return props.value;\n    }\n    return props.label;\n  });\n  const isChecked = computed(() => {\n    const value = model.value;\n    if (isBoolean(value)) {\n      return value;\n    } else if (isArray(value)) {\n      if (isObject(actualValue.value)) {\n        return value.map(toRaw).some((o) => isEqual(o, actualValue.value));\n      } else {\n        return value.map(toRaw).includes(actualValue.value);\n      }\n    } else if (value !== null && value !== void 0) {\n      return value === props.trueValue || value === props.trueLabel;\n    } else {\n      return !!value;\n    }\n  });\n  const checkboxButtonSize = useFormSize(computed(() => {\n    var _a;\n    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;\n  }), {\n    prop: true\n  });\n  const checkboxSize = useFormSize(computed(() => {\n    var _a;\n    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;\n  }));\n  const hasOwnLabel = computed(() => {\n    return !!slots.default || !isPropAbsent(actualValue.value);\n  });\n  return {\n    checkboxButtonSize,\n    isChecked,\n    isFocused,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue\n  };\n};\n\nexport { useCheckboxStatus };\n//# sourceMappingURL=use-checkbox-status.mjs.map\n", "import { inject, computed } from 'vue';\nimport '../../../form/index.mjs';\nimport '../../../../utils/index.mjs';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { useFormDisabled } from '../../../form/src/hooks/use-form-common-props.mjs';\n\nconst useCheckboxDisabled = ({\n  model,\n  isChecked\n}) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isLimitDisabled = computed(() => {\n    var _a, _b;\n    const max = (_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value;\n    const min = (_b = checkboxGroup == null ? void 0 : checkboxGroup.min) == null ? void 0 : _b.value;\n    return !isUndefined(max) && model.value.length >= max && !isChecked.value || !isUndefined(min) && model.value.length <= min && isChecked.value;\n  });\n  const isDisabled = useFormDisabled(computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.disabled.value) || isLimitDisabled.value));\n  return {\n    isDisabled,\n    isLimitDisabled\n  };\n};\n\nexport { useCheckboxDisabled };\n//# sourceMappingURL=use-checkbox-disabled.mjs.map\n", "import { defineComponent, useSlots, computed, openBlock, createBlock, resolveDynamicComponent, unref, normalizeClass, withCtx, createElementVNode, withDirectives, createElementBlock, isRef, withModifiers, vModelCheckbox, renderSlot, Fragment, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { checkboxProps, checkboxEmits } from './checkbox.mjs';\nimport './composables/index.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useCheckbox } from './composables/use-checkbox.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst _hoisted_1 = [\"id\", \"indeterminate\", \"name\", \"tabindex\", \"disabled\", \"true-value\", \"false-value\"];\nconst _hoisted_2 = [\"id\", \"indeterminate\", \"disabled\", \"value\", \"name\", \"tabindex\"];\nconst __default__ = defineComponent({\n  name: \"ElCheckbox\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: checkboxProps,\n  emits: checkboxEmits,\n  setup(__props) {\n    const props = __props;\n    const slots = useSlots();\n    const {\n      inputId,\n      isLabeledByFormItem,\n      isChecked,\n      isDisabled,\n      isFocused,\n      checkboxSize,\n      hasOwnLabel,\n      model,\n      actualValue,\n      handleChange,\n      onClickRoot\n    } = useCheckbox(props, slots);\n    const ns = useNamespace(\"checkbox\");\n    const compKls = computed(() => {\n      return [\n        ns.b(),\n        ns.m(checkboxSize.value),\n        ns.is(\"disabled\", isDisabled.value),\n        ns.is(\"bordered\", props.border),\n        ns.is(\"checked\", isChecked.value)\n      ];\n    });\n    const spanKls = computed(() => {\n      return [\n        ns.e(\"input\"),\n        ns.is(\"disabled\", isDisabled.value),\n        ns.is(\"checked\", isChecked.value),\n        ns.is(\"indeterminate\", props.indeterminate),\n        ns.is(\"focus\", isFocused.value)\n      ];\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(!unref(hasOwnLabel) && unref(isLabeledByFormItem) ? \"span\" : \"label\"), {\n        class: normalizeClass(unref(compKls)),\n        \"aria-controls\": _ctx.indeterminate ? _ctx.controls || _ctx.ariaControls : null,\n        onClick: unref(onClickRoot)\n      }, {\n        default: withCtx(() => {\n          var _a, _b;\n          return [\n            createElementVNode(\"span\", {\n              class: normalizeClass(unref(spanKls))\n            }, [\n              _ctx.trueValue || _ctx.falseValue || _ctx.trueLabel || _ctx.falseLabel ? withDirectives((openBlock(), createElementBlock(\"input\", {\n                key: 0,\n                id: unref(inputId),\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event) => isRef(model) ? model.value = $event : null),\n                class: normalizeClass(unref(ns).e(\"original\")),\n                type: \"checkbox\",\n                indeterminate: _ctx.indeterminate,\n                name: _ctx.name,\n                tabindex: _ctx.tabindex,\n                disabled: unref(isDisabled),\n                \"true-value\": (_a = _ctx.trueValue) != null ? _a : _ctx.trueLabel,\n                \"false-value\": (_b = _ctx.falseValue) != null ? _b : _ctx.falseLabel,\n                onChange: _cache[1] || (_cache[1] = (...args) => unref(handleChange) && unref(handleChange)(...args)),\n                onFocus: _cache[2] || (_cache[2] = ($event) => isFocused.value = true),\n                onBlur: _cache[3] || (_cache[3] = ($event) => isFocused.value = false),\n                onClick: _cache[4] || (_cache[4] = withModifiers(() => {\n                }, [\"stop\"]))\n              }, null, 42, _hoisted_1)), [\n                [vModelCheckbox, unref(model)]\n              ]) : withDirectives((openBlock(), createElementBlock(\"input\", {\n                key: 1,\n                id: unref(inputId),\n                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = ($event) => isRef(model) ? model.value = $event : null),\n                class: normalizeClass(unref(ns).e(\"original\")),\n                type: \"checkbox\",\n                indeterminate: _ctx.indeterminate,\n                disabled: unref(isDisabled),\n                value: unref(actualValue),\n                name: _ctx.name,\n                tabindex: _ctx.tabindex,\n                onChange: _cache[6] || (_cache[6] = (...args) => unref(handleChange) && unref(handleChange)(...args)),\n                onFocus: _cache[7] || (_cache[7] = ($event) => isFocused.value = true),\n                onBlur: _cache[8] || (_cache[8] = ($event) => isFocused.value = false),\n                onClick: _cache[9] || (_cache[9] = withModifiers(() => {\n                }, [\"stop\"]))\n              }, null, 42, _hoisted_2)), [\n                [vModelCheckbox, unref(model)]\n              ]),\n              createElementVNode(\"span\", {\n                class: normalizeClass(unref(ns).e(\"inner\"))\n              }, null, 2)\n            ], 2),\n            unref(hasOwnLabel) ? (openBlock(), createElementBlock(\"span\", {\n              key: 0,\n              class: normalizeClass(unref(ns).e(\"label\"))\n            }, [\n              renderSlot(_ctx.$slots, \"default\"),\n              !_ctx.$slots.default ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [\n                createTextVNode(toDisplayString(_ctx.label), 1)\n              ], 64)) : createCommentVNode(\"v-if\", true)\n            ], 2)) : createCommentVNode(\"v-if\", true)\n          ];\n        }),\n        _: 3\n      }, 8, [\"class\", \"aria-controls\", \"onClick\"]);\n    };\n  }\n});\nvar Checkbox = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"checkbox.vue\"]]);\n\nexport { Checkbox as default };\n//# sourceMappingURL=checkbox2.mjs.map\n", "import { defineComponent, useSlots, inject, computed, openBlock, createElementBlock, normalizeClass, unref, withDirectives, isRef, withModifiers, vModelCheckbox, normalizeStyle, renderSlot, createTextVNode, toDisplayString, createCommentVNode } from 'vue';\nimport '../../../hooks/index.mjs';\nimport { checkboxGroupContextKey } from './constants.mjs';\nimport './composables/index.mjs';\nimport { checkboxProps, checkboxEmits } from './checkbox.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useCheckbox } from './composables/use-checkbox.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst _hoisted_1 = [\"name\", \"tabindex\", \"disabled\", \"true-value\", \"false-value\"];\nconst _hoisted_2 = [\"name\", \"tabindex\", \"disabled\", \"value\"];\nconst __default__ = defineComponent({\n  name: \"ElCheckboxButton\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: checkboxProps,\n  emits: checkboxEmits,\n  setup(__props) {\n    const props = __props;\n    const slots = useSlots();\n    const {\n      isFocused,\n      isChecked,\n      isDisabled,\n      checkboxButtonSize,\n      model,\n      actualValue,\n      handleChange\n    } = useCheckbox(props, slots);\n    const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n    const ns = useNamespace(\"checkbox\");\n    const activeStyle = computed(() => {\n      var _a, _b, _c, _d;\n      const fillValue = (_b = (_a = checkboxGroup == null ? void 0 : checkboxGroup.fill) == null ? void 0 : _a.value) != null ? _b : \"\";\n      return {\n        backgroundColor: fillValue,\n        borderColor: fillValue,\n        color: (_d = (_c = checkboxGroup == null ? void 0 : checkboxGroup.textColor) == null ? void 0 : _c.value) != null ? _d : \"\",\n        boxShadow: fillValue ? `-1px 0 0 0 ${fillValue}` : void 0\n      };\n    });\n    const labelKls = computed(() => {\n      return [\n        ns.b(\"button\"),\n        ns.bm(\"button\", checkboxButtonSize.value),\n        ns.is(\"disabled\", isDisabled.value),\n        ns.is(\"checked\", isChecked.value),\n        ns.is(\"focus\", isFocused.value)\n      ];\n    });\n    return (_ctx, _cache) => {\n      var _a, _b;\n      return openBlock(), createElementBlock(\"label\", {\n        class: normalizeClass(unref(labelKls))\n      }, [\n        _ctx.trueValue || _ctx.falseValue || _ctx.trueLabel || _ctx.falseLabel ? withDirectives((openBlock(), createElementBlock(\"input\", {\n          key: 0,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event) => isRef(model) ? model.value = $event : null),\n          class: normalizeClass(unref(ns).be(\"button\", \"original\")),\n          type: \"checkbox\",\n          name: _ctx.name,\n          tabindex: _ctx.tabindex,\n          disabled: unref(isDisabled),\n          \"true-value\": (_a = _ctx.trueValue) != null ? _a : _ctx.trueLabel,\n          \"false-value\": (_b = _ctx.falseValue) != null ? _b : _ctx.falseLabel,\n          onChange: _cache[1] || (_cache[1] = (...args) => unref(handleChange) && unref(handleChange)(...args)),\n          onFocus: _cache[2] || (_cache[2] = ($event) => isFocused.value = true),\n          onBlur: _cache[3] || (_cache[3] = ($event) => isFocused.value = false),\n          onClick: _cache[4] || (_cache[4] = withModifiers(() => {\n          }, [\"stop\"]))\n        }, null, 42, _hoisted_1)), [\n          [vModelCheckbox, unref(model)]\n        ]) : withDirectives((openBlock(), createElementBlock(\"input\", {\n          key: 1,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = ($event) => isRef(model) ? model.value = $event : null),\n          class: normalizeClass(unref(ns).be(\"button\", \"original\")),\n          type: \"checkbox\",\n          name: _ctx.name,\n          tabindex: _ctx.tabindex,\n          disabled: unref(isDisabled),\n          value: unref(actualValue),\n          onChange: _cache[6] || (_cache[6] = (...args) => unref(handleChange) && unref(handleChange)(...args)),\n          onFocus: _cache[7] || (_cache[7] = ($event) => isFocused.value = true),\n          onBlur: _cache[8] || (_cache[8] = ($event) => isFocused.value = false),\n          onClick: _cache[9] || (_cache[9] = withModifiers(() => {\n          }, [\"stop\"]))\n        }, null, 42, _hoisted_2)), [\n          [vModelCheckbox, unref(model)]\n        ]),\n        _ctx.$slots.default || _ctx.label ? (openBlock(), createElementBlock(\"span\", {\n          key: 2,\n          class: normalizeClass(unref(ns).be(\"button\", \"inner\")),\n          style: normalizeStyle(unref(isChecked) ? unref(activeStyle) : void 0)\n        }, [\n          renderSlot(_ctx.$slots, \"default\", {}, () => [\n            createTextVNode(toDisplayString(_ctx.label), 1)\n          ])\n        ], 6)) : createCommentVNode(\"v-if\", true)\n      ], 2);\n    };\n  }\n});\nvar CheckboxButton = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"checkbox-button.vue\"]]);\n\nexport { CheckboxButton as default };\n//# sourceMappingURL=checkbox-button.mjs.map\n", "import '../../../constants/index.mjs';\nimport '../../../hooks/index.mjs';\nimport '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useSizeProp } from '../../../hooks/use-size/index.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { isArray } from '@vue/shared';\n\nconst checkboxGroupProps = buildProps({\n  modelValue: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  disabled: Boolean,\n  min: Number,\n  max: Number,\n  size: useSizeProp,\n  label: String,\n  fill: String,\n  textColor: String,\n  tag: {\n    type: String,\n    default: \"div\"\n  },\n  validateEvent: {\n    type: Boolean,\n    default: true\n  },\n  ...useAriaProps([\"ariaLabel\"])\n});\nconst checkboxGroupEmits = {\n  [UPDATE_MODEL_EVENT]: (val) => isArray(val),\n  change: (val) => isArray(val)\n};\n\nexport { checkboxGroupEmits, checkboxGroupProps };\n//# sourceMappingURL=checkbox-group.mjs.map\n", "import { defineComponent, nextTick, computed, provide, toRefs, watch, openBlock, createBlock, resolveDynamicComponent, unref, normalizeClass, withCtx, renderSlot } from 'vue';\nimport { pick } from 'lodash-unified';\nimport '../../../constants/index.mjs';\nimport '../../../utils/index.mjs';\nimport '../../../hooks/index.mjs';\nimport '../../form/index.mjs';\nimport { checkboxGroupProps, checkboxGroupEmits } from './checkbox-group.mjs';\nimport { checkboxGroupContextKey } from './constants.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { useFormItem, useFormItemInputId } from '../../form/src/hooks/use-form-item.mjs';\nimport { UPDATE_MODEL_EVENT } from '../../../constants/event.mjs';\nimport { useDeprecated } from '../../../hooks/use-deprecated/index.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\n\nconst __default__ = defineComponent({\n  name: \"ElCheckboxGroup\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: checkboxGroupProps,\n  emits: checkboxGroupEmits,\n  setup(__props, { emit }) {\n    const props = __props;\n    const ns = useNamespace(\"checkbox\");\n    const { formItem } = useFormItem();\n    const { inputId: groupId, isLabeledByFormItem } = useFormItemInputId(props, {\n      formItemContext: formItem\n    });\n    const changeEvent = async (value) => {\n      emit(UPDATE_MODEL_EVENT, value);\n      await nextTick();\n      emit(\"change\", value);\n    };\n    const modelValue = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(val) {\n        changeEvent(val);\n      }\n    });\n    provide(checkboxGroupContextKey, {\n      ...pick(toRefs(props), [\n        \"size\",\n        \"min\",\n        \"max\",\n        \"disabled\",\n        \"validateEvent\",\n        \"fill\",\n        \"textColor\"\n      ]),\n      modelValue,\n      changeEvent\n    });\n    useDeprecated({\n      from: \"label\",\n      replacement: \"aria-label\",\n      version: \"2.8.0\",\n      scope: \"el-checkbox-group\",\n      ref: \"https://element-plus.org/en-US/component/checkbox.html\"\n    }, computed(() => !!props.label));\n    watch(() => props.modelValue, () => {\n      if (props.validateEvent) {\n        formItem == null ? void 0 : formItem.validate(\"change\").catch((err) => debugWarn(err));\n      }\n    });\n    return (_ctx, _cache) => {\n      var _a;\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        id: unref(groupId),\n        class: normalizeClass(unref(ns).b(\"group\")),\n        role: \"group\",\n        \"aria-label\": !unref(isLabeledByFormItem) ? _ctx.label || _ctx.ariaLabel || \"checkbox-group\" : void 0,\n        \"aria-labelledby\": unref(isLabeledByFormItem) ? (_a = unref(formItem)) == null ? void 0 : _a.labelId : void 0\n      }, {\n        default: withCtx(() => [\n          renderSlot(_ctx.$slots, \"default\")\n        ]),\n        _: 3\n      }, 8, [\"id\", \"class\", \"aria-label\", \"aria-labelledby\"]);\n    };\n  }\n});\nvar CheckboxGroup = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"checkbox-group.vue\"]]);\n\nexport { CheckboxGroup as default };\n//# sourceMappingURL=checkbox-group2.mjs.map\n", "import '../../utils/index.mjs';\nimport Checkbox from './src/checkbox2.mjs';\nimport CheckboxButton from './src/checkbox-button.mjs';\nimport CheckboxGroup from './src/checkbox-group2.mjs';\nexport { checkboxGroupEmits, checkboxGroupProps } from './src/checkbox-group.mjs';\nexport { checkboxEmits, checkboxProps } from './src/checkbox.mjs';\nexport { checkboxGroupContextKey } from './src/constants.mjs';\nimport { withInstall, withNoopInstall } from '../../utils/vue/install.mjs';\n\nconst ElCheckbox = withInstall(Checkbox, {\n  CheckboxButton,\n  CheckboxGroup\n});\nconst ElCheckboxButton = withNoopInstall(CheckboxButton);\nconst ElCheckboxGroup = withNoopInstall(CheckboxGroup);\n\nexport { ElCheckbox, ElCheckboxButton, ElCheckboxGroup, ElCheckbox as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["checkboxProps", "modelValue", "type", "Number", "String", "Boolean", "default", "label", "Object", "value", "indeterminate", "disabled", "checked", "name", "trueValue", "falseValue", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "id", "controls", "border", "size", "useSizeProp", "tabindex", "validateEvent", "useAriaProps", "checkboxEmits", "UPDATE_MODEL_EVENT", "val", "isString", "isNumber", "isBoolean", "change", "checkboxGroupContextKey", "Symbol", "useCheckboxEvent", "props", "model", "isLimitExceeded", "hasOwnLabel", "isDisabled", "isLabeledByFormItem", "checkboxGroup", "inject", "formItem", "useFormItem", "emit", "getCurrentInstance", "getLabeledValue", "_a", "_b", "_c", "_d", "includes", "computed", "watch", "validate", "catch", "err", "debugWarn", "handleChange", "e", "target", "onClickRoot", "async", "<PERSON><PERSON><PERSON>", "some", "item", "tagName", "nextTick", "emitChangeEvent", "useCheckbox", "slots", "elFormItem", "isGroup", "selfModel", "ref", "isUndefined", "get", "set", "isArray", "max", "length", "changeEvent", "call", "useCheckboxModel", "isFocused", "isChecked", "checkboxButtonSize", "checkboxSize", "actualValue", "isPropAbsent", "isObject", "map", "toRaw", "o", "isEqual", "useFormSize", "prop", "useCheckboxStatus", "isLimitDisabled", "min", "useFormDisabled", "useCheckboxDisabled", "inputId", "useFormItemInputId", "formItemContext", "disableIdGeneration", "disableIdManagement", "push", "useDeprecated", "from", "replacement", "version", "scope", "_hoisted_1", "_hoisted_2", "__default__", "defineComponent", "Checkbox", "_export_sfc", "emits", "setup", "__props", "useSlots", "ns", "useNamespace", "compKls", "b", "m", "is", "spanKls", "_ctx", "_cache", "openBlock", "createBlock", "resolveDynamicComponent", "unref", "class", "normalizeClass", "ariaControls", "onClick", "withCtx", "createElementVNode", "withDirectives", "createElementBlock", "key", "$event", "isRef", "onChange", "args", "onFocus", "onBlur", "withModifiers", "vModelCheckbox", "renderSlot", "$slots", "createCommentVNode", "Fragment", "createTextVNode", "toDisplayString", "_", "CheckboxButton", "activeStyle", "fillValue", "fill", "backgroundColor", "borderColor", "color", "textColor", "boxShadow", "labelKls", "bm", "be", "style", "normalizeStyle", "checkboxGroupProps", "buildProps", "definePropType", "Array", "tag", "checkboxGroupEmits", "CheckboxGroup", "groupId", "provide", "pick", "toRefs", "role", "aria<PERSON><PERSON><PERSON>", "labelId", "ElCheckbox", "withInstall", "withNoopInstall"], "mappings": "gbASA,MAAMA,EAAgB,CACpBC,WAAY,CACVC,KAAM,CAACC,OAAQC,OAAQC,SACvBC,aAAS,GAEXC,MAAO,CACLL,KAAM,CAACE,OAAQC,QAASF,OAAQK,QAChCF,aAAS,GAEXG,MAAO,CACLP,KAAM,CAACE,OAAQC,QAASF,OAAQK,QAChCF,aAAS,GAEXI,cAAeL,QACfM,SAAUN,QACVO,QAASP,QACTQ,KAAM,CACJX,KAAME,OACNE,aAAS,GAEXQ,UAAW,CACTZ,KAAM,CAACE,OAAQD,QACfG,aAAS,GAEXS,WAAY,CACVb,KAAM,CAACE,OAAQD,QACfG,aAAS,GAEXU,UAAW,CACTd,KAAM,CAACE,OAAQD,QACfG,aAAS,GAEXW,WAAY,CACVf,KAAM,CAACE,OAAQD,QACfG,aAAS,GAEXY,GAAI,CACFhB,KAAME,OACNE,aAAS,GAEXa,SAAU,CACRjB,KAAME,OACNE,aAAS,GAEXc,OAAQf,QACRgB,KAAMC,EACNC,SAAU,CAACnB,OAAQD,QACnBqB,cAAe,CACbtB,KAAMG,QACNC,SAAS,MAERmB,EAAa,CAAC,kBAEbC,GAAgB,CACpBC,CAACA,GAAsBC,GAAQC,EAASD,IAAQE,EAASF,IAAQG,EAAUH,GAC3EI,OAASJ,GAAQC,EAASD,IAAQE,EAASF,IAAQG,EAAUH,IChEzDK,GAA0BC,OAAO,2BCOjCC,GAAmB,CAACC,GACxBC,QACAC,kBACAC,cACAC,aACAC,0BAEA,MAAMC,EAAgBC,EAAOV,QAAyB,IAChDW,SAAEA,GAAaC,KACfC,KAAEA,GAASC,IACjB,SAASC,EAAgBvC,GACvB,IAAIwC,EAAIC,EAAIC,EAAIC,EAChB,MAAO,EAAC,EAAMhB,EAAMtB,UAAWsB,EAAMpB,WAAWqC,SAAS5C,GAAyE,OAA/DyC,EAA+B,OAAzBD,EAAKb,EAAMtB,WAAqBmC,EAAKb,EAAMpB,YAAqBkC,EAA8E,OAAjEE,EAAgC,OAA1BD,EAAKf,EAAMrB,YAAsBoC,EAAKf,EAAMnB,aAAsBmC,CAC/N,CAuBD,MAAM5B,EAAgB8B,GAAS,KAAwB,MAAjBZ,OAAwB,EAASA,EAAclB,gBAAkBY,EAAMZ,gBAM7G,OALA+B,GAAM,IAAMnB,EAAMnC,aAAY,KACxBuB,EAAcf,QACJ,MAAZmC,GAA4BA,EAASY,SAAS,UAAUC,OAAOC,GAAQC,MACxE,IAEI,CACLC,aA1BF,SAAsBC,GACpB,GAAIvB,EAAgB7B,MAClB,OACF,MAAMqD,EAASD,EAAEC,OACjBhB,EAAK,SAAUE,EAAgBc,EAAOlD,SAAUiD,EACjD,EAsBCE,YArBFC,eAA2BH,GACzB,IAAIvB,EAAgB7B,QAEf8B,EAAY9B,QAAU+B,EAAW/B,OAASgC,EAAoBhC,MAAO,CACnDoD,EAAEI,eACOC,MAAMC,GAA0B,UAAjBA,EAAKC,YAEhD/B,EAAM5B,MAAQuC,EAAgB,EAAC,EAAOZ,EAAMrB,WAAYqB,EAAMnB,YAAYoC,SAAShB,EAAM5B,cACnF4D,IAjBZ,SAAyBzD,EAASiD,GAChCf,EAAK,SAAUE,EAAgBpC,GAAUiD,EAC1C,CAgBKS,CAAgBjC,EAAM5B,MAAOoD,GAEhC,CACF,EAUA,ECvCGU,GAAc,CAACnC,EAAOoC,KAC1B,MAAQ5B,SAAU6B,GAAe5B,KAC3BR,MAAEA,EAAKqC,QAAEA,EAAOpC,gBAAEA,GCPD,CAACF,IACxB,MAAMuC,EAAYC,GAAI,IAChB9B,KAAEA,GAASC,IACXL,EAAgBC,EAAOV,QAAyB,GAChDyC,EAAUpB,GAAS,KAAqC,IAA/BuB,EAAYnC,KACrCJ,EAAkBsC,GAAI,GACtBvC,EAAQiB,EAAS,CACrB,GAAAwB,GACE,IAAI7B,EAAIC,EACR,OAAOwB,EAAQjE,MAA4E,OAAnEwC,EAAsB,MAAjBP,OAAwB,EAASA,EAAczC,iBAAsB,EAASgD,EAAGxC,MAAmC,OAA1ByC,EAAKd,EAAMnC,YAAsBiD,EAAKyB,EAAUlE,KACxK,EACD,GAAAsE,CAAInD,GACF,IAAIqB,EAAIC,EACJwB,EAAQjE,OAASuE,EAAQpD,IAC3BU,EAAgB7B,WAAoG,KAA9B,OAA5DwC,EAAsB,MAAjBP,OAAwB,EAASA,EAAcuC,UAAe,EAAShC,EAAGxC,QAAqBmB,EAAIsD,QAA2B,MAAjBxC,OAAwB,EAASA,EAAcuC,IAAIxE,QAAUmB,EAAIsD,OAAS7C,EAAM5B,MAAMyE,QACxM,IAA1B5C,EAAgB7B,QAAyF,OAApEyC,EAAsB,MAAjBR,OAAwB,EAASA,EAAcyC,cAAgCjC,EAAGkC,KAAK1C,EAAed,MAEhJkB,EAAKnB,EAAoBC,GACzB+C,EAAUlE,MAAQmB,EAErB,IAEH,MAAO,CACLS,QACAqC,UACApC,kBACD,EDnB2C+C,CAAiBjD,IACvDkD,UACJA,EAASC,UACTA,EAASC,mBACTA,EAAkBC,aAClBA,EAAYlD,YACZA,EAAWmD,YACXA,GEbsB,EAACtD,EAAOoC,GAASnC,YACzC,MAAMK,EAAgBC,EAAOV,QAAyB,GAChDqD,EAAYV,GAAI,GAChBc,EAAcpC,GAAS,IACtBqC,EAAavD,EAAM3B,OAGjB2B,EAAM7B,MAFJ6B,EAAM3B,QAIX8E,EAAYjC,GAAS,KACzB,MAAM7C,EAAQ4B,EAAM5B,MACpB,OAAIsB,EAAUtB,GACLA,EACEuE,EAAQvE,GACbmF,EAASF,EAAYjF,OAChBA,EAAMoF,IAAIC,GAAO5B,MAAM6B,GAAMC,EAAQD,EAAGL,EAAYjF,SAEpDA,EAAMoF,IAAIC,GAAOzC,SAASqC,EAAYjF,OAEtCA,QACFA,IAAU2B,EAAMtB,WAAaL,IAAU2B,EAAMpB,YAE3CP,CACV,IAeH,MAAO,CACL+E,mBAdyBS,EAAY3C,GAAS,KAC9C,IAAIL,EACJ,OAAqE,OAA7DA,EAAsB,MAAjBP,OAAwB,EAASA,EAAcrB,WAAgB,EAAS4B,EAAGxC,KAAK,IAC3F,CACFyF,MAAM,IAWNX,YACAD,YACAG,aAXmBQ,EAAY3C,GAAS,KACxC,IAAIL,EACJ,OAAqE,OAA7DA,EAAsB,MAAjBP,OAAwB,EAASA,EAAcrB,WAAgB,EAAS4B,EAAGxC,KAAK,KAU7F8B,YARkBe,GAAS,MAClBkB,EAAMlE,UAAYqF,EAAaD,EAAYjF,SAQpDiF,cACD,EF/BGS,CAAkB/D,EAAOoC,EAAO,CAAEnC,WAChCG,WAAEA,GGjBkB,GAC1BH,QACAkD,gBAEA,MAAM7C,EAAgBC,EAAOV,QAAyB,GAChDmE,EAAkB9C,GAAS,KAC/B,IAAIL,EAAIC,EACR,MAAM+B,EAAmE,OAA5DhC,EAAsB,MAAjBP,OAAwB,EAASA,EAAcuC,UAAe,EAAShC,EAAGxC,MACtF4F,EAAmE,OAA5DnD,EAAsB,MAAjBR,OAAwB,EAASA,EAAc2D,UAAe,EAASnD,EAAGzC,MAC5F,OAAQoE,EAAYI,IAAQ5C,EAAM5B,MAAMyE,QAAUD,IAAQM,EAAU9E,QAAUoE,EAAYwB,IAAQhE,EAAM5B,MAAMyE,QAAUmB,GAAOd,EAAU9E,KAAK,IAGhJ,MAAO,CACL+B,WAFiB8D,EAAgBhD,GAAS,KAAwB,MAAjBZ,OAAwB,EAASA,EAAc/B,SAASF,QAAU2F,EAAgB3F,SAGnI2F,kBACD,EHEsBG,CAAoB,CAAElE,QAAOkD,eAC9CiB,QAAEA,EAAO/D,oBAAEA,GAAwBgE,EAAmBrE,EAAO,CACjEsE,gBAAiBjC,EACjBkC,oBAAqBpE,EACrBqE,oBAAqBlC,KAEjBd,aAAEA,EAAYG,YAAEA,GAAgB5B,GAAiBC,EAAO,CAC5DC,QACAC,kBACAC,cACAC,aACAC,wBAEoB,IAEdQ,EAAIC,EAsCZ,OA/BEd,EAAMxB,UANAoE,EAAQ3C,EAAM5B,SAAW4B,EAAM5B,MAAM4C,SAASqC,EAAYjF,OAC5D4B,EAAM5B,MAAMoG,KAAKnB,EAAYjF,OAE7B4B,EAAM5B,MAAwE,OAA/DyC,EAA+B,OAAzBD,EAAKb,EAAMtB,WAAqBmC,EAAKb,EAAMpB,YAAqBkC,GAM3F4D,EAAc,CACZC,KAAM,WACNC,YAAa,gBACbC,QAAS,QACTC,MAAO,cACPtC,IAAK,0DACJtB,GAAS,MAAQlB,EAAMjB,YAC1B2F,EAAc,CACZC,KAAM,qBACNC,YAAa,QACbC,QAAS,QACTC,MAAO,cACPtC,IAAK,0DACJtB,GAAS,IAAMoB,EAAQjE,OAASkF,EAAavD,EAAM3B,UACtDqG,EAAc,CACZC,KAAM,aACNC,YAAa,aACbC,QAAS,QACTC,MAAO,cACPtC,IAAK,0DACJtB,GAAS,MAAQlB,EAAMpB,aAC1B8F,EAAc,CACZC,KAAM,cACNC,YAAa,cACbC,QAAS,QACTC,MAAO,cACPtC,IAAK,0DACJtB,GAAS,MAAQlB,EAAMnB,cACnB,CACLuF,UACA/D,sBACA8C,YACA/C,aACA8C,YACAE,qBACAC,eACAlD,cACAF,QACAqD,cACA9B,eACAG,cACD,EIlFGoD,GAAa,CAAC,KAAM,gBAAiB,OAAQ,WAAY,WAAY,aAAc,eACnFC,GAAa,CAAC,KAAM,gBAAiB,WAAY,QAAS,OAAQ,YAClEC,GAAcC,EAAgB,CAClCzG,KAAM,eA+GR,IAAI0G,GAA2BC,EA7GGF,EAAgB,IAC7CD,GACHjF,MAAOpC,EACPyH,MAAO/F,GACP,KAAAgG,CAAMC,GACJ,MAAMvF,EAAQuF,EACRnD,EAAQoD,KACRpB,QACJA,EAAO/D,oBACPA,EAAmB8C,UACnBA,EAAS/C,WACTA,EAAU8C,UACVA,EAASG,aACTA,EAAYlD,YACZA,EAAWF,MACXA,EAAKqD,YACLA,EAAW9B,aACXA,EAAYG,YACZA,GACEQ,GAAYnC,EAAOoC,GACjBqD,EAAKC,EAAa,YAClBC,EAAUzE,GAAS,IAChB,CACLuE,EAAGG,IACHH,EAAGI,EAAExC,EAAahF,OAClBoH,EAAGK,GAAG,WAAY1F,EAAW/B,OAC7BoH,EAAGK,GAAG,WAAY9F,EAAMhB,QACxByG,EAAGK,GAAG,UAAW3C,EAAU9E,UAGzB0H,EAAU7E,GAAS,IAChB,CACLuE,EAAGhE,EAAE,SACLgE,EAAGK,GAAG,WAAY1F,EAAW/B,OAC7BoH,EAAGK,GAAG,UAAW3C,EAAU9E,OAC3BoH,EAAGK,GAAG,gBAAiB9F,EAAM1B,eAC7BmH,EAAGK,GAAG,QAAS5C,EAAU7E,UAG7B,MAAO,CAAC2H,EAAMC,KACLC,IAAaC,EAAYC,GAAyBC,EAAMlG,IAAgBkG,EAAMhG,GAAuB,OAAS,SAAU,CAC7HiG,MAAOC,EAAeF,EAAMV,IAC5B,gBAAiBK,EAAK1H,cAAgB0H,EAAKjH,UAAYiH,EAAKQ,aAAe,KAC3EC,QAASJ,EAAM1E,IACd,CACDzD,QAASwI,GAAQ,KACf,IAAI7F,EAAIC,EACR,MAAO,CACL6F,EAAmB,OAAQ,CACzBL,MAAOC,EAAeF,EAAMN,KAC3B,CACDC,EAAKtH,WAAasH,EAAKrH,YAAcqH,EAAKpH,WAAaoH,EAAKnH,WAAa+H,GAAgBV,IAAaW,EAAmB,QAAS,CAChIC,IAAK,EACLhI,GAAIuH,EAAMjC,GACV,sBAAuB6B,EAAO,KAAOA,EAAO,GAAMc,GAAWC,EAAM/G,GAASA,EAAM5B,MAAQ0I,EAAS,MACnGT,MAAOC,EAAeF,EAAMZ,GAAIhE,EAAE,aAClC3D,KAAM,WACNQ,cAAe0H,EAAK1H,cACpBG,KAAMuH,EAAKvH,KACXU,SAAU6G,EAAK7G,SACfZ,SAAU8H,EAAMjG,GAChB,aAAuC,OAAxBS,EAAKmF,EAAKtH,WAAqBmC,EAAKmF,EAAKpH,UACxD,cAAyC,OAAzBkC,EAAKkF,EAAKrH,YAAsBmC,EAAKkF,EAAKnH,WAC1DoI,SAAUhB,EAAO,KAAOA,EAAO,GAAK,IAAIiB,IAASb,EAAM7E,IAAiB6E,EAAM7E,EAAN6E,IAAuBa,IAC/FC,QAASlB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GACjE+I,OAAQnB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GAChEoI,QAASR,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAC9C,CAAC,WACH,KAAM,GAAItC,KAAc,CACzB,CAACuC,EAAgBjB,EAAMpG,MACpB2G,GAAgBV,IAAaW,EAAmB,QAAS,CAC5DC,IAAK,EACLhI,GAAIuH,EAAMjC,GACV,sBAAuB6B,EAAO,KAAOA,EAAO,GAAMc,GAAWC,EAAM/G,GAASA,EAAM5B,MAAQ0I,EAAS,MACnGT,MAAOC,EAAeF,EAAMZ,GAAIhE,EAAE,aAClC3D,KAAM,WACNQ,cAAe0H,EAAK1H,cACpBC,SAAU8H,EAAMjG,GAChB/B,MAAOgI,EAAM/C,GACb7E,KAAMuH,EAAKvH,KACXU,SAAU6G,EAAK7G,SACf8H,SAAUhB,EAAO,KAAOA,EAAO,GAAK,IAAIiB,IAASb,EAAM7E,IAAiB6E,EAAM7E,EAAN6E,IAAuBa,IAC/FC,QAASlB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GACjE+I,OAAQnB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GAChEoI,QAASR,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAC9C,CAAC,WACH,KAAM,GAAIrC,KAAc,CACzB,CAACsC,EAAgBjB,EAAMpG,MAEzB0G,EAAmB,OAAQ,CACzBL,MAAOC,EAAeF,EAAMZ,GAAIhE,EAAE,WACjC,KAAM,IACR,GACH4E,EAAMlG,IAAgB+F,IAAaW,EAAmB,OAAQ,CAC5DC,IAAK,EACLR,MAAOC,EAAeF,EAAMZ,GAAIhE,EAAE,WACjC,CACD8F,EAAWvB,EAAKwB,OAAQ,WACvBxB,EAAKwB,OAAOtJ,QAEHuJ,EAAmB,QAAQ,IAFbvB,IAAaW,EAAmBa,EAAU,CAAEZ,IAAK,GAAK,CAC5Ea,EAAgBC,EAAgB5B,EAAK7H,OAAQ,IAC5C,MACF,IAAMsJ,EAAmB,QAAQ,GACrC,IAEHI,EAAG,GACF,EAAG,CAAC,QAAS,gBAAiB,YAEpC,IAEmD,CAAC,CAAC,SAAU,kBCjHlE,MAAM9C,GAAa,CAAC,OAAQ,WAAY,WAAY,aAAc,eAC5DC,GAAa,CAAC,OAAQ,WAAY,WAAY,SAC9CC,GAAcC,EAAgB,CAClCzG,KAAM,qBA2FR,IAAIqJ,GAAiC1C,EAzFHF,EAAgB,IAC7CD,GACHjF,MAAOpC,EACPyH,MAAO/F,GACP,KAAAgG,CAAMC,GACJ,MAAMvF,EAAQuF,EACRnD,EAAQoD,KACRtC,UACJA,EAASC,UACTA,EAAS/C,WACTA,EAAUgD,mBACVA,EAAkBnD,MAClBA,EAAKqD,YACLA,EAAW9B,aACXA,GACEW,GAAYnC,EAAOoC,GACjB9B,EAAgBC,EAAOV,QAAyB,GAChD4F,EAAKC,EAAa,YAClBqC,EAAc7G,GAAS,KAC3B,IAAIL,EAAIC,EAAIC,EAAIC,EAChB,MAAMgH,EAA6G,OAAhGlH,EAAmE,OAA7DD,EAAsB,MAAjBP,OAAwB,EAASA,EAAc2H,WAAgB,EAASpH,EAAGxC,OAAiByC,EAAK,GAC/H,MAAO,CACLoH,gBAAiBF,EACjBG,YAAaH,EACbI,MAA6G,OAArGpH,EAAwE,OAAlED,EAAsB,MAAjBT,OAAwB,EAASA,EAAc+H,gBAAqB,EAAStH,EAAG1C,OAAiB2C,EAAK,GACzHsH,UAAWN,EAAY,cAAcA,SAAc,EACpD,IAEGO,EAAWrH,GAAS,IACjB,CACLuE,EAAGG,EAAE,UACLH,EAAG+C,GAAG,SAAUpF,EAAmB/E,OACnCoH,EAAGK,GAAG,WAAY1F,EAAW/B,OAC7BoH,EAAGK,GAAG,UAAW3C,EAAU9E,OAC3BoH,EAAGK,GAAG,QAAS5C,EAAU7E,UAG7B,MAAO,CAAC2H,EAAMC,KACZ,IAAIpF,EAAIC,EACR,OAAOoF,IAAaW,EAAmB,QAAS,CAC9CP,MAAOC,EAAeF,EAAMkC,KAC3B,CACDvC,EAAKtH,WAAasH,EAAKrH,YAAcqH,EAAKpH,WAAaoH,EAAKnH,WAAa+H,GAAgBV,IAAaW,EAAmB,QAAS,CAChIC,IAAK,EACL,sBAAuBb,EAAO,KAAOA,EAAO,GAAMc,GAAWC,EAAM/G,GAASA,EAAM5B,MAAQ0I,EAAS,MACnGT,MAAOC,EAAeF,EAAMZ,GAAIgD,GAAG,SAAU,aAC7C3K,KAAM,WACNW,KAAMuH,EAAKvH,KACXU,SAAU6G,EAAK7G,SACfZ,SAAU8H,EAAMjG,GAChB,aAAuC,OAAxBS,EAAKmF,EAAKtH,WAAqBmC,EAAKmF,EAAKpH,UACxD,cAAyC,OAAzBkC,EAAKkF,EAAKrH,YAAsBmC,EAAKkF,EAAKnH,WAC1DoI,SAAUhB,EAAO,KAAOA,EAAO,GAAK,IAAIiB,IAASb,EAAM7E,IAAiB6E,EAAM7E,EAAN6E,IAAuBa,IAC/FC,QAASlB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GACjE+I,OAAQnB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GAChEoI,QAASR,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAC9C,CAAC,WACH,KAAM,GAAItC,KAAc,CACzB,CAACuC,EAAgBjB,EAAMpG,MACpB2G,GAAgBV,IAAaW,EAAmB,QAAS,CAC5DC,IAAK,EACL,sBAAuBb,EAAO,KAAOA,EAAO,GAAMc,GAAWC,EAAM/G,GAASA,EAAM5B,MAAQ0I,EAAS,MACnGT,MAAOC,EAAeF,EAAMZ,GAAIgD,GAAG,SAAU,aAC7C3K,KAAM,WACNW,KAAMuH,EAAKvH,KACXU,SAAU6G,EAAK7G,SACfZ,SAAU8H,EAAMjG,GAChB/B,MAAOgI,EAAM/C,GACb2D,SAAUhB,EAAO,KAAOA,EAAO,GAAK,IAAIiB,IAASb,EAAM7E,IAAiB6E,EAAM7E,EAAN6E,IAAuBa,IAC/FC,QAASlB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GACjE+I,OAAQnB,EAAO,KAAOA,EAAO,GAAMc,GAAW7D,EAAU7E,OAAQ,GAChEoI,QAASR,EAAO,KAAOA,EAAO,GAAKoB,GAAc,QAC9C,CAAC,WACH,KAAM,GAAIrC,KAAc,CACzB,CAACsC,EAAgBjB,EAAMpG,MAEzB+F,EAAKwB,OAAOtJ,SAAW8H,EAAK7H,OAAS+H,IAAaW,EAAmB,OAAQ,CAC3EC,IAAK,EACLR,MAAOC,EAAeF,EAAMZ,GAAIgD,GAAG,SAAU,UAC7CC,MAAOC,EAAetC,EAAMlD,GAAakD,EAAM0B,QAAe,IAC7D,CACDR,EAAWvB,EAAKwB,OAAQ,UAAW,CAAE,GAAE,IAAM,CAC3CG,EAAgBC,EAAgB5B,EAAK7H,OAAQ,OAE9C,IAAMsJ,EAAmB,QAAQ,IACnC,EAAE,CAER,IAEyD,CAAC,CAAC,SAAU,yBC9FxE,MAAMmB,GAAqBC,EAAW,CACpChL,WAAY,CACVC,KAAMgL,EAAeC,OACrB7K,QAAS,IAAM,IAEjBK,SAAUN,QACVgG,IAAKlG,OACL8E,IAAK9E,OACLkB,KAAMC,EACNf,MAAOH,OACPiK,KAAMjK,OACNqK,UAAWrK,OACXgL,IAAK,CACHlL,KAAME,OACNE,QAAS,OAEXkB,cAAe,CACbtB,KAAMG,QACNC,SAAS,MAERmB,EAAa,CAAC,gBAEb4J,GAAqB,CACzB1J,CAACA,GAAsBC,GAAQoD,EAAQpD,GACvCI,OAASJ,GAAQoD,EAAQpD,IClBrByF,GAAcC,EAAgB,CAClCzG,KAAM,oBAoER,IAAIyK,GAAgC9D,EAlEFF,EAAgB,IAC7CD,GACHjF,MAAO4I,GACPvD,MAAO4D,GACP,KAAA3D,CAAMC,GAAS7E,KAAEA,IACf,MAAMV,EAAQuF,EACRE,EAAKC,EAAa,aAClBlF,SAAEA,GAAaC,KACb2D,QAAS+E,EAAO9I,oBAAEA,GAAwBgE,EAAmBrE,EAAO,CAC1EsE,gBAAiB9D,IAEbuC,EAAcnB,MAAOvD,IACzBqC,EAAKnB,EAAoBlB,SACnB4D,IACNvB,EAAK,SAAUrC,EAAM,EAEjBR,EAAaqD,EAAS,CAC1BwB,IAAG,IACM1C,EAAMnC,WAEf,GAAA8E,CAAInD,GACFuD,EAAYvD,EACb,IA2BH,OAzBA4J,EAAQvJ,GAAyB,IAC5BwJ,EAAKC,EAAOtJ,GAAQ,CACrB,OACA,MACA,MACA,WACA,gBACA,OACA,cAEFnC,aACAkF,gBAEF2B,EAAc,CACZC,KAAM,QACNC,YAAa,aACbC,QAAS,QACTC,MAAO,oBACPtC,IAAK,0DACJtB,GAAS,MAAQlB,EAAM7B,SAC1BgD,GAAM,IAAMnB,EAAMnC,aAAY,KACxBmC,EAAMZ,gBACI,MAAZoB,GAA4BA,EAASY,SAAS,UAAUC,OAAOC,GAAQC,MACxE,IAEI,CAACyE,EAAMC,KACZ,IAAIpF,EACJ,OAAOqF,IAAaC,EAAYC,EAAwBJ,EAAKgD,KAAM,CACjElK,GAAIuH,EAAM8C,GACV7C,MAAOC,EAAeF,EAAMZ,GAAIG,EAAE,UAClC2D,KAAM,QACN,aAAelD,EAAMhG,QAA0E,EAAnD2F,EAAK7H,OAAS6H,EAAKwD,WAAa,iBAC5E,kBAAmBnD,EAAMhG,GAAiD,OAAzBQ,EAAKwF,EAAM7F,SAAqB,EAASK,EAAG4I,aAAU,GACtG,CACDvL,QAASwI,GAAQ,IAAM,CACrBa,EAAWvB,EAAKwB,OAAQ,cAE1BK,EAAG,GACF,EAAG,CAAC,KAAM,QAAS,aAAc,mBAAmB,CAE1D,IAEwD,CAAC,CAAC,SAAU,wBC3ElE,MAAC6B,GAAaC,EAAYxE,GAAU,CACvC2C,kBACAoB,mBAEuBU,EAAgB9B,IACjB8B,EAAgBV", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}
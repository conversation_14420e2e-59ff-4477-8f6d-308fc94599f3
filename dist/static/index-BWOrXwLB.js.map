{"version": 3, "file": "index-BWOrXwLB.js", "sources": ["../../node_modules/element-plus/es/components/link/src/link.mjs", "../../node_modules/element-plus/es/components/link/src/link2.mjs", "../../node_modules/element-plus/es/components/link/index.mjs"], "sourcesContent": ["import '../../../utils/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\n\nconst linkProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"info\", \"danger\", \"default\"],\n    default: \"default\"\n  },\n  underline: {\n    type: Boolean,\n    default: true\n  },\n  disabled: { type: Boolean, default: false },\n  href: { type: String, default: \"\" },\n  target: {\n    type: String,\n    default: \"_self\"\n  },\n  icon: {\n    type: iconPropType\n  }\n});\nconst linkEmits = {\n  click: (evt) => evt instanceof MouseEvent\n};\n\nexport { linkEmits, linkProps };\n//# sourceMappingURL=link.mjs.map\n", "import { defineComponent, computed, openBlock, createElementBlock, normalizeClass, unref, createBlock, withCtx, resolveDynamicComponent, createCommentVNode, renderSlot } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport '../../../hooks/index.mjs';\nimport { linkProps, linkEmits } from './link.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\n\nconst _hoisted_1 = [\"href\", \"target\"];\nconst __default__ = defineComponent({\n  name: \"ElLink\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: linkProps,\n  emits: linkEmits,\n  setup(__props, { emit }) {\n    const props = __props;\n    const ns = useNamespace(\"link\");\n    const linkKls = computed(() => [\n      ns.b(),\n      ns.m(props.type),\n      ns.is(\"disabled\", props.disabled),\n      ns.is(\"underline\", props.underline && !props.disabled)\n    ]);\n    function handleClick(event) {\n      if (!props.disabled)\n        emit(\"click\", event);\n    }\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"a\", {\n        class: normalizeClass(unref(linkKls)),\n        href: _ctx.disabled || !_ctx.href ? void 0 : _ctx.href,\n        target: _ctx.disabled || !_ctx.href ? void 0 : _ctx.target,\n        onClick: handleClick\n      }, [\n        _ctx.icon ? (openBlock(), createBlock(unref(ElIcon), { key: 0 }, {\n          default: withCtx(() => [\n            (openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))\n          ]),\n          _: 1\n        })) : createCommentVNode(\"v-if\", true),\n        _ctx.$slots.default ? (openBlock(), createElementBlock(\"span\", {\n          key: 1,\n          class: normalizeClass(unref(ns).e(\"inner\"))\n        }, [\n          renderSlot(_ctx.$slots, \"default\")\n        ], 2)) : createCommentVNode(\"v-if\", true),\n        _ctx.$slots.icon ? renderSlot(_ctx.$slots, \"icon\", { key: 2 }) : createCommentVNode(\"v-if\", true)\n      ], 10, _hoisted_1);\n    };\n  }\n});\nvar Link = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"link.vue\"]]);\n\nexport { Link as default };\n//# sourceMappingURL=link2.mjs.map\n", "import '../../utils/index.mjs';\nimport Link from './src/link2.mjs';\nexport { linkEmits, linkProps } from './src/link.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\n\nconst ElLink = withInstall(Link);\n\nexport { ElLink, ElLink as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["linkProps", "buildProps", "type", "String", "values", "default", "underline", "Boolean", "disabled", "href", "target", "icon", "iconPropType", "linkEmits", "click", "evt", "MouseEvent", "_hoisted_1", "__default__", "defineComponent", "name", "ElLink", "withInstall", "_export_sfc", "props", "emits", "setup", "__props", "emit", "ns", "useNamespace", "linkKls", "computed", "b", "m", "is", "handleClick", "event", "_ctx", "_cache", "openBlock", "createElementBlock", "class", "normalizeClass", "unref", "onClick", "createBlock", "ElIcon", "key", "withCtx", "resolveDynamicComponent", "_", "createCommentVNode", "$slots", "e", "renderSlot"], "mappings": "2JAIA,MAAMA,EAAYC,EAAW,CAC3BC,KAAM,CACJA,KAAMC,OACNC,OAAQ,CAAC,UAAW,UAAW,UAAW,OAAQ,SAAU,WAC5DC,QAAS,WAEXC,UAAW,CACTJ,KAAMK,QACNF,SAAS,GAEXG,SAAU,CAAEN,KAAMK,QAASF,SAAS,GACpCI,KAAM,CAAEP,KAAMC,OAAQE,QAAS,IAC/BK,OAAQ,CACNR,KAAMC,OACNE,QAAS,SAEXM,KAAM,CACJT,KAAMU,KAGJC,EAAY,CAChBC,MAAQC,GAAQA,aAAeC,YClB3BC,EAAa,CAAC,OAAQ,UACtBC,EAAcC,EAAgB,CAClCC,KAAM,WCJH,MAACC,EAASC,ED+CYC,EAzCOJ,EAAgB,IAC7CD,EACHM,MAAOxB,EACPyB,MAAOZ,EACP,KAAAa,CAAMC,GAASC,KAAEA,IACf,MAAMJ,EAAQG,EACRE,EAAKC,EAAa,QAClBC,EAAUC,GAAS,IAAM,CAC7BH,EAAGI,IACHJ,EAAGK,EAAEV,EAAMtB,MACX2B,EAAGM,GAAG,WAAYX,EAAMhB,UACxBqB,EAAGM,GAAG,YAAaX,EAAMlB,YAAckB,EAAMhB,aAE/C,SAAS4B,EAAYC,GACdb,EAAMhB,UACToB,EAAK,QAASS,EACjB,CACD,MAAO,CAACC,EAAMC,KACLC,IAAaC,EAAmB,IAAK,CAC1CC,MAAOC,EAAeC,EAAMb,IAC5BtB,KAAM6B,EAAK9B,WAAa8B,EAAK7B,UAAO,EAAS6B,EAAK7B,KAClDC,OAAQ4B,EAAK9B,WAAa8B,EAAK7B,UAAO,EAAS6B,EAAK5B,OACpDmC,QAAST,GACR,CACDE,EAAK3B,MAAQ6B,IAAaM,EAAYF,EAAMG,GAAS,CAAEC,IAAK,GAAK,CAC/D3C,QAAS4C,GAAQ,IAAM,EACpBT,IAAaM,EAAYI,EAAwBZ,EAAK3B,WAEzDwC,EAAG,KACCC,EAAmB,QAAQ,GACjCd,EAAKe,OAAOhD,SAAWmC,IAAaC,EAAmB,OAAQ,CAC7DO,IAAK,EACLN,MAAOC,EAAeC,EAAMf,GAAIyB,EAAE,WACjC,CACDC,EAAWjB,EAAKe,OAAQ,YACvB,IAAMD,EAAmB,QAAQ,GACpCd,EAAKe,OAAO1C,KAAO4C,EAAWjB,EAAKe,OAAQ,OAAQ,CAAEL,IAAK,IAAOI,EAAmB,QAAQ,IAC3F,GAAInC,GAEV,IAE+C,CAAC,CAAC,SAAU", "x_google_ignoreList": [0, 1, 2]}
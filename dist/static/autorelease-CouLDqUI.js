import{H as e,e as t,p as o,J as a,a as c,L as s,aa as n,T as l,V as i,o as h,h as p,v as d,w as r,j as m,l as b,x as _,a5 as g,F as u,r as f,C as y,a6 as v,a4 as k,t as w,ab as x,E as C,ac as j,ad as M,ae as T,af as D,n as I,a7 as S,ag as z,k as E,ah as $,ai as O,aj as R,ak as B,al as V,am as F,an as P,ao as A,ap as q,aq as N,ar as U,as as G,at as L,au as W,a9 as Y,X as H,Y as J,av as Q,Z as X,P as Z,Q as K,aw as ee}from"./index-awKTxnvj.js";import{_ as te}from"./ProjectTypeFilter--v8GjLSk.js";import{E as oe,b as ae,a as ce}from"./index-ENIpyTzl.js";import{E as se,a as ne,b as le,v as ie}from"./directive-CeALrXM5.js";import{E as he}from"./index-BWOrXwLB.js";import{E as pe}from"./index-DeelQlL1.js";import"./index-TjDDNAcU.js";const de=e=>(Z("data-v-341ca16e"),e=e(),K(),e),re={class:"index-conntainer"},me={class:"ar-container"},be={style:{float:"left","font-size":"13px"}},_e={style:{float:"left"}},ge=["id"],ue={class:"ar-container"},fe={class:"ar-container"},ye=de((()=>b("br",null,null,-1))),ve={class:"ar-container"},ke={class:"dialog-footer"},we=["href"],xe=de((()=>b("br",null,null,-1))),Ce=de((()=>b("br",null,null,-1))),je=de((()=>b("br",null,null,-1))),Me={key:0},Te=de((()=>b("br",null,null,-1))),De={class:"ar-container"},Ie={style:{display:"flex"}},Se={style:{display:"flex","justify-content":"center"}},ze=de((()=>b("span",null,"您正在进行live自动发布流程，请确认您的操作是否要继续。",-1))),Ee={class:"dialog-footer"},$e={style:{display:"flex"}},Oe={style:{display:"flex"}},Re=["href"],Be=e(t({__name:"autorelease",setup(e){const t=o(!1),Z=o(!1);o(!1);const K=o(!1);o("first");const de=a([]),Be=a([]),Ve=a([]),Fe=a([{"shopee-chatbot-intent":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intent","shopee-chatbot-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.admin","shopee-chatbot-adminasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminasynctask","shopee-chatbot-adminconfigservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminconfigservice","shopee-chatbot-adminservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminservice","shopee-chatbot-agentcontrol":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.agentcontrol","shopee-chatbot-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.asynctask","shopee-chatbot-auditlog":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.auditlog","shopee-chatbot-botapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi","shopee-chatbot-context":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.context","shopee-chatbot-dm":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.dm","shopee-chatbot-featurecenter":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featurecenter","shopee-chatbot-intentclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intentclarification","shopee-chatbot-messageasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageasynctask","shopee-chatbot-messageservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageservice","shopee-chatbot-messageverification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.message_service.messageverification","shopee-chatbot-nlu":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.nlu","shopee-chatbot-ordercard":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.ordercard","shopee-chatbot-pilotapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.pilotapi","shopee-chatbotcommon-adminasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminasynctask","shopee-chatbotcommon-adminconfigservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminconfigservice","shopee-chatbotcommon-adminservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminservice","shopee-chatbotcommon-agentcontrol":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.agentcontrol","shopee-chatbotcommon-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.asynctask","shopee-chatbotcommon-botapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.botapi","shopee-chatbotcommon-context":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.context","shopee-chatbotcommon-dm":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.dm","shopee-chatbotcommon-featurecenter":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featurecenter","shopee-chatbotcommon-nlu":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu","shopee-chatbotcommon-productrecommend":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.productrecommend","shopee-chatbotcommon-rulebaseservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.rulebaseservice","shopee-chatbotcommon-shopconsole":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsole","shopee-chatbotcommon-intentclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intentclarification","shopee-chatbot-websocketgwy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.websocketgwy","shopee-csdata-metricservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.data.metricservice","shopee-chatbotcommon-logic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.ai_video_chatbot.engineer.logic","shopee-chatbotcommon-msgdetection":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.ai_video_chatbot.engineer.msgdetection"}]),Pe=a([{"shopee-chatbot-autotraining":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining","shopee-annotation-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.admin","shopee-annotation-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.asynctask","shopee-annotation-timetask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.timetask","shopee-annotation-dataproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.dataproxy","shopee-agorithmservice-component":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.ai_engineering.nlu_component","shopee-chatbot-experimentmanagement":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.experimentmanagement","shopee-chatbot-featureapiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featureapiproxy","shopee-chatbot-modelgw":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.modelgw","shopee-chatbot-realtime":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.realtime","shopee-chatbot-recallmanager":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallmanager","shopee-chatbot-recallservice":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recallservice","shopee-chatbot-recommendation":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.recommendation","shopee-chatbotcommon-apadmin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadmin","shopee-chatbotcommon-apasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apasynctask","shopee-chatbotcommon-apdataproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apdataproxy","shopee-chatbotcommon-component":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu_component","shopee-chatbotcommon-experimentmanagement":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.experimentmanagement","shopee-chatbotcommon-featureapiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featureapiproxy","shopee-chatbotcommon-intent":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intent","shopee-chatbotcommon-kbadmin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmin","shopee-chatbotcommon-kbapi":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbapi","shopee-chatbotcommon-kbasynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbasynctask","shopee-chatbotcommon-kblabelclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kblabelclarification","shopee-chatbotcommon-modelgw":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.modelgw","shopee-knowledgebase-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.admin","shopee-knowledgebase-api":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.api","shopee-knowledgebase-asynctask":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.asynctask","shopee-knowledgebase-labelclarification":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.labelclarification","shopee-chatbotcommon-promptmanagements":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.promptmanagements","shopee-knowledgeplatform-admin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin","shopee-knowledgeplatform-api":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.api","shopee-knowledgeplatform-qa_tools":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.qa_tools","shopee-knowledgeplatform-offline":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.offline","shopee-chatbot-apiadmin":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.apistore.apiadmin","shopee-chatbot-apiflowserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.apistore.apiflowserving"}]),Ae=a([{"shopee-chatbot-api":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.api","shopee-chatbot-autotraining":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.autotraining","shopee-chatbotcommon-tfapiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfapiproxy","shopee-chatbotcommon-tfeditor":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeditor","shopee-chatbotcommon-tfserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfserving","shopee-chatbotcommon-tfvariateserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfvariateserving","shopee-taskflow-apiproxy":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.apiproxy","shopee-taskflow-editor":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.editor","shopee-taskflow-taskflowserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowserving","shopee-taskflow-taskflowsop":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.taskflowsop","shopee-taskflow-variateserving":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.variateserving"}]),qe=a([{"shopee-autotrainingportal-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.autotrainingportal.adminstatic","shopee-annotation-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.annotation.adminstatic","shopee-cbrcmdplt-rcmdpltstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.cbrcmdplt.rcmdpltstatic","shopee-chatbot-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.adminstatic","shopee-chatbot-chatbotcsatstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotcsatstatic","shopee-chatbot-chatbotrnstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotrnstatic","shopee-chatbot-chatbotstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.chatbotstatic","shopee-chatbot-csatstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.csatstatic","shopee-chatbot-dashboardstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.dashboardstatic","shopee-chatbotcommon-admincommonsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.admincommonsaasstatic","shopee-chatbotcommon-adminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminsaasstatic","shopee-chatbotcommon-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminstatic","shopee-chatbotcommon-annotationadminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.annotationadminstatic","shopee-chatbotcommon-apadminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.apadminsaasstatic","shopee-chatbotcommon-csatstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.csatstatic","shopee-chatbotcommon-kbadmincommonsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadmincommonsaasstatic","shopee-chatbotcommon-kbadminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.kbadminsaasstatic","shopee-chatbotcommon-shopconsolestatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.shopconsolestatic","shopee-chatbotcommon-static":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.static","shopee-chatbotcommon-tfeadmincommonsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadmincommonsaasstatic","shopee-chatbotcommon-tfeadminsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tfeadminsaasstatic","shopee-chatbotcommon-tmcsaasstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.tmcsaasstatic","shopee-gec-gecstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.function.gecstatic","shopee-knowledgebase-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.knowledgebase.adminstatic","shopee-taskflow-adminstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.taskflow.adminstatic","shopee-cschat-h5":"https://space.shopee.io/console/cmdb/overview/detail/shopee.customer_service_and_chatbot.customer_service.cschannel.cschat.h5","shopee-knowledgeplatform-adminstatic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.adminstatic","shopee-knowledgeplatformnode-knowledgeplatformnode":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.admin_portal","shopee-knowledgeplatform-guidesstatic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.knowledgeplatform.guidesstatic","shopee-chatbot-insights":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.adminportal.insightstatic","shopee-chatbotcommon-insightssaasstatic-test":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.marketplace_others.shopee_content_service.chatbot.chatbotcommon.insightssaasstatic","shopee-chatbot-mmfchatbotconsole":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.platform.mmfchatbotconsole","shopee-chatbot-h5mmfchatbotsharedrcstatic":"https://space-next.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.h5mmfchatbotsharedrcstatic","shopee-chatbot-tmcstatic":"https://space.shopee.io/console/cmdb/deployment/detail/shopee.chat_and_chatbot.cs_chatbot.engineer.adminportal.tmcstatic"}]),Ne=()=>{window.open("https://monitoring.infra.sz.shopee.io/grafana/d/kj1f3huVk/chatbot-fa-ban-kan-ban?orgId=10&from=now-1h&to=now&refresh=30s","_blank")},Ue=c((()=>{const e={},t=Fe.map((e=>Object.keys(e))).flat();for(const o of Be)if(t.includes(o)){const t=Fe.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)})),Ge=c((()=>{const e={},t=Pe.map((e=>Object.keys(e))).flat();for(const o of Be)if(t.includes(o)){const t=Pe.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)})),Le=c((()=>{const e={},t=Ae.map((e=>Object.keys(e))).flat();for(const o of Be)if(t.includes(o)){const t=Ae.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)})),We=c((()=>{const e={},t=qe.map((e=>Object.keys(e))).flat();for(const o of de)if(t.includes(o)){const t=qe.find((e=>Object.keys(e).includes(o)))[o];e[o]={name:o,link:t}}return Object.values(e)}));let Ye=o(!1),He=o(!1),Je=o(!1),Qe=o(!1),Xe=o(!1),Ze=o(!1),Ke=o(0);o("wait"),o("wait");const et=o(localStorage.getItem("releaseTypeFilter")||"bus"),tt=o([]);s((()=>{const e=localStorage.getItem("releaseTypeFilter");e&&(et.value=e)}));const ot=()=>{localStorage.setItem("releaseTypeFilter",et.value),st(),tt.value.length>0?at():(nt.value="",dt.splice(0,dt.length))},at=()=>{const e=new Date;e.setHours(0,0,0,0);let t=null,o=null;console.log("尝试选择最近发布单，过滤后项目数量:",tt.value.length),tt.value.forEach((a=>{const c=a.title||"";let s=c.match(/(bus|adhoc|hotfix)[_-](\d{6})/i);if(s||(s=c.match(/(bus|adhoc|hotfix)[_-](\d{8})/i)),s){const c=s[2];let l,i,h;if(6===c.length){const e=parseInt(c.slice(0,2));l=e<50?2e3+e:1900+e,i=parseInt(c.slice(2,4))-1,h=parseInt(c.slice(4,6))}else{if(8!==c.length)return void console.log(`无法识别的日期格式: ${c}`);l=parseInt(c.slice(0,4)),i=parseInt(c.slice(4,6))-1,h=parseInt(c.slice(6,8))}try{const c=new Date(l,i,h,0,0,0);isNaN(c.getTime())?console.log(`日期无效: ${l}-${i+1}-${h}`):(console.log(`解析发布单 ${a.title}: ${l}-${i+1}-${h}, date=${c}, 今天=${e}`),c>=e&&(!t||c<t)&&(t=c,o=a,console.log(`找到更近的发布单: ${a.title}, 日期: ${c}`)))}catch(n){console.log(`日期解析错误: ${n.message}`)}}else console.log(`无法解析日期格式: ${a.title}`)})),o?(nt.value=o.title,console.log("自动选择最近发布单:",nt.value)):console.log("未找到合适的发布单自动选择")},ct=o(localStorage.getItem("projectTypeFilter")||"SPCB"),st=()=>{console.log("过滤项目列表，当前过滤类型:",et.value,"项目类型:",ct.value),pt.length&&(tt.value=pt.filter((e=>{const t="All"===et.value||e.title?.toLowerCase().includes(et.value.toLowerCase()),o=e.key&&e.key.startsWith(ct.value);return t&&o})),console.log("过滤后项目数量:",tt.value.length),0===tt.value.length&&(nt.value="",dt.splice(0,dt.length)))},nt=o(),lt=o(!1),it=(e,t)=>t.type===e,ht=o(),pt=a([]);let dt=a([]);const rt=(e,t)=>{let o={jira_title:nt.value};B(o),V({message:"已进行checklist消息push，请耐心等待seatalk自动发送消息。",type:"success",duration:5e3})},mt=(e,t)=>{let o={jira_title:nt.value};F(o),V({message:"已进行Signed off消息push，请耐心等待seatalk自动发送消息。",type:"success",duration:5e3})},bt=(e,t)=>{let o={jira_title:nt.value};P(o),V({message:"已进行MR消息push，请耐心等待seatalk自动发送消息。",type:"success",duration:5e3})};async function _t(e){console.log(dt);const t=new Set(dt.map((e=>e.jira_key)));Ve.splice(0,Ve.length),Be.splice(0,Be.length),de.splice(0,de.length),console.log(`get data for ${e}`);let o,a={title:e};o=await ee(a),0===o.length?dt.splice(0,dt.length):(dt.splice(0,dt.length),o.data.forEach((e=>{console.log(e),t.has(e.jira_key)||(dt.push(e),t.add(e.jira_key))}))),console.log(dt)}async function gt(e){Xe.value=!1,Qe.value=!1,console.log(e),await _t(e),lt.value=!0,console.log(dt),await async function(e){Xe.value=!1,Qe.value=!1,console.log(e),await _t(e),lt.value=!0;let t=o();console.log(dt),t.value=await U(dt),lt.value=!1,console.log(t);const a=dt.length;t.value.data.slice(0,a).forEach(((e,t)=>{console.log(e.result_all),e.signoff_status?dt[t].sign_off=e.signoff_status:dt[t].sign_off="",e.config_center?dt[t].config_center=e.config_center:dt[t].config_center="",e.Code_Merged?dt[t].Code_Merged=e.Code_Merged:dt[t].Code_Merged="",e.shopee_region?dt[t].region=e.shopee_region:dt[t].region="",e.redis_check?dt[t].redis_change=e.redis_check:dt[t].redis_change="",e.DB_Change?dt[t].DB_Change=e.DB_Change:dt[t].DB_Change="",e.result?dt[t].result=e.result:dt[t].result="",e.merge_list?dt[t].merge_list=e.merge_list:dt[t].merge_list="",e.status?dt[t].status=e.status:dt[t].status="",e.dev_pic?dt[t].dev_pic=e.dev_pic:dt[t].dev_pic="",e.PM?dt[t].PM=e.PM:dt[t].PM="",e.qa_pic?dt[t].qa_pic=e.qa_pic:dt[t].qa_pic="",console.log(e.redis_check),console.log(dt),dt[t].services="",e.services_list.services_list_be.forEach((e=>{""===dt[t].services?dt[t].services+=`${e}`:dt[t].services+=`\n${e}`,Be.includes(e)||(Be.push(e),Ve.push(e))})),e.services_list.services_list_fe.forEach((e=>{""===dt[t].services?dt[t].services+=`${e}`:dt[t].services+=`\n${e}`,de.includes(e)||(de.push(e),Ve.push(e))}))})),0!==de.length&&(Qe.value=!0);0!==Be.length&&(Xe.value=!0);console.log(de),console.log(Be),Ze.value=!0}(e),lt.value=!1,V({message:"已更新状态",type:"success",duration:5e3})}function ut(e){return"TO DO"===e?"#42526e":"Done"===e?"#00875a":"Waiting"===e?"#42526e":"#0052CC"}function ft(e){return"TO DO"===e?"TO DO":"Done"===e?"DONE":"Waiting"===e?"WAITING":"Icebox"===e?"ICEBOX":"Doing"===e?"DOING":"UAT"===e?"UAT":"Delivering"===e?"DELIVERING":"Developing"===e?"DEVELOPING":"Testing"===e?"TESTING":e}s((async()=>{const e=localStorage.getItem("releaseTypeFilter");e&&(et.value=e);const t=localStorage.getItem("projectTypeFilter");t&&(ct.value=t);const o=(await n()).data.data;ht.value=o,o.forEach((e=>{pt.push(e)})),st();const a=localStorage.getItem("selectedProject");a?(console.log("从localStorage恢复选中项目:",a),nt.value=a):at();const c=localStorage.getItem("active");if(c&&(Ke.value=parseInt(c)),document.body.innerHTML.trim().length>0){const e=document.querySelector(".el-empty");e&&(e.style.display="none")}}));const yt=a({name:"",merge:!0});l(nt,((e,t)=>{e&&e!==t&&(console.log(`选择项目变化: ${t} -> ${e}, 重新加载数据`),dt.splice(0,dt.length),async function(e){if(!e)return console.log("没有选择发布单，不进行数据查询"),void dt.splice(0,dt.length);Xe.value=!1,Qe.value=!1,console.log(`获取发布单数据: ${e}`),await _t(e),lt.value=!0;let t=o();if(console.log("releaseTableData前:",dt),t.value=await G(dt),console.log("API响应:",t.value),!t.value||!t.value.data)return console.log("API返回数据为空或格式不正确"),void(lt.value=!1);lt.value=!1,dt.length,dt.forEach((e=>{const o=t.value.data.find((t=>e.jira_key===t.feature_key));o&&(console.log("匹配项数据:",o),e.bug_resolved=Number(o.bug_resolved||0),e.bug_total=Number(o.bug_total||0),console.log(`Bug数据: ${e.bug_resolved}/${e.bug_total}`),e.type=o.type,e.jira_key=o.feature_key,e.jira_link=`https://jira.shopee.io/browse/${o.feature_key}`,e.jira_title=o.feature_title,o.signoff_status?e.sign_off=o.signoff_status:e.sign_off="",o.config_center?e.config_center=o.config_center:e.config_center="",o.shopee_region?e.region=o.shopee_region:e.region="",o.redis_check?e.redis_change=o.redis_check:e.redis_change="",o.result?e.result=o.result:e.result="",o.merge_list?e.merge_list=o.merge_list:e.merge_list="",o.status?e.status=o.status:e.status="",o.Code_Merged?e.Code_Merged=o.Code_Merged:e.Code_Merged="",o.DB_Change?e.DB_Change=o.DB_Change:e.DB_Change="",o.dev_pic?e.dev_pic=o.dev_pic:e.dev_pic="",o.PM?e.PM=o.PM:e.PM="",o.qa_pic?e.qa_pic=o.qa_pic:e.qa_pic="",e.services="",o.services_list.services_list_be.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,Be.includes(t)||(Be.push(t),Ve.push(t))})),o.services_list.services_list_fe.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,de.includes(t)||(de.push(t),Ve.push(t))})))})),0!==de.length&&(Qe.value=!0),0!==Be.length&&(Xe.value=!0),console.log("fe_services:",de),console.log("be_services:",Be),console.log("最终表格数据:",dt),Ze.value=!0}(e)),e&&localStorage.setItem("selectedProject",e),yt.name=function(e){if(e){let t=e.replace(/【Release】|发布单/g,"").replace(/\s/g,"");return console.log(t),t}return""}(e)}),{flush:"post"}),l(dt,((e,t)=>{localStorage.setItem("releaseTableData",JSON.stringify(dt)),0===dt.length&&(Je.value=!1),0!==dt.length&&(Je.value=!0)})),l(ct,(e=>{localStorage.setItem("projectTypeFilter",e),st(),tt.value.length>0?at():(nt.value="",dt.splice(0,dt.length))})),l(Ve,((e,t)=>{0===Ve.length&&(Ye.value=!1),0!==Ve.length&&(Ye.value=!0)})),l(et,(e=>{localStorage.setItem("releaseTypeFilter",et.value),st(),tt.value.length>0?at():(nt.value="",dt.splice(0,dt.length)),nt.value&&!tt.value.some((e=>e.title===nt.value))&&(nt.value="",dt.splice(0,dt.length))}));const vt=o([]),kt=o([]),wt=o([]),xt=o([]),Ct=e=>{vt.value=[],vt.value=e,console.log(jt.value),console.log(vt.value)},jt=c((()=>vt.value.map(((e,t)=>({...e,id:t+1}))))),Mt=e=>{kt.value=[],kt.value=e};c((()=>kt.value.map(((e,t)=>({...e,id:t+1})))));const Tt=e=>{wt.value=[],wt.value=e};c((()=>wt.value.map(((e,t)=>({...e,id:t+1})))));const Dt=e=>{xt.value=[],xt.value=e};c((()=>xt.value.map(((e,t)=>({...e,id:t+1})))));let It=a([]);const St=()=>{t.value=!1,Z.value=!0;const e=vt.value.concat(kt.value,wt.value,xt.value),o=Array.from(new Set(e)).map((e=>e.name));console.log(o);const a={services_list:o,title:yt.name,if_merge:yt.merge};console.log(a),L(a).then((e=>{console.log(e.repo);for(let t in e.repo)console.log(t),console.log(e.repo[t]),It.push({repo:e.repo[t].repo,url:e.repo[t].url,status:e.repo[t].status})})),Z.value=!1,console.log(It)};function zt(e){if(de.includes(e)){return qe.map((e=>Object.keys(e))).flat().includes(e)}if(Be.includes(e)){const t=Fe.map((e=>Object.keys(e))).flat(),o=Pe.map((e=>Object.keys(e))).flat(),a=Ae.map((e=>Object.keys(e))).flat();return t.includes(e)||o.includes(e)||a.includes(e)}return!1}0!==de.length&&(Qe.value=!0),0!==Be.length&&(Xe.value=!0);const Et=[];return Ve.forEach((e=>{zt(e)||Et.push(e)})),console.log("未正确分组的服务:",Et),console.log(de),console.log(Be),(e,a)=>{const c=te,s=se,n=ne,l=W,B=he,F=Y,P=pe,U=i("SuccessFilled"),G=le,L=H,ee=J,de=Q,Be=X,Ve=ie;return h(),p("div",re,[d(R,{name:"el-fade-in-linear"},{default:r((()=>[d(m(oe),{class:"card",shadow:"hover",width:"50px",height:"50px"},{default:r((()=>[b("div",me,[d(c,{projectType:ct.value,"onUpdate:projectType":a[0]||(a[0]=e=>ct.value=e)},null,8,["projectType"]),d(n,{modelValue:et.value,"onUpdate:modelValue":a[1]||(a[1]=e=>et.value=e),class:"release-type-filter",onChange:ot},{default:r((()=>[d(s,{label:"bus"},{default:r((()=>[_("Bus")])),_:1}),d(s,{label:"adhoc"},{default:r((()=>[_("Adhoc")])),_:1}),d(s,{label:"hotfix"},{default:r((()=>[_("Hotfix")])),_:1}),d(s,{label:"All"},{default:r((()=>[_("全部")])),_:1})])),_:1},8,["modelValue"]),d(m(g),{modelValue:nt.value,"onUpdate:modelValue":a[2]||(a[2]=e=>nt.value=e),filterable:"",clearable:"",placeholder:"请选择发布单",style:{width:"380px","margin-left":"10px"}},{default:r((()=>[(h(!0),p(u,null,f(tt.value,(e=>(h(),y(m(v),{key:e.title,label:e.title,value:e.title},{default:r((()=>[b("span",be,[d(B,{href:"https://jira.shopee.io/browse/"+e.key,target:"_blank",underline:!1},{default:r((()=>[d(l,{class:"box-item",effect:"customized",content:"点击跳转到JIRA",placement:"left-start"},{default:r((()=>[d(m(k),{type:"danger"},{default:r((()=>[_(w(e.key),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["href"])]),d(l,{class:"box-item",effect:"customized",content:"点击拉取数据并且展示发布详情",placement:"right-start"},{default:r((()=>[b("span",_e,w(e.title),1)])),_:2},1024)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),d(l,{class:"box-item",effect:"customized",content:"点击进行后台数据更新",placement:"top-start"},{default:r((()=>[x((h(),y(m(C),{size:"default",type:"success",onClick:a[3]||(a[3]=e=>gt(nt.value)),icon:m(j),"element-loading-text":"AR正在进行后台数据更新，请耐心等待"},{default:r((()=>[_("更新 ")])),_:1},8,["icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1}),d(l,{class:"box-item",effect:"customized",content:"点击发送Signed off字段提醒到seatalk",placement:"top-start"},{default:r((()=>[d(m(C),{type:"primary",size:"default",onClick:mt,icon:m(M)},{default:r((()=>[_(" Signed off提醒 ")])),_:1},8,["icon"])])),_:1}),d(l,{class:"box-item",effect:"customized",content:"点击发送MR提醒到seatalk",placement:"top-start"},{default:r((()=>[d(m(C),{type:"primary",size:"default",onClick:bt,icon:m(M)},{default:r((()=>[_(" MR提醒 ")])),_:1},8,["icon"])])),_:1}),d(l,{class:"box-item",effect:"customized",content:"点击发送checklist字段提醒到seatalk",placement:"top-start"},{default:r((()=>[d(m(C),{type:"primary",size:"default",onClick:rt,icon:m(M)},{default:r((()=>[_(" Checklist提醒 ")])),_:1},8,["icon"])])),_:1}),d(l,{class:"box-item",effect:"customized",content:"点击跳转monitor看板",placement:"top-start"},{default:r((()=>[d(m(C),{type:"danger",size:"default",onClick:Ne,icon:m(M)},{default:r((()=>[_(" monitor看板 ")])),_:1},8,["icon"])])),_:1}),d(m(T),{modelValue:m(He),"onUpdate:modelValue":a[4]||(a[4]=e=>D(He)?He.value=e:He=e),"show-close":!1},{header:r((({titleId:e,titleClass:t})=>[b("h4",{id:e,class:I(t)},w(nt.value),11,ge)])),default:r((()=>[_(" 暂无失败信息。 ")])),_:1},8,["modelValue"]),b("div",ue,[d(F,{size:20},{default:r((()=>[d(m(S))])),_:1}),d(P,{class:"mx-1",type:"warning",size:"default"},{default:r((()=>[_("后台会不断更新数据，请自行刷新页面")])),_:1})])]),b("div",fe,[d(m(ae),{data:m(dt),stripe:"",border:"","highlight-current-row":"",fit:"","header-cell-style":{background:"#cacfd7",color:"#606266"},"empty-text":"暂无数据"},{default:r((()=>[d(m(ce),{label:"编号","min-width":"21","header-align":"center",align:"center"},{default:r((e=>[_(w(e.$index+1),1)])),_:1}),d(m(ce),{prop:"type",label:"类型","header-align":"center",align:"center","min-width":"30",filters:[{text:"Epic",value:"Epic"},{text:"Bug",value:"Bug"},{text:"Task",value:"Task"},{text:"Sub-task",value:"Sub-task"},{text:"Story",value:"Story"}],"filter-method":it,"filter-placement":"bottom-end"},{default:r((({row:e})=>{return[d(F,{class:I((t=e.type,"Epic"===t?"Epic-icon":"Sub-task"===t?"ST-icon":"Task"===t?"Task-icon":"Bug"===t?"Bug-icon":"Story"===t?"Story-icon":void 0))},null,8,["class"])];var t})),_:1}),d(m(ce),{prop:"jira_key",label:"单号","min-width":60,"header-align":"center",align:"center"},{default:r((({row:e})=>[d(B,{underline:!1,href:e.jira_link,target:"_blank",type:"primary"},{default:r((()=>[_(w(e.jira_key),1)])),_:2},1032,["href"])])),_:1}),d(m(ce),{prop:"jira_title",label:"需求名","min-width":150},{default:r((({row:e})=>[d(B,{underline:!1,href:e.jira_link,target:"_blank",type:"primary"},{default:r((()=>[_(w(e.jira_title),1)])),_:2},1032,["href"])])),_:1}),d(m(ce),{prop:"bug_resolution_rate",label:"Bug解决率","min-width":80,"header-align":"center",align:"center"},{default:r((({row:e})=>[b("span",{style:z({color:Number(e.bug_resolved||0)===Number(e.bug_total||0)?"#67C23A":"#F56C6C"})},["number"==typeof e.bug_resolved&&"number"==typeof e.bug_total?(h(),p(u,{key:0},[_(w(e.bug_resolved)+"/"+w(e.bug_total),1)],64)):(h(),p(u,{key:1},[_(w(e.bug_resolved||0)+"/"+w(e.bug_total||0),1)],64))],4)])),_:1}),d(m(ce),{label:"周一","header-align":"center",align:"center"},{default:r((()=>[d(m(ce),{prop:"sign_off",label:"Signed off","header-align":"center",align:"center","min-width":"40"},{header:r((()=>[_(" Signed"),ye,_("off ")])),default:r((({row:e})=>["Confirmed"===e.sign_off?(h(),y(F,{key:0,size:20,color:"Confirmed"===e.sign_off?"#67c23a":"#F56C67"},{default:r((()=>[d(U)])),_:2},1032,["color"])):E("",!0),""===e.sign_off?(h(),y(F,{key:1,size:20,color:"pass"===e.sign_off?"#67c23a":"#F56C67"},{default:r((()=>[d(m($))])),_:2},1032,["color"])):E("",!0)])),_:1})])),_:1}),d(m(ce),{label:"周二","header-align":"center",align:"center"},{default:r((()=>[d(m(ce),{type:"expand",label:"提MR","min-width":"32"},{default:r((e=>[b("div",null,[b("div",ve,[d(l,{class:"box-item",effect:"customized",content:"点击创建MR",placement:"top-start"},{default:r((()=>[x((h(),y(m(C),{type:"danger",onClick:a[5]||(a[5]=e=>K.value=!0),size:"small",icon:m(O),"element-loading-text":"AR正在创建MR, 请耐心等待..."},{default:r((()=>[_("创建 ")])),_:1},8,["icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1}),d(l,{class:"box-item",effect:"customized",content:"点击复制",placement:"top-start"},{default:r((()=>[x((h(),y(m(C),{type:"primary",size:"small",onClick:t=>async function(e){let t=o();nt.value,t=await q(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{V({message:"恭喜，MR信息已复制到剪切板！",type:"success"})})).catch((e=>{V.error("复制剪切板失败！")}))}(e),icon:m(M),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:r((()=>[_("复制 ")])),_:2},1032,["onClick","icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024),d(l,{class:"box-item",effect:"customized",content:"点击发送MR提醒到seatalk",placement:"top-start"},{default:r((()=>[x((h(),y(m(C),{type:"primary",size:"small",onClick:t=>async function(e){let t=o();t=await A(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{V({message:"恭喜，MR信息已发送到seatalk！",type:"success"})})).catch((e=>{V.error("MR信息发送失败！")}))}(e),icon:m(M),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:r((()=>[_("发送 ")])),_:2},1032,["onClick","icon"])),[[Ve,lt.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024)]),d(G,{modelValue:K.value,"onUpdate:modelValue":a[7]||(a[7]=e=>K.value=e),title:"Warning",width:"30%","align-center":""},{footer:r((()=>[b("span",ke,[d(m(C),{onClick:a[6]||(a[6]=e=>K.value=!1)},{default:r((()=>[_("取消")])),_:1}),d(m(C),{type:"primary",onClick:t=>async function(e){console.log(e.row),o(),await N(e.row)}(e)},{default:r((()=>[_(" 确认 ")])),_:2},1032,["onClick"])])])),default:r((()=>[b("span",null,"请确认是否开始自动提MR？发布单： "+w(nt.value),1)])),_:2},1032,["modelValue"]),d(m(ae),{data:e.row.merge_list,border:"","header-cell-style":{background:"#def1ce",color:"#606266"}},{default:r((()=>[d(m(ce),{label:"仓库",prop:"repo_name"}),d(m(ce),{label:"分支",prop:"branch_name"}),d(m(ce),{label:"PIC",prop:"pic"}),d(m(ce),{label:"MR地址",prop:"web_url"},{default:r((({row:e})=>[b("a",{href:e.web_url,target:"_blank"},w(e.web_url),9,we)])),_:1}),d(m(ce),{label:"MR状态",prop:"merge_status"}),d(m(ce),{label:"MR作者",prop:"author"})])),_:2},1032,["data"])])])),_:1}),d(m(ce),{prop:"Code_Merged",label:"Code Merged","header-align":"center",align:"center","min-width":"40"},{header:r((()=>[_(" Code"),xe,_("Merged ")])),default:r((({row:e})=>["Confirmed"===e.Code_Merged?(h(),y(F,{key:0,size:20,color:"Confirmed"===e.Code_Merged?"#67c23a":"#F56C67"},{default:r((()=>[d(U)])),_:2},1032,["color"])):E("",!0),""===e.Code_Merged?(h(),y(F,{key:1,size:20,color:"pass"===e.Code_Merged?"#67c23a":"#F56C67"},{default:r((()=>[d(m($))])),_:2},1032,["color"])):E("",!0)])),_:1})])),_:1}),d(m(ce),{label:"周三","header-align":"center",align:"center"},{default:r((()=>[d(m(ce),{prop:"config_center",label:"Config Changed","header-align":"center",align:"center","min-width":"43"},{header:r((()=>[_(" Config"),Ce,_("Changed ")])),default:r((({row:e})=>["Confirmed"===e.config_center?(h(),y(F,{key:0,size:20,color:"Confirmed"===e.config_center?"#67c23a":"#F56C67"},{default:r((()=>[d(U)])),_:2},1032,["color"])):E("",!0),""===e.config_center?(h(),y(F,{key:1,size:20,color:"pass"===e.config_center?"#67c23a":"#F56C67"},{default:r((()=>[d(m($))])),_:2},1032,["color"])):E("",!0)])),_:1}),d(m(ce),{prop:"DB_Change",label:"DB Changed","header-align":"center",align:"center","min-width":"43"},{default:r((({row:e})=>["Confirmed"===e.DB_Change?(h(),y(F,{key:0,size:20,color:"Confirmed"===e.DB_Change?"#67c23a":"#F56C67"},{default:r((()=>[d(U)])),_:2},1032,["color"])):E("",!0),""===e.DB_Change?(h(),y(F,{key:1,size:20,color:"pass"===e.DB_Change?"#67c23a":"#F56C67"},{default:r((()=>[d(m($))])),_:2},1032,["color"])):E("",!0)])),header:r((()=>[_(" DB"),je,_("Changed ")])),_:1}),d(m(ce),{prop:"services",label:"services","min-width":"180","header-align":"center",align:"center",style:{"white-space":"pre-wrap"}},{default:r((({row:e})=>[e.services&&""!==e.services?(h(),p("div",Me,[(h(!0),p(u,null,f(e.services.split("\n"),((e,t)=>(h(),p("div",{style:z({color:zt(e)?"inherit":"#F56C67"})},w(e),5)))),256))])):E("",!0),e.services&&""!==e.services?E("",!0):(h(),y(F,{key:1,size:20,color:"#F56C67"},{default:r((()=>[d(m($))])),_:1}))])),_:1}),d(m(ce),{prop:"region",label:"Region","header-align":"center",align:"center","min-width":"37"},{default:r((({row:e})=>[""!==e.region?(h(),y(P,{key:0,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:r((()=>[_(w(e.region),1)])),_:2},1032,["color"])):E("",!0),""===e.region?(h(),y(F,{key:1,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:r((()=>[d(m($))])),_:2},1032,["color"])):E("",!0)])),_:1})])),_:1}),d(m(ce),{prop:"PM",label:"PM","header-align":"center",align:"center","min-width":"50"},{default:r((({row:e})=>[_(w(e.PM),1)])),_:1}),d(m(ce),{prop:"dev_pic",label:"DEV PIC","header-align":"center",align:"center","min-width":"40"},{header:r((()=>[_(" DEV"),Te,_("PIC ")])),default:r((({row:e})=>[_(w(e.dev_pic),1)])),_:1}),d(m(ce),{prop:"qa_pic",label:"QA","header-align":"center",align:"center","min-width":"50"},{default:r((({row:e})=>[_(w(e.qa_pic),1)])),_:1}),d(m(ce),{prop:"status",label:"Status","header-align":"center",align:"center","min-width":"60"},{default:r((({row:e})=>{return[d(m(k),{class:"bold-text",effect:"dark",type:(t=e.status,"TO DO"===t?"info":"Done"===t?"success":"Waiting"===t?"info":"Icebox"===t?"icebox":"Doing"===t?"doing":"UAT"===t?"uat":"Delivering"===t?"delivering":"Developing"===t?"developing":"Testing"===t?"testing":void 0),color:ut(e.status)},{default:r((()=>[_(w(ft(e.status)),1)])),_:2},1032,["type","color"])];var t})),_:1})])),_:1},8,["data"])]),b("div",De,[d(P,{class:"mx-1",type:"danger"},{default:r((()=>[_("请勾选下面的服务，点击一键四连启动发布流程。前端4个仓库只支持打TAG，不支持合代码，请找bin.wang合代码，4个仓库为仓库为：")])),_:1}),d(B,{href:"https://git.garena.com/shopee/marketing/web-chatbot",underline:!1,type:"danger",target:"_blank"},{default:r((()=>[_("web-chatbot、")])),_:1}),d(B,{href:"https://git.garena.com/shopee/seller-fe/cs-chat",underline:!1,type:"danger",target:"_blank"},{default:r((()=>[_("cs-chat、")])),_:1}),d(B,{href:"https://git.garena.com/shopee/chatbot/web-chatbot-csat",underline:!1,type:"danger",target:"_blank"},{default:r((()=>[_("web-chatbot-csat、")])),_:1}),d(B,{href:"https://git.garena.com/shopee/chatbot/web-csat-rn",underline:!1,type:"danger",target:"_blank"},{default:r((()=>[_("web-csat-rn。")])),_:1})]),b("div",Ie,[d(m(ae),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e78181",color:"#f8f7f7"},data:Ue.value,style:{width:"100%"},onSelectionChange:Ct,"empty-text":"暂无数据"},{default:r((()=>[d(m(ce),{type:"selection",width:"55"}),d(m(ce),{"header-align":"center",label:"平台BE1组"},{default:r((e=>[d(B,{href:e.row.link,target:"_blank",underline:!1},{default:r((()=>[_(w(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),d(m(ae),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#819ee7",color:"#f8f7f7"},"header-align":"center",data:Ge.value,style:{width:"100%"},onSelectionChange:Mt,"empty-text":"暂无数据"},{default:r((()=>[d(m(ce),{type:"selection",width:"55"}),d(m(ce),{"header-align":"center",label:"平台BE2组"},{default:r((e=>[d(B,{href:e.row.link,target:"_blank",underline:!1},{default:r((()=>[_(w(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),d(m(ae),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#81e7c8",color:"#f8f7f7"},"header-align":"center",data:Le.value,style:{width:"100%"},onSelectionChange:Tt,"empty-text":"暂无数据"},{default:r((()=>[d(m(ce),{type:"selection",width:"55"}),d(m(ce),{"header-align":"center",label:"功能BE组"},{default:r((e=>[d(B,{href:e.row.link,target:"_blank",underline:!1},{default:r((()=>[_(w(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),d(m(ae),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e7a881",color:"#f8f7f7"},"header-align":"center",data:We.value,style:{width:"100%"},onSelectionChange:Dt,"empty-text":"暂无数据"},{default:r((()=>[d(m(ce),{type:"selection",width:"55"}),d(m(ce),{"header-align":"center",label:"FE组"},{default:r((e=>[d(B,{href:e.row.link,target:"_blank",underline:!1},{default:r((()=>[_(w(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"])])])),_:1})])),_:1}),d(m(oe),{class:"card",shadow:"hover",width:"50px",height:"50px"},{default:r((()=>[d(Be,{model:yt,"label-width":"auto"},{default:r((()=>[b("div",Se,[d(G,{modelValue:t.value,"onUpdate:modelValue":a[9]||(a[9]=e=>t.value=e),title:"live-发布确认",width:"30%","before-close":e.handleClose},{footer:r((()=>[b("span",Ee,[d(m(C),{onClick:a[8]||(a[8]=e=>t.value=!1)},{default:r((()=>[_("取消")])),_:1}),d(m(C),{type:"primary",onClick:St},{default:r((()=>[_(" 确认发布 ")])),_:1})])])),default:r((()=>[ze])),_:1},8,["modelValue","before-close"])]),b("div",$e,[d(ee,{label:"标题",style:{"max-width":"460px"}},{default:r((()=>[d(L,{modelValue:yt.name,"onUpdate:modelValue":a[10]||(a[10]=e=>yt.name=e)},null,8,["modelValue"])])),_:1}),d(m(k),{class:"ml-2",size:"large"},{default:r((()=>[_("标题规范：bus/adhoc/bugfix-YYYYMMDD。若当日多次同类型发布，在标题后面自增号码以区分，示例：adhoc-20230831-1、adhoc-20230831-2。")])),_:1})]),b("div",Oe,[d(ee,{label:"是否合代码"},{default:r((()=>[d(l,{class:"box-item",effect:"customized",content:"勾选则从master合代码到release，不勾选则默认只打TAG",placement:"top-start"},{default:r((()=>[d(de,{modelValue:yt.merge,"onUpdate:modelValue":a[11]||(a[11]=e=>yt.merge=e)},null,8,["modelValue"])])),_:1})])),_:1}),d(ee,null,{default:r((()=>[d(l,{class:"box-item",effect:"customized",content:"创建MR、合代码到release、打TAG、预编译",placement:"top-start"},{default:r((()=>[x((h(),y(m(C),{type:"success","element-loading-text":"正在处理，辛苦等待一下，中途请不要关闭此页面...",onClick:a[12]||(a[12]=e=>t.value=!0)},{default:r((()=>[_("一键四连")])),_:1})),[[Ve,Z.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1})])),_:1})])])),_:1},8,["model"]),d(m(ae),{data:m(It),border:"",style:{width:"100%"},"header-cell-style":{background:"#cacfd7",color:"#606266"},"empty-text":"暂无数据"},{default:r((()=>[d(m(ce),{prop:"repo",label:"仓库","header-align":"center",width:"180"}),d(m(ce),{prop:"url",label:"MR地址","header-align":"center",width:"500"},{default:r((({row:e})=>[b("a",{href:e.url,target:"_blank"},w(e.url),9,Re)])),_:1}),d(m(ce),{prop:"status",label:"状态","header-align":"center"})])),_:1},8,["data"])])),_:1})])}}}),[["__scopeId","data-v-341ca16e"]]);export{Be as default};
//# sourceMappingURL=autorelease-CouLDqUI.js.map

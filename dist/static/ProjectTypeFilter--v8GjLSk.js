import{H as e,p as a,T as t,o as p,C as o,w as s,v as r,x as l}from"./index-awKTxnvj.js";import{E as d,a as u}from"./directive-CeALrXM5.js";const c=e({__name:"ProjectTypeFilter",props:{projectType:{type:String,default:"SPCB"}},emits:["update:projectType"],setup(e,{emit:c}){const n=e,i=c,j=a(n.projectType),m=e=>{i("update:projectType",e)};return t((()=>n.projectType),(e=>{j.value=e})),(e,a)=>{const t=d,c=u;return p(),o(c,{modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),class:"project-type-filter",onChange:m},{default:s((()=>[r(t,{label:"SPCB"},{default:s((()=>[l("SPCB")])),_:1}),r(t,{label:"SPCT"},{default:s((()=>[l("SPCT")])),_:1})])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-2737ba1d"]]);export{c as _};
//# sourceMappingURL=ProjectTypeFilter--v8GjLSk.js.map

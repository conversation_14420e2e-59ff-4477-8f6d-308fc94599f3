System.register(["./index-legacy.C52nWfoy.js","./directive-legacy.xes9WOkH.js"],(function(e,t){"use strict";var a,r,l,p,u,o,c,s,d,n;return{setters:[e=>{a=e.H,r=e.p,l=e.T,p=e.o,u=e.C,o=e.w,c=e.v,s=e.x},e=>{d=e.E,n=e.a}],execute:function(){e("_",a({__name:"ProjectTypeFilter",props:{projectType:{type:String,default:"SPCB"}},emits:["update:projectType"],setup(e,{emit:t}){const a=e,i=t,y=r(a.projectType),j=e=>{i("update:projectType",e)};return l((()=>a.projectType),(e=>{y.value=e})),(e,t)=>{const a=d,r=n;return p(),u(r,{modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=e=>y.value=e),class:"project-type-filter",onChange:j},{default:o((()=>[c(a,{label:"SPCB"},{default:o((()=>[s("SPCB")])),_:1}),c(a,{label:"SPCT"},{default:o((()=>[s("SPCT")])),_:1})])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-2737ba1d"]]))}}}));
//# sourceMappingURL=ProjectTypeFilter-legacy.wfFkpZSQ.js.map

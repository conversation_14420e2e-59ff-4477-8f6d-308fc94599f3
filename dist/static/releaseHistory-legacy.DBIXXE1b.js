System.register(["./index-legacy.C52nWfoy.js","./ProjectTypeFilter-legacy.wfFkpZSQ.js","./index-legacy.CAqey3Xi.js","./directive-legacy.xes9WOkH.js","./index-legacy.Co5M3uHU.js","./index-legacy.x5ItpLKU.js","./index-legacy.CNmEMj-H.js"],(function(e,t){"use strict";var a,o,l,n,i,c,r,s,d,p,g,h,f,u,m,b,v,w,_,y,x,k,C,j,F,I,T,M,S,A,D,z,E,B,O,V,P,R,H,$,N,q,L,U,G,W,J,Q,X,K,Y,Z,ee,te,ae;return{setters:[e=>{a=e.G,o=e.H,l=e.e,n=e.p,i=e.J,c=e.L,r=e.a,s=e.al,d=e.as,p=e.T,g=e.ax,h=e.V,f=e.o,u=e.h,m=e.v,b=e.w,v=e.j,w=e.l,_=e.x,y=e.a5,x=e.F,k=e.r,C=e.C,j=e.a6,F=e.ae,I=e.af,T=e.n,M=e.t,S=e.ag,A=e.k,D=e.ah,z=e.ab,E=e.E,B=e.ai,O=e.ad,V=e.a4,P=e.aj,R=e.ao,H=e.ap,$=e.aq,N=e.aw,q=e.a9,L=e.au,U=e.P,G=e.Q},e=>{W=e._},e=>{J=e.E,Q=e.b,X=e.a},e=>{K=e.E,Y=e.a,Z=e.b,ee=e.v},e=>{te=e.E},e=>{ae=e.E},null],execute:function(){var t=document.createElement("style");function oe(){return a.get("https://autorelease.chatbot.shopee.io/api/get_jira_release_list_finished",{params:{project_type:"SPCB,SPCT",release_type:"Bus,Adhoc,Hotfix"}}).then((function(e){return e})).catch((function(e){console.log(e)}))}t.textContent="a[data-v-d8982ff0]{text-decoration:none}.index-container[data-v-d8982ff0]{height:100%;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}div[data-v-d8982ff0]{font-size:12px;margin:5px;border:1px;padding:0}.el-table .warning-row{--el-table-tr-bg-color: var(--el-color-warning-light-9)}.el-table .success-row{--el-table-tr-bg-color: var(--el-color-success-light-9)}.el-table .cell{white-space:pre-wrap!important}.table-header{background-color:#00f;color:#fff}.n-gradient-text[data-v-d8982ff0]{font-size:24px}.scrollbar-fe-item[data-v-d8982ff0]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;height:30px;margin:10px;text-align:center;border-radius:4px;background:var(--el-color-success-light-9);color:var(--el-color-success)}.scrollbar-be-item[data-v-d8982ff0]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;height:30px;margin:10px;text-align:center;border-radius:4px;background:var(--el-color-primary-light-9);color:var(--el-color-primary)}.ml-2[data-v-d8982ff0]{margin:10px}.data-list[data-v-d8982ff0]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between}.data-item[data-v-d8982ff0]{width:calc(33.33% - 5px);margin-bottom:20px;border-radius:5px;padding:10px;background-color:#f5f5f5}.data-item__title[data-v-d8982ff0]{font-weight:700;color:#333;margin-bottom:5px}.data-item__content[data-v-d8982ff0]{color:#666}.itxst[data-v-d8982ff0]{width:600px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.itxst>div[data-v-d8982ff0]:nth-of-type(1){-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.itxst>div[data-v-d8982ff0]:nth-of-type(2){width:270px;padding-left:20px}.item[data-v-d8982ff0]{border:solid 1px #eee;padding:6px 10px;text-align:left}.item[data-v-d8982ff0]:hover{cursor:move}.item+.item[data-v-d8982ff0]{margin-top:10px}.ghost[data-v-d8982ff0]{border:solid 1px rgb(19,41,239)}.chosenClass[data-v-d8982ff0]{background-color:#f1f1f1}.el-popper.is-customized{padding:6px 12px;background:-webkit-linear-gradient(left,rgb(159,229,151),rgb(204,229,129));background:linear-gradient(90deg,#9fe597,#cce581)}.el-popper.is-customized .el-popper__arrow:before{background:-webkit-linear-gradient(45deg,#b2e68d,#bce689);background:linear-gradient(45deg,#b2e68d,#bce689);right:0}.el-card+.el-card{margin-top:20px}.ar-container{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.Epic-icon{background-image:url(\"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20xmlns='http://www.w3.org/2000/svg'%20viewbox='0%200%2016%2016'%3e%3cg%20fill='none'%20fill-rule='evenodd'%3e%3cpath%20d='M0%202.002A2%202%200%200%201%202.002%200h11.996A2%202%200%200%201%2016%202.002v11.996A2%202%200%200%201%2013.998%2016H2.002A2%202%200%200%201%200%2013.998V2.002z'%20fill='%236554C0'/%3e%3cpath%20d='M11.898%207.666a.678.678%200%200%200-.564-1.04L8%206.624V3.187a.678.678%200%200%200-.666-.687.65.65%200%200%200-.54.307.693.693%200%200%200-.088.178l-2.598%205.34A.738.738%200%200%200%204%208.689c0%20.38.3.687.667.687H8v3.438c0%20.38.3.687.667.687a.655.655%200%200%200%20.557-.331l.022-.035c.014-.029.03-.055.041-.085l2.61-5.383z'%20fill='%23FFF'%20fill-rule='nonzero'/%3e%3c/g%3e%3c/svg%3e\");width:16px!important;height:16px!important}.ST-icon{background-image:url(\"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20xmlns='http://www.w3.org/2000/svg'%20viewbox='0%200%2016%2016'%3e%3cg%20fill='none'%20fill-rule='evenodd'%3e%3cpath%20d='M0%201.777C0%20.796.796%200%201.777%200h12.446C15.204%200%2016%20.796%2016%201.777v12.446c0%20.981-.796%201.777-1.777%201.777H1.777A1.778%201.778%200%200%201%200%2014.223V1.777z'%20fill='%232684FF'/%3e%3cpath%20d='M7.933%207h4.134c.515%200%20.933.418.933.933v4.134a.933.933%200%200%201-.933.933H7.933A.933.933%200%200%201%207%2012.067V7.933C7%207.418%207.418%207%207.933%207z'%20fill='%23FFF'/%3e%3cpath%20d='M4.5%204.5v3h3v-3h-3zM3.933%203h4.134c.515%200%20.933.418.933.933v4.134A.933.933%200%200%201%208.067%209H3.933A.933.933%200%200%201%203%208.067V3.933C3%203.418%203.418%203%203.933%203z'%20fill='%23FFF'%20fill-rule='nonzero'/%3e%3c/g%3e%3c/svg%3e\");width:16px!important;height:16px!important}.Bug-icon{background-image:url(\"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20xmlns='http://www.w3.org/2000/svg'%20viewbox='0%200%2016%2016'%3e%3cg%20fill='none'%20fill-rule='evenodd'%3e%3cpath%20d='M0%201.777C0%20.796.796%200%201.777%200h12.446C15.204%200%2016%20.796%2016%201.777v12.446c0%20.981-.796%201.777-1.777%201.777H1.777A1.778%201.778%200%200%201%200%2014.223V1.777z'%20fill='%23FF5630'/%3e%3ccircle%20fill='%23FFF'%20cx='8'%20cy='8'%20r='4'/%3e%3c/g%3e%3c/svg%3e\");width:16px!important;height:16px!important}.Story-icon{background-image:url(\"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20xmlns='http://www.w3.org/2000/svg'%20viewbox='0%200%2016%2016'%3e%3cg%20fill='none'%20fill-rule='evenodd'%3e%3cpath%20d='M0%201.777C0%20.796.796%200%201.777%200h12.446C15.204%200%2016%20.796%2016%201.777v12.446c0%20.981-.796%201.777-1.777%201.777H1.777A1.778%201.778%200%200%201%200%2014.223V1.777z'%20fill='%2336B37E'/%3e%3cpath%20d='M4.5%2012.5V4.378c0-.485.392-.878.875-.878h5.25c.483%200%20.875.393.875.878V12.5L8%208.988%204.5%2012.5z'%20fill='%23FFF'/%3e%3c/g%3e%3c/svg%3e\");width:16px!important;height:16px!important}.Task-icon{background-image:url(\"data:image/svg+xml,%3csvg%20width='16'%20height='16'%20xmlns='http://www.w3.org/2000/svg'%20viewbox='0%200%2016%2016'%3e%3cg%20fill='none'%20fill-rule='evenodd'%3e%3cpath%20d='M0%201.777C0%20.796.796%200%201.777%200h12.446C15.204%200%2016%20.796%2016%201.777v12.446c0%20.981-.796%201.777-1.777%201.777H1.777A1.778%201.778%200%200%201%200%2014.223V1.777z'%20stroke-linecap='round'%20stroke-linejoin='round'%20fill='%232684FF'/%3e%3cpath%20d='M6.5%2010.086l5.793-5.793a1%201%200%200%201%201.414%201.414l-6.5%206.5a1%201%200%200%201-1.414%200l-3.5-3.5a1%201%200%200%201%201.414-1.414L6.5%2010.086z'%20fill='%23FFF'%20fill-rule='nonzero'/%3e%3c/g%3e%3c/svg%3e\");width:16px!important;height:16px!important}.to-do-text{background-color:#42526e;border-color:#42526e;color:#fff}.done-text{background-color:#00875a;border-color:#00875a;color:#fff}.doing-text,.delivering-text,.developing-text{background-color:#0052cc;border-color:#0052cc;color:#fff}.waiting-text{background-color:#42526e;border-color:#42526e;color:#fff}.bold-text{font-weight:700}.el-radio-group[data-v-d8982ff0]{margin-bottom:10px}.release-type-filter[data-v-d8982ff0]{height:32px}.release-type-filter[data-v-d8982ff0] .el-radio-button__inner{height:32px;line-height:32px;padding:0 15px}@media (min-width: 768px){.ar-container[data-v-d8982ff0]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;gap:10px}.el-radio-group[data-v-d8982ff0]{margin-bottom:0}}\n",document.head.appendChild(t),a.defaults.timeout=5e4,a.interceptors.request.use((e=>e),(e=>Promise.error(e)));const le=e=>(U("data-v-d8982ff0"),e=e(),G(),e),ne={class:"index-conntainer"},ie={class:"ar-container"},ce=["id"],re={class:"ar-container"},se=le((()=>w("br",null,null,-1))),de={class:"ar-container"},pe={class:"dialog-footer"},ge=["href"],he=le((()=>w("br",null,null,-1))),fe=le((()=>w("br",null,null,-1))),ue=le((()=>w("br",null,null,-1))),me={key:0},be=le((()=>w("br",null,null,-1))),ve={style:{display:"flex"}},we=l({__name:"releaseHistory",setup(e){n(!1);const t=n();n(!1),n(!1);const a=n(!1);n("first");const o=i([]),l=i([]),U=i([]),G=i(["shopee-chatbot-intent","shopee-chatbot-admin","shopee-chatbot-adminasynctask","shopee-chatbot-adminconfigservice","shopee-chatbot-adminservice","shopee-chatbot-agentcontrol","shopee-chatbot-asynctask","shopee-chatbot-auditlog","shopee-chatbot-botapi","shopee-chatbot-context","shopee-chatbot-dm","shopee-chatbot-featurecenter","shopee-chatbot-intentclarification","shopee-chatbot-messageasynctask","shopee-chatbot-messageservice","shopee-chatbot-messageverification","shopee-chatbot-nlu","shopee-chatbot-ordercard","shopee-chatbot-pilotapi","shopee-chatbotcommon-adminasynctask","shopee-chatbotcommon-adminconfigservice","shopee-chatbotcommon-adminservice","shopee-chatbotcommon-agentcontrol","shopee-chatbotcommon-asynctask","shopee-chatbotcommon-botapi","shopee-chatbotcommon-context","shopee-chatbotcommon-dm","shopee-chatbotcommon-featurecenter","shopee-chatbotcommon-nlu","shopee-chatbotcommon-productrecommend","shopee-chatbotcommon-rulebaseservice","shopee-chatbotcommon-shopconsole","shopee-chatbot-websocketgwy","shopee-chatbotcommon-logic","shopee-chatbotcommon-msgdetection"]),le=i(["shopee-chatbot-autotraining","shopee-annotation-admin","shopee-annotation-asynctask","shopee-agorithmservice-component","shopee-chatbot-experimentmanagement","shopee-chatbot-featureapiproxy","shopee-chatbot-modelgw","shopee-chatbot-realtime","shopee-chatbot-recallmanager","shopee-chatbot-recallservice","shopee-chatbot-recommendation","shopee-chatbotcommon-apadmin","shopee-chatbotcommon-apasynctask","shopee-chatbotcommon-component","shopee-chatbotcommon-experimentmanagement","shopee-chatbotcommon-featureapiproxy","shopee-chatbotcommon-intent","shopee-chatbotcommon-kbadmin","shopee-chatbotcommon-kbapi","shopee-chatbotcommon-kbasynctask","shopee-chatbotcommon-kblabelclarification","shopee-chatbotcommon-modelgw","shopee-knowledgebase-admin","shopee-knowledgebase-api","shopee-knowledgebase-asynctask","shopee-knowledgebase-labelclarification","shopee-chatbotcommon-promptmanagements","shopee-knowledgeplatform-offline","shopee-knowledgeplatform-admin","shopee-knowledgeplatform-api","shopee-knowledgeplatform-qa_tools"]),we=i(["shopee-chatbot-api","shopee-chatbot-autotraining","shopee-chatbotcommon-tfapiproxy","shopee-chatbotcommon-tfeditor","shopee-chatbotcommon-tfserving","shopee-chatbotcommon-tfvariateserving","shopee-taskflow-apiproxy","shopee-taskflow-editor","shopee-taskflow-taskflowserving","shopee-taskflow-taskflowsop","shopee-taskflow-variateserving"]),_e=i(["shopee-autotrainingportal-adminstatic","shopee-annotation-adminstatic","shopee-cbrcmdplt-rcmdpltstatic","shopee-chatbot-adminstatic","shopee-chatbot-chatbotcsatstatic","shopee-chatbot-chatbotrnstatic","shopee-chatbot-chatbotstatic","shopee-chatbot-csatstatic","shopee-chatbot-dashboardstatic","shopee-chatbot-tmcstatic","shopee-chatbotcommon-admincommonsaasstatic","shopee-chatbotcommon-adminsaasstatic","shopee-chatbotcommon-adminstatic","shopee-chatbotcommon-annotationadminstatic","shopee-chatbotcommon-apadminsaasstatic","shopee-chatbotcommon-csatstatic","shopee-chatbotcommon-kbadmincommonsaasstatic","shopee-chatbotcommon-kbadminsaasstatic","shopee-chatbotcommon-shopconsolestatic","shopee-chatbotcommon-static","shopee-chatbotcommon-tfeadmincommonsaasstatic","shopee-chatbotcommon-tfeadminsaasstatic","shopee-chatbotcommon-tmcsaasstatic","shopee-gec-gecstatic","shopee-knowledgebase-adminstatic","shopee-taskflow-adminstatic","shopee-cschat-h5","shopee-knowledgeplatform-adminstatic","shopee-knowledgeplatform-guidesstatic","shopee-knowledgeplatformnode-knowledgeplatformnode","shopee-chatbot-insights","shopee-chatbotcommon-insightssaasstatic-test","shopee-chatbot-mmfchatbotconsole"]);function ye(e){return o.includes(e)?_e.includes(e):!!l.includes(e)&&(G.includes(e)||le.includes(e)||we.includes(e))}n([]),c((async()=>{try{const e=await oe();if(console.log("API返回数据:",e),!e||!e.data||!e.data.data)return void console.error("API返回数据格式不正确:",e);const a=e.data.data;if(console.log("解析后的发布单数据:",a),t.value&&(Array.isArray(a)&&a.includes(t.value)||(console.log("之前选择的发布单不在当前列表中，清空选择"),t.value="")),!t.value){const e=new Date;e.setHours(0,0,0,0);let o=null,l=Infinity;Array.isArray(a)&&a.forEach((a=>{console.log("处理项目:",a);const n=a.match(/(bus|adhoc|hotfix)-(\d{6,8})/i);if(n){const i=n[2];let c,r,s;6===i.length?(c=parseInt("20"+i.substring(0,2)),r=parseInt(i.substring(2,4))-1,s=parseInt(i.substring(4,6))):8===i.length&&(c=parseInt(i.substring(0,4)),r=parseInt(i.substring(4,6))-1,s=parseInt(i.substring(6,8)));const d=new Date(c,r,s);console.log(`日期解析: ${a} => ${d.toISOString().split("T")[0]}`);const p=e.getTime()-d.getTime(),g=Math.floor(p/864e5);g>=0&&g<l&&(l=g,o=d,t.value=a,console.log("自动选择历史发布单:",t.value,"天数差:",g))}}))}if($e.value=a,Ne.splice(0,Ne.length),Array.isArray(a)&&a.forEach((e=>{Ne.push({title:e})})),console.log("处理后的项目列表:",Ne),Ve(),t.value&&Le(t.value),document.body.innerHTML.trim().length>0){const e=document.querySelector(".el-empty");e&&(e.style.display="none")}}catch(e){console.error("获取发布单列表失败:",e)}}));const xe=r((()=>{const e={},t=G.map((e=>Object.keys(e))).flat();for(const a of l)if(t.includes(a)){const t=G.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)})),ke=r((()=>{const e={},t=le.map((e=>Object.keys(e))).flat();for(const a of l)if(t.includes(a)){const t=le.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)})),Ce=r((()=>{const e={},t=we.map((e=>Object.keys(e))).flat();for(const a of l)if(t.includes(a)){const t=we.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)})),je=r((()=>{const e={},t=_e.map((e=>Object.keys(e))).flat();for(const a of o)if(t.includes(a)){const t=_e.find((e=>Object.keys(e).includes(a)))[a];e[a]={name:a,link:t}}return Object.values(e)}));let Fe=n(!1),Ie=n(!1),Te=n(!1),Me=n(!1),Se=n(!1),Ae=n(!1),De=n(0);n("wait"),n("wait");const ze=n("SPCB"),Ee=n(localStorage.getItem("releaseTypeFilter")||"bus"),Be=n([]);c((async()=>{const e=localStorage.getItem("releaseTypeFilter");e&&(Ee.value=e);try{const e=(await oe()).data;if(console.log("获取到的发布单列表:",e),Ne.splice(0,Ne.length),Array.isArray(e)&&e.forEach((e=>{Ne.push(e)})),Ve(),Be.value.length>0&&!t.value&&Ze(),t.value&&Le(t.value),document.body.innerHTML.trim().length>0){const e=document.querySelector(".el-empty");e&&(e.style.display="none")}}catch(a){console.error("获取发布单列表失败:",a),s.error("获取发布单列表失败")}}));const Oe=()=>{localStorage.setItem("releaseTypeFilter",Ee.value),Ve(),Be.value.length>0?Ze():(t.value="",qe.splice(0,qe.length))},Ve=()=>{console.log("过滤项目列表，当前过滤类型:",Ee.value,"项目类型:",ze.value),Ne.length&&(Be.value=Ne.filter((e=>{const t="all"===Ee.value||e.release_type?.toLowerCase()===Ee.value.toLowerCase(),a=e.project_type===ze.value;return t&&a})),console.log("过滤后项目数量:",Be.value.length),0===Be.value.length&&(t.value="",qe.splice(0,qe.length)))},Pe=n(),Re=n(!1),He=(e,t)=>t.type===e,$e=n(),Ne=i([]);let qe=i([]);async function Le(e){if(!e)return console.log("没有选择发布单，不进行数据查询"),qe.splice(0,qe.length),Se.value=!1,Me.value=!1,Ae.value=!1,l.splice(0,l.length),o.splice(0,o.length),void U.splice(0,U.length);Se.value=!1,Me.value=!1,console.log(`获取发布单数据: ${e}`),await async function(e){console.log(qe);const t=new Set(qe.map((e=>e.jira_key)));U.splice(0,U.length),l.splice(0,l.length),o.splice(0,o.length),console.log(`get data for ${e}`);let a,n={title:e};a=await N(n),console.log(a),console.log(await N(n)),console.log(a),0===a.length?qe.splice(0,qe.length):(qe.splice(0,qe.length),a.data.forEach((e=>{console.log(e),t.has(e.jira_key)||(qe.push(e),t.add(e.jira_key))}))),console.log(qe)}(e),Re.value=!0;let t=n();if(console.log("releaseTableData前:",qe),t.value=await d(qe),Re.value=!1,console.log("API响应:",t.value),!t.value||!t.value.data||0===t.value.data.length)return console.log("API返回数据为空或格式不正确"),qe.splice(0,qe.length),void(Ae.value=!1);qe.length,qe.forEach((e=>{const a=t.value.data.find((t=>e.jira_key===t.feature_key));a&&(console.log("匹配项数据:",a),e.type=a.type,e.jira_key=a.feature_key,e.jira_link=`https://jira.shopee.io/browse/${a.feature_key}`,e.jira_title=a.feature_title,e.bug_resolved=Number(a.bug_resolved||0),e.bug_total=Number(a.bug_total||0),console.log(`Bug数据: ${e.bug_resolved}/${e.bug_total}`),a.signoff_status?e.sign_off=a.signoff_status:e.sign_off="",a.config_center?e.config_center=a.config_center:e.config_center="",a.shopee_region?e.region=a.shopee_region:e.region="",a.redis_check?e.redis_change=a.redis_check:e.redis_change="",a.result?e.result=a.result:e.result="",a.merge_list?e.merge_list=a.merge_list:e.merge_list="",a.status?e.status=a.status:e.status="",a.Code_Merged?e.Code_Merged=a.Code_Merged:e.Code_Merged="",a.DB_Change?e.DB_Change=a.DB_Change:e.DB_Change="",a.dev_pic?e.dev_pic=a.dev_pic:e.dev_pic="",a.PM?e.PM=a.PM:e.PM="",a.qa_pic?e.qa_pic=a.qa_pic:e.qa_pic="",e.services="",a.services_list.services_list_be.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,l.includes(t)||(l.push(t),U.push(t))})),a.services_list.services_list_fe.forEach((t=>{""===e.services?e.services+=`${t}`:e.services+=`\n${t}`,o.includes(t)||(o.push(t),U.push(t))})))})),0!==o.length&&(Me.value=!0),0!==l.length&&(Se.value=!0),console.log("fe_services:",o),console.log("be_services:",l),console.log("最终表格数据:",qe),Ae.value=!0}function Ue(e){return"TO DO"===e?"#42526e":"Done"===e?"#00875a":"Waiting"===e?"#42526e":"#0052CC"}function Ge(e){return"TO DO"===e?"TO DO":"Done"===e?"DONE":"Waiting"===e?"WAITING":"Icebox"===e?"ICEBOX":"Doing"===e?"DOING":"UAT"===e?"UAT":"Delivering"===e?"DELIVERING":"Developing"===e?"DEVELOPING":"Testing"===e?"TESTING":e}const We=i({name:"",merge:!0});function Je(e){if(e){let t=e.replace(/【Release】|发布单/g,"").replace(/\s/g,"");return console.log(t),t}return""}p(Pe,((e,t)=>{""!==e&&e!==t&&(qe.splice(0,qe.length),Le(e)),We.name=Je(e)})),p(qe,((e,t)=>{localStorage.setItem("releaseTableData",JSON.stringify(qe)),0===qe.length&&(Te.value=!1),0!==qe.length&&(Te.value=!0)})),p(U,((e,t)=>{0===U.length&&(Fe.value=!1),0!==U.length&&(Fe.value=!0)})),g((()=>{const e=localStorage.getItem("selectedProject");let t=localStorage.getItem("active");e&&(Pe.value=e),t&&(De.value=t)})),g((()=>{localStorage.setItem("selectedProject",Pe.value)}));const Qe=n([]),Xe=n([]),Ke=n([]),Ye=n([]);r((()=>Qe.value.map(((e,t)=>({...e,id:t+1}))))),r((()=>Xe.value.map(((e,t)=>({...e,id:t+1}))))),r((()=>Ke.value.map(((e,t)=>({...e,id:t+1}))))),r((()=>Ye.value.map(((e,t)=>({...e,id:t+1}))))),i([]),p(t,((e,t)=>{""!==e&&(qe.splice(0,qe.length),Le(e))})),g((()=>{const e=localStorage.getItem("selectedProjectFinished");let a=localStorage.getItem("active");e&&(t.value=e),a&&(De.value=a)})),g((()=>{localStorage.setItem("selectedProjectFinished",t.value)})),p([Ee,ze],(()=>{localStorage.setItem("releaseTypeFilter",Ee.value),Ve(),Be.value.length>0?Ze():(t.value="",qe.splice(0,qe.length))})),p(t,((e,t)=>{e!==t&&(qe.splice(0,qe.length),Le(e)),We.name=Je(e)}));const Ze=()=>{const e=new Date;let a=-1,o=Infinity,l=null;console.log("开始寻找最近的历史发布单..."),Be.value.forEach(((t,n)=>{const i=/(?:Bus|Adhoc|Hotfix)-(\d{8})/i;let c=t.title.match(/(?:Bus|Adhoc|Hotfix)-(\d{6})/i),r=null,s=null;if(c&&c[1]){r=c[1];const e=parseInt("20"+r.substring(0,2)),t=parseInt(r.substring(2,4))-1,a=parseInt(r.substring(4,6));s=new Date(e,t,a)}else if(c=t.title.match(i),c&&c[1]){r=c[1];const e=parseInt(r.substring(0,4)),t=parseInt(r.substring(4,6))-1,a=parseInt(r.substring(6,8));s=new Date(e,t,a)}if(s&&!isNaN(s.getTime())){console.log(`项目: ${t.title}, 日期: ${s.toISOString().split("T")[0]}`);const i=e.getTime()-s.getTime(),c=Math.floor(i/864e5);(c>=0&&c<o||0===c&&-1===a)&&(o=c,a=n,l=t)}})),-1!==a&&l?(console.log(`自动选择最近的历史发布单: ${l.title}`),t.value=l.title,Le(t.value)):(console.log("没有找到合适的历史发布单"),t.value="",qe.length=0)};return(e,o)=>{const l=W,i=K,c=Y,r=q,d=te,p=h("SuccessFilled"),g=L,N=Z,U=ae,G=ee;return f(),u("div",ne,[m(P,{name:"el-fade-in-linear"},{default:b((()=>[m(v(J),{class:"card",shadow:"hover",width:"50px",height:"50px"},{default:b((()=>[w("div",ie,[m(l,{projectType:ze.value,"onUpdate:projectType":o[0]||(o[0]=e=>ze.value=e)},null,8,["projectType"]),m(c,{modelValue:Ee.value,"onUpdate:modelValue":o[1]||(o[1]=e=>Ee.value=e),class:"release-type-filter",onChange:Oe},{default:b((()=>[m(i,{label:"bus"},{default:b((()=>[_("Bus")])),_:1}),m(i,{label:"adhoc"},{default:b((()=>[_("Adhoc")])),_:1}),m(i,{label:"hotfix"},{default:b((()=>[_("Hotfix")])),_:1}),m(i,{label:"all"},{default:b((()=>[_("全部")])),_:1})])),_:1},8,["modelValue"]),m(v(y),{modelValue:t.value,"onUpdate:modelValue":o[2]||(o[2]=e=>t.value=e),filterable:"",clearable:"",placeholder:"please select release ticket",style:{width:"380px","margin-left":"10px"}},{default:b((()=>[(f(!0),u(x,null,k(Be.value,(e=>(f(),C(v(j),{key:"string"==typeof e?e:e.title,label:"string"==typeof e?e:e.title,value:"string"==typeof e?e:e.title},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),m(v(F),{modelValue:v(Ie),"onUpdate:modelValue":o[3]||(o[3]=e=>I(Ie)?Ie.value=e:Ie=e),"show-close":!1},{header:b((({titleId:e,titleClass:t})=>[w("h4",{id:e,class:T(t)},M(Pe.value),11,ce)])),default:b((()=>[_(" 暂无失败信息。 ")])),_:1},8,["modelValue"])]),w("div",re,[m(v(Q),{data:v(qe),stripe:"",border:"","highlight-current-row":"",fit:"","header-cell-style":{background:"#cacfd7",color:"#606266"},"empty-text":"暂无数据"},{default:b((()=>[m(v(X),{label:"编号","min-width":"21","header-align":"center",align:"center"},{default:b((e=>[_(M(e.$index+1),1)])),_:1}),m(v(X),{prop:"type",label:"类型","header-align":"center",align:"center","min-width":"30",filters:[{text:"Epic",value:"Epic"},{text:"Bug",value:"Bug"},{text:"Task",value:"Task"},{text:"Sub-task",value:"Sub-task"},{text:"Story",value:"Story"}],"filter-method":He,"filter-placement":"bottom-end"},{default:b((({row:e})=>{return[m(r,{class:T((t=e.type,"Epic"===t?"Epic-icon":"Sub-task"===t?"ST-icon":"Task"===t?"Task-icon":"Bug"===t?"Bug-icon":"Story"===t?"Story-icon":void 0))},null,8,["class"])];var t})),_:1}),m(v(X),{prop:"jira_key",label:"单号","min-width":60,"header-align":"center",align:"center"},{default:b((({row:e})=>[m(d,{underline:!1,href:e.jira_link,target:"_blank",type:"primary"},{default:b((()=>[_(M(e.jira_key),1)])),_:2},1032,["href"])])),_:1}),m(v(X),{prop:"jira_title",label:"需求名","min-width":150}),m(v(X),{prop:"bug_resolution_rate",label:"Bug解决率","min-width":80,"header-align":"center",align:"center"},{default:b((({row:e})=>[w("span",{style:S({color:Number(e.bug_resolved||0)===Number(e.bug_total||0)?"#67C23A":"#F56C6C"})},["number"==typeof e.bug_resolved&&"number"==typeof e.bug_total?(f(),u(x,{key:0},[_(M(e.bug_resolved)+"/"+M(e.bug_total),1)],64)):(f(),u(x,{key:1},[_(M(e.bug_resolved||0)+"/"+M(e.bug_total||0),1)],64))],4)])),_:1}),m(v(X),{label:"周一","header-align":"center",align:"center"},{default:b((()=>[m(v(X),{prop:"sign_off",label:"Signed off","header-align":"center",align:"center","min-width":"40"},{header:b((()=>[_(" Signed"),se,_("off ")])),default:b((({row:e})=>["Confirmed"===e.sign_off?(f(),C(r,{key:0,size:20,color:"Confirmed"===e.sign_off?"#67c23a":"#F56C67"},{default:b((()=>[m(p)])),_:2},1032,["color"])):A("",!0),""===e.sign_off?(f(),C(r,{key:1,size:20,color:"pass"===e.sign_off?"#67c23a":"#F56C67"},{default:b((()=>[m(v(D))])),_:2},1032,["color"])):A("",!0)])),_:1})])),_:1}),m(v(X),{label:"周二","header-align":"center",align:"center"},{default:b((()=>[m(v(X),{type:"expand",label:"提MR","min-width":"32"},{default:b((e=>[w("div",null,[w("div",de,[m(g,{class:"box-item",effect:"customized",content:"点击创建MR",placement:"top-start"},{default:b((()=>[z((f(),C(v(E),{type:"danger",onClick:o[4]||(o[4]=e=>a.value=!0),size:"small",icon:v(B),"element-loading-text":"AR正在创建MR, 请耐心等待..."},{default:b((()=>[_("创建 ")])),_:1},8,["icon"])),[[G,Re.value,void 0,{fullscreen:!0,lock:!0}]])])),_:1}),m(g,{class:"box-item",effect:"customized",content:"点击复制",placement:"top-start"},{default:b((()=>[z((f(),C(v(E),{type:"primary",size:"small",onClick:t=>async function(e){let t=n();Pe.value,t=await H(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{s({message:"恭喜，MR信息已复制到剪切板！",type:"success"})})).catch((e=>{s.error("复制剪切板失败！")}))}(e),icon:v(O),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:b((()=>[_("复制 ")])),_:2},1032,["onClick","icon"])),[[G,Re.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024),m(g,{class:"box-item",effect:"customized",content:"点击发送MR提醒到seatalk",placement:"top-start"},{default:b((()=>[z((f(),C(v(E),{type:"primary",size:"small",onClick:t=>async function(e){let t=n();t=await R(e.row),console.log(t),navigator.clipboard.writeText(t.data).then((()=>{s({message:"恭喜，MR信息已发送到seatalk！",type:"success"})})).catch((e=>{s.error("MR信息发送失败！")}))}(e),icon:v(O),"element-loading-text":"AR正在处理数据，请耐心等待..."},{default:b((()=>[_("发送 ")])),_:2},1032,["onClick","icon"])),[[G,Re.value,void 0,{fullscreen:!0,lock:!0}]])])),_:2},1024)]),m(N,{modelValue:a.value,"onUpdate:modelValue":o[6]||(o[6]=e=>a.value=e),title:"Warning",width:"30%","align-center":""},{footer:b((()=>[w("span",pe,[m(v(E),{onClick:o[5]||(o[5]=e=>a.value=!1)},{default:b((()=>[_("取消")])),_:1}),m(v(E),{type:"primary",onClick:t=>async function(e){console.log(e.row),n(),await $(e.row)}(e)},{default:b((()=>[_(" 确认 ")])),_:2},1032,["onClick"])])])),default:b((()=>[w("span",null,"请确认是否开始自动提MR？发布单： "+M(Pe.value),1)])),_:2},1032,["modelValue"]),m(v(Q),{data:e.row.merge_list,border:"","header-cell-style":{background:"#def1ce",color:"#606266"}},{default:b((()=>[m(v(X),{label:"仓库",prop:"repo_name"}),m(v(X),{label:"分支",prop:"branch_name"}),m(v(X),{label:"PIC",prop:"pic"}),m(v(X),{label:"MR地址",prop:"web_url"},{default:b((({row:e})=>[w("a",{href:e.web_url,target:"_blank"},M(e.web_url),9,ge)])),_:1}),m(v(X),{label:"MR状态",prop:"merge_status"}),m(v(X),{label:"MR作者",prop:"author"})])),_:2},1032,["data"])])])),_:1}),m(v(X),{prop:"Code_Merged",label:"Code Merged","header-align":"center",align:"center","min-width":"40"},{header:b((()=>[_(" Code"),he,_("Merged ")])),default:b((({row:e})=>["Confirmed"===e.Code_Merged?(f(),C(r,{key:0,size:20,color:"Confirmed"===e.Code_Merged?"#67c23a":"#F56C67"},{default:b((()=>[m(p)])),_:2},1032,["color"])):A("",!0),""===e.Code_Merged?(f(),C(r,{key:1,size:20,color:"pass"===e.Code_Merged?"#67c23a":"#F56C67"},{default:b((()=>[m(v(D))])),_:2},1032,["color"])):A("",!0)])),_:1})])),_:1}),m(v(X),{label:"周三","header-align":"center",align:"center"},{default:b((()=>[m(v(X),{prop:"config_center",label:"Config Changed","header-align":"center",align:"center","min-width":"43"},{header:b((()=>[_(" Config"),fe,_("Changed ")])),default:b((({row:e})=>["Confirmed"===e.config_center?(f(),C(r,{key:0,size:20,color:"Confirmed"===e.config_center?"#67c23a":"#F56C67"},{default:b((()=>[m(p)])),_:2},1032,["color"])):A("",!0),""===e.config_center?(f(),C(r,{key:1,size:20,color:"pass"===e.config_center?"#67c23a":"#F56C67"},{default:b((()=>[m(v(D))])),_:2},1032,["color"])):A("",!0)])),_:1}),m(v(X),{prop:"DB_Change",label:"DB Changed","header-align":"center",align:"center","min-width":"43"},{default:b((({row:e})=>["Confirmed"===e.DB_Change?(f(),C(r,{key:0,size:20,color:"Confirmed"===e.DB_Change?"#67c23a":"#F56C67"},{default:b((()=>[m(p)])),_:2},1032,["color"])):A("",!0),""===e.DB_Change?(f(),C(r,{key:1,size:20,color:"pass"===e.DB_Change?"#67c23a":"#F56C67"},{default:b((()=>[m(v(D))])),_:2},1032,["color"])):A("",!0)])),header:b((()=>[_(" DB"),ue,_("Changed ")])),_:1}),m(v(X),{prop:"services",label:"服务","min-width":150,"header-align":"center",align:"center",style:{"white-space":"pre-wrap"}},{default:b((({row:e})=>[""!==e.services?(f(),u("div",me,[(f(!0),u(x,null,k(e.services.split("\n"),((e,t)=>(f(),u("div",{style:S({color:ye(e)?"inherit":"#F56C67"})},M(e),5)))),256))])):A("",!0),""===e.services?(f(),C(r,{key:1,size:20,color:"#F56C67"},{default:b((()=>[m(v(D))])),_:1})):A("",!0)])),_:1}),m(v(X),{prop:"region",label:"Region","header-align":"center",align:"center","min-width":"37"},{default:b((({row:e})=>[""!==e.region?(f(),C(U,{key:0,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:b((()=>[_(M(e.region),1)])),_:2},1032,["color"])):A("",!0),""===e.region?(f(),C(r,{key:1,size:20,color:"pass"===e.region?"#67c23a":"#F56C67"},{default:b((()=>[m(v(D))])),_:2},1032,["color"])):A("",!0)])),_:1})])),_:1}),m(v(X),{prop:"PM",label:"PM","header-align":"center",align:"center","min-width":"50"},{default:b((({row:e})=>[_(M(e.PM),1)])),_:1}),m(v(X),{prop:"dev_pic",label:"DEV PIC","header-align":"center",align:"center","min-width":"40"},{header:b((()=>[_(" DEV"),be,_("PIC ")])),default:b((({row:e})=>[_(M(e.dev_pic),1)])),_:1}),m(v(X),{prop:"qa_pic",label:"QA","header-align":"center",align:"center","min-width":"50"},{default:b((({row:e})=>[_(M(e.qa_pic),1)])),_:1}),m(v(X),{prop:"status",label:"Status","header-align":"center",align:"center","min-width":"60"},{default:b((({row:e})=>{return[m(v(V),{class:"bold-text",effect:"dark",type:(t=e.status,"TO DO"===t?"info":"Done"===t?"success":"Waiting"===t?"info":"Icebox"===t?"icebox":"Doing"===t?"doing":"UAT"===t?"uat":"Delivering"===t?"delivering":"Developing"===t?"developing":"Testing"===t?"testing":void 0),color:Ue(e.status)},{default:b((()=>[_(M(Ge(e.status)),1)])),_:2},1032,["type","color"])];var t})),_:1})])),_:1},8,["data"])]),w("div",ve,[m(v(Q),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e78181",color:"#f8f7f7"},data:xe.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:b((()=>[m(v(X),{"header-align":"center",label:"平台BE1组"},{default:b((e=>[m(d,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[_(M(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),m(v(Q),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#819ee7",color:"#f8f7f7"},"header-align":"center",data:ke.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:b((()=>[m(v(X),{"header-align":"center",label:"平台BE2组"},{default:b((e=>[m(d,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[_(M(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),m(v(Q),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#81e7c8",color:"#f8f7f7"},"header-align":"center",data:Ce.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:b((()=>[m(v(X),{"header-align":"center",label:"功能BE组"},{default:b((e=>[m(d,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[_(M(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"]),m(v(Q),{ref:"multipleTableRef",border:"","header-cell-style":{background:"#e7a881",color:"#f8f7f7"},"header-align":"center",data:je.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:b((()=>[m(v(X),{"header-align":"center",label:"FE组"},{default:b((e=>[m(d,{href:e.row.link,target:"_blank",underline:!1},{default:b((()=>[_(M(e.row.name),1)])),_:2},1032,["href"])])),_:1})])),_:1},8,["data"])])])),_:1})])),_:1})])}}});e("default",o(we,[["__scopeId","data-v-d8982ff0"]]))}}}));
//# sourceMappingURL=releaseHistory-legacy.DBIXXE1b.js.map

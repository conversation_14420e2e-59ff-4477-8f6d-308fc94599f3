{"version": 3, "file": "index-BVxaBybA.js", "sources": ["../../src/assets/login.png", "../../src/views/login/comp/LoginForm.vue", "../../src/views/login/index.vue"], "sourcesContent": ["export default \"__VITE_ASSET__DhDaM43U__\"", "<template>\n  <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"validateForm\" class=\"login-ruleForm\">\n    <el-form-item prop=\"username\">\n      <el-input :placeholder=\"t('login.username')\" v-model=\"ruleForm.username\">\n        <template #prefix>\n          <icon-user theme=\"outline\" size=\"16\" fill=\"#999\" />\n        </template>\n      </el-input>\n    </el-form-item>\n    <el-form-item prop=\"password\">\n      <el-input\n        @keyup.enter=\"handleLogin\"\n        :placeholder=\"t('login.password')\"\n        type=\"password\"\n        v-model=\"ruleForm.password\"\n      >\n        <template #prefix>\n          <icon-lock theme=\"outline\" size=\"16\" fill=\"#999\" />\n        </template>\n      </el-input>\n    </el-form-item>\n    <el-form-item>\n      <div class=\"login-check\">\n        <el-checkbox v-model=\"checkedPwd\">{{ t('login.rememberPwd') }}</el-checkbox>\n        <el-button text type=\"primary\">{{ t('login.forgotPwd') }}</el-button>\n      </div>\n    </el-form-item>\n    <el-form-item>\n      <el-button type=\"primary\" :loading=\"loading\" class=\"login-btn\" round @click=\"handleLogin\">{{\n        t('login.loginBtn')\n      }}</el-button>\n    </el-form-item>\n<!--    <el-divider>{{ t('login.thirdparty') }}</el-divider>-->\n<!--    <el-form-item>-->\n<!--      <div class=\"login-methods\">-->\n<!--        <icon-wechat theme=\"outline\" size=\"24\" fill=\"#333\" />-->\n<!--        <icon-alipay theme=\"outline\" size=\"24\" fill=\"#333\" />-->\n<!--        <icon-github theme=\"outline\" size=\"24\" fill=\"#333\" />-->\n<!--        <icon-twitter theme=\"outline\" size=\"24\" fill=\"#333\" />-->\n<!--        <icon-google theme=\"outline\" size=\"24\" fill=\"#333\" />-->\n<!--      </div>-->\n<!--    </el-form-item>-->\n  </el-form>\n</template>\n\n<script>\n  import { reactive, toRefs, ref, unref, watch } from 'vue';\n  import { useStore } from 'vuex';\n  import { useRouter } from 'vue-router';\n  import { useI18n } from 'vue-i18n';\n  export default {\n    setup() {\n      const { t } = useI18n();\n      const store = useStore();\n      const router = useRouter();\n      const validateForm = ref(null);\n      const state = reactive({\n        ruleForm: {\n          username: 'admin',\n          password: 'admin',\n        },\n        loading: false,\n        checkedPwd: false,\n        redirect: undefined,\n        rules: {\n          username: [{ required: true, message: t('login.rules.username'), trigger: 'blur' }],\n          password: [{ required: true, message: t('login.rules.password'), trigger: 'blur' }],\n        },\n      });\n\n      watch(\n        () => router.currentRoute,\n        ({ _value }) => {\n          const route = _value;\n          state.redirect = (route.query && route.query.redirect) || '/';\n        },\n        {\n          immediate: true,\n        }\n      );\n\n      const handleLogin = async () => {\n        const form = unref(validateForm);\n        if (!form) return;\n        await form.validate((valid) => {\n          if (valid) {\n            state.valid = true;\n            state.loading = true;\n            store\n              .dispatch('user/login', state.ruleForm)\n              .then(() => {\n                const routerPath =\n                  state.redirect === '/404' || state.redirect === '/401' ? '/' : state.redirect;\n                router.push(routerPath).catch(() => {});\n                state.loading = false;\n              })\n              .catch(() => {\n                state.loading = false;\n              });\n          }\n        });\n      };\n      return {\n        ...toRefs(state),\n        validateForm,\n        handleLogin,\n        t,\n      };\n    },\n  };\n</script>\n<style lang=\"scss\" scoped>\n  .login-ruleForm {\n    margin-top: 1rem;\n    :deep(.el-input__prefix) {\n      top: 2px;\n      padding: 0 4px;\n    }\n    .login-methods {\n      width: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: space-around;\n    }\n    .login-btn {\n      width: 100%;\n    }\n    .login-check {\n      width: 100%;\n      display: flex;\n      align-content: center;\n      justify-content: space-between;\n    }\n  }\n</style>\n", "<template>\n  <div class=\"login-wrapper\">\n    <el-header class=\"header\">\n      <Logo class=\"logo\" />\n      <LangChange class=\"lang\" color=\"#fff\" />\n    </el-header>\n    <div class=\"login-container\">\n      <div class=\"login-left hidden-sm-and-down\">\n        <div class=\"login-left-wrap\">\n          <img class=\"img\" src=\"@/assets/login.png\" alt=\"login-bg\" />\n          <h2 class=\"desc\">{{ t('login.desc') }}</h2>\n          <p class=\"tip\">{{ t('login.tip') }}</p>\n        </div>\n      </div>\n      <div class=\"login-form\" :class=\"{ 'is-mobile': isMobile }\">\n        <div class=\"form-warp\">\n          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n            <el-tab-pane :label=\"t('login.title')\" name=\"first\">\n              <LoginForm />\n            </el-tab-pane>\n<!--            <el-tab-pane :label=\"t('register.title')\" name=\"second\">-->\n<!--              <RegisterForm />-->\n<!--            </el-tab-pane>-->\n          </el-tabs>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n  import { ref, computed } from 'vue';\n  import { useStore } from 'vuex';\n  import LoginForm from 'views/login/comp/LoginForm.vue';\n  import RegisterForm from './comp/RegisterForm.vue';\n  import LangChange from '@/components/LangChange/index.vue';\n  import { useI18n } from 'vue-i18n';\n\n  const store = useStore();\n  const { t } = useI18n();\n  const activeName = ref('first');\n  const isMobile = computed(() => {\n    return store.getters['setting/isMobile'];\n  });\n\n  const handleClick = (val) => {\n    console.log(val);\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  .login-wrapper {\n    position: relative;\n    .header {\n      position: absolute;\n      top: 0;\n      right: 0;\n      left: 0;\n      z-index: 99;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 0 40px;\n      color: #fff;\n      background: transparent;\n      .logo {\n        justify-content: start;\n        :deep(.logo-title) {\n          color: #fff !important;\n        }\n      }\n      .lang:hover {\n        background: transparent;\n      }\n    }\n    .login-container {\n      display: flex;\n      width: 100%;\n      height: 100vh;\n      overflow: hidden;\n      background-color: $dark-bg-color;\n      .login-left {\n        position: relative;\n        display: flex;\n        flex-direction: column;\n        width: 50vw;\n        height: 100%;\n        background-image: url('@/assets/login-bg-dark.svg');\n        background-repeat: no-repeat;\n        background-position: 100%;\n        background-size: auto 100%;\n        &-wrap {\n          height: 80vh;\n          margin: auto;\n          .img {\n            width: 280px;\n            margin-top: 10vh;\n          }\n          .title,\n          .desc {\n            max-width: 500px;\n            font-weight: bold;\n            color: #fff;\n            letter-spacing: 1.2px;\n          }\n          .desc {\n            font-size: 28px;\n          }\n          .tip {\n            color: #fff;\n          }\n        }\n      }\n      .login-form {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 50vw;\n        height: 100vh;\n        .form-warp {\n          width: 400px;\n          padding: 1rem 3rem 0 3rem;\n          margin: auto;\n          background-color: #fff;\n          border-radius: 8px;\n        }\n        &.is-mobile {\n          width: 100%;\n          .form-warp {\n            width: 100%;\n            margin: 0 15px;\n          }\n        }\n      }\n    }\n  }\n</style>\n"], "names": ["_imports_0", "URL", "url", "href", "setup", "t", "useI18n", "store", "useStore", "state", "reactive", "ruleForm", "username", "password", "checkedPwd", "redirect", "undefined", "rules", "required", "message", "trigger", "route", "_value", "immediate", "toRefs", "form", "valid", "loading", "dispatch", "routerPath", "router", "push", "catch", "_openBlock", "_createBlock", "_component_el_form", "model", "_ctx", "ref", "_createVNode", "_component_el_input", "placeholder", "$setup", "modelValue", "_component_icon_user", "theme", "size", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleLogin", "type", "_component_icon_lock", "default", "_withCtx", "_component_el_button", "text", "class", "round", "activeName", "isMobile", "computed", "getters", "handleClick", "val", "console", "log"], "mappings": "oUAAA,MAAeA,EAAA,GAAA,IAAAC,IAAA,iCAAAC,KAAAC,wCCmDX,KAAAC,GACE,MAAAC,EAAAA,GAAAC,IACAC,EAAAC,oBAGAC,EAAAC,EAAA,CACEC,SAAA,CACEC,SAAA,QACAC,SAAA,oBAGFC,YAAA,EACAC,cAAAC,EACAC,MAAA,CACEL,SAAA,CAAA,CAAAM,UAAA,EAAAC,QAAAd,EAAA,wBAAAe,QAAA,SACAP,SAAA,CAAA,CAAAK,UAAA,EAAAC,QAAAd,EAAA,wBAAAe,QAAA,mDAOA,MAAAC,EAAAC,8CAGF,CACEC,WAAA,IAyBJ,MAAA,IACEC,EAAAf,qDApBAgB,yBAEEC,IACEjB,EAAAiB,OAAA,EACAjB,EAAAkB,SAAA,IAEEC,SAAA,aAAAnB,EAAAE,qBAEE,MAAAkB,EACE,SAAApB,EAAAM,UAAA,SAAAN,EAAAM,SAAA,IAAAN,EAAAM,SACFe,EAAAC,KAAAF,GAAAG,OAAA,SACAvB,EAAAkB,SAAA,CAAA,gBAGAlB,EAAAkB,SAAA,CAAA,IAEN,GACF,sGAnGI,OAAAM,IAAAC,EAAAC,EAAA,CAAkBC,MAAAC,EAAA1B,SAAcM,MAAAoB,EAAApB,MAAmBqB,IAAA,+FAE9CC,EAAAC,EAAA,CAA2CC,YAAAC,EAAArC,EAAA,kBAAAsC,WAAAN,EAAA1B,SAAAC,sEACzC,gBACE2B,EAAAK,EAAA,CAAgBC,MAAA,UAAUC,KAAA,2GAMtCP,EAAAC,EAAA,CACAO,QAAAC,EAAAN,EAAAO,YAAA,CAAA,UACDR,YAAAC,EAAArC,EAAA,kBACS6C,KAAA,WAAAP,WAAAN,EAAA1B,SAAAE,sEAEE,gBACE0B,EAAAY,EAAA,CAAgBN,MAAA,UAAUC,KAAA,wFAKzCM,QAAAC,GAAA,IAAA,2KAEad,EAAAe,EAAA,CAAKC,KAAA,iHAIPhB,EAAAe,EAAA,CAAgBJ,KAAA,UAAkBvB,QAAAU,EAAAV,QAAkB6B,MAAA,YAAOC,MAAA,yeCG5E,MAAAlD,EAAAC,KAQAH,EAAAA,GAAAC,IACAoD,EAAApB,EAAA,SACAqB,EAAAC,GAAA,IACArD,EAAAsD,QAAA,sBAGAC,EAAAC,IACAC,QAAAC,IAAAF,EAAA"}
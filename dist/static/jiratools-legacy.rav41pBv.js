System.register(["./index-legacy.C52nWfoy.js"],(function(e,i){"use strict";var l,o,t,n,d,u,s,r,c,a,p;return{setters:[e=>{l=e.H,o=e.o,t=e.h,n=e.l,d=e.ab,u=e.be,s=e.k,r=e.F,c=e.r,a=e.t,p=e.ct}],execute:function(){const i={key:0},h=["onUpdate:modelValue"],g=["onClick"];e("default",l({data:()=>({dialogVisible:!1,description:"",jql:"",cron:"",groupId:"",items:[]}),methods:{showDialog(){this.dialogVisible=!0},save(){this.items.push({description:this.description,jql:this.jql,cron:this.cron,groupId:this.groupId,switch:!1}),this.dialogVisible=!1},execute(e){}}},[["render",function(e,l,v,m,b,V){return o(),t("div",null,[n("button",{onClick:l[0]||(l[0]=(...e)=>V.showDialog&&V.showDialog(...e))},"New"),b.dialogVisible?(o(),t("div",i,[d(n("input",{"onUpdate:modelValue":l[1]||(l[1]=e=>b.description=e),placeholder:"Description"},null,512),[[u,b.description]]),d(n("input",{"onUpdate:modelValue":l[2]||(l[2]=e=>b.jql=e),placeholder:"JQL"},null,512),[[u,b.jql]]),d(n("input",{"onUpdate:modelValue":l[3]||(l[3]=e=>b.cron=e),placeholder:"Cron Expression"},null,512),[[u,b.cron]]),d(n("input",{"onUpdate:modelValue":l[4]||(l[4]=e=>b.groupId=e),placeholder:"Seatalk Group ID"},null,512),[[u,b.groupId]]),n("button",{onClick:l[5]||(l[5]=(...e)=>V.save&&V.save(...e))},"Save")])):s("",!0),(o(!0),t(r,null,c(b.items,(e=>(o(),t("div",{key:e.id},[n("div",null,a(e.description),1),n("div",null,a(e.jql),1),n("div",null,a(e.cron),1),n("div",null,a(e.groupId),1),d(n("input",{type:"checkbox","onUpdate:modelValue":i=>e.switch=i},null,8,h),[[p,e.switch]]),n("button",{onClick:i=>V.execute(e.id)},"立即执行",8,g)])))),128))])}]]))}}}));
//# sourceMappingURL=jiratools-legacy.rav41pBv.js.map

{"version": 3, "file": "401-Bjr1OMno.js", "sources": ["../../src/views/errorPage/401.vue"], "sourcesContent": ["<template>\n  <ErrorPage type=\"401\" :title=\"t('errorPages.401.desc')\" :msg=\"t('errorPages.401.remark')\" />\n</template>\n\n<script setup>\n  import ErrorPage from '@/components/ErrorPage/index.vue';\n  import { useI18n } from 'vue-i18n';\n  const { t } = useI18n();\n</script>\n"], "names": ["t", "useI18n"], "mappings": "oIAKA,MAAAA,EAAAA,GAAAC"}
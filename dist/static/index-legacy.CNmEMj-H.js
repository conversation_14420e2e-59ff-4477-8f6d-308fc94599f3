System.register(["./index-legacy.C52nWfoy.js"],(function(e,l){"use strict";var a,t,n,u,o,i,s,d,r,c,b,v,m,h,p,f,x,k,g,C,y,L,V,S,B,I,E,F,z,N,w,U,D,_,O,G,$,j,R,A,M,P,T,H,J,K,q,Q,W,X,Y,Z,ee;return{setters:[e=>{a=e.a$,t=e.br,n=e.U,u=e.bl,o=e.bm,i=e.b3,s=e.aG,d=e.a,r=e.aN,c=e.bo,b=e.b5,v=e.aJ,m=e.T,h=e.b8,p=e.aV,f=e.p,x=e.s,k=e.bn,g=e.i,C=e.cs,y=e.aP,L=e.b7,V=e.bs,S=e.bp,B=e.e,I=e.bC,E=e.f,F=e.o,z=e.C,N=e.w,w=e.l,U=e.n,D=e.j,_=e.ab,O=e.h,G=e.af,$=e.aH,j=e.ct,R=e.m,A=e.F,M=e.x,P=e.t,T=e.k,H=e.az,J=e._,K=e.ag,q=e.b,Q=e.d,W=e.aT,X=e.cu,Y=e.M,Z=e.D,ee=e.bt}],execute:function(){const l={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:a,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0},...t(["ariaControls"])},le={[n]:e=>u(e)||o(e)||i(e),change:e=>u(e)||o(e)||i(e)},ae=Symbol("checkboxGroupContextKey"),te=(e,{model:l,isLimitExceeded:a,hasOwnLabel:t,isDisabled:n,isLabeledByFormItem:u})=>{const o=s(ae,void 0),{formItem:i}=b(),{emit:r}=v();function c(l){var a,t,n,u;return[!0,e.trueValue,e.trueLabel].includes(l)?null==(t=null!=(a=e.trueValue)?a:e.trueLabel)||t:null!=(u=null!=(n=e.falseValue)?n:e.falseLabel)&&u}const f=d((()=>(null==o?void 0:o.validateEvent)||e.validateEvent));return m((()=>e.modelValue),(()=>{f.value&&(null==i||i.validate("change").catch((e=>h())))})),{handleChange:function(e){if(a.value)return;const l=e.target;r("change",c(l.checked),e)},onClickRoot:async function(o){a.value||t.value||n.value||!u.value||o.composedPath().some((e=>"LABEL"===e.tagName))||(l.value=c([!1,e.falseValue,e.falseLabel].includes(l.value)),await p(),function(e,l){r("change",c(e),l)}(l.value,o))}}},ne=(e,l)=>{const{formItem:a}=b(),{model:t,isGroup:u,isLimitExceeded:o}=(e=>{const l=f(!1),{emit:a}=v(),t=s(ae,void 0),u=d((()=>!1===r(t))),o=f(!1),i=d({get(){var a,n;return u.value?null==(a=null==t?void 0:t.modelValue)?void 0:a.value:null!=(n=e.modelValue)?n:l.value},set(e){var s,d;u.value&&x(e)?(o.value=void 0!==(null==(s=null==t?void 0:t.max)?void 0:s.value)&&e.length>(null==t?void 0:t.max.value)&&e.length>i.value.length,!1===o.value&&(null==(d=null==t?void 0:t.changeEvent)||d.call(t,e))):(a(n,e),l.value=e)}});return{model:i,isGroup:u,isLimitExceeded:o}})(e),{isFocused:m,isChecked:h,checkboxButtonSize:p,checkboxSize:B,hasOwnLabel:I,actualValue:E}=((e,l,{model:a})=>{const t=s(ae,void 0),n=f(!1),u=d((()=>k(e.value)?e.label:e.value)),o=d((()=>{const l=a.value;return i(l)?l:x(l)?g(u.value)?l.map(C).some((e=>y(e,u.value))):l.map(C).includes(u.value):null!=l?l===e.trueValue||l===e.trueLabel:!!l}));return{checkboxButtonSize:L(d((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value})),{prop:!0}),isChecked:o,isFocused:n,checkboxSize:L(d((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value}))),hasOwnLabel:d((()=>!!l.default||!k(u.value))),actualValue:u}})(e,l,{model:t}),{isDisabled:F}=(({model:e,isChecked:l})=>{const a=s(ae,void 0),t=d((()=>{var t,n;const u=null==(t=null==a?void 0:a.max)?void 0:t.value,o=null==(n=null==a?void 0:a.min)?void 0:n.value;return!r(u)&&e.value.length>=u&&!l.value||!r(o)&&e.value.length<=o&&l.value}));return{isDisabled:c(d((()=>(null==a?void 0:a.disabled.value)||t.value))),isLimitDisabled:t}})({model:t,isChecked:h}),{inputId:z,isLabeledByFormItem:N}=V(e,{formItemContext:a,disableIdGeneration:I,disableIdManagement:u}),{handleChange:w,onClickRoot:U}=te(e,{model:t,isLimitExceeded:o,hasOwnLabel:I,isDisabled:F,isLabeledByFormItem:N});var D,_;return e.checked&&(x(t.value)&&!t.value.includes(E.value)?t.value.push(E.value):t.value=null==(_=null!=(D=e.trueValue)?D:e.trueLabel)||_),S({from:"controls",replacement:"aria-controls",version:"2.8.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},d((()=>!!e.controls))),S({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},d((()=>u.value&&k(e.value)))),S({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},d((()=>!!e.trueLabel))),S({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},d((()=>!!e.falseLabel))),{inputId:z,isLabeledByFormItem:N,isChecked:h,isDisabled:F,isFocused:m,checkboxButtonSize:p,checkboxSize:B,hasOwnLabel:I,model:t,actualValue:E,handleChange:w,onClickRoot:U}},ue=["id","indeterminate","name","tabindex","disabled","true-value","false-value"],oe=["id","indeterminate","disabled","value","name","tabindex"],ie=B({name:"ElCheckbox"}),se=B({...ie,props:l,emits:le,setup(e){const l=e,a=I(),{inputId:t,isLabeledByFormItem:n,isChecked:u,isDisabled:o,isFocused:i,checkboxSize:s,hasOwnLabel:r,model:c,actualValue:b,handleChange:v,onClickRoot:m}=ne(l,a),h=E("checkbox"),p=d((()=>[h.b(),h.m(s.value),h.is("disabled",o.value),h.is("bordered",l.border),h.is("checked",u.value)])),f=d((()=>[h.e("input"),h.is("disabled",o.value),h.is("checked",u.value),h.is("indeterminate",l.indeterminate),h.is("focus",i.value)]));return(e,l)=>(F(),z(H(!D(r)&&D(n)?"span":"label"),{class:U(D(p)),"aria-controls":e.indeterminate?e.controls||e.ariaControls:null,onClick:D(m)},{default:N((()=>{var a,n;return[w("span",{class:U(D(f))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?_((F(),O("input",{key:0,id:D(t),"onUpdate:modelValue":l[0]||(l[0]=e=>G(c)?c.value=e:null),class:U(D(h).e("original")),type:"checkbox",indeterminate:e.indeterminate,name:e.name,tabindex:e.tabindex,disabled:D(o),"true-value":null!=(a=e.trueValue)?a:e.trueLabel,"false-value":null!=(n=e.falseValue)?n:e.falseLabel,onChange:l[1]||(l[1]=(...e)=>D(v)&&D(v)(...e)),onFocus:l[2]||(l[2]=e=>i.value=!0),onBlur:l[3]||(l[3]=e=>i.value=!1),onClick:l[4]||(l[4]=$((()=>{}),["stop"]))},null,42,ue)),[[j,D(c)]]):_((F(),O("input",{key:1,id:D(t),"onUpdate:modelValue":l[5]||(l[5]=e=>G(c)?c.value=e:null),class:U(D(h).e("original")),type:"checkbox",indeterminate:e.indeterminate,disabled:D(o),value:D(b),name:e.name,tabindex:e.tabindex,onChange:l[6]||(l[6]=(...e)=>D(v)&&D(v)(...e)),onFocus:l[7]||(l[7]=e=>i.value=!0),onBlur:l[8]||(l[8]=e=>i.value=!1),onClick:l[9]||(l[9]=$((()=>{}),["stop"]))},null,42,oe)),[[j,D(c)]]),w("span",{class:U(D(h).e("inner"))},null,2)],2),D(r)?(F(),O("span",{key:0,class:U(D(h).e("label"))},[R(e.$slots,"default"),e.$slots.default?T("v-if",!0):(F(),O(A,{key:0},[M(P(e.label),1)],64))],2)):T("v-if",!0)]})),_:3},8,["class","aria-controls","onClick"]))}});var de=J(se,[["__file","checkbox.vue"]]);const re=["name","tabindex","disabled","true-value","false-value"],ce=["name","tabindex","disabled","value"],be=B({name:"ElCheckboxButton"}),ve=B({...be,props:l,emits:le,setup(e){const l=e,a=I(),{isFocused:t,isChecked:n,isDisabled:u,checkboxButtonSize:o,model:i,actualValue:r,handleChange:c}=ne(l,a),b=s(ae,void 0),v=E("checkbox"),m=d((()=>{var e,l,a,t;const n=null!=(l=null==(e=null==b?void 0:b.fill)?void 0:e.value)?l:"";return{backgroundColor:n,borderColor:n,color:null!=(t=null==(a=null==b?void 0:b.textColor)?void 0:a.value)?t:"",boxShadow:n?`-1px 0 0 0 ${n}`:void 0}})),h=d((()=>[v.b("button"),v.bm("button",o.value),v.is("disabled",u.value),v.is("checked",n.value),v.is("focus",t.value)]));return(e,l)=>{var a,o;return F(),O("label",{class:U(D(h))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?_((F(),O("input",{key:0,"onUpdate:modelValue":l[0]||(l[0]=e=>G(i)?i.value=e:null),class:U(D(v).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:D(u),"true-value":null!=(a=e.trueValue)?a:e.trueLabel,"false-value":null!=(o=e.falseValue)?o:e.falseLabel,onChange:l[1]||(l[1]=(...e)=>D(c)&&D(c)(...e)),onFocus:l[2]||(l[2]=e=>t.value=!0),onBlur:l[3]||(l[3]=e=>t.value=!1),onClick:l[4]||(l[4]=$((()=>{}),["stop"]))},null,42,re)),[[j,D(i)]]):_((F(),O("input",{key:1,"onUpdate:modelValue":l[5]||(l[5]=e=>G(i)?i.value=e:null),class:U(D(v).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:D(u),value:D(r),onChange:l[6]||(l[6]=(...e)=>D(c)&&D(c)(...e)),onFocus:l[7]||(l[7]=e=>t.value=!0),onBlur:l[8]||(l[8]=e=>t.value=!1),onClick:l[9]||(l[9]=$((()=>{}),["stop"]))},null,42,ce)),[[j,D(i)]]),e.$slots.default||e.label?(F(),O("span",{key:2,class:U(D(v).be("button","inner")),style:K(D(n)?D(m):void 0)},[R(e.$slots,"default",{},(()=>[M(P(e.label),1)]))],6)):T("v-if",!0)],2)}}});var me=J(ve,[["__file","checkbox-button.vue"]]);const he=q({modelValue:{type:Q(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:a,label:String,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0},...t(["ariaLabel"])}),pe={[n]:e=>x(e),change:e=>x(e)},fe=B({name:"ElCheckboxGroup"}),xe=B({...fe,props:he,emits:pe,setup(e,{emit:l}){const a=e,t=E("checkbox"),{formItem:u}=b(),{inputId:o,isLabeledByFormItem:i}=V(a,{formItemContext:u}),s=async e=>{l(n,e),await p(),l("change",e)},r=d({get:()=>a.modelValue,set(e){s(e)}});return W(ae,{...X(Y(a),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:r,changeEvent:s}),S({from:"label",replacement:"aria-label",version:"2.8.0",scope:"el-checkbox-group",ref:"https://element-plus.org/en-US/component/checkbox.html"},d((()=>!!a.label))),m((()=>a.modelValue),(()=>{a.validateEvent&&(null==u||u.validate("change").catch((e=>h())))})),(e,l)=>{var a;return F(),z(H(e.tag),{id:D(o),class:U(D(t).b("group")),role:"group","aria-label":D(i)?void 0:e.label||e.ariaLabel||"checkbox-group","aria-labelledby":D(i)?null==(a=D(u))?void 0:a.labelId:void 0},{default:N((()=>[R(e.$slots,"default")])),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var ke=J(xe,[["__file","checkbox-group.vue"]]);e("E",Z(de,{CheckboxButton:me,CheckboxGroup:ke})),ee(me),ee(ke)}}}));
//# sourceMappingURL=index-legacy.CNmEMj-H.js.map

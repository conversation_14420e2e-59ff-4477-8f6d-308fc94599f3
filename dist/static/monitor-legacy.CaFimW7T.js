System.register(["./index-legacy.C52nWfoy.js","./index-legacy.CAqey3Xi.js","./index-legacy.Co5M3uHU.js","./index-legacy.x5ItpLKU.js","./index-legacy.CNmEMj-H.js"],(function(e,t){"use strict";var l,a,r,n,d,i,o,s,u,p,c,f,_,h,m,b,y,w,g,v,A,T,G,x,k,C,I,D,E,S,V;return{setters:[e=>{l=e.H,a=e.e,r=e.p,n=e.J,d=e.a,i=e.L,o=e.a3,s=e.o,u=e.C,p=e.w,c=e.v,f=e.l,_=e.j,h=e.x,m=e.t,b=e.a4,y=e.a5,w=e.h,g=e.F,v=e.r,A=e.a6,T=e.a7,G=e.a8,x=e.a9,k=e.a1,C=e.a2},e=>{I=e.E,D=e.b,E=e.a},e=>{S=e.E},e=>{V=e.E},null],execute:function(){const t={class:"index-conntainer"},M={class:"ar-container"},j=a({__name:"monitor",props:{newTableData:{type:Array,required:!0}},setup(e){const l=r("first"),a=(e,t)=>{console.log(e,t)};r(1),r("");const j=r("");r(0),r(10),r(!0),r([]),r("chatbot");const U=n([]),R=n([]);n(["chatbot","inhouse","channel","data"]);const F=new Set,L=n([]),$=r(),z=n([]),q=n([]),H=d((()=>j.value?z.filter((e=>e.TAG===j.value)):z)),J=(e,t,l)=>t[l.property]===e,B=({row:e})=>"FAILURE"===e.build_result?"warning-row":"",K=async()=>{const e=(await G()).data.data;q.splice(0,q.length),z.splice(0,z.length),L.splice(0,L.length),L.push(...e);const t=function(e){const t=n([]),l=new Map;for(let a=0;a<e.length;a++){const t=e[a],{pipeline_name:r,end_time:n,build_result:d}=t,i=l.get(r);(!i||new Date(n)>new Date(i))&&l.set(r,n)}for(let a=0;a<e.length;a++){const r=e[a],{pipeline_name:n,end_time:d,build_result:i}=r;"FAILURE"===i&&d===l.get(n)&&t.push(r)}return t}(L);q.push(...t),L.forEach(((e,t)=>{e.TAG&&!F.has(e.TAG)&&(F.add(e.TAG),U.push({text:e.TAG,value:e.TAG}),R.push(e.TAG));const l={pipeline_name:e.pipeline_name,build_type:e.build_type,duration:e.duration,CID:e.CID,start_time:e.start_time,end_time:e.end_time,TAG:e.TAG,space_link:e.space_link,executor:e.executor,build_result:e.build_result,err_msg:e.err_msg,index:e.index};e.TAG&&!F.has(e.TAG)&&(F.add(e.TAG),U.push({text:e.TAG,value:e.TAG}),R.push(e.TAG)),z.push(l)}))},N=r(null);i((async()=>{K();const e=setInterval(K,6e4);N.value=e})),o((()=>{clearInterval(N.value)}));const O=e;r({}),d((()=>{const e={};for(const t of O.newTableData){const l=t.start_time.split(" ")[0];if(console.log(l),!e[l]){const t=`hsl(${Math.floor(360*Math.random())}, ${Math.floor(30*Math.random())+70}%, ${Math.floor(20*Math.random())+70}%)`;e[l]=t}}return e}));const P=({row:e})=>parseInt(e.index)%2==0?"success-row":"primary-row";return(e,r)=>{const n=S,d=x,i=V,o=k,G=C;return s(),u(G,{modelValue:l.value,"onUpdate:modelValue":r[1]||(r[1]=e=>l.value=e),class:"demo-tabs",onTabClick:a,type:"border-card"},{default:p((()=>[c(o,{label:"服务",name:"first"},{default:p((()=>[f("div",t,[c(_(I),{shadow:"hover"},{default:p((()=>[c(_(D),{ref_key:"tableRef",ref:$,data:q,columns:e.tableColumns,"row-class-name":B,style:{width:"auto",height:"100%"},border:"","header-cell-style":{background:"#ee4d2d",color:"#f3f1f6"}},{default:p((()=>[c(_(E),{prop:"start_time",label:"部署生效时间",sortable:"","default-sort":{prop:"start_time",order:"descending"}}),c(_(E),{prop:"end_time",label:"任务结束时间",sortable:"","default-sort":{prop:"end_time",order:"descending"}}),c(_(E),{prop:"pipeline_name",label:"服务","min-width":200,style:{"white-space":"nowrap","min-width":0}},{default:p((({row:e})=>[c(n,{underline:!1,href:e.space_link,target:"_blank",type:"primary"},{default:p((()=>[h(m(e.pipeline_name),1)])),_:2},1032,["href"])])),_:1}),c(_(E),{prop:"duration",label:"部署间隔","min-width":60,"header-":""}),c(_(E),{prop:"build_type",label:"类型","min-width":55,"header-":""},{default:p((({row:e})=>[c(_(b),{type:"灰度发布"===e.build_type?"info":"全量发布"===e.build_type?"success":"warning"},{default:p((()=>[h(m(e.build_type),1)])),_:2},1032,["type"])])),_:1}),c(_(E),{prop:"build_result",label:"部署结果","min-width":50,"header-":""},{default:p((({row:e})=>[c(_(b),{type:"SUCCESS"===e.build_result?"success":"danger"},{default:p((()=>[h(m(e.build_result),1)])),_:2},1032,["type"])])),_:1}),c(_(E),{prop:"CID",label:"部署地区","header-":""}),c(_(E),{prop:"TAG",label:"TAG",filters:U,"filter-method":J,"filtered-value":e.filteredValue,"filter-placement":"bottom-end","header-":""},{default:p((({row:e})=>[c(_(b),{key:"string",type:"warning",class:"mx-1",effect:"light"},{default:p((()=>[h(m(e.TAG),1)])),_:2},1024)])),_:1},8,["filters","filtered-value"]),c(_(E),{prop:"executor",label:"部署触发人","min-width":65,"header-":""})])),_:1},8,["data","columns"]),f("div",M,[c(_(y),{modelValue:j.value,"onUpdate:modelValue":r[0]||(r[0]=e=>j.value=e),filterable:"",placeholder:"请选择TAG来进行筛选",clearable:""},{default:p((()=>[(s(!0),w(g,null,v(R,(e=>(s(),u(_(A),{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),c(d,{size:20},{default:p((()=>[c(_(T))])),_:1}),c(i,{class:"mx-1",type:"danger",size:"default"},{default:p((()=>[h("注意，同一天发布的服务背景色是统一的")])),_:1})]),c(_(D),{ref_key:"tableRef",ref:$,data:H.value,columns:e.tableColumns,"row-class-name":P,style:{width:"auto",height:"100%"},border:"","header-cell-style":{background:"#eef1f6",color:"#606266"}},{default:p((()=>[c(_(E),{prop:"start_time",label:"部署生效时间",sortable:"","default-sort":{prop:"start_time",order:"descending"}}),c(_(E),{prop:"end_time",label:"任务结束时间",sortable:"","default-sort":{prop:"end_time",order:"descending"}}),c(_(E),{prop:"pipeline_name",label:"服务","min-width":200,style:{"white-space":"nowrap","min-width":0}},{default:p((({row:e})=>[c(n,{underline:!1,href:e.space_link,target:"_blank",type:"primary"},{default:p((()=>[h(m(e.pipeline_name),1)])),_:2},1032,["href"])])),_:1}),c(_(E),{prop:"duration",label:"部署间隔","min-width":60,"header-":""}),c(_(E),{prop:"build_type",label:"类型","min-width":55,"header-":""},{default:p((({row:e})=>[c(_(b),{type:"灰度发布"===e.build_type?"info":"全量发布"===e.build_type?"success":"warning"},{default:p((()=>[h(m(e.build_type),1)])),_:2},1032,["type"])])),_:1}),c(_(E),{prop:"build_result",label:"部署结果","min-width":50,"header-":""},{default:p((({row:e})=>[c(_(b),{type:"SUCCESS"===e.build_result?"success":"danger"},{default:p((()=>[h(m(e.build_result),1)])),_:2},1032,["type"])])),_:1}),c(_(E),{prop:"CID",label:"部署地区","header-":""}),c(_(E),{prop:"TAG",label:"TAG",filters:U,"filter-method":J,"filtered-value":e.filteredValue,"filter-placement":"bottom-end","header-":""},{default:p((({row:e})=>[c(_(b),{key:"string",type:"warning",class:"mx-1",effect:"light"},{default:p((()=>[h(m(e.TAG),1)])),_:2},1024)])),_:1},8,["filters","filtered-value"]),c(_(E),{prop:"executor",label:"部署触发人","min-width":65,"header-":""})])),_:1},8,["data","columns"])])),_:1})])])),_:1}),c(o,{label:"配置",name:"second"})])),_:1},8,["modelValue"])}}});e("default",l(j,[["__scopeId","data-v-58091246"]]))}}}));
//# sourceMappingURL=monitor-legacy.CaFimW7T.js.map

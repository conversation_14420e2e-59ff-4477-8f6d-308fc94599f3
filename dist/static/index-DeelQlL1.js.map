{"version": 3, "file": "index-DeelQlL1.js", "sources": ["../../node_modules/element-plus/es/components/text/src/text.mjs", "../../node_modules/element-plus/es/components/text/src/text2.mjs", "../../node_modules/element-plus/es/components/text/index.mjs"], "sourcesContent": ["import '../../../utils/index.mjs';\nimport '../../../constants/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { componentSizes } from '../../../constants/size.mjs';\n\nconst textProps = buildProps({\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"info\", \"warning\", \"danger\", \"\"],\n    default: \"\"\n  },\n  size: {\n    type: String,\n    values: componentSizes,\n    default: \"\"\n  },\n  truncated: {\n    type: Boolean\n  },\n  lineClamp: {\n    type: [String, Number]\n  },\n  tag: {\n    type: String,\n    default: \"span\"\n  }\n});\n\nexport { textProps };\n//# sourceMappingURL=text.mjs.map\n", "import { defineComponent, computed, openBlock, createBlock, resolveDynamicComponent, normalizeClass, unref, normalizeStyle, withCtx, renderSlot } from 'vue';\nimport '../../../hooks/index.mjs';\nimport '../../form/index.mjs';\nimport '../../../utils/index.mjs';\nimport { textProps } from './text.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useFormSize } from '../../form/src/hooks/use-form-common-props.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isUndefined } from '../../../utils/types.mjs';\n\nconst __default__ = defineComponent({\n  name: \"ElText\"\n});\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: textProps,\n  setup(__props) {\n    const props = __props;\n    const textSize = useFormSize();\n    const ns = useNamespace(\"text\");\n    const textKls = computed(() => [\n      ns.b(),\n      ns.m(props.type),\n      ns.m(textSize.value),\n      ns.is(\"truncated\", props.truncated),\n      ns.is(\"line-clamp\", !isUndefined(props.lineClamp))\n    ]);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        class: normalizeClass(unref(textKls)),\n        style: normalizeStyle({ \"-webkit-line-clamp\": _ctx.lineClamp })\n      }, {\n        default: withCtx(() => [\n          renderSlot(_ctx.$slots, \"default\")\n        ]),\n        _: 3\n      }, 8, [\"class\", \"style\"]);\n    };\n  }\n});\nvar Text = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"text.vue\"]]);\n\nexport { Text as default };\n//# sourceMappingURL=text2.mjs.map\n", "import '../../utils/index.mjs';\nimport Text from './src/text2.mjs';\nexport { textProps } from './src/text.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\n\nconst ElText = withInstall(Text);\n\nexport { ElText, ElText as default };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["textProps", "buildProps", "type", "String", "values", "default", "size", "componentSizes", "truncated", "Boolean", "lineClamp", "Number", "tag", "__default__", "defineComponent", "name", "ElText", "withInstall", "_export_sfc", "props", "setup", "__props", "textSize", "useFormSize", "ns", "useNamespace", "textKls", "computed", "b", "m", "value", "is", "isUndefined", "_ctx", "_cache", "openBlock", "createBlock", "resolveDynamicComponent", "class", "normalizeClass", "unref", "style", "normalizeStyle", "withCtx", "renderSlot", "$slots", "_"], "mappings": "wJAKA,MAAMA,EAAYC,EAAW,CAC3BC,KAAM,CACJA,KAAMC,OACNC,OAAQ,CAAC,UAAW,UAAW,OAAQ,UAAW,SAAU,IAC5DC,QAAS,IAEXC,KAAM,CACJJ,KAAMC,OACNC,OAAQG,EACRF,QAAS,IAEXG,UAAW,CACTN,KAAMO,SAERC,UAAW,CACTR,KAAM,CAACC,OAAQQ,SAEjBC,IAAK,CACHV,KAAMC,OACNE,QAAS,UCdPQ,EAAcC,EAAgB,CAClCC,KAAM,WCNH,MAACC,EAASC,EDmCYC,EA3BOJ,EAAgB,IAC7CD,EACHM,MAAOnB,EACP,KAAAoB,CAAMC,GACJ,MAAMF,EAAQE,EACRC,EAAWC,IACXC,EAAKC,EAAa,QAClBC,EAAUC,GAAS,IAAM,CAC7BH,EAAGI,IACHJ,EAAGK,EAAEV,EAAMjB,MACXsB,EAAGK,EAAEP,EAASQ,OACdN,EAAGO,GAAG,YAAaZ,EAAMX,WACzBgB,EAAGO,GAAG,cAAeC,EAAYb,EAAMT,eAEzC,MAAO,CAACuB,EAAMC,KACLC,IAAaC,EAAYC,EAAwBJ,EAAKrB,KAAM,CACjE0B,MAAOC,EAAeC,EAAMd,IAC5Be,MAAOC,EAAe,CAAE,qBAAsBT,EAAKvB,aAClD,CACDL,QAASsC,GAAQ,IAAM,CACrBC,EAAWX,EAAKY,OAAQ,cAE1BC,EAAG,GACF,EAAG,CAAC,QAAS,UAEnB,IAE+C,CAAC,CAAC,SAAU", "x_google_ignoreList": [0, 1, 2]}
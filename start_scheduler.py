#!/usr/bin/env python3
"""
定时任务调度器启动脚本
用于启动用户定时任务执行器
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

# 导入Django管理命令
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    print("🚀 启动ChatbotAR定时任务调度器...")
    print("💡 使用 Ctrl+C 停止调度器")
    print("📖 更多选项: python manage.py run_scheduled_tasks --help")
    print("-" * 50)
    
    # 执行Django管理命令
    execute_from_command_line([
        'manage.py', 
        'run_scheduled_tasks',
        '--interval', '60',  # 默认60秒检查一次
        '--verbose'
    ]) 
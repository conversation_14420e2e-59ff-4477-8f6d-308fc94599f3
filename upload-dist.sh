#!/bin/bash

# 构建项目
yarn run build

# 判断构建是否成功
if [ $? -ne 0 ]; then
    echo "构建失败"
    echo "上传失败"
    exit 1
fi

# 压缩 dist 文件夹为 dist.zip
rm -rf dist.zip
zip -r dist.zip dist

# 判断压缩是否成功
if [ $? -ne 0 ]; then
    echo "压缩失败"
    echo "上传失败"
    exit 1
fi

# 执行上传命令（请将 "smc ftp **" 替换为实际的上传命令）
# 示例：smc ftp dist.zip /path/to/destination
# 请根据你的实际情况填写上传命令

# 假设上传命令为 smc ftp dist.zip /path/to/destination
smc sftp *************:/home/<USER>/SDE <<< "put /Users/<USER>/git/chatbot-ar-fe/dist.zip"
# 判断上传是否成功
if [ $? -eq 0 ]; then
    echo "上传成功"
else
    echo "上传失败"
fi

#!/bin/bash

# ChatbotAR 高级功能快速部署脚本
# 用于快速部署和配置高级定时任务功能

set -e

echo "🚀 ChatbotAR 高级功能部署脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    if ! command -v python &> /dev/null; then
        log_error "Python未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    python_version=$(python --version 2>&1 | cut -d' ' -f2)
    log_success "Python版本: $python_version"
}

# 检查Django环境
check_django() {
    log_info "检查Django环境..."
    if ! python -c "import django" &> /dev/null; then
        log_error "Django未安装，请先安装Django"
        exit 1
    fi
    
    django_version=$(python -c "import django; print(django.get_version())")
    log_success "Django版本: $django_version"
}

# 执行数据库迁移
run_migrations() {
    log_info "执行数据库迁移..."
    
    # 生成迁移文件
    log_info "生成迁移文件..."
    python manage.py makemigrations
    
    # 执行迁移
    log_info "执行数据库迁移..."
    python manage.py migrate
    
    log_success "数据库迁移完成"
}

# 初始化任务模板
init_templates() {
    log_info "初始化任务模板..."
    
    if python manage.py init_task_templates; then
        log_success "任务模板初始化完成"
    else
        log_warning "任务模板初始化失败，请手动执行"
    fi
}

# 创建示例白名单用户
create_sample_whitelist() {
    log_info "是否创建示例白名单用户？(y/n)"
    read -r create_sample
    
    if [[ $create_sample == "y" || $create_sample == "Y" ]]; then
        echo "请输入用户ID:"
        read -r user_id
        
        echo "请输入用户邮箱 (可选):"
        read -r user_email
        
        echo "请输入员工代码 (可选):"
        read -r employee_code
        
        echo "选择权限类型:"
        echo "1) 所有高级功能 (all_features)"
        echo "2) 群聊通知 (group_notification)"
        echo "3) 任务模板 (task_template)"
        echo "4) 批量管理 (batch_management)"
        echo "5) 统计报表 (statistics_report)"
        echo "6) 自定义组合"
        read -r permission_choice
        
        case $permission_choice in
            1) features="all_features" ;;
            2) features="group_notification" ;;
            3) features="task_template" ;;
            4) features="batch_management" ;;
            5) features="statistics_report" ;;
            6) 
                echo "请输入功能列表 (用逗号分隔):"
                read -r features
                ;;
            *) 
                log_warning "无效选择，使用默认权限 (task_template)"
                features="task_template"
                ;;
        esac
        
        # 构建命令
        cmd="python manage.py manage_whitelist add --user-id \"$user_id\" --features \"$features\""
        
        if [[ -n $user_email ]]; then
            cmd="$cmd --user-email \"$user_email\""
        fi
        
        if [[ -n $employee_code ]]; then
            cmd="$cmd --employee-code \"$employee_code\""
        fi
        
        cmd="$cmd --granted-by \"setup_script\""
        
        log_info "执行命令: $cmd"
        if eval $cmd; then
            log_success "示例白名单用户创建成功"
        else
            log_error "示例白名单用户创建失败"
        fi
    fi
}

# 检查系统状态
check_system_status() {
    log_info "检查系统状态..."
    
    # 检查模型
    log_info "检查数据模型..."
    if python -c "from app01.models import UserScheduledTask, TaskTemplate, AdvancedTaskFeatureWhitelist; print('✅ 模型导入成功')" 2>/dev/null; then
        log_success "数据模型检查通过"
    else
        log_error "数据模型检查失败"
        return 1
    fi
    
    # 检查高级功能管理器
    log_info "检查高级功能管理器..."
    if python -c "from app01.ai_module.advanced_task_manager import advanced_task_manager; print('✅ 高级功能管理器导入成功')" 2>/dev/null; then
        log_success "高级功能管理器检查通过"
    else
        log_error "高级功能管理器检查失败"
        return 1
    fi
    
    # 统计信息
    log_info "系统统计信息:"
    python -c "
from app01.models import TaskTemplate, AdvancedTaskFeatureWhitelist
print(f'📋 任务模板数量: {TaskTemplate.objects.count()}')
print(f'👥 白名单用户数量: {AdvancedTaskFeatureWhitelist.objects.filter(is_active=True).count()}')
"
}

# 显示使用说明
show_usage() {
    log_info "部署完成！使用说明："
    echo ""
    echo "🎯 基本命令格式："
    echo "   advanced <功能> <操作> [参数]"
    echo ""
    echo "📢 群聊通知："
    echo "   advanced group create \"任务名\" \"查询\" \"调度\" \"群组ID\""
    echo ""
    echo "📋 任务模板："
    echo "   advanced template list"
    echo "   advanced template create from <ID> \"任务名\""
    echo ""
    echo "🔄 批量管理："
    echo "   advanced batch pause 1,2,3"
    echo "   advanced batch resume 1,2,3"
    echo ""
    echo "📊 统计报表："
    echo "   advanced stats"
    echo ""
    echo "🎫 权限管理："
    echo "   advanced whitelist status"
    echo ""
    echo "📚 管理员命令："
    echo "   python manage.py manage_whitelist list"
    echo "   python manage.py init_task_templates"
    echo ""
    echo "📖 详细文档请参考: ADVANCED_FEATURES_DEPLOYMENT.md"
}

# 主函数
main() {
    echo ""
    log_info "开始部署高级功能..."
    echo ""
    
    # 检查环境
    check_python
    check_django
    
    # 执行部署步骤
    run_migrations
    init_templates
    create_sample_whitelist
    
    # 检查系统状态
    if check_system_status; then
        log_success "系统状态检查通过"
    else
        log_error "系统状态检查失败，请检查配置"
        exit 1
    fi
    
    # 显示使用说明
    show_usage
    
    echo ""
    log_success "🎉 高级功能部署完成！"
    echo ""
}

# 脚本选项处理
case "${1:-}" in
    --help|-h)
        echo "ChatbotAR 高级功能部署脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h     显示帮助信息"
        echo "  --check        仅检查系统状态"
        echo "  --migrate      仅执行数据库迁移"
        echo "  --templates    仅初始化模板"
        echo ""
        exit 0
        ;;
    --check)
        check_python
        check_django
        check_system_status
        exit 0
        ;;
    --migrate)
        check_python
        check_django
        run_migrations
        exit 0
        ;;
    --templates)
        check_python
        check_django
        init_templates
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 --help 查看帮助信息"
        exit 1
        ;;
esac 
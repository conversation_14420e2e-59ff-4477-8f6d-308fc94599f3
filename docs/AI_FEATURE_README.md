# ${BOT_NAME} AI 增强功能使用指南

## 概述

${BOT_NAME} AI 增强功能为现有的聊天机器人添加了基于大语言模型(LLM)的智能JIRA查询能力。用户可以使用自然语言进行JIRA查询，AI会理解用户意图，自动生成JQL查询并格式化结果。

## 主要功能

- 🔍 **智能JIRA查询**：使用自然语言查询JIRA数据
- 📊 **数据统计分析**：按多维度统计和分析JIRA数据
- 📝 **JIRA写操作**：支持状态变更、评论、创建子任务等
- ⏰ **定时任务管理**：设置定期查询和通知
- 📄 **文档处理**：读取、总结、翻译Confluence文档

## 使用方法

### 基本语法

在群聊中：
```
@${BOT_NAME} /ai <你的查询>
```

在私聊中：
```
/ai <你的查询>
```

### 示例查询

```
@${BOT_NAME} /ai 查看SPCB-1234的状态
@${BOT_NAME} /ai 这周有哪些高优先级的bug
@${BOT_NAME} /ai 分配给我的未完成任务
@${BOT_NAME} /ai 查询Epic SPCB-5678的进度
```

## 功能详解

### 1. 智能JIRA查询

支持多种查询方式：

- **单号查询**：`查询SPCB-1234的状态`
- **人员查询**：`我的任务`、`分配给zhang.san的bug`
- **状态查询**：`未完成的任务`、`已关闭的bug`
- **时间查询**：`上周完成的工作`、`本月新建的需求`
- **优先级查询**：`高优先级bug`、`紧急任务`
- **复合查询**：`本周我完成的高优先级任务`

### 2. 数据统计分析

支持多维度统计：

- **数量统计**：`我今天完成了几个任务`
- **状态分布**：`我的任务状态分布`
- **优先级分析**：`bug的优先级分布`
- **趋势分析**：`最近两周的bug趋势`

### 3. JIRA写操作

支持以下写操作：

- **状态变更**：`把SPCB-1234改为Done`
- **添加评论**：`给SPCB-1234添加评论：测试已完成`
- **创建子任务**：`在SPCB-1234下创建子任务：完成单元测试`

### 4. 定时任务管理

支持创建和管理个人定时任务：

- **创建任务**：`schedule create "每日任务报告" "查询我今天的任务" "每天 09:00"`
- **查看任务**：`schedule list`
- **暂停任务**：`schedule pause 1`
- **恢复任务**：`schedule resume 1`
- **删除任务**：`schedule delete 1`

### 5. 文档处理

支持Confluence文档处理：

- **文档总结**：`总结这个文档 https://confluence.example.com/pages/123`
- **文档翻译**：`翻译SPCB-1234的PRD为英文`
- **文档问答**：`SPCB-1234的PRD中关于API的部分说了什么`

## 高级功能

### 上下文理解

系统能够理解对话上下文，支持多轮对话：

```
用户: /ai 查询我的bug
AI: [返回bug列表]
用户: /ai 只看高优先级的
AI: [返回高优先级bug]
用户: /ai 按状态分组
AI: [返回按状态分组的结果]
```

### 群组上下文

在特定JIRA单号的群组中，系统会自动识别群名中的单号：

```
在"SPCB-1234-开发群"中:
用户: @${BOT_NAME} /ai 这个需求的状态
AI: [返回SPCB-1234的状态]
```

### 个性化配置

用户可以配置个人偏好：

```
/ai config jira_token YOUR_JIRA_TOKEN  # 配置JIRA Token
/ai config default_project SPCB        # 设置默认项目
/ai config language zh                 # 设置语言偏好
```

## 常见问题

### Q: 如何获取JIRA Token?

A: 访问JIRA个人设置 → 安全 → 创建API Token

### Q: 为什么需要添加"/ai"前缀?

A: "/ai"前缀用于区分AI功能和传统命令

### Q: 查询结果不准确怎么办?

A: 尝试提供更具体的查询条件，或使用更明确的关键词

### Q: 定时任务不工作怎么办?

A: 检查任务状态，确认时间格式正确，必要时重新创建

## 技术支持

如有问题，请联系:

- 项目仓库: [${BOT_NAME} Repository]
- 技术支持: <EMAIL>

---

**版本**: 1.0.0
**更新日期**: 2023-12-01 
# SeaTalk 用户邮箱获取方案分析

## 🔍 现状分析

### 1. **SeaTalk 消息回调数据结构**
根据现有代码分析，SeaTalk 的消息回调包含：

#### 私聊消息 (`message_from_bot_subscriber`)
```json
{
  "event_type": "message_from_bot_subscriber",
  "event": {
    "employee_code": "23774",  // 只有employee_code
    "message": { ... }
  }
}
```

#### 群聊消息
```json
{
  "event_type": "message_from_group",
  "event": {
    "message": {
      "sender": {
        "email": "<EMAIL>",  // 直接包含真实邮箱！
        "employee_code": "23774",
        "name": "<PERSON>"
      }
    }
  }
}
```

### 2. **问题根源**
- **私聊**：只有 `employee_code`，没有真实邮箱
- **群聊**：有完整的用户信息，包括真实邮箱
- **当前做法**：私聊时用 `employee_code` 拼接 `@shopee.com`，导致错误邮箱

---

## 💡 解决方案对比

### 方案一：映射表维护（当前临时方案）
```python
known_mappings = {
    '23774': '<EMAIL>',
    '348162': '<EMAIL>',
    '122033': '<EMAIL>',
}
```

**优点**：
- ✅ 立即生效
- ✅ 实现简单

**缺点**：
- ❌ 维护困难
- ❌ 新用户需要手动添加
- ❌ 无法扩展

### 方案二：SeaTalk API 反查（推荐）
使用现有的 `get_employee_codes` API 反向查询

```python
async def get_user_email_by_employee_code(employee_code: str) -> Optional[str]:
    """通过employee_code反查真实邮箱"""
    try:
        # 调用SeaTalk API获取用户详细信息
        url = "https://openapi.seatalk.io/contacts/v2/get_user_info_by_employee_code"
        # 或者使用现有的反向查询逻辑
        return await reverse_lookup_email(employee_code)
    except Exception as e:
        logger.error(f"反查邮箱失败: {str(e)}")
        return None
```

**优点**：
- ✅ 自动化，无需维护
- ✅ 支持所有用户
- ✅ 数据准确性高

**缺点**：
- ❌ 需要额外API调用
- ❌ 可能有性能开销

### 方案三：缓存 + API 混合（最佳）
结合缓存和API调用，优化性能

```python
class UserEmailCache:
    def __init__(self):
        self.cache = {}  # employee_code -> email
        self.cache_ttl = 3600  # 1小时过期
    
    async def get_email(self, employee_code: str) -> Optional[str]:
        # 1. 先查缓存
        if employee_code in self.cache:
            return self.cache[employee_code]
        
        # 2. API反查
        email = await self._api_lookup(employee_code)
        if email:
            self.cache[employee_code] = email
        
        return email
```

---

## 🚀 推荐实现方案

### 第一步：创建用户信息获取工具类
```python
class SeatalkUserInfoManager:
    """SeaTalk用户信息管理器"""
    
    def __init__(self):
        self.email_cache = {}
        self.cache_ttl = 3600
    
    async def get_user_email(self, data: dict, employee_code: str = None) -> str:
        """智能获取用户邮箱"""
        
        # 1. 群聊：直接从sender获取
        if data.get("event_type") != "message_from_bot_subscriber":
            sender_info = data.get("event", {}).get("message", {}).get("sender", {})
            email = sender_info.get("email")
            if email:
                return email
        
        # 2. 私聊：通过employee_code反查
        if employee_code:
            return await self._get_email_by_employee_code(employee_code)
        
        return None
    
    async def _get_email_by_employee_code(self, employee_code: str) -> Optional[str]:
        """通过employee_code获取真实邮箱"""
        
        # 检查缓存
        if employee_code in self.email_cache:
            cache_data = self.email_cache[employee_code]
            if time.time() - cache_data['timestamp'] < self.cache_ttl:
                return cache_data['email']
        
        # API查询
        try:
            # 方法1：使用现有的批量查询API（需要已知邮箱）
            # 方法2：调用用户详情API（如果存在）
            # 方法3：使用企业通讯录API
            
            email = await self._api_lookup_email(employee_code)
            
            # 更新缓存
            if email:
                self.email_cache[employee_code] = {
                    'email': email,
                    'timestamp': time.time()
                }
            
            return email
            
        except Exception as e:
            logger.error(f"API查询邮箱失败: {str(e)}")
            return None
```

### 第二步：修改views.py
```python
# 初始化用户信息管理器
user_info_manager = SeatalkUserInfoManager()

async def handle_ai_query_async(ai_query: str, seatalk_id: str, group_id: str, data: dict):
    # ... existing code ...
    
    # 智能获取用户邮箱
    user_email = await user_info_manager.get_user_email(data, employee_code)
    
    # ... rest of the code ...
```

---

## 🔧 立即可行的方案

由于SeaTalk API的限制，目前最可行的方案是：

### 1. **短期方案**：扩展映射表
```python
# 在views.py中创建动态映射表
class UserEmailMapping:
    def __init__(self):
        # 核心用户映射
        self.core_mappings = {
            '23774': '<EMAIL>',
            '348162': '<EMAIL>', 
            '122033': '<EMAIL>',
        }
        
        # 动态缓存
        self.dynamic_cache = {}
    
    def get_email(self, employee_code: str) -> Optional[str]:
        # 1. 核心用户
        if employee_code in self.core_mappings:
            return self.core_mappings[employee_code]
        
        # 2. 动态缓存
        if employee_code in self.dynamic_cache:
            return self.dynamic_cache[employee_code]
        
        # 3. 尝试从其他来源获取
        return self._try_get_email(employee_code)
```

### 2. **中期方案**：建立用户信息同步机制
- 定期同步企业通讯录
- 建立用户信息数据库表
- 提供管理界面

### 3. **长期方案**：优化架构
- 统一用户身份识别
- 集成企业身份系统
- 提供用户管理API

---

## 📋 实施建议

### 立即执行（今天）：
1. 创建 `SeatalkUserInfoManager` 类
2. 实现核心用户映射 + 动态缓存
3. 修改 `views.py` 使用新的邮箱获取逻辑

### 本周内：
1. 添加更多用户映射
2. 实现用户信息缓存机制
3. 添加日志和监控

### 本月内：
1. 建立用户信息数据库表
2. 提供用户管理界面
3. 实现自动同步机制

---

## 🎯 测试验证

修改后需要验证：
1. **群聊**：确认能正确获取真实邮箱
2. **私聊**：确认核心用户能正确映射
3. **权限**：确认超级管理员身份正确识别
4. **性能**：确认不影响响应速度

---

**结论**：建议采用 **方案三（混合方案）**，先实现核心用户映射，再逐步完善API反查和缓存机制。 
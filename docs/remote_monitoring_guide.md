# 废弃函数远程监控指南

## 🚀 部署后监控步骤

### 1. 立即检查（部署后立即执行）
```bash
# 登录远程服务器
ssh user@your-server

# 进入项目目录
cd /path/to/chatbot-ar-be

# 运行监控脚本检查状态
python3 monitor_deprecated_functions.py

# Django健康检查
python3 manage.py check

# 测试导入views模块
python3 -c "import app01.views; print('✅ Views模块导入成功')"
```

### 2. 日志监控（持续监控）
```bash
# 实时监控废弃警告
tail -f logs/*.log | grep -i "deprecat\|warning"

# 检查已有日志中的警告
grep -i "deprecationwarning" logs/*.log

# 监控特定时间范围的日志
tail -n 1000 logs/crontab.log | grep -i deprecat
```

### 3. 定期检查（建议每3天运行一次）
```bash
# 检查废弃函数是否被调用
python3 monitor_deprecated_functions.py

# 检查最近日志
find logs/ -name "*.log" -mtime -3 -exec grep -l "deprecat" {} \;
```

## ⚠️ 关键监控点

### 如果看到 DeprecationWarning：
1. **不要立即删除函数** - 说明有地方还在使用
2. **找到调用源** - 检查日志中的堆栈跟踪
3. **先修改调用代码** - 替换为新的实现
4. **再次监控** - 确认警告消失后才删除

### 如果1-2周无任何警告：
1. **从最安全的开始删除**：
   - `timeformat` (极低风险)
   - `failuremsg_old` (低风险)  
   - `failuremsg` (低风险)
   - `MRnotdeal_20230726` (低风险)
   - `mergefromfeature` (中等风险，最后删除)

## 🔧 监控命令速查

```bash
# 快速状态检查
python3 monitor_deprecated_functions.py

# 日志警告检查  
grep -r "deprecat" logs/ --include="*.log" | tail -10

# Django状态检查
python3 manage.py check

# 导入测试
python3 -c "import app01.views"
```

## 📅 监控时间表

- **第1天**: 部署后立即检查
- **第3天**: 首次定期检查
- **第7天**: 一周检查
- **第14天**: 如无警告，考虑开始删除 
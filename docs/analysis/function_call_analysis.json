{"project": "chatbot-ar-be", "analysis_date": "2025-06-11", "analysis_type": "function_call_relationships", "url_routes": {"description": "djangoProject/urls.py中定义的所有路由", "routes": [{"path": "deploy", "view": "views.deploy", "name": "deploy"}, {"path": "apiCallback", "view": "views.apiCallback", "name": "apiCallback"}, {"path": "api/merge", "view": "views.merge", "name": "merge"}, {"path": "api/tag", "view": "views.tag", "name": "tag"}, {"path": "api/read_data_json", "view": "views.read_data_json", "name": "read_data_json", "status": "active", "note": "前端在使用此接口"}, {"path": "api/create_jira_ticket", "view": "views.create_jira_ticket", "name": "create_jira_ticket"}, {"path": "api/new_read_data_json", "view": "views.new_read_data_json", "name": "new_read_data_json", "status": "deprecated", "note": "前端不在使用此接口，数据较少"}, {"path": "api/log_analysis", "view": "views.log_analysis", "name": "log_analysis"}, {"path": "api/get_jira_release_list", "view": "views.get_jira_release_list", "name": "get_jira_release_list"}, {"path": "api/send_title", "view": "views.get_single_release_by_title", "name": "get_single_release_by_title"}, {"path": "api/pod_update", "view": "views.pod_update", "name": "pod_update"}, {"path": "api/startAuto", "view": "views.startAuto", "name": "startAuto"}, {"path": "api/seatalk", "view": "views.seatalk", "name": "seatalk"}, {"path": "api/autocheckdata", "view": "views.autocheckdata", "name": "autocheckdata"}, {"path": "api/getCalendarJiraReleaseList", "view": "views.getCalendarJiraReleaseList", "name": "getCalendarJiraReleaseList"}, {"path": "api/getAllJiraReleaseList", "view": "views.getAllJiraReleaseList", "name": "getAllJiraReleaseList"}, {"path": "api/get_all_jira_release_list_details", "view": "views.get_all_jira_release_list_details", "name": "get_all_jira_release_list_details"}, {"path": "api/get_seatalk_recall", "view": "views.get_seatalk_recall", "name": "get_seatalk_recall"}, {"path": "api/real_time_push_message", "view": "views.real_time_push_message", "name": "real_time_push_message"}, {"path": "api/jira_seatalk", "view": "views.jira_seatalk", "name": "jira_seatalk"}, {"path": "api/mr_seatalk_msg", "view": "views.mr_seatalk_msg", "name": "mr_seatalk_msg"}, {"path": "api/mr_copy_msg", "view": "views.mr_copy_msg", "name": "mr_copy_msg"}, {"path": "api/autochecknewdata", "view": "views.autochecknewdata", "name": "autochecknewdata"}, {"path": "api/get_jira_release_list_finished", "view": "views.get_jira_release_list_finished", "name": "get_jira_release_list_finished"}, {"path": "api/start_single_ar", "view": "views.start_single_ar", "name": "start_single_ar"}, {"path": "api/mr_seatalk_single_feature_msg", "view": "views.mr_seatalk_single_feature_msg", "name": "mr_seatalk_single_feature_msg"}, {"path": "api/get_key_jira_release_list", "view": "views.get_key_jira_release_list", "name": "get_key_jira_release_list"}, {"path": "api/get_all_release_by_title_api", "view": "views.get_all_release_by_title_api", "name": "get_all_release_by_title_api"}, {"path": "api/signed_off_seatalk", "view": "views.signed_off_seatalk", "name": "signed_off_seatalk"}, {"path": "api/ar_start_merge", "view": "views.ar_start_merge", "name": "ar_start_merge"}, {"path": "api/get_unreleased_versions", "view": "views.get_unreleased_versions", "name": "get_unreleased_versions"}, {"path": "api/check-timeline/", "view": "views.check_timeline_changes_route", "name": "check_timeline"}, {"path": "api/epic-reminder/", "view": "views.epic_reminder_manual_trigger", "name": "epic_reminder_manual_trigger"}], "total_routes": 31}, "cron_jobs": {"description": "djangoProject/settings.py中定义的定时任务", "jobs": [{"schedule": "0 * * * *", "function": "app01.views.callback", "description": "每小时整点执行回调函数"}, {"schedule": "* * * * *", "function": "app01.views.save_releases", "description": "每分钟执行一次，更新DB里的历史发布单数据"}, {"schedule": "0 10,15,18 * * *", "function": "app01.views.failuremsg_final", "description": "每天10点、15点、18点发送服务部署失败的消息提醒"}, {"schedule": "0 10-19 * * *", "function": "app01.views.MRnotdeal", "description": "每天10-19点整点执行，提醒 TL 合并未处理的MR（合并请求）"}, {"schedule": "0 9-19 * * *", "function": "app01.views.MRnotdealchannel_new", "description": "每天9-19点整点执行，处理新的MR消息（CS channel 组）"}, {"schedule": "0 9-19 * * *", "function": "app01.views.MRnotdeal_data", "description": "每天9-19点整点执行，处理MR数据（CS DATA 组）", "status": "function_missing"}, {"schedule": "* * * * *", "function": "app01.views.save_JIRA_Release_List_Details", "description": "每分钟执行一次，AR平台-日历页面，保存发布单列表到DB"}, {"schedule": "* * * * *", "function": "app01.views.saveCalendarJiraReleaseList", "description": "每分钟执行一次，AR平台-日历页面，保存日历发布信息到DB"}, {"schedule": "*/10 * * * *", "function": "app01.views.get_all_release_by_title", "description": "每10分钟执行一次，根据标题获取所有发布版本并更新数据库"}, {"schedule": "0 10-12,14-19 * * *", "function": "app01.views.cronjob_auto_seatalk_router", "description": "工作时间（10-12点，14-19点）整点执行，自动Seatalk路由功能"}, {"schedule": "*/10 * * * *", "function": "app01.views.get_unreleased_versions", "description": "每10分钟执行一次，获取未发布的版本信息"}, {"schedule": "0 10,16 * * *", "function": "app01.views.cronjob_SPS_live_bug_of_chatbot_reminder", "description": "每天10点和16点执行，SPS线上聊天机器人bug提醒"}, {"schedule": "0 10,16 * * *", "function": "app01.views.cronjob_SPCB_live_bug_reminder", "description": "每天10点和16点执行，SPCB线上bug提醒"}, {"schedule": "*/5 * * * *", "function": "app01.views.cronjob_new_SPS_live_bug_of_chatbot_mirror", "description": "每5分钟执行一次，新的SPS聊天机器人线上bug镜像监控"}, {"schedule": "0 10,16 * * *", "function": "app01.views.cronjob_SPS_live_bug_of_chat_reminder", "description": "每天10点和16点执行，SPS聊天线上bug提醒"}, {"schedule": "*/5 * * * *", "function": "app01.views.cronjob_new_SPS_live_bug_of_chat_mirror", "description": "每5分钟执行一次，新的SPS聊天线上bug镜像监控"}, {"schedule": "0 10,15 * * *", "function": "app01.views.cronjob_chatbot_adhoc_reminder", "description": "每天10点和15点执行，聊天机器人临时任务提醒"}, {"schedule": "0 22 * * *", "function": "app01.views.update_service_live", "description": "每天晚上22点执行，更新服务线上状态", "status": "function_missing"}, {"schedule": "0 * * * *", "function": "app01.seatalk_group_manager.check_timeline_changes", "description": "每小时执行一次，检查时间线变化"}, {"schedule": "*/5 * * * *", "function": "app01.seatalk_group_manager.check_jira_assignee_changes", "description": "每5分钟执行一次，检查JIRA任务分配人变化"}], "total_jobs": 20, "missing_functions": ["MRnotdeal_data", "update_service_live"]}, "deprecated_functions": {"description": "已标记为废弃的函数列表", "functions": [{"name": "MRnotdeal_20230726", "line": 3351, "size_lines": 259, "risk_level": "LOW", "reason": "从未被调用的历史遗留代码", "deprecated_date": "2025-06-11", "contains_operations": ["requests", "seatalk", "create"]}, {"name": "failuremsg", "line": 3887, "size_lines": 166, "risk_level": "LOW", "reason": "已被failuremsg_final替代", "deprecated_date": "2025-06-11", "contains_operations": ["requests", "seatalk", "webhook"]}, {"name": "failuremsg_old", "line": 4060, "size_lines": 144, "risk_level": "LOW", "reason": "已被新版本替代", "deprecated_date": "2025-06-11", "contains_operations": ["requests", "seatalk", "webhook"]}, {"name": "mergefromfeature", "line": 1394, "size_lines": 38, "risk_level": "MEDIUM", "reason": "可能是实验性代码，但涉及Git操作", "deprecated_date": "2025-06-11", "contains_operations": ["requests"]}, {"name": "timeformat", "line": 3850, "size_lines": 14, "risk_level": "LOW", "reason": "代码注释明确说明有bug且不再使用", "deprecated_date": "2025-06-11", "contains_operations": []}], "total_deprecated": 5, "total_lines_to_remove": 621}, "statistics": {"total_functions_in_views": 115, "functions_used_by_urls": 31, "functions_used_by_cron": 18, "functions_with_internal_calls": 106, "truly_unused_functions": 5, "cleanup_potential": "621 lines (5.4% of views.py)"}, "recommendations": {"immediate": ["部署当前代码到测试环境，观察DeprecationWarning", "修复crontab中引用的缺失函数：MRnotdeal_data, update_service_live"], "short_term": ["1周后删除timeformat函数（风险最低）", "删除failuremsg和failuremsg_old函数", "删除MRnotdeal_20230726函数"], "long_term": ["谨慎删除mergefromfeature函数（涉及Git操作）", "考虑重构频繁调用的核心函数", "建立定期的代码清理流程"]}}
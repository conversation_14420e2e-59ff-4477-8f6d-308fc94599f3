# ChatBot AutoRelease 数据统计系统使用指南

## 📊 系统概述

ChatBot AutoRelease 数据统计系统是一个完整的数据收集、分析和监控解决方案，为系统提供全面的使用情况统计、性能监控和健康状态分析。

### 🎯 核心功能

1. **用户行为统计** - 记录机器人访问事件和用户交互
2. **指令执行追踪** - 完整记录用户指令和系统响应
3. **性能监控** - 实时监控系统性能和响应时间
4. **定时任务监控** - 监控所有crontab任务的执行状态
5. **Web监控面板** - 提供直观的数据可视化界面
6. **智能报表** - 自动生成日报、周报和告警报告

## 🏗️ 系统架构

```
用户交互 → 数据收集中间件 → 异步队列 → 数据处理服务 → PostgreSQL数据库
                                                    ↓
定时任务 → 任务监控器 → 异步队列 → 数据聚合服务 → 统计API服务 → Web监控面板
```

## 📋 部署步骤

### 1. 数据库迁移

```bash
# 创建统计相关的数据表
python manage.py migrate
```

### 2. 配置设置

在 `djangoProject/settings.py` 中添加统计系统配置：

```python
# 统计系统配置
STATISTICS_ENABLED = True
STATISTICS_ASYNC = True
STATISTICS_MIDDLEWARE_ENABLED = True
STATISTICS_SERVICE_ENABLED = True
STATISTICS_REPORTS_ENABLED = True

# 数据保留配置
STATISTICS_DATA_RETENTION_DAYS = 90
STATISTICS_ACCESS_EVENT_RETENTION_DAYS = 30

# 告警阈值配置
STATISTICS_ALERT_ERROR_RATE_THRESHOLD = 10.0
STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD = 5.0
```

### 3. 中间件配置（可选）

如果需要自动收集HTTP请求性能数据，在 `MIDDLEWARE` 中添加：

```python
MIDDLEWARE = [
    # ... 其他中间件
    'app01.statistics.middleware.StatisticsMiddleware',
    'app01.statistics.middleware.DatabaseQueryCountMiddleware',
    # ... 其他中间件
]
```

### 4. URL配置

统计API已自动配置在 `/api/statistics/` 路径下。

## 🚀 使用方法

### 访问监控面板

访问 `http://your-domain/api/statistics/dashboard/` 查看统计监控面板。

### API接口使用

#### 实时监控数据
```bash
curl http://your-domain/api/statistics/realtime/dashboard/
```

#### 指令执行趋势
```bash
curl "http://your-domain/api/statistics/realtime/command-trends/?days=7"
```

#### 性能指标
```bash
curl "http://your-domain/api/statistics/performance/metrics/?hours=24"
```

#### 日报生成
```bash
# JSON格式
curl http://your-domain/api/statistics/reports/daily/

# HTML格式
curl "http://your-domain/api/statistics/reports/daily/?format=html"
```

### 代码集成

#### 1. 指令执行跟踪

使用装饰器自动跟踪指令执行：

```python
from app01.statistics.decorators import track_command_execution

@track_command_execution('ai_query', 'query_tasks')
def process_ai_query(context):
    # 处理AI查询逻辑
    return result
```

#### 2. 定时任务监控

为定时任务添加监控：

```python
from app01.statistics.decorators import track_cronjob_execution

@track_cronjob_execution('callback')
def callback():
    # 定时任务逻辑
    return result
```

#### 3. 手动数据收集

```python
from app01.statistics.decorators import track_bot_access_event

# 收集访问事件
event_data = {
    'event_id': 'unique-event-id',
    'event_type': 'user_enter_chatroom_with_bot',
    'timestamp': int(timezone.now().timestamp()),
    'event': {
        'seatalk_id': 'user-id',
        'email': '<EMAIL>'
    }
}
track_bot_access_event(event_data)
```

## 📊 数据模型

### 核心数据表

1. **BotAccessEvent** - 机器人访问事件
2. **CommandExecutionRecord** - 指令执行记录
3. **SystemPerformanceMetrics** - 系统性能指标
4. **CronJobExecutionMonitor** - 定时任务监控
5. **UserActivitySummary** - 用户活动汇总
6. **SystemHealthSnapshot** - 系统健康快照

### 数据字段说明

#### CommandExecutionRecord（指令执行记录）
- `execution_id` - 执行唯一ID
- `user_id` - 用户SeaTalk ID
- `command_type` - 指令类型（ai_query, jira_query等）
- `raw_input` - 用户原始输入
- `success` - 是否执行成功
- `processing_time` - 处理时间
- `response_content` - 返回内容
- `error_message` - 错误信息

## 🔧 管理命令

### 生成统计汇总

```bash
# 生成用户活动汇总
python manage.py generate_statistics --type user_activity --period daily

# 生成系统健康快照
python manage.py generate_statistics --type health_snapshot

# 清理旧数据
python manage.py generate_statistics --type cleanup --cleanup-days 90

# 批量处理所有统计
python manage.py generate_statistics --type batch
```

## 📈 监控面板功能

### 实时监控
- 当前活跃用户数
- 今日指令执行数
- 系统成功率
- 平均响应时间
- 指令执行趋势图
- 用户活跃度图表

### 指令统计
- 指令类型分布
- 成功率趋势
- 执行记录查询

### 性能监控
- API响应时间
- 数据库查询性能
- 系统资源使用

### 定时任务监控
- 任务执行状态
- 失败任务详情
- 执行时长统计

## ⚠️ 注意事项

### 性能影响
- 统计系统采用异步处理，对主业务影响最小
- 数据收集使用缓冲机制，避免频繁数据库写入
- 可通过配置关闭不需要的统计功能

### 数据隐私
- 用户输入内容会被记录，请确保符合隐私政策
- 可配置最大内容长度限制
- 支持数据自动清理

### 存储空间
- 统计数据会占用额外存储空间
- 建议定期清理历史数据
- 可配置不同类型数据的保留期限

## 🔍 故障排查

### 常见问题

1. **统计数据不更新**
   - 检查 `STATISTICS_ENABLED` 配置
   - 确认数据库连接正常
   - 查看日志中的错误信息

2. **监控面板无法访问**
   - 确认URL配置正确
   - 检查模板文件是否存在
   - 验证视图函数是否正常

3. **性能影响过大**
   - 调整缓冲区大小和刷新间隔
   - 关闭不必要的统计功能
   - 考虑使用专门的队列系统

### 日志查看

```bash
# 查看统计系统相关日志
grep "statistics" /path/to/your/logs/django.log
```

## 📞 技术支持

如有问题或需要技术支持，请联系：
- **项目负责人**: <EMAIL>
- **文档更新**: 2025-07-18

---

**注意**: 本统计系统设计为零干扰部署，不会影响现有用户的正常使用。所有数据收集都是异步进行的，确保系统性能不受影响。

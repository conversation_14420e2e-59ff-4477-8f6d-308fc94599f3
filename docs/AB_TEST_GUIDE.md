# AI模型AB测试管理指南 🤖

## 概述

本系统支持在Qwen2.5-VL和GPT-4.1之间进行AB测试，可以灵活切换不同模式：
- **生产模式**：稳定的Qwen2.5-VL（默认，推荐日常使用）
- **AB测试单模型**：测试单个模型效果  
- **AB测试对比模式**：同时调用两个模型进行对比

## 🚀 快速开始

### 方式1：聊天界面命令

在聊天界面直接输入管理命令：

```bash
# 查看当前状态
abtest status

# 开启双模型对比（推荐测试方式）
abtest enable dual

# 测试单个模型
abtest enable single gpt
abtest enable single qwen

# 关闭AB测试，恢复生产模式
abtest disable
```

### 方式2：命令行脚本

在后端目录执行：

```bash
# 查看当前状态
python manage_ab_test.py status

# 开启双模型对比
python manage_ab_test.py enable dual

# 开启单模型测试
python manage_ab_test.py enable single gpt
python manage_ab_test.py enable single qwen

# 关闭AB测试
python manage_ab_test.py disable
```

## 📊 模式详解

### 1. 正式生产模式（默认）
- **用途**：日常工作使用
- **模型**：Qwen2.5-VL
- **特点**：稳定可靠，经过验证
- **响应格式**：正常AI回复

### 2. AB测试单模型模式
- **用途**：测试单个模型效果
- **模型**：Qwen2.5-VL 或 GPT-4.1（可选择）
- **特点**：专注测试单个模型
- **响应格式**：带模型标识的回复

### 3. AB测试对比模式
- **用途**：对比两个模型效果
- **模型**：同时调用Qwen2.5-VL和GPT-4.1
- **特点**：并行调用，结果对比
- **响应格式**：
```
🤖 **AI模型对比结果**

## 🧭 **Qwen2.5-VL 结果:** (响应时间: 1.2s)
[Qwen回答内容]

## 🤖 **GPT-4.1 结果:** (响应时间: 0.8s)  
[GPT-4.1回答内容]

💡 **请根据上述两个模型的结果选择您认为更好的答案！**
```

## ⚠️ 重要提醒

### 生产环境使用建议
1. **默认使用生产模式**：日常工作建议使用稳定的生产模式
2. **短期AB测试**：AB测试适合短期评估，不建议长期开启
3. **测试完成后及时关闭**：`abtest disable` 恢复生产模式

### 切换流程建议
```bash
# 1. 开启AB测试进行评估
abtest enable dual

# 2. 进行若干轮测试对比

# 3. 测试完成后立即关闭
abtest disable
```

### 性能考虑
- **双模型模式**：响应时间略长（并行调用）
- **单模型模式**：响应时间正常
- **生产模式**：最优性能

## 🔧 技术细节

### 配置文件
AB测试配置在 `app01/ai_config.py` 中：

```python
AB_TEST_CONFIG = {
    'ENABLED': False,  # AB测试总开关 - 默认关闭
    'DUAL_MODEL_COMPARISON': False,  # 双模型对比功能 - 默认关闭  
    'SHOW_MODEL_SOURCE': True,  # 显示模型来源
    'PARALLEL_REQUESTS': True,  # 并行请求两个模型
    'DEFAULT_MODEL': 'qwen',  # 默认使用的模型
    'FALLBACK_MODEL': 'qwen',  # 主模型失败时的备用模型
    'SECONDARY_MODEL_NAME': 'GPT-4.1',  # 次要模型显示名称
}
```

### 运行时切换
- 支持运行时动态切换（无需重启服务）
- 配置变更会自动备份原始文件
- 实时生效，立即应用新配置

### 安全特性
- 自动配置备份
- 异常时优雅降级到生产模式
- 详细的操作日志记录

## 📝 使用示例

### 场景1：测试GPT-4.1效果
```bash
# 1. 开启GPT-4.1单模型测试
abtest enable single gpt

# 2. 进行几轮问答测试
# ...测试各种查询...

# 3. 测试完成，恢复生产模式
abtest disable
```

### 场景2：两模型效果对比
```bash
# 1. 开启双模型对比
abtest enable dual

# 2. 问同样的问题，对比两个模型回答
# 例如："帮我查询本周的bug单"

# 3. 观察哪个模型回答更好

# 4. 测试完成，恢复生产模式  
abtest disable
```

## ❓ 常见问题

**Q: AB测试会影响其他用户吗？**
A: 不会。AB测试只影响当前启用的实例，其他用户使用的实例不受影响。

**Q: 如何判断哪个模型更好？**
A: 可以从回答准确性、完整性、响应速度等方面进行评估。

**Q: 配置更改后需要重启服务吗？**
A: 聊天界面命令方式无需重启，命令行脚本方式需要重启Django服务。

**Q: 为什么我看不到模型对比结果？**
A: 请确认已正确开启AB测试的双模型对比模式（`abtest enable dual`）。

**Q: 如何恢复到默认配置？**
A: 执行 `abtest disable` 即可恢复到默认的生产模式。 
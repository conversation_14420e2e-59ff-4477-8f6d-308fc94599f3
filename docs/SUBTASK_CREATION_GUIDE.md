# 子任务创建功能指南

## 概述

${BOT_NAME} 现已支持通过自然语言创建 JIRA 子任务（Subtask）。用户可以通过简单的命令在指定的父单下创建子任务，系统会自动提取相关信息并完成创建。

## 功能特点

- 🔄 **自然语言处理**: 理解多种表达方式的创建请求
- 📋 **自动提取信息**: 智能识别父单号、任务标题、工作量等信息
- 🔧 **灵活工作量表示**: 支持多种工作量表示方式（天、小时、周等）
- 🔑 **JIRA Token 认证**: 安全的用户认证机制
- 🧠 **智能默认值**: 自动补全未提供的必要信息

## 使用方法

### 基本语法

```
@${BOT_NAME} /ai 在 <父单号> 建单/建subtask：<任务标题>。工作量 <工作量>
```

### 示例命令

#### 基本创建

```
@${BOT_NAME} /ai 在 SPCB-51331 建单：完成功能测试。工作量 2d
```

#### 使用 "建subtask" 关键词

```
@${BOT_NAME} /ai 在 SPCB-51331 建subtask：今天完成接口测试。工作量 1d
```

#### 详细描述任务

```
@${BOT_NAME} /ai 在 SPCB-51331 建单：完成用户登录功能的单元测试和集成测试。工作量 3d
```

#### 不同工作量表示

```
@${BOT_NAME} /ai 在 SPCB-51331 建单：数据库优化。工作量 8h
@${BOT_NAME} /ai 在 SPCB-51331 建单：性能测试。工作量 1周
@${BOT_NAME} /ai 在 SPCB-51331 建单：代码review。工作量 3天
```

#### 简化语法 (无"在"字)

```
@${BOT_NAME} /ai SPCB-51331 开发工作
@${BOT_NAME} /ai SPCB-51331 测试工作
@${BOT_NAME} /ai JIRA-123 case设计
@${BOT_NAME} /ai TEST-456 发布验证
@${BOT_NAME} /ai SPCB-51331 UAT验证
```

#### 其他表达方式

```
@${BOT_NAME} /ai 给 SPCB-51331 创建一个性能测试的子任务
@${BOT_NAME} /ai JIRA-123 需要一个代码review的任务
@${BOT_NAME} /ai 为 TEST-456 新建集成测试工作
```

## 支持的语法格式

系统支持多种语法格式，以适应不同的表达习惯：

### 1. 标准格式（推荐）

```
@${BOT_NAME} /ai 在 <父单号> 建单：<任务标题>。工作量 <工作量>
```

- 清晰明了，包含所有必要信息
- 工作量可选，默认为1天

### 2. 简化格式

```
@${BOT_NAME} /ai <父单号> 建单：<任务标题>
@${BOT_NAME} /ai <父单号> 建subtask：<任务标题>
```

- 省略了"在"字
- 工作量使用默认值（1天）

### 3. 极简格式

```
@${BOT_NAME} /ai <父单号> <工作类型>
```

- 只提供父单号和简单工作描述
- 系统会自动生成合适的标题
- 工作量使用默认值（1天）

### 4. 自然语言格式

```
@${BOT_NAME} /ai 给 <父单号> 创建一个<描述>的子任务
@${BOT_NAME} /ai <父单号> 需要一个<描述>的任务
@${BOT_NAME} /ai 为 <父单号> 新建<描述>工作
```

- 更接近自然语言表达
- 系统会尽力提取关键信息

## 工作量表示

系统支持多种工作量表示方式：

| 表示方式 | 示例 | 转换为Story Points |
|---------|------|-------------------|
| 天 | 2d, 2天, 2日 | 2 |
| 小时 | 4h, 4小时, 4hr | 0.5 (4/8) |
| 周 | 1w, 1周, 1week | 5 (1*5) |

- 默认工作量：1天（1 Story Point）
- 1天 = 8小时 = 1 Story Point
- 1周 = 5天 = 5 Story Points

## 字段映射

创建子任务时，系统会自动设置以下字段：

| 字段 | 来源 | 默认值 |
|------|------|--------|
| 父单号 | 用户输入 | 必填 |
| 标题 | 用户输入 | 根据工作类型生成 |
| 工作量 | 用户输入 | 1 |
| 计划开始日期 | 用户输入/推断 | 当天 |
| 计划结束日期 | 用户输入/推断 | 根据工作量计算 |
| 经办人 | 用户输入 | 当前用户 |

## JIRA Token 配置

首次使用时，需要配置JIRA Token：

```
@${BOT_NAME} /ai config jira_token YOUR_JIRA_TOKEN
```

- Token会被安全加密存储
- 每个用户需要单独配置
- Token有效期遵循JIRA系统设置

## 注意事项

1. 子任务创建需要用户具有父任务的编辑权限
2. 工作量会自动转换为Story Points
3. 如果未指定经办人，默认为发送命令的用户
4. 创建成功后会返回子任务链接和关键信息
5. 所有子任务创建操作都会记录日志，便于审计和问题排查 
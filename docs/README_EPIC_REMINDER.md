# Epic关键节点风险预警提醒功能

## 功能概述

Epic关键节点风险预警提醒功能是一个自动化的提醒系统，用于在Epic的关键时间节点前提前1个工作日（或在固定时间）向相关团队成员发送风险预警提醒。

## 功能特性

### 支持的提醒类型

1. **联调开始提醒**（Planned Integration Start Date）
   - 提前1个工作日提醒
   - 目标受众：所有Dev（开发人员）
   - 提醒内容：准备联调环境、确保开发完成

2. **提测时间提醒**（Planned QA Start Date）
   - 提前1个工作日提醒
   - 目标受众：所有Dev（开发人员）
   - 提醒内容：完成自测、准备测试环境

3. **UAT开始提醒**（Planned UAT Start）
   - 提前1个工作日提醒
   - 目标受众：PM（产品经理）
   - 提醒内容：准备UAT测试用例、协调资源

4. **UAT sign-off提醒**（Planned UAT Due Date）
   - 提前1个工作日提醒
   - 目标受众：PM（产品经理）
   - 提醒内容：确认UAT完成、准备sign-off

5. **发布信息提醒**（固定在周四）
   - 每周四当天提醒
   - 目标受众：所有Dev（开发人员）
   - 提醒内容：确认QA/UAT sign-off、代码合入状态

### 配置特性

- **项目独立配置**：每种提醒类型都可以单独为不同项目配置开启/关闭
- **受众配置**：可配置提醒对象（dev, pm, all）
- **灵活的消息模板**：支持自定义提醒消息内容
- **工作日计算**：自动排除周末，计算工作日

## 部署和配置

### 1. 文件结构

```
chatbot-ar-be/
├── app01/
│   ├── views.py                    # 主视图文件，包含API接口
│   ├── epic_milestone_reminder.py  # Epic提醒功能核心实现
│   └── ...
├── djangoProject/
│   ├── urls.py                     # URL路由配置
│   └── ...
└── README_EPIC_REMINDER.md         # 本文档
```

### 2. 配置说明

在 `epic_milestone_reminder.py` 中的 `EPIC_REMINDER_CONFIG` 配置：

```python
EPIC_REMINDER_CONFIG = {
    "integration_start": {
        "enabled_projects": ["SPCB"],  # 可配置多个项目
        "enabled": True,               # 是否启用
        "reminder_days": 1,            # 提前几个工作日
        "target_audience": "dev",      # 目标受众：dev/pm/all
        "message_template": "..."      # 消息模板
    },
    # ... 其他配置项
}
```

### 3. 群组配置

**重要更新**：群组获取方式已更改为从数据库动态查询：

```python
def get_epic_project_group_id(epic_key):
    """
    根据Epic key从数据库中查询对应的群组ID
    参考seatalk_group_manager.py中的查询逻辑
    """
    try:
        # 从数据库查找包含该JIRA号的群组
        groups = SeatalkGroup.objects.filter(group_name__contains=epic_key)
        
        if not groups.exists():
            raise Exception(f"未找到包含 {epic_key} 的群组")
        
        # 如果找到多个群组，取第一个
        group = groups.first()
        return group.group_id
        
    except Exception as e:
        raise Exception(f"查询群组失败: {str(e)}")
```

**配置说明**：
- 系统会自动从`SeatalkGroup`数据库表中查询包含Epic key的群组
- 不需要手动配置项目与群组的映射关系
- 如果找到多个匹配的群组，会使用第一个群组
- 如果找不到匹配的群组，会抛出异常并通知管理员

### 4. 错误监控配置

**新增功能**：系统现在会自动将所有错误信息推送到调试群 `"NzQzMzAxODcyMjAy"`

```python
def send_error_notification(error_message, epic_key=None):
    """
    发送错误通知到调试群 "NzQzMzAxODcyMjAy"
    """
    # 错误信息会发送到调试群
```

**错误监控特性**：
- **自动通知**：所有错误都会自动推送到调试群
- **详细信息**：包含时间戳、Epic信息、具体错误内容
- **集中监控**：在调试群中集中监控所有错误信息
- **分类处理**：区分Epic特定错误和系统级错误

**监控覆盖范围**：
- Epic查询失败
- 群组查询失败
- 日期解析错误
- Seatalk发送失败
- 系统级异常

## 使用方法

### 1. 手动触发（测试）

通过API接口手动触发检查：

```bash
curl -X POST http://your-domain/api/epic-reminder/
```

响应示例：
```json
{
    "success": true,
    "message": "Epic关键节点提醒检查已完成"
}
```

### 2. 定时任务配置

建议通过crontab配置每天早上10点自动执行：

```bash
# 编辑crontab
crontab -e

# 添加以下行（每天早上10点执行）
0 10 * * * curl -X POST http://your-domain/api/epic-reminder/ >/dev/null 2>&1
```

### 3. 监控和日志

- 所有操作都会通过 `icecream` 库记录日志
- 错误信息会发送到调试群：`"NzQzMzAxODcyMjAy"`
- 成功发送的提醒会记录Epic key和群组ID

## 工作流程

1. **定时执行**：每天早上10点运行检查
2. **查询Epic**：获取所有未完成的Epic（状态不为Done/Closed/Icebox）
3. **提取信息**：获取Epic的关键日期和团队成员信息
4. **日期计算**：计算是否需要在今天发送提醒
5. **发送提醒**：格式化消息并发送到对应的Seatalk群组
6. **@相关人员**：自动@对应的团队成员

## 自定义配置

### 添加新的提醒类型

1. 在 `EPIC_REMINDER_CONFIG` 中添加新配置
2. 在 `check_epic_milestone_reminders()` 函数中添加对应的检查逻辑
3. 如需要新的JIRA自定义字段，在 `date_custom_fields` 中添加

### 修改消息模板

在配置中的 `message_template` 字段修改消息内容，支持以下占位符：
- `{epic_key}`: Epic的key
- `{epic_title}`: Epic的标题
- `{date}`: 相关日期

### 配置新项目

1. 在 `enabled_projects` 列表中添加项目key
2. 在 `get_epic_project_group_id()` 中添加项目与群组的映射
3. 根据需要调整JQL查询条件
4. **新增**：确保数据库中 `SeatalkGroup` 表包含该项目Epic对应的群组信息

**数据库配置说明**：
- 群组信息存储在 `SeatalkGroup` 模型中
- 系统通过 `group_name__contains=epic_key` 查询匹配的群组
- 确保群组名称中包含Epic key的项目标识符（如 "SPCB-123" 能匹配到相应群组）

## 注意事项

1. **时区问题**：确保服务器时区与业务时区一致
2. **JIRA权限**：确保JIRA token有足够权限读取Epic信息
3. **Seatalk权限**：确保机器人有权限向目标群组发送消息
4. **字段映射**：JIRA自定义字段ID可能因环境而异，需要根据实际情况调整
5. **网络连接**：确保服务器能正常访问JIRA和Seatalk API

## 故障排除

### 常见问题

1. **Epic查询为空**
   - 检查JQL查询条件是否正确
   - 确认JIRA token权限

2. **日期字段为空**
   - 确认Epic中是否填写了相关日期字段
   - 检查字段ID映射是否正确

3. **群组查询失败**
   - 检查数据库中 `SeatalkGroup` 表是否包含相关群组
   - 确认群组名称中包含Epic key
   - 验证数据库连接是否正常

4. **Seatalk发送失败**
   - 检查群组ID是否正确
   - 确认机器人权限

5. **@人员不生效**
   - 确认邮箱地址格式正确
   - 检查人员字段映射

6. **错误通知异常**
   - 所有错误都会自动发送到调试群 `"NzQzMzAxODcyMjAy"`
- 如果错误通知本身失败，会在系统日志中记录

### 日志查看

检查应用日志中的icecream输出，关键词：
- "开始检查Epic关键节点提醒"
- "Epic提醒已发送"
- "Epic关键节点提醒检查失败"

## 更新历史

- v1.0.0: 初始版本，支持5种基本提醒类型
- 后续版本将支持更多自定义选项和提醒类型 
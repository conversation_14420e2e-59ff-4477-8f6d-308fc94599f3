# 🤖 ChatbotAR 自动化测试框架

## 📋 概述

ChatbotAR 自动化测试框架是一个功能完整的AI系统测试解决方案，专为测试聊天机器人和AI助手而设计。该框架具有以下特点：

- **🔒 精准拦截**: 只拦截用户可见的输出，保持内部处理的真实性
- **📊 智能评估**: 结合规则和LLM的双重评估机制
- **🧹 自动清理**: 自动追踪和清理测试产生的副作用
- **📄 美观报告**: 生成详细的HTML和JSON格式测试报告
- **⚡ 高性能**: 支持并发测试和性能监控

## 🏗️ 架构设计

### 核心组件

```
📦 testing_framework/
├── 🔧 precision_interceptor.py    # 精准拦截器
├── 📋 test_case_manager.py        # 测试用例管理器
├── 🤖 result_evaluator.py         # 结果评估器
├── 🎯 test_controller.py          # 测试控制器
├── 📊 report_generator.py         # 报告生成器
├── 🚀 test_manager.py             # 测试管理器
├── 💬 seatalk_simulator.py        # Seatalk模拟器
├── 📁 test_cases/                 # 测试用例目录
├── 📄 reports/                    # 测试报告目录
└── 📝 logs/                       # 测试日志目录
```

### 工作流程

```mermaid
graph TD
    A[开始测试] --> B[启用精准拦截]
    B --> C[加载测试用例]
    C --> D[执行测试]
    D --> E[拦截输出消息]
    E --> F[LLM智能评估]
    F --> G[生成测试报告]
    G --> H[清理副作用]
    H --> I[禁用拦截模式]
    I --> J[测试完成]
```

## 🚀 快速开始

### 1. 基本使用

```bash
# 运行快速测试（推荐首次使用）
python test_runner.py quick

# 运行全面测试
python test_runner.py full

# 运行系统诊断
python test_runner.py diagnostic

# 查看系统状态
python test_runner.py status
```

### 2. 高级用法

```bash
# 运行指定优先级的测试
python test_runner.py custom -p high,medium

# 运行指定测试套件
python test_runner.py custom -s basic_tests,jira_tests

# 限制测试用例数量
python test_runner.py custom -p high --max-cases 10

# 查看测试套件信息
python test_runner.py info

# 查看测试报告
python test_runner.py reports
```

## 📋 测试用例管理

### 测试套件结构

测试框架包含以下预定义测试套件：

#### 1. 基础功能测试 (`basic_tests`)
- **用途**: 测试AI助手的基础交互功能
- **优先级**: High
- **包含用例**:
  - 问候测试
  - 帮助功能测试
  - 功能介绍测试

#### 2. JIRA查询测试 (`jira_tests`)
- **用途**: 测试各种JIRA查询功能
- **优先级**: High
- **包含用例**:
  - 简单JIRA查询
  - 高优先级Bug查询
  - 项目特定查询
  - 复杂查询测试

#### 3. 定时任务测试 (`schedule_tests`)
- **用途**: 测试定时任务管理功能
- **优先级**: High
- **包含用例**:
  - 任务列表查询
  - 简单任务创建
  - 智能任务创建
  - 自然语言任务创建

#### 4. 群聊功能测试 (`group_tests`)
- **用途**: 测试群聊中的AI功能
- **优先级**: Medium
- **包含用例**:
  - 群聊问候
  - 群聊JIRA查询

#### 5. 边界情况测试 (`edge_tests`)
- **用途**: 测试各种边界情况和错误处理
- **优先级**: Medium
- **包含用例**:
  - 空消息测试
  - 无效JQL测试
  - 长文本测试

#### 6. 性能测试 (`performance_tests`)
- **用途**: 测试系统性能和并发处理能力
- **优先级**: Low
- **包含用例**:
  - 响应时间测试
  - 大数据量查询

### 自定义测试用例

#### 测试用例格式

```json
{
  "id": "custom_001",
  "name": "自定义测试名称",
  "description": "测试描述",
  "type": "private_chat",  // 或 "group_chat"
  "priority": "high",      // high, medium, low
  "user_key": "test_user_001",
  "message": "/ai 测试消息",
  "expected": {
    "should_respond": true,
    "should_contain": ["关键词1", "关键词2"],
    "should_not_contain": ["错误", "失败"],
    "max_response_time": 10,
    "should_query_jira": false,
    "should_create_task": false
  },
  "evaluation_criteria": {
    "accuracy": 8,
    "completeness": 7,
    "friendliness": 9,
    "usefulness": 6
  },
  "cleanup_required": false
}
```

#### 创建自定义测试用例

```python
from app01.testing_framework.test_manager import test_manager

# 创建自定义测试用例
custom_case = {
    "id": "my_test_001",
    "name": "我的自定义测试",
    "type": "private_chat",
    "message": "/ai 这是我的测试消息",
    "expected": {
        "should_respond": True,
        "max_response_time": 15
    }
}

result = test_manager.create_custom_test_case("my_custom_suite", custom_case)
```

## 🔒 精准拦截机制

### 拦截策略

精准拦截器采用以下策略确保测试安全：

1. **只拦截输出**: 仅拦截发送给用户的消息，不影响内部处理
2. **保持真实性**: AI处理、JIRA查询、数据库操作保持真实
3. **副作用追踪**: 自动追踪测试产生的数据变更
4. **限流保护**: 控制查询频率，保护系统性能

### 拦截范围

- ✅ Seatalk群聊消息发送
- ✅ Seatalk私聊消息发送
- ✅ 私聊API消息发送
- ❌ JIRA查询（保持真实）
- ❌ 数据库操作（保持真实）
- ❌ AI处理流程（保持真实）

## 📊 评估机制

### 双重评估体系

测试框架采用基础评估 + LLM评估的双重机制：

#### 1. 基础评估（规则基础）
- **响应存在性**: 检查是否有响应
- **响应时间**: 检查响应时间是否合理
- **内容匹配**: 检查必需内容是否包含
- **内容规避**: 检查是否避免了禁止内容
- **功能检查**: 检查特定功能是否触发

#### 2. LLM评估（智能评估）
- **准确性**: 响应是否准确回答问题
- **完整性**: 响应是否完整包含必要信息
- **友好性**: 响应语言是否友好易懂
- **实用性**: 响应对用户是否有实际帮助

### 评分机制

- **基础评估权重**: 40%
- **LLM评估权重**: 60%
- **最终分数**: 加权平均分（1-10分）
- **通过标准**: 根据测试用例期望标准判定

## 📄 测试报告

### HTML报告特性

- **📊 可视化仪表板**: 直观的统计图表和进度条
- **🎯 交互式过滤**: 按状态过滤测试结果
- **📱 响应式设计**: 支持移动设备查看
- **🔍 详细分析**: 每个测试用例的详细评估信息
- **💡 改进建议**: 基于测试结果的优化建议

### 报告内容

1. **测试概览**
   - 总测试数、通过数、失败数
   - 通过率和平均分
   - 测试耗时统计

2. **会话信息**
   - 会话ID和状态
   - 开始/结束时间
   - 平均响应时间

3. **详细结果**
   - 每个测试用例的执行结果
   - 基础评估和LLM评估详情
   - 响应内容和拦截消息
   - 问题分析和改进建议

4. **清理操作**
   - 副作用清理记录
   - 任务删除结果

## 🧹 自动清理机制

### 副作用追踪

测试框架自动追踪以下副作用：

- **定时任务创建**: 记录创建的任务ID
- **数据库修改**: 记录数据变更（如需要）
- **文件创建**: 记录临时文件（如需要）

### 清理策略

1. **任务清理**: 自动删除测试中创建的定时任务
2. **数据回滚**: 根据需要回滚数据修改
3. **文件清理**: 清理临时文件和缓存
4. **状态重置**: 重置系统状态到测试前

## ⚡ 性能优化

### 并发控制

- **限流机制**: 控制JIRA查询频率（默认300ms间隔）
- **查询限制**: 每次测试最多100个JIRA查询
- **批量处理**: 支持批量评估和报告生成

### 资源管理

- **内存优化**: 及时清理测试数据和缓存
- **文件管理**: 自动清理旧的报告文件
- **会话管理**: 限制活动会话数量

## 🔧 配置选项

### 测试配置

```python
# 测试控制器配置
test_controller.run_test_suite(
    suite_names=['basic_tests', 'jira_tests'],  # 指定测试套件
    priorities=['high', 'medium'],              # 指定优先级
    max_cases=20,                               # 最大用例数
    enable_cleanup=True                         # 启用自动清理
)
```

### 拦截器配置

```python
# 精准拦截器配置
precision_interceptor.rate_limiter.jira_query_limit = 50    # JIRA查询限制
precision_interceptor.rate_limiter.query_interval = 0.5    # 查询间隔
```

### 评估器配置

```python
# 结果评估器配置
result_evaluator._merge_evaluations(
    basic_weight=0.4,    # 基础评估权重
    llm_weight=0.6       # LLM评估权重
)
```

## 🚨 故障排除

### 常见问题

#### 1. 测试拦截失败
**症状**: 测试过程中仍然发送了真实消息
**解决方案**:
```bash
# 检查拦截器状态
python test_runner.py status

# 重启测试框架
python -c "from app01.testing_framework.precision_interceptor import precision_interceptor; precision_interceptor.disable_testing_mode()"
```

#### 2. LLM评估失败
**症状**: 评估结果显示LLM评估错误
**解决方案**:
- 检查AI助手服务状态
- 确认LLM API可用性
- 查看详细错误日志

#### 3. 测试用例未找到
**症状**: 运行特定测试用例时提示未找到
**解决方案**:
```bash
# 查看可用测试套件
python test_runner.py info

# 检查测试用例文件
ls app01/testing_framework/test_cases/
```

#### 4. 报告生成失败
**症状**: 测试完成但报告未生成
**解决方案**:
- 检查reports目录权限
- 确认磁盘空间充足
- 查看详细错误日志

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 使用icecream调试
from icecream import ic
ic.enable()  # 启用调试输出
ic.disable() # 禁用调试输出
```

## 📈 最佳实践

### 1. 测试策略

- **渐进式测试**: 从快速测试开始，逐步扩展到全面测试
- **定期执行**: 建议每日执行快速测试，每周执行全面测试
- **关注重点**: 优先测试核心功能和高风险区域

### 2. 用例设计

- **覆盖全面**: 确保覆盖所有主要功能点
- **边界测试**: 包含边界条件和异常情况
- **真实场景**: 模拟真实用户使用场景

### 3. 结果分析

- **趋势监控**: 关注通过率和响应时间趋势
- **问题分类**: 对失败测试进行分类分析
- **持续改进**: 根据测试结果持续优化系统

### 4. 维护管理

- **定期清理**: 定期清理旧的报告和日志
- **用例更新**: 根据功能变更及时更新测试用例
- **文档同步**: 保持测试文档与实际功能同步

## 📚 API参考

### TestManager

```python
from app01.testing_framework.test_manager import test_manager

# 运行快速测试
result = await test_manager.run_quick_test(max_cases=5)

# 运行全面测试
result = await test_manager.run_full_test(suite_names=['basic_tests'])

# 运行自定义测试
result = await test_manager.run_custom_test(
    priorities=['high'],
    suite_names=['jira_tests'],
    max_cases=10
)

# 获取系统状态
status = test_manager.get_system_status()

# 获取测试报告
reports = test_manager.get_test_reports()
```

### TestController

```python
from app01.testing_framework.test_controller import test_controller

# 运行测试套件
result = await test_controller.run_test_suite(
    suite_names=['basic_tests'],
    priorities=['high', 'medium'],
    max_cases=20,
    enable_cleanup=True
)

# 获取会话状态
status = test_controller.get_session_status(session_id)

# 取消测试会话
success = await test_controller.cancel_session(session_id)
```

### PrecisionInterceptor

```python
from app01.testing_framework.precision_interceptor import precision_interceptor

# 启用测试模式
precision_interceptor.enable_testing_mode(session_id)

# 获取拦截消息
messages = precision_interceptor.get_intercepted_messages()

# 禁用测试模式
precision_interceptor.disable_testing_mode()
```

## 🤝 贡献指南

### 添加新测试套件

1. 在 `test_cases/` 目录创建新的JSON文件
2. 按照标准格式定义测试用例
3. 在 `test_case_manager.py` 中注册新套件

### 扩展评估机制

1. 在 `result_evaluator.py` 中添加新的评估维度
2. 更新评估提示和解析逻辑
3. 调整权重配置

### 改进报告格式

1. 修改 `report_generator.py` 中的HTML模板
2. 添加新的可视化组件
3. 优化样式和交互效果

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和错误信息
3. 联系开发团队获取支持

---

**🎉 祝您测试愉快！** 
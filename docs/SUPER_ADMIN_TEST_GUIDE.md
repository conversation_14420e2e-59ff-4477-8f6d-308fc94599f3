# 🔥 超级管理员完整测试指南

## 🎯 测试目标
验证超级管理员 `<EMAIL>` 的所有权限功能，确保定时任务权限管理系统正常工作。

---

## 🚀 一级测试（核心权限功能）

### 1. **权限查询测试** ✅
```bash
# 基础权限查询（已通过）
/ai permission

# 权限帮助
/ai permission help

# 权限检查特定JQL
/ai permission check "status = Open"
/ai permission check "project = SPCB AND status = Open"
```

### 2. **全局任务创建测试** 🔥
```bash
# 测试1: 创建全局Bug监控任务
/ai schedule create "全局紧急Bug监控" "priority = Highest AND status = Open" "daily 08:00"

# 测试2: 创建跨项目任务
/ai schedule create "跨项目Bug统计" "project in (SPCB, SPCT) AND status = Open" "daily 09:30"

# 测试3: 创建复杂JQL任务
/ai schedule create "高优先级未分配任务" "priority in (High, Highest) AND assignee is EMPTY" "daily 10:00"
```

### 3. **智能通知任务创建** 🧠
```bash
# 测试4: 创建智能通知（发送给指定人员）
/ai smart notify "Daily Bug Report" "status = Open AND priority = High" "daily 09:00" --target private --assignee

# 测试5: 创建智能通知（发送到群组）
/ai smart notify "SPCB项目提醒" "project = SPCB AND status = 'In Progress'" "daily 10:00" --target group

# 测试6: 创建周报通知
/ai smart notify "Weekly Summary" "resolved >= -7d" "weekly monday 09:00" --target private
```

---

## 🎯 二级测试（任务管理功能）

### 4. **任务列表和管理** 📋
```bash
# 查看所有任务
/ai schedule list

# 查看任务统计
/ai schedule stats

# 管理员模式查看其他用户任务
/ai schedule list --user=<EMAIL>
/ai schedule list --project=SPCB

# 查看任务详情
/ai schedule show <任务ID>
```

### 5. **任务状态管理** ⚙️
```bash
# 暂停任务（管理员权限）
/ai schedule pause <任务ID> --admin

# 恢复任务
/ai schedule resume <任务ID> --admin

# 删除任务（谨慎操作）
/ai schedule delete <任务ID> --admin --confirm
```

---

## 🎯 三级测试（边界和安全测试）

### 6. **权限边界测试** 🔒
```bash
# 测试超级管理员可以创建任意范围任务
/ai schedule create "测试全局权限" "status = Open" "daily 11:00"

# 测试复杂权限场景
/ai permission check "project = SPCB AND assignee = currentUser()"
/ai permission check "status in (Open, 'In Progress', Reopened)"
```

### 7. **自然语言任务创建** 🗣️
```bash
# 测试7: 自然语言创建任务
/ai 帮我创建一个每天早上9点检查高优先级Bug的定时任务

# 测试8: 复杂自然语言
/ai 我需要一个任务，每周一上午10点统计SPCB项目上周解决的问题数量

# 测试9: 多轮对话任务创建
/ai 创建定时任务
# 然后按提示回答问题
```

---

## 🎯 四级测试（高级功能）

### 8. **群组通知测试** 👥
```bash
# 测试10: 发送到指定JIRA群组
/ai schedule create "SPCB-1234群组提醒" "project = SPCB AND status = Open" "daily 09:00" --group "SPCB-1234"

# 测试11: 发送到多个目标
/ai smart notify "重要Bug提醒" "priority = Highest" "daily 08:00" --target both
```

### 9. **任务调度测试** ⏰
```bash
# 测试12: 不同时间格式
/ai schedule create "测试时间1" "status = Open" "daily 14:30"
/ai schedule create "测试时间2" "status = Open" "weekly friday 16:00"
/ai schedule create "测试时间3" "status = Open" "monthly 1 09:00"

# 测试13: 工作日任务
/ai schedule create "工作日检查" "status = Open" "weekdays 09:00"
```

---

## 🎯 五级测试（系统集成）

### 10. **与其他功能集成** 🔗
```bash
# 测试14: 结合JIRA查询
/ai 查询 "project = SPCB AND status = Open"
# 然后创建基于此查询的定时任务

# 测试15: Epic提醒集成
/ai epic reminder SPCB-1234

# 测试16: 项目统计
/ai 统计SPCB项目本周的Bug数量
```

---

## 📊 预期结果验证

### ✅ 成功标准
1. **任务创建**：所有任务都能成功创建，无权限被拒绝
2. **任务管理**：可以查看、暂停、恢复、删除任意任务
3. **权限检查**：所有JQL查询都显示"允许"
4. **通知发送**：定时任务能正常执行并发送通知
5. **日志记录**：操作都有详细的日志记录

### ❌ 失败标准
- 任何"权限被拒绝"的错误
- 任务创建失败
- 无法管理其他用户的任务
- 系统异常或崩溃

---

## 🚨 重要测试场景

### A. **压力测试**
```bash
# 创建多个任务测试系统稳定性
/ai schedule create "测试任务1" "status = Open" "daily 09:00"
/ai schedule create "测试任务2" "status = Open" "daily 09:05"
/ai schedule create "测试任务3" "status = Open" "daily 09:10"
# ... 继续创建5-10个任务
```

### B. **异常处理测试**
```bash
# 测试错误的JQL
/ai schedule create "错误JQL测试" "invalid jql query" "daily 09:00"

# 测试错误的时间格式
/ai schedule create "错误时间测试" "status = Open" "invalid time format"

# 测试空参数
/ai schedule create "" "" ""
```

### C. **清理测试**
```bash
# 测试完成后清理
/ai schedule list
# 删除所有测试任务
/ai schedule delete <测试任务ID> --admin --confirm
```

---

## 🎯 快速测试脚本

如果您想快速验证核心功能，可以按顺序执行：

```bash
# 1. 权限验证
/ai permission

# 2. 创建全局任务
/ai schedule create "超管测试任务" "priority = High" "daily 09:00"

# 3. 查看任务列表
/ai schedule list

# 4. 查看统计
/ai schedule stats

# 5. 暂停任务
/ai schedule pause <任务ID> --admin

# 6. 删除任务
/ai schedule delete <任务ID> --admin --confirm
```

---

## 📝 测试记录模板

建议记录测试结果：

```
测试时间：2024-XX-XX XX:XX
测试环境：正式环境
测试用户：<EMAIL>

✅ 权限查询：正常
✅ 全局任务创建：正常
✅ 智能通知：正常
✅ 任务管理：正常
✅ 自然语言：正常
❌ 发现问题：[如果有]

总体评估：[通过/需要修复]
```

---

**🎉 开始测试吧！** 

建议从**一级测试**开始，逐步深入。如果遇到任何问题，请立即告诉我具体的错误信息。 
# ChatbotAR 高级定时任务功能部署指南

## 概述

本文档介绍ChatbotAR AI功能中高级定时任务系统的部署和使用方法。高级功能包括群聊通知、任务模板、批量管理、统计报表等，并配备完善的白名单权限控制系统。

## 功能特性

### 🚀 核心高级功能

1. **群聊通知** - 将定时任务结果发送到指定群聊
2. **条件触发** - 基于JIRA事件的智能触发器
3. **任务模板** - 预定义常用任务模板，支持变量替换
4. **批量管理** - 批量创建、暂停、恢复任务
5. **统计报表** - 详细的任务执行统计和性能分析

### 🔐 权限控制系统

- 基于白名单的功能访问控制
- 细粒度的功能权限管理
- 使用限制和过期时间控制
- 完整的审计日志

## 部署步骤

### 1. 数据库迁移

```bash
# 生成迁移文件
python manage.py makemigrations

# 执行数据库迁移
python manage.py migrate
```

### 2. 初始化任务模板

```bash
# 创建预定义的任务模板
python manage.py init_task_templates

# 强制重新创建所有模板（可选）
python manage.py init_task_templates --force
```

### 3. 配置白名单权限

```bash
# 为用户添加所有高级功能权限
python manage.py manage_whitelist add \
    --user-id "user123" \
    --user-email "<EMAIL>" \
    --features "all_features" \
    --max-tasks 50 \
    --max-templates 20 \
    --granted-by "admin"

# 为用户添加特定功能权限
python manage.py manage_whitelist add \
    --user-id "user456" \
    --features "group_notification,task_template" \
    --expires-days 90

# 查看所有白名单
python manage.py manage_whitelist list

# 检查用户权限状态
python manage.py manage_whitelist check --user-id "user123"
```

### 4. 启动定时任务调度器

```bash
# 启动任务调度器（后台运行）
python manage.py start_task_scheduler &

# 或者使用systemd服务（推荐生产环境）
sudo systemctl start chatbot-scheduler
sudo systemctl enable chatbot-scheduler
```

## 使用指南

### 基础命令格式

所有高级功能都以 `advanced` 前缀开始：

```
advanced <功能> <操作> [参数]
```

### 1. 群聊通知功能

```bash
# 创建群聊通知任务
advanced group create "团队日报" "今天团队完成的任务" "每天 18:00" "group_123"

# 查看群聊通知帮助
advanced group
```

**参数说明：**
- 任务名称：自定义任务名称
- 查询内容：JIRA查询语句
- 调度表达式：时间调度规则
- 群组ID：目标群聊的唯一标识

### 2. 任务模板功能

```bash
# 查看所有可用模板
advanced template list

# 按分类查看模板
advanced template list daily

# 从模板创建任务
advanced template create from 1 "我的每日检查"

# 查看模板功能帮助
advanced template
```

**模板分类：**
- `daily` - 每日任务模板
- `weekly` - 每周任务模板
- `monthly` - 每月任务模板
- `project` - 项目管理模板
- `bug_tracking` - Bug跟踪模板
- `custom` - 自定义模板

### 3. 批量管理功能

```bash
# 批量暂停任务
advanced batch pause 1,2,3,4

# 批量恢复任务
advanced batch resume 1,2,3,4

# 查看批量管理帮助
advanced batch
```

### 4. 统计报表功能

```bash
# 生成当前统计报表
advanced stats

# 生成指定日期范围的报表
advanced stats 2024-01-01 to 2024-01-31

# 查看统计功能帮助
advanced stats
```

### 5. 权限管理

```bash
# 查看自己的权限状态
advanced whitelist status

# 查看权限管理帮助
advanced whitelist
```

## 管理员操作

### 白名单管理

```bash
# 添加用户到白名单
python manage.py manage_whitelist add \
    --user-id "new_user" \
    --user-email "<EMAIL>" \
    --employee-code "EMP001" \
    --features "group_notification,task_template,batch_management" \
    --max-tasks 30 \
    --max-templates 15 \
    --expires-days 180 \
    --granted-by "admin_name"

# 更新用户权限
python manage.py manage_whitelist update \
    --user-id "existing_user" \
    --features "all_features" \
    --max-tasks 100

# 移除用户权限
python manage.py manage_whitelist remove --user-id "user_to_remove"

# 查看所有白名单用户
python manage.py manage_whitelist list

# 检查特定用户权限
python manage.py manage_whitelist check --user-id "user_to_check"
```

### 模板管理

```bash
# 初始化预定义模板
python manage.py init_task_templates

# 强制重新创建模板
python manage.py init_task_templates --force
```

### 系统监控

```bash
# 查看任务调度器状态
python manage.py scheduler_status

# 查看任务执行日志
python manage.py view_task_logs --days 7

# 清理过期数据
python manage.py cleanup_old_data --days 90
```

## 配置说明

### 环境变量配置

```bash
# .env 文件配置
ADVANCED_FEATURES_ENABLED=true
MAX_CONCURRENT_TASKS=10
TASK_EXECUTION_TIMEOUT=300
STATISTICS_RETENTION_DAYS=90
```

### Django设置

```python
# settings.py
INSTALLED_APPS = [
    # ... 其他应用
    'app01',
]

# 高级功能配置
ADVANCED_TASK_FEATURES = {
    'ENABLED': True,
    'MAX_CONCURRENT_TASKS': 10,
    'TASK_TIMEOUT': 300,
    'STATISTICS_RETENTION_DAYS': 90,
    'DEFAULT_MAX_TASKS_PER_USER': 20,
    'DEFAULT_MAX_TEMPLATES_PER_USER': 10,
}
```

## 系统服务配置

### Systemd服务文件

创建 `/etc/systemd/system/chatbot-scheduler.service`：

```ini
[Unit]
Description=ChatbotAR Task Scheduler
After=network.target

[Service]
Type=simple
User=chatbot
WorkingDirectory=/path/to/chatbot-ar-be
Environment=DJANGO_SETTINGS_MODULE=chatbot_ar_be.settings
ExecStart=/path/to/venv/bin/python manage.py start_task_scheduler
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl start chatbot-scheduler
sudo systemctl enable chatbot-scheduler
```

## 监控和日志

### 日志配置

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'advanced_tasks': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/chatbot/advanced_tasks.log',
        },
    },
    'loggers': {
        'app01.ai_module.advanced_task_manager': {
            'handlers': ['advanced_tasks'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 监控指标

系统提供以下监控指标：

- 任务执行成功率
- 平均执行时间
- 并发任务数量
- 用户活跃度
- 功能使用统计

## 故障排除

### 常见问题

1. **权限被拒绝**
   ```
   错误：您没有使用此高级功能的权限
   解决：联系管理员添加相应的白名单权限
   ```

2. **任务创建失败**
   ```
   错误：任务数量已达到上限
   解决：删除不需要的任务或联系管理员提高限制
   ```

3. **模板不可用**
   ```
   错误：模板不存在或无权访问
   解决：检查模板ID是否正确，确认是否有访问权限
   ```

### 调试命令

```bash
# 检查系统状态
python manage.py check_advanced_features

# 测试权限系统
python manage.py test_permissions --user-id "test_user"

# 验证模板系统
python manage.py validate_templates

# 检查调度器状态
python manage.py scheduler_health_check
```

## 安全考虑

1. **权限控制**：确保只有授权用户才能访问高级功能
2. **数据隔离**：用户只能访问自己的任务和数据
3. **输入验证**：所有用户输入都经过严格验证
4. **审计日志**：记录所有重要操作的审计日志
5. **资源限制**：防止资源滥用和系统过载

## 性能优化

1. **数据库索引**：为常用查询字段添加索引
2. **缓存策略**：缓存频繁访问的数据
3. **异步处理**：使用异步操作提高响应速度
4. **批量操作**：优化批量数据处理
5. **定期清理**：清理过期数据和日志

## 升级和维护

### 版本升级

```bash
# 备份数据库
python manage.py dumpdata > backup.json

# 更新代码
git pull origin main

# 执行迁移
python manage.py migrate

# 重启服务
sudo systemctl restart chatbot-scheduler
```

### 定期维护

```bash
# 每周执行：清理过期数据
python manage.py cleanup_old_data --days 30

# 每月执行：优化数据库
python manage.py optimize_database

# 每季度执行：生成系统报告
python manage.py generate_system_report
```

## 支持和联系

如有问题或需要支持，请联系：

- 技术支持：<EMAIL>
- 系统管理员：<EMAIL>
- 项目文档：https://docs.company.com/chatbot-ar

---

**注意**：本文档基于当前版本编写，具体功能可能因版本而异。请参考最新的API文档和发布说明。 
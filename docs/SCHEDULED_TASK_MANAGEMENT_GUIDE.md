# 定时任务管理系统完整指南

## 📋 目录
- [概述](#概述)
- [权限管理](#权限管理)
- [任务创建](#任务创建)
- [任务管理](#任务管理)
- [智能通知](#智能通知)
- [自然语言创建](#自然语言创建)
- [命令参考](#命令参考)
- [配置管理](#配置管理)
- [安全建议](#安全建议)
- [故障排除](#故障排除)

## 📚 相关文档
- **[智能通知技术文档](SMART_NOTIFICATION_README.md)** - 详细的技术实现和开发指南
- **[高级功能部署指南](ADVANCED_FEATURES_DEPLOYMENT.md)** - 系统部署和配置说明
- **[生产环境影响评估](PRODUCTION_DEPLOYMENT_IMPACT_ASSESSMENT.md)** - 部署前的影响分析

> 💡 **提示**: 本文档主要面向最终用户，提供完整的使用指南。开发者请参考上述技术文档获取实现细节。

---

## 概述

定时任务管理系统是一个强大的自动化工具，允许用户创建、管理和监控定期执行的JIRA查询任务。系统支持多种创建方式、智能通知分发、权限控制和灵活的调度配置。

### 🌟 核心特性
- **多种创建方式**: 命令行、自然语言、智能引导
- **权限分级管理**: 三级权限体系确保安全性
- **智能通知**: 根据assignee自动分组发送
- **灵活调度**: 支持多种时间表达式
- **统一管理**: 查看、暂停、恢复、删除任务
- **统计分析**: 任务执行情况和权限范围统计

---

## 权限管理

### 🔒 权限分级体系

#### 🔴 超级管理员
- **用户**: `<EMAIL>`
- **权限范围**: 
  - 创建任意范围的定时任务
  - 查看和管理所有用户的任务
  - 访问全局统计信息
  - 执行系统级管理操作

#### 🟡 项目管理员 (PJM)
- **SPCB项目**: `<EMAIL>`
- **SPCT项目**: `<EMAIL>`
- **权限范围**:
  - 创建管理项目范围内的定时任务
  - 查看和管理项目内所有任务
  - 访问项目统计信息
  - 暂停/恢复/删除项目内不合规任务

#### 🟢 普通用户
- **权限范围**:
  - 创建发送给自己的定时任务
  - 创建指定项目群的通知任务
  - 只能管理自己创建的任务
  - 查看个人任务统计

### 🛡️ 权限检查机制

#### 任务创建权限
```python
# 检查规则
1. JQL查询范围分析 (self_only/project_specific/global)
2. 通知目标安全性验证
3. 用户角色权限匹配
4. 项目权限边界检查
```

#### 任务管理权限
```python
# 操作权限矩阵
操作类型    | 超级管理员 | 项目管理员 | 普通用户
----------|----------|----------|--------
查看任务   | 全部      | 项目范围   | 仅自己
暂停任务   | 全部      | 项目范围   | 仅自己  
恢复任务   | 全部      | 项目范围   | 仅自己
删除任务   | 全部      | 项目范围   | 仅自己
查看统计   | 全部      | 项目范围   | 仅自己
```

---

## 任务创建

### 📝 创建方式对比

| 方式 | 适用场景 | 优势 | 示例 |
|-----|---------|------|------|
| **命令行** | 精确控制 | 快速、准确 | `schedule create "任务名" "JQL" "时间"` |
| **自然语言** | 新手友好 | 智能解析、引导式 | `每天早上提醒我查看bug` |
| **智能通知** | 团队协作 | 自动分组、个性化 | 包含assignee关键词自动启用 |

### ⏰ 时间表达式

#### 支持格式
```bash
# 基本格式
daily HH:MM              # 每天指定时间
weekly DAY HH:MM         # 每周指定日期和时间  
workdays HH:MM           # 工作日指定时间
monthly DD HH:MM         # 每月指定日期和时间

# 示例
daily 09:00              # 每天上午9点
weekly monday 14:00      # 每周一下午2点
workdays 18:00           # 工作日下午6点
monthly 01 10:00         # 每月1号上午10点
```

#### 中文支持
```bash
每天 09:00
每周一 14:00  
工作日 18:00
每月1号 10:00
```

---

## 任务管理

### 📋 基本管理命令

#### 查看任务列表
```bash
# 基本用法
/ai schedule list                    # 查看自己的任务

# 管理员用法 (需要权限)
/ai schedule list --user=email      # 查看指定用户任务
/ai schedule list --project=SPCB    # 查看项目任务
/ai schedule list --all             # 查看所有可管理任务
```

#### 任务状态控制
```bash
# 暂停任务
/ai schedule pause <任务ID>         # 暂停自己的任务
/ai schedule pause <任务ID> --admin # 管理员暂停任务

# 恢复任务  
/ai schedule resume <任务ID>        # 恢复自己的任务
/ai schedule resume <任务ID> --admin # 管理员恢复任务

# 删除任务
/ai schedule delete <任务ID>        # 删除自己的任务
/ai schedule delete <任务ID> --admin # 管理员删除任务
```

#### 统计信息
```bash
# 查看统计
/ai schedule stats                  # 个人/权限范围统计
/ai schedule stats --project=SPCB   # 项目统计 (需要权限)
```

### 📊 任务信息显示

#### 任务列表格式
```
✅ 📋 **每日Bug检查**
   ID: 123
   创建者: <EMAIL>          # 管理员模式显示
   查询: project = SPCB AND status = Open
   频率: daily @ 09:00
   类型: 私聊
   目标: 私聊: <EMAIL>      # 通知目标
   状态: active
   成功率: 95.2% (20/21)
   下次执行: 2024-01-15 09:00
   创建时间: 2024-01-01 10:30      # 管理员模式显示
```

#### 通知目标显示格式
```
# 私聊通知
目标: 私聊: <EMAIL>

# 群聊通知
目标: 群聊: SPCB项目群 (group_id_123456)
```

#### 统计信息格式
```
📊 任务统计信息

📈 总体统计:
• 总任务数: 45
• 活跃任务: 38  
• 暂停任务: 7
• 智能任务: 23

📋 项目分布:                        # 管理员可见
• SPCB: 28 个任务
• SPCT: 17 个任务

👥 用户排行 (Top 10):               # 超级管理员可见
• <EMAIL>: 12 个任务
• <EMAIL>: 8 个任务

🏷️ 您的角色: 项目管理员
```

---

## 智能通知

### 🧠 智能通知特性

#### 自动启用条件
智能通知在以下情况自动启用：
- JQL查询包含 `assignee` 关键词
- 任务名称包含 `智能`、`smart`、`分配`、`合并` 等关键词
- 显式指定智能配置参数

#### 核心功能
1. **自动分组**: 根据ticket的assignee自动分组
2. **个性化通知**: 每人只收到分配给自己的ticket
3. **消息合并**: 同一人的多个ticket合并为一条消息
4. **智能回退**: 无assignee时发送给任务创建者

#### 通知目标类型
```python
'private'    # 私聊通知 (默认)
'group'      # 群聊通知
'both'       # 私聊+群聊
'smart'      # 智能选择 (推荐)
```

### 📬 通知消息格式

#### 个人通知示例
```
🔔 定时任务通知: 每日Bug检查

您有 3 个待处理的ticket:

🐛 SPCB-123: 登录页面显示异常
   状态: Open → 优先级: High
   链接: https://jira.shopee.io/browse/SPCB-123

🐛 SPCB-124: 数据加载缓慢  
   状态: In Progress → 优先级: Medium
   链接: https://jira.shopee.io/browse/SPCB-124

📊 统计: 共3个ticket，2个高优先级
⏰ 下次提醒: 明天 09:00
```

---

## 自然语言创建

### 💬 自然语言支持

#### 智能解析能力
- **时间识别**: "每天早上9点"、"工作日下午5点"
- **项目识别**: "SPCB项目"、"我的项目"
- **状态识别**: "未完成的"、"高优先级的"
- **人员识别**: "分配给我的"、"我创建的"

#### 创建流程示例
```
用户: 每天早上10点提醒我查看分配给我的未完成bug

AI: 我来帮您创建这个定时任务：

📋 任务配置:
• 任务名称: 每日Bug提醒
• 查询内容: assignee = currentUser() AND status != Closed AND type = Bug  
• 执行时间: 每天 10:00
• 通知方式: 私聊通知

这个配置是否符合您的需求？您可以说"确认"来创建，或者告诉我需要修改的地方。

用户: 确认

AI: ✅ 定时任务创建成功！任务ID: 156
```

#### 多轮对话支持
```
用户: 帮我设置一个定时任务

AI: 好的！我需要了解一些信息来帮您创建定时任务：
1. 您希望查询什么内容？(比如：我的bug、项目进度等)
2. 多久执行一次？(比如：每天、每周一等)
3. 什么时间执行？(比如：上午9点、下午5点等)

请告诉我这些信息，我会为您智能配置。

用户: 每周一查看SPCB项目的bug

AI: 明白了！还需要确认执行时间，您希望每周一的什么时候收到通知呢？
比如：上午9点、下午2点等
```

---

## 命令参考

### 📚 完整命令列表

#### 任务创建
```bash
# 标准创建
/ai schedule create "任务名称" "JQL查询" "时间表达式"

# 智能通知创建 (包含关键词)
/ai schedule create "智能Bug提醒" "assignee = currentUser()" "daily 09:00"

# 自然语言创建
/ai schedule create 每天早上提醒我查看bug
/ai 帮我设置定时任务查看项目进度
```

#### 任务管理
```bash
# 查看任务
/ai schedule list                    # 个人任务
/ai schedule list --user=email      # 指定用户 (管理员)
/ai schedule list --project=SPCB    # 项目任务 (管理员)
/ai schedule list --all             # 全部任务 (管理员)

# 控制任务
/ai schedule pause <ID>             # 暂停
/ai schedule resume <ID>            # 恢复
/ai schedule delete <ID>            # 删除
/ai schedule pause <ID> --admin     # 管理员操作

# 统计信息
/ai schedule stats                  # 基本统计
/ai schedule stats --project=SPCB   # 项目统计
```

#### 权限查询
```bash
/ai permission                      # 查看权限信息
/ai permission help                 # 权限帮助
/ai permission check "JQL查询"      # 检查查询权限
```

#### 帮助信息
```bash
/ai schedule help                   # 完整帮助
/ai schedule                        # 基本帮助
```

### 🔧 高级用法示例

#### 项目管理员操作
```bash
# 查看项目内所有任务
/ai schedule list --project=SPCB

# 暂停不合规的任务
/ai schedule pause 123 --admin

# 查看项目统计
/ai schedule stats --project=SPCB
```

#### 智能通知配置
```bash
# 基本智能通知
/ai schedule create "团队Bug提醒" "project = SPCB AND assignee in (user1, user2)" "daily 09:00"

# 高级智能通知 (自动检测)
/ai schedule create "智能分配提醒" "assignee != empty AND status = Open" "workdays 17:00"
```

---

## 配置管理

### ⚙️ 权限配置

#### 超级管理员配置
```python
# task_permission_manager.py
SUPER_ADMINS = {
    '<EMAIL>'
}
```

#### 项目管理员配置
```python
# task_permission_manager.py  
PROJECT_MANAGERS = {
    '<EMAIL>': ['SPCB'],
    '<EMAIL>': ['SPCT'],
    # 新增项目管理员
    '<EMAIL>': ['NEW_PROJECT']
}
```

#### 支持项目配置
```python
# task_permission_manager.py
SUPPORTED_PROJECTS = [
    'SPCB', 'SPCT', 'SPMB', 'SPMT', 'SPFB', 'SPFT',
    # 新增项目
    'NEW_PROJECT'
]
```

### 🔄 系统配置

#### 调度器配置
```python
# task_scheduler.py
DEFAULT_SCHEDULE_SETTINGS = {
    'max_tasks_per_user': 50,
    'max_execution_time': 300,  # 5分钟
    'retry_attempts': 3,
    'notification_timeout': 30
}
```

#### 智能通知配置
```python
# advanced_task_manager.py
SMART_NOTIFICATION_CONFIG = {
    'max_tickets_per_message': 10,
    'enable_message_merge': True,
    'fallback_to_creator': True,
    'max_notification_delay': 60
}
```

---

## 安全建议

### 🛡️ 权限管理最佳实践

#### 1. 权限最小化原则
- 用户只获得完成工作所需的最小权限
- 定期审查和调整权限分配
- 避免过度授权

#### 2. 定期权限审计
```bash
# 建议的审计检查
1. 检查超级管理员列表
2. 验证项目管理员权限范围
3. 审查高权限用户的操作日志
4. 监控异常权限使用模式
```

#### 3. 任务创建限制
- 限制普通用户创建全局影响任务
- 监控大范围通知任务的创建
- 对频繁创建任务的用户进行提醒

#### 4. 操作日志记录
```python
# 记录关键操作
- 管理员操作他人任务
- 权限被拒绝的操作尝试  
- 大范围通知任务的创建
- 系统配置的修改
```

### 🚨 安全警告

#### 高风险操作
1. **全局查询任务**: 可能影响所有项目的查询
2. **大范围通知**: 向多人发送通知的任务
3. **频繁执行**: 执行频率过高的任务
4. **权限提升**: 尝试超出权限范围的操作

#### 防护措施
- 自动检测和拦截高风险操作
- 要求额外确认步骤
- 记录详细的操作日志
- 及时通知相关管理员

---

## 故障排除

### 🔍 常见问题解决

#### 权限相关问题

**问题**: "您没有权限执行此操作"
```bash
解决步骤:
1. 检查用户角色: /ai permission
2. 确认操作范围是否在权限内
3. 联系项目管理员或超级管理员
4. 检查任务是否为自己创建
```

**问题**: "普通用户只能管理自己创建的任务"
```bash
解决方案:
1. 确认任务ID是否正确
2. 检查任务创建者是否为本人
3. 如需管理他人任务，联系管理员
```

#### 任务创建问题

**问题**: "调度表达式解析失败"
```bash
检查项目:
1. 时间格式是否正确 (HH:MM)
2. 日期格式是否支持
3. 参考时间表达式示例
4. 使用双引号包围参数
```

**问题**: "参数不足"
```bash
确认格式:
/ai schedule create "任务名称" "JQL查询" "时间表达式"
- 使用双引号包围包含空格的参数
- 确保三个必需参数都提供
```

#### 任务执行问题

**问题**: 任务未按时执行
```bash
检查项目:
1. 任务状态是否为 active
2. 下次执行时间是否正确
3. 系统调度器是否运行正常
4. 查看任务执行日志
```

**问题**: 通知未收到
```bash
排查步骤:
1. 检查通知配置类型
2. 确认Seatalk连接状态  
3. 查看任务执行成功率
4. 检查用户ID和群组设置
```

#### 智能通知问题

**问题**: 智能分组不正确
```bash
检查项目:
1. JQL查询是否包含assignee字段
2. Ticket是否有有效的assignee
3. 用户邮箱格式是否正确
4. 回退机制是否正常工作
```

### 📞 支持联系

#### 技术支持
- **超级管理员**: <EMAIL>
- **SPCB项目**: <EMAIL>  
- **SPCT项目**: <EMAIL>

#### 紧急情况
如遇到系统故障或安全问题，请立即联系超级管理员。

---

## 更新日志

### Version 2.0 (2024-01-15)
- ✅ 新增三级权限管理体系
- ✅ 增强任务管理命令 (支持管理员模式)
- ✅ 添加任务统计功能
- ✅ 完善权限检查机制
- ✅ 优化用户界面和提示信息

### Version 1.5 (2024-01-10)  
- ✅ 新增自然语言任务创建
- ✅ 智能通知功能增强
- ✅ 多轮对话支持
- ✅ 改进时间表达式解析

### Version 1.0 (2024-01-01)
- ✅ 基础定时任务功能
- ✅ 命令行创建方式
- ✅ 基本任务管理
- ✅ 简单通知机制

---

*本文档涵盖了定时任务管理系统的所有功能和使用方法。如有疑问或建议，请联系相关管理员。* 
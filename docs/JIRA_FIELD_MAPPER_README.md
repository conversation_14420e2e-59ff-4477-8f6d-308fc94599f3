# JIRA 字段映射管理功能

## 📋 功能概述

这是一个完善的 JIRA 自定义字段映射管理系统，能够自动获取、缓存和定期更新 JIRA 字段映射关系。

## 🎯 解决的问题

1. **自动字段发现**：无需手动维护字段映射表
2. **智能分类管理**：按业务类别自动分类字段
3. **性能优化**：24小时缓存，避免频繁API调用
4. **容错机制**：多层备选方案，确保系统稳定性
5. **定时更新**：每天自动更新，保持映射关系最新

## 🏗️ 架构设计

### 核心模块

```
app01/ai_module/
├── field_mapper.py          # 字段映射管理器
├── field_update_task.py     # 定时更新任务
└── management/commands/
    └── update_jira_fields.py # Django管理命令
```

### 数据流程

```mermaid
graph TD
    A[JIRA API] --> B[字段映射器]
    B --> C[内存缓存]
    B --> D[数据库存储]
    E[定时任务] --> B
    F[AI助手] --> C
    G[手动命令] --> B
```

## 🔧 核心功能

### 1. 自动字段映射获取

```python
from app01.ai_module.field_mapper import field_mapper

# 获取所有字段映射
mapping = await field_mapper.get_all_fields_mapping()

# 字段映射结构
{
    'all_fields': {},           # 所有字段
    'custom_fields': {},        # 自定义字段
    'system_fields': {},        # 系统字段
    'by_category': {            # 按类别分类
        'people': {},           # 人员相关
        'timeline': {},         # 时间相关
        'project': {},          # 项目相关
        'technical': {},        # 技术相关
        'release_checklist': {} # 发布检查清单
    },
    'name_to_id': {},          # 名称到ID映射
    'id_to_name': {},          # ID到名称映射
    'last_updated': '...',     # 最后更新时间
    'total_count': 150         # 总字段数
}
```

### 2. 智能字段分类

系统会自动将字段分类到以下类别：

- **people**: 人员相关字段（PM、开发、QA等）
- **timeline**: 时间相关字段（开始时间、结束时间等）
- **project**: 项目相关字段（URL、跨团队协作等）
- **technical**: 技术相关字段（环境、配置等）
- **release_checklist**: 发布检查清单字段

### 3. 基于意图的字段选择

```python
# 根据AI意图获取最优字段
fields = await field_mapper.get_fields_by_intent('query_timeline')
# 返回: ['key', 'summary', 'status', 'customfield_11520', 'customfield_11509', ...]
```

### 4. 字段搜索功能

```python
# 搜索包含关键词的字段
results = await field_mapper.search_fields('developer')
# 返回匹配的字段信息列表
```

## 📅 定时更新机制

### 1. Django 管理命令

```bash
# 查看字段映射状态
python manage.py update_jira_fields --show-status

# 手动更新字段映射
python manage.py update_jira_fields

# 强制更新（忽略缓存时间）
python manage.py update_jira_fields --force
```

### 2. Crontab 配置

在系统 crontab 中添加：

```bash
# 每天凌晨2点自动更新
0 2 * * * cd /path/to/chatbot-ar-be && python manage.py update_jira_fields >> /var/log/jira_field_update.log 2>&1
```

或使用 django-crontab：

```python
# settings.py
CRONJOBS = [
    ('0 2 * * *', 'app01.ai_module.field_update_cron.daily_field_update'),
]
```

## 🎮 AI 助手集成

### 用户命令

```bash
# 查看字段映射状态
/ai fields status

# 搜索字段
/ai fields search developer

# 强制更新字段映射
/ai fields update
```

### 响应示例

```
🔧 JIRA 字段映射状态:

📅 上次更新: 2024-01-15 02:00:00
⏰ 距离上次更新: 8.5 小时
🔄 需要更新: 否
📊 总字段数: 156
🔧 自定义字段数: 89
⭐ 重要字段数: 25
```

## 🔍 Shopee 特有字段

系统预定义了 Shopee JIRA 中的重要字段：

### 发布检查清单
- `customfield_34000`: DB Changed
- `customfield_34001`: Config Changed  
- `customfield_34002`: Code Merged
- `customfield_34003`: Signed off

### 人员相关
- `customfield_10306`: Product Manager
- `customfield_10307`: Developer
- `customfield_10308`: QA
- `customfield_37800`: BE List
- `customfield_37801`: FE List

### 时间线相关
- `customfield_11520`: Planned Dev Start Date
- `customfield_11509`: Planned Dev Due Date
- `customfield_11521`: Planned QA Start Date
- `customfield_11510`: Planned QA Due Date

## 🛡️ 容错机制

### 1. 多层缓存策略
- **内存缓存**: 24小时有效期
- **数据库存储**: 持久化备份
- **备用映射**: 基于已知重要字段

### 2. 错误处理
- JIRA连接失败时使用数据库备份
- 数据库读取失败时使用备用映射
- 异步操作异常时降级到同步方案

### 3. 性能优化
- 异步操作避免阻塞
- 线程池执行同步JIRA调用
- 智能缓存减少API调用

## 📊 监控和日志

### 日志记录
```python
logger.info("成功获取并缓存了 156 个字段映射")
logger.warning("字段映射仍然有效，跳过更新")
logger.error("获取字段映射失败: Connection timeout")
```

### 状态监控
```python
status = await field_mapper.get_update_status()
# 返回详细的状态信息，包括更新时间、字段数量等
```

## 🚀 部署说明

### 1. 安装依赖
```bash
pip install aiohttp cryptography
```

### 2. 数据库迁移
```bash
python manage.py makemigrations
python manage.py migrate
```

### 3. 初始化字段映射
```bash
python manage.py update_jira_fields --force
```

### 4. 设置定时任务
```bash
# 添加到 crontab
crontab -e
0 2 * * * cd /path/to/chatbot-ar-be && python manage.py update_jira_fields
```

## 🔧 配置选项

### 缓存配置
```python
# field_mapper.py
CACHE_TIMEOUT = 24 * 60 * 60  # 24小时
```

### 重要字段配置
```python
# 在 SHOPEE_IMPORTANT_FIELDS 中添加新的重要字段
'customfield_xxxxx': {
    'name': 'New Field Name',
    'category': 'project',
    'type': 'select'
}
```

## 📈 性能指标

- **字段获取时间**: 通常 < 3秒
- **缓存命中率**: > 95%
- **内存占用**: < 10MB
- **更新频率**: 每天1次
- **API调用减少**: > 90%

## 🔮 未来扩展

1. **字段变更检测**: 监控字段的新增、删除、修改
2. **字段使用统计**: 分析哪些字段最常被查询
3. **智能字段推荐**: 基于查询历史推荐相关字段
4. **多项目支持**: 支持不同JIRA项目的字段映射
5. **字段关系分析**: 分析字段之间的依赖关系

---

这个字段映射管理系统为 ChatbotAR AI 功能提供了强大的基础设施支持，确保了系统的可扩展性和稳定性！ 
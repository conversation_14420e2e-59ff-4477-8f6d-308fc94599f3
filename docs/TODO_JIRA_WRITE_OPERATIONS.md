# JIRA写操作功能改进 TODO

## 📋 问题描述

**发现时间**: 2025-01-13  
**问题类型**: 功能限制  
**优先级**: 中等

### 🐛 当前问题
用户尝试使用AI助手修改JIRA单状态时遇到限制：

```
用户输入: "/ai 把SPCB-53562改成 done"
系统回复: "抱歉，查询失败：暂不支持该类型的写操作，请提供更具体的操作描述。"
```

### 🎯 期望功能
用户希望能够通过自然语言指令直接修改JIRA单的状态，例如：
- "把SPCB-53562改成done"
- "将这个单子标记为完成"
- "修改SPCB-12345的状态为In Progress"

## 🔍 技术分析

### 当前实现状态
- ✅ JIRA读操作：完全支持
- ✅ Token管理：已实现异步保存
- ⚠️ JIRA写操作：功能受限

### 需要改进的模块
1. **意图识别** (`ai_assistant.py`)
   - 需要更好地识别状态修改意图
   - 支持更多自然语言表达方式

2. **JQL生成** (`jql_generation.py`)
   - 当前主要支持查询操作
   - 需要扩展写操作的处理逻辑

3. **JIRA写操作** (`smart_jira_query.py`)
   - `update_issue()` 方法需要增强
   - 支持更多字段的修改操作

## 🚀 改进方案

### Phase 1: 基础状态修改
- [ ] 支持基本的状态转换（To Do → In Progress → Done）
- [ ] 改进意图识别，支持更多状态修改表达
- [ ] 增强错误提示，提供具体的操作建议

### Phase 2: 扩展字段修改
- [ ] 支持优先级修改
- [ ] 支持经办人变更
- [ ] 支持描述/评论添加

### Phase 3: 批量操作
- [ ] 支持批量状态修改
- [ ] 支持条件性批量操作

## 🔧 技术实现要点

### 1. 意图识别增强
```python
# 需要识别的模式
patterns = [
    "把{issue_key}改成{status}",
    "将{issue_key}标记为{status}",
    "修改{issue_key}的状态为{status}",
    "{issue_key}完成了",
    "关闭{issue_key}"
]
```

### 2. 状态映射
```python
STATUS_MAPPING = {
    'done': 'Done',
    '完成': 'Done', 
    '关闭': 'Closed',
    '进行中': 'In Progress',
    '待处理': 'To Do'
}
```

### 3. 权限验证
- 确保用户有修改权限
- 验证状态转换的合法性
- 提供清晰的错误信息

## ⚠️ 注意事项

1. **权限控制**: 确保只有有权限的用户才能修改
2. **状态验证**: 验证状态转换是否符合工作流规则
3. **审计日志**: 记录所有写操作的详细日志
4. **回滚机制**: 提供操作撤销功能（如果需要）

## 📅 开发计划

**暂时不开发** - 等待产品需求确认和优先级评估

### 前置条件
- [ ] 产品团队确认需求优先级
- [ ] 确定支持的JIRA工作流类型
- [ ] 制定详细的权限控制策略

### 预估工作量
- Phase 1: 3-5个工作日
- Phase 2: 5-7个工作日  
- Phase 3: 7-10个工作日

## 📝 相关文件

- `app01/ai_module/ai_assistant.py` - 意图识别和处理
- `app01/ai_module/smart_jira_query.py` - JIRA操作实现
- `app01/ai_module/prompts/jql_generation.py` - JQL生成逻辑

---

**创建时间**: 2025-01-13  
**创建人**: AI Assistant  
**状态**: 待评估 
# 机器人名称重构文档

## 概述

为了便于后续修改机器人名称，我们对代码进行了重构，将所有硬编码的机器人名称（"ChatbotAR"）替换为从配置中获取的变量。这样，当需要修改机器人名称时，只需要修改一处配置即可。

## 主要修改

1. **集中管理机器人名称**
   - 在 `bot_config.py` 中添加了 `BOT_NAME` 变量
   - 修改了 `get_current_bot_name()` 方法，直接返回 `BOT_NAME` 变量
   - `BOT_NAMES` 列表现在引用 `BOT_NAME` 变量，确保一致性

2. **创建工具函数**
   - 新增 `utils.py` 文件，提供 `get_bot_name()` 函数
   - 便于在不直接导入 `BotConfig` 类的情况下获取机器人名称

3. **更新模块引用**
   - 修改 `ai_config.py` 中的文档注释
   - 修改 `ai_module/__init__.py` 中的模块注释和作者信息
   - 修改 `ai_module/ai_assistant.py` 中的系统提示词
   - 修改 `ai_module/document_integration.py` 中的日志信息

4. **移除硬编码名称**
   - 修改 `views.py` 中的注释
   - 修改 `README_DOCUMENT_PROCESSING.md` 中的项目名称引用
   - 修改多个文档中的硬编码名称，使用变量表示法

## 关于 BOT_NAME 和 BOT_NAMES

### 两者的关系和设计意图

`BOT_NAME` 和 `BOT_NAMES` 看起来有些重复，但它们各自有不同的用途：

- **BOT_NAME**：单一的当前机器人名称，是系统中使用的主要名称
- **BOT_NAMES**：支持的所有机器人名称列表，包括当前名称和历史名称（用于兼容性）

这种设计允许机器人同时响应多个名称，便于在更改机器人名称时进行平滑过渡。当用户使用旧名称时，系统仍然能够正确响应。

### 实现方式

```python
# 机器人名称 - 集中管理，便于后续修改
BOT_NAME = "ChatbotAR"

# 支持的机器人名称列表（按优先级排序，新名称在前）
BOT_NAMES = [
    BOT_NAME,  # 当前主要名称
    # "NewBotName",  # 未来可能的新名称，取消注释后生效
]
```

当需要修改机器人名称时，只需更改 `BOT_NAME` 变量，`BOT_NAMES` 会自动包含新名称。如果需要支持多个名称，可以在 `BOT_NAMES` 中添加额外的名称。

## 文档中的变量表示法

为了便于在文档中使用机器人名称，我们引入了变量表示法：

- 在 Markdown 文档中使用 `${BOT_NAME}` 表示机器人名称
- 这样，当机器人名称变更时，只需要在渲染文档时替换这个变量

例如：

```markdown
# ${BOT_NAME} 使用指南

欢迎使用 ${BOT_NAME}！本文档将介绍如何使用这个强大的工具。

## 基本命令

@${BOT_NAME} help - 获取帮助
@${BOT_NAME} status - 查看状态
```

## 使用方法

### 修改机器人名称

如需修改机器人名称，只需更改 `bot_config.py` 中的 `BOT_NAME` 变量：

```python
# 机器人名称 - 集中管理，便于后续修改
BOT_NAME = "新机器人名称"
```

### 在代码中获取机器人名称

有两种方式获取机器人名称：

1. **直接使用 `BotConfig` 类**

```python
from app01.bot_config import BotConfig

bot_name = BotConfig.BOT_NAME
# 或者
bot_name = BotConfig.get_current_bot_name()
```

2. **使用工具函数**

```python
from app01.utils import get_bot_name

bot_name = get_bot_name()
```

### 在文档中使用变量表示法

在 Markdown 文档中，使用 `${BOT_NAME}` 表示机器人名称：

```markdown
# ${BOT_NAME} 功能指南

使用 @${BOT_NAME} 命令来调用机器人。
```

## 后续工作

1. 继续检查和更新其他可能包含硬编码机器人名称的文件
2. 开发文档变量替换工具，自动处理文档中的 `${BOT_NAME}` 变量
3. 考虑添加国际化支持，使机器人名称可以根据语言环境动态变化
4. 为前端界面添加类似的配置机制 
# ChatbotAR 模拟Seatalk API测试指南

## 🎯 核心概念

这是一个**超级简单**的测试方案，正如您所建议的：
- **一个接口**：`/api/mock-seatalk/webhook/`
- **自动拦截**：当 `test_mode: true` 时，所有输出都被拦截
- **真实处理**：AI处理、JIRA查询、数据库操作都是真实的
- **无副作用**：用户不会收到任何消息

## 🚀 快速开始

### 最简单的测试请求

```bash
curl -X POST http://localhost:8000/api/mock-seatalk/webhook/ \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "message_from_bot_subscriber",
    "event": {
      "message": {
        "text": {"plain_text": "/ai 你好"},
        "sender": {"seatalk_id": "test", "email": "<EMAIL>"}
      }
    },
    "test_mode": true
  }'
```

这就是您想要的最简单方案！🚀

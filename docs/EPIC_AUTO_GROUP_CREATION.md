# Epic状态变更自动建群功能

## 功能概述

Epic状态变更自动建群功能是一个定时任务，用于监控JIRA中Epic的状态变更，并在符合条件时自动创建SeaTalk群组。

## 执行时间

- **执行频率**: 每个工作日（周一到周五）18:00
- **检查范围**: 
  - 周一：检查过去3天（包括周五、周六、周日）的状态变更
  - 周二到周五：检查过去24小时的状态变更

## 触发条件

### 状态变更条件
Epic必须从以下状态之一：
- `WAITING`
- `REQ.GATHERING`
- `FEASIBILITY STUDY`
- `PLANNED`
- `PRD`

切换到以下状态之一：
- `TECH DESIGN`
- `PLANNING`
- `DEVELOPING`

### 额外条件
- Epic的当前状态不能是：`ICEBOX`、`CLOSED`、`DONE`
- 项目必须是：`SPCB` 或 `SPCT`
- 类型必须是：`Epic`

## 实现细节

### 核心函数
```python
check_epic_status_changes_for_group_creation()
```

### 主要流程
1. **连接JIRA**: 使用配置的JIRA Token连接JIRA API
2. **动态时间范围**: 根据当前是周几确定检查的时间范围
3. **JQL查询**: 构建查询条件，获取符合条件的Epic
4. **状态变更检查**: 遍历每个Epic的变更历史，检查是否有符合条件的状态变更
5. **重复建群检测**: 检查数据库中是否已存在包含相同JIRA单号的群组
6. **自动建群**: 对符合条件的Epic调用现有的`auto_create_group_for_jira`函数
7. **结果报告**: 发送执行报告到调试群，包含成功、跳过、失败的统计信息

### 配置位置
- **函数定义**: `app01/seatalk_group_manager.py`
- **定时任务配置**: `djangoProject/settings.py` 中的 `CRONJOBS`
- **日志文件**: `logs/auto_create_group_for_epic.log`

## 监控和报告

### 执行报告
每次执行后会在调试群发送详细的执行报告，包括：
- 执行时间
- 检查范围
- 符合条件的Epic数量
- 成功创建群组的数量
- 跳过创建的数量（群组已存在）
- 失败的数量和详情
- 跳过和失败的详细列表

### 错误处理
- 如果执行过程中出现错误，会发送错误通知到调试群
- 所有错误都会记录到日志文件中

## 测试

### 手动测试
```bash
python test_epic_auto_group.py
```

### 查看日志
```bash
tail -f logs/auto_create_group_for_epic.log
```

## 注意事项

1. **重复建群检测**: 系统现在会自动检测数据库中是否已存在包含相同JIRA单号的群组，如果存在则跳过创建，避免重复建群
2. **JIRA Token**: 确保JIRA Token有效且有足够的权限访问Epic信息
3. **网络连接**: 确保服务器能够正常访问JIRA API
4. **调试群**: 确保调试群ID正确，以便接收执行报告

## 相关文件

- `app01/seatalk_group_manager.py` - 主要实现文件
- `djangoProject/settings.py` - 定时任务配置
- `test_epic_auto_group.py` - 测试脚本
- `logs/auto_create_group_for_epic.log` - 执行日志

## 更新计划任务

如果需要更新定时任务配置，执行以下命令：
```bash
python manage.py crontab add
```

查看当前计划任务：
```bash
python manage.py crontab show
```

删除计划任务：
```bash
python manage.py crontab remove
``` 
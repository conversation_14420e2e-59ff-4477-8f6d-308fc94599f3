# 📋 ChatbotAR 项目测试规则

## 🚨 核心原则：绝不干扰用户

### ❌ 禁止的测试行为

#### 1. 创建真实群组
- ❌ 不能调用 `auto_create_group_for_jira()` 等创建群组的函数
- ❌ 不能测试 "new group" 命令的真实执行
- ❌ 不能调用任何会在Seatalk中创建群组的API

#### 2. 发送真实消息
- ❌ 不能调用 `test_for_seatalk_bot()` 发送群聊消息
- ❌ 不能调用 `sent_aio()` 发送私聊消息
- ❌ 不能调用任何Seatalk发送消息的API

#### 3. 修改生产数据
- ❌ 不能修改真实的JIRA数据
- ❌ 不能更新真实的checklist字段
- ❌ 不能执行任何写入操作

#### 4. 调用外部服务
- ❌ 不能调用真实的JIRA API（除非只读且不影响用户）
- ❌ 不能调用GitLab API
- ❌ 不能发起MR检查等可能产生通知的操作

### ✅ 允许的测试行为

#### 1. 配置和逻辑测试
- ✅ 测试机器人配置管理（bot_config.py）
- ✅ 测试消息解析逻辑
- ✅ 测试命令路由逻辑
- ✅ 测试JIRA单号提取

#### 2. 模拟数据测试
- ✅ 使用模拟的Seatalk数据结构
- ✅ 测试MessageContext的创建和解析
- ✅ 测试命令处理器的返回结果

#### 3. 单元测试
- ✅ 测试辅助函数
- ✅ 测试数据处理逻辑
- ✅ 测试错误处理

## 🔒 安全测试接口

### 使用Mock Seatalk API进行安全测试

**接口地址**: `/api/mock-seatalk/webhook/`

**特点**:
- ✅ 调用真实的ChatbotAR处理逻辑
- ✅ 自动拦截所有输出消息
- ✅ 用户不会收到任何消息
- ✅ 可以安全测试所有功能，包括 "new group"

**使用方法**:
```python
import requests

# 测试私聊命令
response = requests.post('http://localhost:8000/api/mock-seatalk/webhook/', json={
    "test_mode": True,  # 🔑 必须启用测试模式
    "message_type": "private",
    "user_id": "test_user_001", 
    "message": "/ai 你好"
})

# 测试群聊命令（包括 new group）
response = requests.post('http://localhost:8000/api/mock-seatalk/webhook/', json={
    "test_mode": True,
    "message_type": "group",
    "user_id": "test_user_001",
    "group_id": "test_group_001",
    "message": "@ChatbotAR new group SPCT-1234"
})
```

**安全保证**:
- 所有 `test_for_seatalk_bot()` 调用被拦截
- 所有 `sent_aio()` 调用被拦截
- 所有群组创建消息被拦截
- 用户不会收到任何通知

## 📝 测试文件命名规则

### 安全测试文件
- `test_*.py` - 仅包含安全的单元测试
- `safe_test_*.py` - 明确标记为安全测试
- `mock_test_*.py` - 使用Mock接口的测试

### 危险测试文件（需要特别注意）
- `integration_test_*.py` - 集成测试，可能涉及外部服务
- `real_*.py` - 真实环境测试，绝对禁止在生产环境运行

## 🧪 推荐的测试策略

### 1. 分层测试
```
单元测试 (安全) → Mock测试 (安全) → 集成测试 (隔离环境)
```

### 2. 测试环境隔离
- **开发环境**: 可以进行Mock测试
- **测试环境**: 可以进行集成测试
- **生产环境**: 只能进行只读检查

### 3. 代码审查要求
- 所有测试代码必须经过审查
- 特别关注是否包含真实的API调用
- 确认不会影响用户体验

## 🔍 检查清单

在编写测试代码前，请确认：

- [ ] 不会创建真实的群组
- [ ] 不会发送真实的消息
- [ ] 不会修改生产数据
- [ ] 不会调用外部API（除只读外）
- [ ] 使用了适当的Mock机制
- [ ] 添加了充分的注释说明安全性

## 🚨 违规处理

如果发现测试代码违反上述规则：

1. **立即停止运行**
2. **回滚相关更改**
3. **检查是否对用户造成影响**
4. **修复测试代码**
5. **加强代码审查**

## 📄 更新日志

### v1.0.0 (2025-01-07)
- 制定基础测试规则
- 明确禁止和允许的行为
- 规范安全测试接口使用

---

**记住**: 用户体验是第一优先级，任何可能干扰用户的测试都是不可接受的！ 
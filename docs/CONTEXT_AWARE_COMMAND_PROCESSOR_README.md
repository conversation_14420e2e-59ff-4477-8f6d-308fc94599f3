# 🤖 上下文感知命令处理器

## 📋 概述

新的上下文感知命令处理器统一了群聊和私聊的消息处理逻辑，支持机器人名称的灵活配置，并提供了智能的上下文理解能力。

## 🎯 核心功能

### 1. 统一命令处理
- **群聊**：支持@机器人的命令格式，可以从群名自动提取JIRA单号
- **私聊**：支持直接命令格式，需要用户提供完整的JIRA单号
- **AI查询**：统一的/ai前缀处理

### 2. 机器人名称兼容性
- 支持多个机器人名称的同时识别
- 向后兼容，无需修改现有用法
- 新名称优先级管理

### 3. 上下文感知
- **群聊上下文**：自动从群名提取JIRA单号
- **私聊模式**：明确要求提供完整信息
- **智能降级**：上下文不足时提供明确指导

## 🔧 机器人名称修改指南

### 当前配置
```python
# app01/bot_config.py
BOT_NAMES = [
    "ChatbotAR",  # 当前主要名称
    # "NewBotName",  # 未来可能的新名称，取消注释后生效
]
```

### 添加新机器人名称

#### 方法1：平滑过渡（推荐）
1. 编辑 `app01/bot_config.py`
2. 在 `BOT_NAMES` 列表开头添加新名称：
```python
BOT_NAMES = [
    "NewBotName",    # 新的主要名称
    "ChatbotAR",     # 保留旧名称兼容
]
```
3. 重启服务

#### 方法2：完全替换
1. 将新名称放在第一位，移除旧名称：
```python
BOT_NAMES = [
    "NewBotName",  # 新的唯一名称
]
```

### 名称优先级
- 列表中第一个名称为主要名称（用于生成帮助信息）
- 所有名称都会被识别和响应
- 建议保持2-3个名称以确保兼容性

## 📝 支持的命令格式

### 群聊命令
```bash
# 基础查询（可从群名提取JIRA单号）
@ChatbotAR bug
@ChatbotAR timeline
@ChatbotAR new group

# 带单号的查询
@ChatbotAR bug SPCB-1234
@ChatbotAR timeline SPCT-5678
@ChatbotAR new group SPCB-1234

# 其他功能
@ChatbotAR /mr chatbot
@ChatbotAR /checklist SPCB-1234 SO,CM
@ChatbotAR SPCB-1234  # 单号详情查询

# AI功能
@ChatbotAR /ai 查询SPCB-1234的状态
```

### 私聊命令
```bash
# AI功能
/ai 你好
/ai 查询SPCB-1234的状态

# 传统功能（需要完整单号）
bug SPCB-1234
timeline SPCT-5678
new group SPCB-1234

# 其他功能
/mr chatbot
/checklist SPCB-1234 SO,CM
SPCB-1234  # 单号详情查询
```

## 🏗️ 架构设计

### 模块结构
```
app01/
├── bot_config.py          # 机器人配置管理
├── command_processor.py   # 命令处理器
└── views.py              # 主处理逻辑（已重构）
```

### 处理流程
```
1. 接收Seatalk消息
2. 创建MessageContext（消息上下文）
3. 使用CommandProcessor处理命令
4. 根据上下文发送响应
5. 异常时回退到原有逻辑
```

### 上下文感知逻辑
```python
# 群聊模式
if context.has_context and context.group_name:
    jira_key = extract_from_group_name(context.group_name)
else:
    jira_key = extract_from_message(command_text)

# 私聊模式
jira_key = extract_from_message(command_text)
if not jira_key:
    return "请提供完整的JIRA单号"
```

## 🔄 向后兼容性

### 现有功能保持不变
- 所有现有命令格式继续有效
- 现有的群聊@ChatbotAR格式完全兼容
- 私聊/ai前缀保持不变

### 渐进式升级
- 新系统作为主要处理器
- 原有逻辑作为后备处理器
- 异常时自动回退，确保稳定性

## 🧪 测试

### 运行测试脚本
```bash
cd chatbot-ar-be
python test_new_command_processor.py
```

### 测试覆盖范围
- ✅ 机器人配置管理
- ✅ 消息上下文解析
- ✅ 命令处理逻辑
- ✅ 机器人名称兼容性

### 预期输出
```
🚀 开始测试新的上下文感知命令处理器...

🔧 测试机器人配置...
  ✅ 当前机器人名称: ChatbotAR
  ✅ 群聊@提及检测: True, 前缀: @ChatbotAR 
  ✅ 提取的群聊命令: new group SPCT-1234
  ✅ 提取的私聊命令: 你好
  ✅ 群聊帮助信息长度: xxx 字符
  ✅ 私聊帮助信息长度: xxx 字符

📊 测试结果: 4/4 通过
🎉 所有测试通过！新的命令处理器已准备就绪。
```

## 🚀 部署步骤

### 1. 备份现有配置
```bash
cp app01/views.py app01/views.py.backup
```

### 2. 确认新文件存在
- `app01/bot_config.py`
- `app01/command_processor.py`
- `test_new_command_processor.py`

### 3. 运行测试
```bash
python test_new_command_processor.py
```

### 4. 重启服务
```bash
./stop.sh
./start.sh
```

### 5. 验证功能
- 发送群聊@ChatbotAR消息测试
- 发送私聊/ai消息测试
- 确认所有命令正常响应

## 📞 故障排除

### 常见问题

#### Q: 新命令处理器不工作？
A: 检查日志中的"新命令处理器异常"信息，系统会自动回退到原有逻辑

#### Q: 机器人名称修改后不生效？
A: 重启Django服务，确保配置重新加载

#### Q: 私聊命令格式错误？
A: 确认使用了正确的前缀：
- AI查询：`/ai 查询内容`
- 传统命令：`bug SPCB-1234`（需要完整单号）

#### Q: 群聊无法从群名提取JIRA单号？
A: 确认群名包含格式为`SP[A-Z]+-\d{1,6}`的JIRA单号

### 应急回退
如果遇到严重问题，可以临时禁用新处理器：
```python
# 在views.py中注释掉新处理器调用
# return _handle_message_legacy(data)
```

## 🔮 未来扩展

### 计划功能
- [ ] 支持更多消息类型（图片、文件等）
- [ ] 命令别名系统
- [ ] 用户权限管理
- [ ] 命令执行历史记录
- [ ] 动态配置热加载

### 扩展指南
1. 在`command_processor.py`中添加新的`_process_xxx_command`方法
2. 在`_process_traditional_command`中添加路由逻辑
3. 更新帮助信息
4. 添加对应的测试用例

## 📄 更新日志

### v1.0.0 (2025-01-07)
- ✨ 实现上下文感知命令处理器
- ✨ 统一群聊和私聊处理逻辑
- ✨ 支持机器人名称兼容性管理
- ✨ 添加完整的测试框架
- 🔧 重构views.py消息处理逻辑
- 📚 提供详细的使用文档

---

**注意**: 此系统完全向后兼容，不会影响现有功能的正常使用。 
# SeaTalk 项目管理功能详细配置指南

## 文档概述

本文档详细说明 ChatBot AutoRelease 平台中 SeaTalk 项目管理功能的具体实现、配置要求和新项目接入流程。该功能模块主要包括自动群组管理、Epic 里程碑提醒、机器人回调处理等核心组件。

---

## 第一部分：功能详细说明

### 1.1 SeaTalk 群组自动管理

#### 1.1.1 核心功能
**文件位置**: `chatbot-ar-be/app01/seatalk_group_manager.py`

**主要功能**:
- **自动群组创建**: 根据 JIRA Epic 自动创建对应的 SeaTalk 群组
- **成员智能收集**: 从 JIRA Epic 中提取相关人员信息
- **团队 TL 自动添加**: 根据成员邮箱自动识别并添加对应的团队 Leader
- **Timeline 信息推送**: 群组创建后自动发送 Epic 时间线信息
- **变更同步**: 监控 Epic 信息变更并同步到群组

#### 1.1.2 群组创建流程
```python
def auto_create_group_for_jira(jira_key: str) -> Dict:
    """
    1. 连接 JIRA 获取 Epic 详细信息
    2. 提取人员信息（PM、开发、QA等）
    3. 确定群主（基于项目类型）
    4. 收集所有成员邮箱
    5. 创建 SeaTalk 群组
    6. 发送 Timeline 信息
    """
```

#### 1.1.3 人员角色映射
- **Product Manager**: `customfield_10306`
- **Developer**: `customfield_10307`
- **FE List**: `customfield_37801`
- **BE List**: `customfield_37800`
- **QA**: `customfield_10308`
- **QA List**: `customfield_12202`
- **Project Manager**: `customfield_10600`

### 1.2 Epic 里程碑提醒系统

#### 1.2.1 核心功能
**文件位置**: `chatbot-ar-be/app01/epic_milestone_reminder.py`

**提醒类型**:
1. **联调开始提醒** (`integration_start`)
2. **提测时间提醒** (`qa_start`)
3. **UAT开始提醒** (`uat_start`)
4. **UAT Sign-off提醒** (`uat_signoff`)
5. **发布准备提醒** (`release_reminder`)

#### 1.2.2 日期字段映射
- **Planned Integration Start Date**: `customfield_12634`
- **Planned QA Start Date**: `customfield_11521`
- **Planned UAT Start Date**: `customfield_11522`
- **Planned UAT Due Date**: `customfield_11511`

#### 1.2.3 执行机制
- **定时执行**: 每天早上10点
- **工作日检查**: 自动跳过周末
- **群组获取**: 从数据库动态查询匹配的群组
- **错误处理**: 异常自动通知管理员

### 1.3 SeaTalk 机器人回调处理

#### 1.3.1 支持命令
**API 接口**: `/api/get_seatalk_recall`

**命令类型**:
```python
# Timeline 查询
if "timeline" in text.lower() or "tl" in text.lower():
    # 获取 Epic 时间线信息

# Checklist 操作  
if "/checklist" in text.lower():
    # 支持字段: SO, CM, CC, DC

# Bug 状态查询
if "bug" in text.lower():
    # 查询需求下的 Bug 状态
```

#### 1.3.2 Epic Key 识别机制
```python
# 正则匹配模式
pattern = r'SP[A-Z]+-\d{1,6}'

# 识别优先级
1. 消息内容中的 Epic key
2. 群名称中的 Epic key
3. 容错处理和提示
```

### 1.4 定时监控和提醒

#### 1.4.1 Live Bug 监控
- **SPS Chatbot Live Bug**: 每天10点检查
- **SPCB Live Bug**: 每天10点检查
- **Chat Live Bug**: 每天10点检查

#### 1.4.2 MR 处理提醒
- **执行频率**: 每天10-19点整点
- **覆盖团队**: Chatbot、Channel、Data 团队
- **提醒对象**: Team Leader 和相关开发人员

#### 1.4.3 Timeline 变更监控
- **检查频率**: 每60分钟
- **监控范围**: 最近61分钟内更新的 Epic
- **变更类型**: 时间线、人员、状态变更

---

## 第二部分：新项目接入配置详解

### 2.1 项目分类和配置要求

#### 2.1.1 完全新项目接入（如新增 SPXX 项目）
**需要的配置**:
1. ✅ 增加项目配置（类似 SPCB）
2. ✅ JIRA 账号项目权限
3. ✅ 机器人服务范围
4. ✅ 数据库配置
5. ✅ 定时任务配置

#### 2.1.2 现有项目扩展功能
**需要的配置**:
1. ❌ 项目配置（已存在）
2. ✅ JIRA 账号项目权限（如果之前没有）
3. ✅ 机器人服务范围（如果之前没有）
4. ❌ 数据库配置（通用）
5. ❌ 定时任务配置（通用）

### 2.2 详细配置步骤

#### 2.2.1 增加项目配置（类似 SPCB 的配置）

**配置文件**: `chatbot-ar-be/app01/epic_milestone_reminder.py`

**需要修改的配置项**:

```python
# 1. Epic 里程碑提醒配置
EPIC_REMINDER_CONFIG = {
    "integration_start": {
        "enabled_projects": ["SPCB", "SPCT", "SPXX"],  # 添加新项目
        # ... 其他配置保持不变
    },
    "qa_start": {
        "enabled_projects": ["SPCB", "SPCT", "SPXX"],  # 添加新项目
        # ... 其他配置保持不变
    },
    # ... 其他提醒类型同样添加
}
```

**配置文件**: `chatbot-ar-be/app01/seatalk_group_manager.py`

```python
# 2. 群主配置规则
def create_seatalk_group(jira_key: str, ...):
    # 设置群主
    if "SPCB" in jira_key:
        owner_email = "<EMAIL>"
    elif "SPCT" in jira_key:
        owner_email = "<EMAIL>"
    elif "SPXX" in jira_key:  # 添加新项目群主配置
        owner_email = "<EMAIL>"
    else:
        owner_email = "<EMAIL>"
```

**配置文件**: `chatbot-ar-be/app01/views.py`

```python
# 3. 定时任务 JQL 查询配置
def check_epic_milestone_reminders():
    # 更新 JQL 查询以包含新项目
    jql = 'project in (SPCB, SPCT, SPXX) AND type = Epic AND status not in (Done, Closed, Icebox)'

def check_timeline_changes():
    # 更新 Timeline 变更检查的项目范围
    jql = 'project in (SPCB, SPCT, SPXX) AND issuetype = Epic AND updated >= -61m'
```

#### 2.2.2 JIRA 账号项目权限配置

**权限类型**: 所有新项目都需要配置

**所需权限**:
```
项目权限要求:
├── Browse Project (浏览项目)
├── View Issues (查看问题)
├── View Development Tools (查看开发工具)
├── Administer Projects (管理项目) - 可选，用于更新字段
└── View Voters and Watchers (查看关注者) - 用于获取人员信息
```

**配置步骤**:
1. 登录 JIRA 管理界面
2. 进入项目设置 → Permissions
3. 为 `chatbot-ar-service` 账号添加以下权限：
   - Browse Projects
   - View Issues  
   - View Development Tools
   - View Voters and Watchers
4. 如果需要更新 Epic 字段，添加：
   - Edit Issues
   - Administer Projects

**验证方法**:
```bash
# 测试 JIRA 连接和权限
curl -H "Authorization: Bearer YOUR_JIRA_TOKEN" \
     -H "Content-Type: application/json" \
     "https://jira.shopee.io/rest/api/2/project/SPXX"
```

#### 2.2.3 机器人服务范围配置

**权限类型**: 所有新项目都需要配置

**SeaTalk 机器人权限要求**:
```
机器人权限:
├── Send Group Messages (发送群组消息)
├── Create Groups (创建群组)
├── Add/Remove Group Members (添加/移除群组成员)
├── Read Group Information (读取群组信息)
└── Receive Webhooks (接收回调)
```

**配置步骤**:
1. **申请机器人权限扩展**:
   ```
   联系 SeaTalk 管理员扩展机器人服务范围:
   - 机器人名称: ChatbotAR
   - 新增项目: SPXX
   - 所需权限: 群组管理和消息发送
   ```

2. **添加项目相关群组**:
   ```python
   # 在数据库中添加项目群组映射
   # 模型: SeatalkGroup
   
   # 示例群组配置
   group_mappings = [
       {
           "group_name": "SPXX-123 项目群",
           "group_id": "群组ID",
           "project_key": "SPXX-123"
       }
   ]
   ```

3. **验证机器人权限**:
   ```bash
   # 测试发送消息
   curl -X POST "https://autorelease.chatbot.shopee.io/api/jira_seatalk" \
        -H "Content-Type: application/json" \
        -d '{"message": "测试消息", "groupId": "测试群组ID"}'
   ```

#### 2.2.4 数据库配置

**配置类型**: 新项目通常需要初始化数据

**所需操作**:

1. **SeatalkGroup 表初始化**:
   ```sql
   -- 为新项目添加群组记录
   INSERT INTO app01_seatalkgroup (group_name, group_id, created_at)
   VALUES 
   ('SPXX-项目群示例', 'GROUP_ID_PLACEHOLDER', NOW());
   ```

2. **权限验证**:
   ```python
   # 确保数据库用户有以下权限
   GRANT SELECT, INSERT, UPDATE ON app01_seatalkgroup TO chatbot_ar_user;
   GRANT SELECT, INSERT, UPDATE ON app01_deploy TO chatbot_ar_user;
   GRANT SELECT, INSERT, UPDATE ON app01_autorelease TO chatbot_ar_user;
   ```

3. **数据初始化脚本**:
   ```python
   # 创建初始化脚本
   def initialize_project_data(project_key):
       """为新项目初始化必要的数据库记录"""
       # 创建项目配置记录
       # 初始化群组映射
       # 设置默认权限
   ```

#### 2.2.5 定时任务配置

**配置类型**: 新项目通常继承现有配置，但需要验证

**验证项目**:

1. **Crontab 配置检查**:
   ```bash
   # 检查现有定时任务是否覆盖新项目
   crontab -l | grep epic-reminder
   crontab -l | grep check_timeline
   ```

2. **JQL 查询验证**:
   ```python
   # 确保所有定时任务的 JQL 查询都包含新项目
   
   # Epic 里程碑提醒
   jql = 'project in (SPCB, SPCT, SPXX) AND type = Epic'
   
   # Timeline 变更检查  
   jql = 'project in (SPCB, SPCT, SPXX) AND issuetype = Epic'
   
   # Live Bug 监控（如果适用）
   jql = 'project = "SPXX" AND type = bug AND "Server Environment" = "Production (Live)"'
   ```

3. **错误通知配置**:
   ```python
   # 确保新项目的错误也会通知到管理员
   def send_error_notification(error_message, epic_key=None):
       # 错误通知会发送到调试群
       debug_group_id = "NzQzMzAxODcyMjAy"
       # 自动@管理员
       admin_email = "<EMAIL>"
   ```

---

## 第三部分：配置模板和检查清单

### 3.1 新项目接入配置模板

#### 3.1.1 配置文件模板

**Epic 里程碑配置模板**:
```python
# epic_milestone_reminder.py
EPIC_REMINDER_CONFIG = {
    "integration_start": {
        "enabled_projects": ["SPCB", "SPCT", "SPXX"],  # 添加新项目
        "enabled": True,
        "reminder_days": 1,
        "target_audience": "dev",
        "message_template": "🔔 **联调开始提醒** 🔔\n\n项目：【{epic_key}】{epic_title}\n🗓️ 联调开始时间：{date}\n⏰ 距离联调开始还有 1 个工作日\n\n请各位开发同学做好准备工作：\n• 确保开发工作已完成\n• 检查代码质量\n• 准备联调环境\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },
    # ... 其他提醒类型配置
}
```

**群组管理配置模板**:
```python
# seatalk_group_manager.py
def create_seatalk_group(jira_key: str, ...):
    # 群主配置规则
    if "SPCB" in jira_key:
        owner_email = "<EMAIL>"
    elif "SPCT" in jira_key:
        owner_email = "<EMAIL>"
    elif "SPXX" in jira_key:  # 新项目配置
        owner_email = "<EMAIL>"
    else:
        owner_email = "<EMAIL>"
```

#### 3.1.2 数据库初始化模板

```sql
-- SeatalkGroup 表初始化
INSERT INTO app01_seatalkgroup (group_name, group_id, created_at, updated_at)
VALUES 
    ('SPXX-项目群1', 'GROUP_ID_1', NOW(), NOW()),
    ('SPXX-项目群2', 'GROUP_ID_2', NOW(), NOW());

-- 验证数据
SELECT * FROM app01_seatalkgroup WHERE group_name LIKE 'SPXX%';
```

### 3.2 配置检查清单

#### 3.2.1 接入前检查清单

- [ ] **项目信息确认**
  - [ ] 项目 Key (如 SPXX)
  - [ ] 项目负责人邮箱
  - [ ] 项目团队结构
  - [ ] JIRA 项目是否已创建

- [ ] **权限准备**
  - [ ] JIRA 项目访问权限申请
  - [ ] SeaTalk 机器人权限扩展申请
  - [ ] 数据库访问权限确认

#### 3.2.2 配置实施检查清单

- [ ] **代码配置**
  - [ ] epic_milestone_reminder.py 中的 enabled_projects 更新
  - [ ] seatalk_group_manager.py 中的群主配置添加
  - [ ] views.py 中的 JQL 查询更新
  - [ ] 定时任务配置检查

- [ ] **权限配置**  
  - [ ] JIRA token 项目权限验证
  - [ ] SeaTalk 机器人群组权限验证
  - [ ] 数据库连接权限验证

- [ ] **数据初始化**
  - [ ] SeatalkGroup 表数据添加
  - [ ] 测试群组创建
  - [ ] 初始消息发送测试

#### 3.2.3 接入后验证检查清单

- [ ] **功能验证**
  - [ ] Epic 群组自动创建测试
  - [ ] Timeline 信息推送测试  
  - [ ] 里程碑提醒功能测试
  - [ ] 机器人回调命令测试

- [ ] **定时任务验证**
  - [ ] Epic 里程碑检查执行确认
  - [ ] Timeline 变更监控确认
  - [ ] Live Bug 提醒测试（如适用）

- [ ] **错误处理验证**
  - [ ] 异常情况错误通知测试
  - [ ] 日志记录确认
  - [ ] 回滚方案准备

---

## 第四部分：故障排除和维护

### 4.1 常见配置问题

#### 4.1.1 JIRA 权限问题
**问题症状**: Epic 信息获取失败，提示权限不足
**解决方案**:
```python
# 检查 JIRA 连接和权限
def test_jira_permissions(project_key):
    jira = JIRA("https://jira.shopee.io", token_auth=JIRA_TOKEN)
    try:
        # 测试项目访问
        project = jira.project(project_key)
        print(f"项目访问成功: {project.name}")
        
        # 测试 Epic 查询
        jql = f'project = {project_key} AND type = Epic'
        issues = jira.search_issues(jql, maxResults=1)
        print(f"Epic 查询成功，找到 {len(issues)} 个结果")
        
    except Exception as e:
        print(f"权限检查失败: {str(e)}")
```

#### 4.1.2 SeaTalk 机器人权限问题
**问题症状**: 群组创建失败或消息发送失败
**解决方案**:
```python
# 测试机器人权限
def test_seatalk_permissions():
    try:
        # 测试发送消息到调试群
        test_message = "机器人权限测试消息"
        test_group_id = "NzQzMzAxODcyMjAy"
        test_for_seatalk_bot(test_message, False, test_group_id)
        print("SeaTalk 机器人权限正常")
    except Exception as e:
        print(f"SeaTalk 权限检查失败: {str(e)}")
```

#### 4.1.3 群组查询失败问题
**问题症状**: 找不到对应的 SeaTalk 群组
**解决方案**:
```python
# 群组查询调试
def debug_group_lookup(epic_key):
    try:
        groups = SeatalkGroup.objects.filter(group_name__contains=epic_key)
        print(f"查找 {epic_key} 相关群组:")
        for group in groups:
            print(f"  - {group.group_name}: {group.group_id}")
        
        if not groups.exists():
            print(f"未找到包含 {epic_key} 的群组，请检查数据库配置")
            
    except Exception as e:
        print(f"群组查询失败: {str(e)}")
```

### 4.2 维护和监控

#### 4.2.1 定期检查项目
```bash
# 每周执行的维护检查
#!/bin/bash

# 1. 检查 JIRA 连接
curl -H "Authorization: Bearer $JIRA_TOKEN" \
     "https://jira.shopee.io/rest/api/2/myself"

# 2. 检查定时任务执行状态
tail -n 100 /var/log/cron.log | grep epic-reminder

# 3. 检查数据库连接
python manage.py dbshell -c "SELECT COUNT(*) FROM app01_seatalkgroup;"

# 4. 检查 SeaTalk 机器人状态
curl -X POST "https://autorelease.chatbot.shopee.io/api/jira_seatalk" \
     -H "Content-Type: application/json" \
     -d '{"message": "系统状态检查", "groupId": "NzQzMzAxODcyMjAy"}'
```

#### 4.2.2 性能监控
```python
# 定时任务执行时间监控
def monitor_epic_reminder_performance():
    start_time = time.time()
    
    try:
        check_epic_milestone_reminders()
        execution_time = time.time() - start_time
        
        if execution_time > 300:  # 超过5分钟
            alert_message = f"Epic 提醒任务执行时间过长: {execution_time:.2f}秒"
            test_for_seatalk_bot(alert_message, False, "NzQzMzAxODcyMjAy")
            
    except Exception as e:
        error_message = f"Epic 提醒任务执行失败: {str(e)}"
        test_for_seatalk_bot(error_message, False, "NzQzMzAxODcyMjAy")
```

---

## 第五部分：高级配置和定制

### 5.1 自定义提醒类型

#### 5.1.1 添加新的提醒类型
```python
# 在 EPIC_REMINDER_CONFIG 中添加新配置
EPIC_REMINDER_CONFIG = {
    # ... 现有配置
    
    # 新增：Code Review 提醒
    "code_review_reminder": {
        "enabled_projects": ["SPCB", "SPXX"],
        "enabled": True,
        "reminder_days": 1,
        "target_audience": "dev",
        "target_weekday": None,  # 不限制星期
        "message_template": "👨‍💻 **Code Review 提醒** 👨‍💻\n\n项目：【{epic_key}】{epic_title}\n📅 Code Review 截止时间：{date}\n⏰ 请及时完成 Code Review\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },
    
    # 新增：发布后回顾提醒
    "post_release_review": {
        "enabled_projects": ["SPCB", "SPXX"],
        "enabled": True,
        "reminder_days": 1,  # 发布后1天
        "target_audience": "all",
        "message_template": "📊 **发布后回顾提醒** 📊\n\n项目：【{epic_key}】{epic_title}\n📅 发布完成时间：{date}\n⏰ 请准备发布后回顾会议\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    }
}
```

#### 5.1.2 自定义 JIRA 字段映射
```python
# 为新项目添加特殊字段映射
PROJECT_SPECIFIC_FIELDS = {
    "SPCB": {
        "custom_review_date": "customfield_12345",
        "release_manager": "customfield_12346"
    },
    "SPXX": {
        "custom_review_date": "customfield_22345",
        "release_manager": "customfield_22346"
    }
}

def get_project_specific_field(epic_key, field_name):
    """获取项目特定的字段值"""
    project_key = epic_key.split('-')[0]
    if project_key in PROJECT_SPECIFIC_FIELDS:
        field_id = PROJECT_SPECIFIC_FIELDS[project_key].get(field_name)
        if field_id:
            return getattr(epic_issue.fields, field_id, None)
    return None
```

### 5.2 团队特定配置

#### 5.2.1 团队 TL 映射配置
```python
# 团队 TL 邮箱映射 - 新项目需要配置
TEAM_LEADER_MAPPING = {
    # Chatbot 团队
    "chatbot": {
        "fe": ["<EMAIL>"],
        "be": ["<EMAIL>"],
        "qa": ["<EMAIL>"]
    },
    
    # 新项目团队 - 需要添加
    "spxx": {
        "fe": ["<EMAIL>"],
        "be": ["<EMAIL>"],
        "qa": ["<EMAIL>"]
    }
}

def get_team_leaders_for_project(project_key):
    """根据项目获取团队 TL"""
    project_key = project_key.lower()
    if project_key.startswith('spcb'):
        return TEAM_LEADER_MAPPING["chatbot"]
    elif project_key.startswith('spxx'):
        return TEAM_LEADER_MAPPING["spxx"]
    else:
        return {}
```

### 5.3 消息模板定制

#### 5.3.1 项目特定消息模板
```python
# 项目特定的消息模板
PROJECT_MESSAGE_TEMPLATES = {
    "SPCB": {
        "integration_start": "🔔 **Chatbot 联调开始提醒** 🔔\n\n...",
        "qa_start": "🧪 **Chatbot 提测时间提醒** 🧪\n\n..."
    },
    "SPXX": {
        "integration_start": "🔔 **SPXX 项目联调开始提醒** 🔔\n\n...",
        "qa_start": "🧪 **SPXX 项目提测时间提醒** 🧪\n\n..."
    }
}

def get_message_template(project_key, reminder_type):
    """获取项目特定的消息模板"""
    if project_key in PROJECT_MESSAGE_TEMPLATES:
        return PROJECT_MESSAGE_TEMPLATES[project_key].get(
            reminder_type, 
            EPIC_REMINDER_CONFIG[reminder_type]["message_template"]
        )
    return EPIC_REMINDER_CONFIG[reminder_type]["message_template"]
```

---

## 第六部分：安全和最佳实践

### 6.1 安全配置

#### 6.1.1 权限最小化原则
```python
# JIRA Token 权限范围限制
JIRA_PERMISSIONS = {
    "required": [
        "Browse Projects",
        "View Issues", 
        "View Development Tools"
    ],
    "optional": [
        "Edit Issues",  # 仅在需要更新字段时
        "Administer Projects"  # 仅管理员需要
    ]
}

# SeaTalk 机器人权限限制
SEATALK_PERMISSIONS = {
    "required": [
        "Send Group Messages",
        "Read Group Information"
    ],
    "project_specific": [
        "Create Groups",  # 仅特定项目需要
        "Manage Group Members"  # 仅特定项目需要
    ]
}
```

#### 6.1.2 敏感信息保护
```python
# 环境变量配置
import os
from django.conf import settings

# 敏感信息通过环境变量配置
JIRA_TOKEN = os.getenv('JIRA_TOKEN', '')
SEATALK_BOT_TOKEN = os.getenv('SEATALK_BOT_TOKEN', '')
SEATALK_WEBHOOK_SECRET = os.getenv('SEATALK_WEBHOOK_SECRET', '')

# 数据库连接信息加密
DATABASE_CONFIG = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', ''),
        'USER': os.getenv('DB_USER', ''),
        'PASSWORD': os.getenv('DB_PASSWORD', ''),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
    }
}
```

### 6.2 最佳实践

#### 6.2.1 错误处理和日志
```python
import logging
from icecream import ic

# 配置日志记录
logger = logging.getLogger(__name__)

def robust_epic_reminder_check():
    """健壮的 Epic 提醒检查"""
    try:
        ic("开始 Epic 里程碑提醒检查")
        
        # 主要逻辑
        check_epic_milestone_reminders()
        
        ic("Epic 里程碑提醒检查完成")
        
    except JIRAError as e:
        error_msg = f"JIRA 连接错误: {str(e)}"
        logger.error(error_msg)
        send_error_notification(error_msg)
        
    except Exception as e:
        error_msg = f"系统错误: {str(e)}"
        logger.error(error_msg)
        send_error_notification(error_msg)
        
    finally:
        # 清理资源
        cleanup_resources()
```

#### 6.2.2 性能优化
```python
from django.core.cache import cache
from concurrent.futures import ThreadPoolExecutor
import threading

# 缓存优化
def get_epic_info_cached(epic_key):
    """缓存 Epic 信息以提高性能"""
    cache_key = f"epic_info_{epic_key}"
    cached_info = cache.get(cache_key)
    
    if cached_info:
        return cached_info
        
    # 从 JIRA 获取信息
    epic_info = get_epic_info_from_jira(epic_key)
    
    # 缓存1小时
    cache.set(cache_key, epic_info, 3600)
    return epic_info

# 并发处理优化
def process_epics_concurrently(epic_list):
    """并发处理多个 Epic"""
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = []
        for epic in epic_list:
            future = executor.submit(process_single_epic, epic)
            futures.append(future)
        
        # 等待所有任务完成
        for future in futures:
            try:
                future.result(timeout=30)
            except Exception as e:
                logger.error(f"Epic 处理失败: {str(e)}")
```

#### 6.2.3 配置验证
```python
def validate_project_configuration(project_key):
    """验证项目配置的完整性"""
    validation_results = {
        "jira_access": False,
        "seatalk_permissions": False,
        "database_config": False,
        "reminder_config": False
    }
    
    # 1. 验证 JIRA 访问
    try:
        jira = JIRA("https://jira.shopee.io", token_auth=JIRA_TOKEN)
        project = jira.project(project_key)
        validation_results["jira_access"] = True
    except:
        pass
    
    # 2. 验证 SeaTalk 权限
    try:
        test_group_id = "NzQzMzAxODcyMjAy"
        test_message = f"配置验证测试 - {project_key}"
        test_for_seatalk_bot(test_message, False, test_group_id)
        validation_results["seatalk_permissions"] = True
    except:
        pass
    
    # 3. 验证数据库配置
    try:
        SeatalkGroup.objects.filter(group_name__contains=project_key).exists()
        validation_results["database_config"] = True
    except:
        pass
    
    # 4. 验证提醒配置
    for reminder_type, config in EPIC_REMINDER_CONFIG.items():
        if project_key in config.get("enabled_projects", []):
            validation_results["reminder_config"] = True
            break
    
    return validation_results
```

---

## 总结

本文档详细说明了 SeaTalk 项目管理功能的配置要求和接入流程。新项目接入时需要关注以下几个关键点：

1. **必须配置项**（所有新项目）:
   - JIRA 账号项目权限
   - SeaTalk 机器人服务范围

2. **条件配置项**（取决于项目类型）:
   - 项目特定配置（如 SPCB 类似配置）
   - 数据库初始化
   - 定时任务配置

3. **验证要点**:
   - 功能完整性测试
   - 权限有效性验证
   - 错误处理机制确认

遵循本文档的配置流程，可以确保新项目顺利接入 SeaTalk 项目管理功能，并享受完整的自动化协作体验。

如有任何配置问题，请联系：
- **技术负责人**: <EMAIL>
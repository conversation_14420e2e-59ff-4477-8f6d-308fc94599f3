# ChatBot AutoRelease 统计系统部署前检查清单

## 📋 部署前必检项目

### 🔍 环境检查
- [ ] **服务器环境**
  - [ ] Python 版本 >= 3.8
  - [ ] Django 版本 >= 3.2
  - [ ] 数据库服务正常运行
  - [ ] 磁盘空间 >= 1GB 可用
  - [ ] 内存使用率 < 80%

- [ ] **数据库检查**
  - [ ] 数据库连接正常
  - [ ] 数据库用户权限充足 (CREATE TABLE, ALTER TABLE)
  - [ ] 数据库备份已完成
  - [ ] 备份文件完整性验证

- [ ] **代码检查**
  - [ ] 代码已更新到最新版本
  - [ ] 迁移文件存在: `app01/migrations/0024_add_statistics_models.py`
  - [ ] 统计系统代码完整
  - [ ] 测试脚本可执行

### 🧪 功能测试
- [ ] **测试执行**
  - [ ] 运行 `python test_statistics_comprehensive.py` 通过
  - [ ] 运行 `python manage.py check` 无错误
  - [ ] 运行 `python manage.py migrate --plan` 确认迁移计划

- [ ] **依赖检查**
  - [ ] 所有 Python 依赖已安装
  - [ ] 数据库驱动正常工作
  - [ ] 必要的系统命令可用 (curl, git)

### 🔒 安全检查
- [ ] **权限检查**
  - [ ] 部署用户非 root 用户
  - [ ] 数据库用户权限最小化
  - [ ] 文件权限设置正确

- [ ] **备份验证**
  - [ ] 数据库完整备份已创建
  - [ ] 配置文件已备份
  - [ ] 备份文件可访问且完整

### 📅 时间规划
- [ ] **部署时间**
  - [ ] 选择业务低峰期 (建议凌晨或周末)
  - [ ] 预留 1-2 小时部署时间
  - [ ] 通知相关团队成员

- [ ] **回滚准备**
  - [ ] 回滚方案已准备
  - [ ] 紧急联系人已确认
  - [ ] 监控工具已就绪

## 🚀 部署方式选择

### 方式一：自动化脚本部署 (推荐)
```bash
# 运行自动化部署脚本
./scripts/deploy_statistics_system.sh
```

**优点:**
- 自动化程度高
- 内置安全检查
- 自动备份和验证
- 详细的日志输出

**适用场景:**
- 标准环境部署
- 首次部署
- 需要完整记录的部署

### 方式二：手动部署
```bash
# 按照详细步骤手动执行
# 参考: docs/DATABASE_DEPLOYMENT_GUIDE.md
```

**优点:**
- 完全可控
- 可以逐步验证
- 适合复杂环境

**适用场景:**
- 特殊环境配置
- 需要自定义步骤
- 调试部署问题

## ⚠️ 风险评估

### 高风险项目
- [ ] **数据库迁移失败**
  - 风险: 数据库结构损坏
  - 缓解: 完整备份 + 测试环境验证
  - 回滚: 恢复备份 + 回滚迁移

- [ ] **服务无法启动**
  - 风险: 应用服务中断
  - 缓解: 配置验证 + 分阶段重启
  - 回滚: 恢复配置 + 重启服务

### 中风险项目
- [ ] **性能影响**
  - 风险: 系统响应变慢
  - 缓解: 异步处理 + 性能监控
  - 回滚: 禁用统计功能

- [ ] **存储空间不足**
  - 风险: 数据库写入失败
  - 缓解: 磁盘监控 + 数据清理
  - 回滚: 清理统计数据

### 低风险项目
- [ ] **统计功能异常**
  - 风险: 统计数据不准确
  - 缓解: 功能测试 + 数据验证
  - 回滚: 重新配置

## 📞 紧急联系信息

### 技术负责人
- **姓名**: liang.tang
- **邮箱**: <EMAIL>
- **职责**: 技术问题解决

### 运维负责人
- **姓名**: [请填写]
- **邮箱**: [请填写]
- **职责**: 服务器和数据库问题

### 业务负责人
- **姓名**: [请填写]
- **邮箱**: [请填写]
- **职责**: 业务影响评估

## 🔄 回滚方案

### 紧急回滚 (5分钟内)
```bash
# 1. 禁用统计功能
sed -i 's/STATISTICS_ENABLED = True/STATISTICS_ENABLED = False/' djangoProject/settings.py

# 2. 重启服务
sudo systemctl restart your-django-service
```

### 完整回滚 (15分钟内)
```bash
# 1. 回滚数据库迁移
python manage.py migrate app01 0023

# 2. 恢复配置文件
cp djangoProject/settings.py.backup.* djangoProject/settings.py

# 3. 重启服务
sudo systemctl restart your-django-service

# 4. 验证回滚
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/
```

### 灾难恢复 (30分钟内)
```bash
# 1. 恢复数据库备份
psql -h localhost -U username -d database < backup_YYYYMMDD_HHMMSS.sql

# 2. 恢复代码版本
git reset --hard HEAD~1

# 3. 重启所有服务
sudo systemctl restart your-django-service
```

## 📊 部署后监控

### 立即监控 (部署后1小时)
- [ ] 应用服务状态
- [ ] 数据库连接状态
- [ ] API接口响应时间
- [ ] 错误日志监控

### 短期监控 (部署后24小时)
- [ ] 系统性能指标
- [ ] 数据收集功能
- [ ] 用户反馈收集
- [ ] 存储空间使用

### 长期监控 (部署后1周)
- [ ] 统计数据准确性
- [ ] 系统稳定性
- [ ] 性能趋势分析
- [ ] 用户使用情况

## ✅ 部署完成确认

### 功能确认
- [ ] 监控面板可访问: `http://your-domain/api/statistics/dashboard/`
- [ ] API接口正常响应
- [ ] 数据收集功能工作
- [ ] 现有功能未受影响

### 文档确认
- [ ] 部署日志已保存
- [ ] 配置变更已记录
- [ ] 用户手册已更新
- [ ] 运维文档已更新

### 团队确认
- [ ] 开发团队确认功能正常
- [ ] 运维团队确认系统稳定
- [ ] 业务团队确认无影响
- [ ] 项目经理确认部署完成

---

**检查清单版本**: v1.0.0  
**最后更新**: 2025-07-18  
**检查人**: ________________  
**检查时间**: ________________  
**部署批准**: ________________

# 命令处理器分析报告

## 指令处理总体流程

系统通过`CommandProcessor`类处理所有来自用户的指令，主要入口是`process_command`方法。处理流程分为以下几个主要步骤：

1. 消息上下文初始化（通过`MessageContext`类）
2. 提取命令文本
3. 优先处理AI查询指令
4. 尝试处理SPCPM专用指令
5. 处理传统固定格式指令
6. 无法识别的指令转交AI处理

## 消息上下文处理

系统通过`MessageContext`类处理两种类型的消息上下文：
- 群聊消息：`event_type = "new_mentioned_message_received_from_group_chat"`
- 私聊消息：`event_type = "message_from_bot_subscriber"`

对于群聊消息，系统会：
1. 设置`type = 'group'`
2. 获取群ID、发送者ID等信息
3. 通过`get_group_info`获取群名作为上下文信息
4. 使用`bot_config.extract_command_from_group_message`提取命令内容

对于私聊消息，系统会：
1. 设置`type = 'private'`
2. 获取发送者ID等信息
3. 使用`bot_config.extract_command_from_private_message`提取命令内容

## 指令识别与处理流程

系统按照以下优先级处理命令：

1. **AI查询指令**：以`/ai`开头的指令直接交给AI处理
2. **SPCPM专用指令**：尝试使用`_process_spcpm_command`处理
3. **传统固定格式指令**：使用指令模式匹配系统找到最佳匹配的传统指令
4. **无法识别的指令**：交给AI处理作为兜底方案

## 指令模式匹配系统

系统实现了一个灵活的指令模式匹配系统，通过`CommandPattern`类定义和匹配指令模式：

### CommandPattern类

```python
class CommandPattern:
    """指令模式定义类"""
    
    def __init__(self, name, patterns=None, context_aware=False, priority=0, exclude_patterns=None):
        """
        初始化指令模式
        
        Args:
            name: 指令名称
            patterns: 匹配模式列表（正则表达式或关键词组合）
            context_aware: 是否考虑上下文（如群名）
            priority: 优先级（数字越大优先级越高）
            exclude_patterns: 排除模式列表
        """
        self.name = name
        self.patterns = patterns or []
        self.context_aware = context_aware
        self.priority = priority
        self.exclude_patterns = exclude_patterns or []
```

### 指令模式注册

`CommandProcessor`类在初始化时注册所有传统指令的匹配模式：

```python
def __init__(self):
    """初始化命令处理器"""
    self.command_patterns = [
        # Bug查询指令
        CommandPattern(
            name="bug_query",
            patterns=[
                re.compile(r'^bug\s+SP[A-Z]+-\d+$'),  # "bug SPCB-1234"
                re.compile(r'^SP[A-Z]+-\d+\s+bug$'),  # "SPCB-1234 bug"
                re.compile(r'^bug$')                  # 纯"bug"（需要上下文）
            ],
            context_aware=True,
            priority=2,
            exclude_patterns=[
                r'每[天周月]',
                r'定时',
                r'创建',
                r'schedule',
                r'linkedissues',
                r'提醒',
                r'通知'
            ]
        ),
        # 其他指令模式...
    ]
```

### 指令匹配流程

1. 使用`_find_best_command_match`方法找到最佳匹配的指令
2. 对每个指令模式计算匹配分数，考虑优先级和上下文信息
3. 选择匹配分数最高的指令作为最佳匹配
4. 根据最佳匹配的指令类型调用相应处理方法

### 上下文感知匹配

系统通过`MessageContext.get_context_info`方法提供丰富的上下文信息：

```python
def get_context_info(self) -> Dict[str, Any]:
    """获取上下文信息供指令匹配使用"""
    context_info = {
        'type': self.type,
        'has_context': self.has_context,
        'group_id': self.group_id,
        'group_name': self.group_name,
        'jira_keys': []
    }
    
    # 提取上下文中的JIRA单号
    if self.group_name:
        context_info['jira_keys'] = self.extract_jira_keys(self.group_name)
        
    # 添加是否为纯SPCPM群组信息
    is_pure_spcpm, spcpm_id = self.is_pure_spcpm_group()
    context_info['is_pure_spcpm'] = is_pure_spcpm
    context_info['spcpm_id'] = spcpm_id
    
    return context_info
```

### 匹配规则优化

1. **精确模式匹配**：使用正则表达式进行精确匹配，而非简单的关键词检测
2. **排除模式**：定义排除模式，避免误触发（如定时任务指令中的bug关键词）
3. **优先级机制**：为不同指令模式设置优先级，解决冲突
4. **上下文感知**：考虑群名等上下文信息进行匹配

## JIRA单号上下文处理

系统通过`get_jira_key_with_context`方法智能获取JIRA单号：

1. 首先尝试从命令文本中提取
2. 如果命令中没有，且是群聊模式，则尝试从群名中提取
3. 如果都没有，则返回None

## 纯SPCPM群组识别

系统通过`is_pure_spcpm_group`方法识别纯SPCPM群组：

1. 检查群名是否包含SPCPM单号
2. 检查是否包含其他项目单号
3. 如果只有SPCPM单号，没有其他项目单号，则认为是纯SPCPM群组

## 传统指令处理

系统支持多种传统固定格式指令，现在通过指令模式匹配系统进行精确匹配：

- MR检查：`chatbot mr`或`mr chatbot`
- Bug查询：匹配`bug SPCB-1234`、`SPCB-1234 bug`或纯`bug`（需要上下文）
- Timeline查询：匹配`timeline SPCB-1234`、`SPCB-1234 timeline`、`tl SPCB-1234`等
- 创建群组：匹配`new group SPCB-1234`或`SPCB-1234 new group`
- Checklist：匹配`SPCB-1234 SO\CM\CC\DB`格式
- JIRA单号查询：匹配纯JIRA单号
- 添加服务：匹配`add service`或`service add`
- 定时任务：匹配包含时间表达式和任务关键词的自然语言指令

## SPCPM专用指令

系统支持多种SPCPM专用指令：

- Timeline列表：`list timeline reminder`、`tr list`或`list tr`
- 添加/移除Timeline：`tr+`/`tr-`或`add/remove timeline reminder`
- 查看Timeline：`tr SPCPM-xxx`或包含`timeline`/`tl`和SPCPM单号
- @角色：包含角色关键词和SPCPM单号
- SPCPM帮助：`spcpm`、`spcpm帮助`或`spcpm help`

## 基于大模型的意图分类系统

系统已经实现了一个完善的基于大模型的意图分类系统，主要位于`app01/ai_module/ai_assistant.py`和`app01/ai_module/prompts/intent_detection.py`中。

### 意图分类架构

1. **意图分类入口**：`AIAssistant._detect_intent()`方法
2. **执行意图识别**：`AIAssistant._execute_intent_detection()`方法
3. **意图提示词构建**：`IntentDetectionPrompts.build_prompt()`方法
4. **意图排序**：`IntentRanker.rank_intents()`方法

### 支持的意图类型（6大类）

1. **jira_query**：JIRA查询类
   - 功能：根据用户指令提取关键信息，生成JQL查询，返回JIRA数据
   - 触发关键词：查询、搜索、找、看、显示、我的、分配给、负责的等

2. **jira_statistics**：JIRA数据统计类
   - 功能：查询JIRA数据后进行统计分析，生成图表或报告
   - 触发关键词：统计、分析、汇总、报告、图表、趋势、分布、多少、数量、占比等

3. **jira_write**：JIRA写操作类
   - 功能：需要JIRA Token的写操作，包括状态变更、评论、字段更新、创建subtask等
   - 触发关键词：更新、修改、改变、设置、评论、回复、添加、创建、新建、提交等

4. **schedule_management**：定时任务管理
   - 功能：管理用户级别的计划任务，支持定期查询和通知
   - 触发关键词：定时、计划、每天、每周、提醒、通知、推送、暂停、恢复、删除任务等

5. **document_processing**：文档处理类
   - 功能：处理Confluence文档、PRD、TRD等文档的读取、总结、翻译、问答
   - 触发关键词：PRD、TRD、文档、总结、翻译、分析、confluence、wiki等

6. **general**：其他类
   - 功能：使用大模型自身能力回答的问题
   - 包含内容：闲聊、文本翻译、问题咨询、代码生成、bug分析、日志分析等

### 意图分类流程

1. 预处理：检查是否明确是创建子任务的请求
2. 构建意图识别提示词
3. 调用LLM进行意图识别（支持多模型对比）
4. 解析意图识别结果
5. 根据意图类型执行相应处理

## 实体提取系统

系统已经实现了一个完善的实体提取系统，主要位于`app01/ai_module/field_mapper.py`和各个处理模块中。

### 实体提取架构

1. **JIRA字段映射管理**：`JiraFieldMapper`类提供了字段映射和分类功能
2. **字段分类**：按业务类别自动分类字段（人员、时间、项目、技术、发布检查清单等）
3. **字段映射缓存**：24小时缓存，避免频繁API调用
4. **容错机制**：多层备选方案，确保系统稳定性

### 主要实体类型

1. **项目信息**：项目前缀、项目名称
2. **人员信息**：处理人、报告人、关注人
   - 个人查询：`"我的任务"` → `assignee: ["currentUser()"]`
   - 用户名查询：`"liang.tang的任务"` → `assignee: ["liang.tang"]`
   - 邮箱查询：`"******************的任务"` → `assignee: ["<EMAIL>"]`

3. **时间范围**：相对时间、绝对时间
   - 相对时间：上周、本月、最近、今天、昨天
   - 绝对时间：2024-01-01、1月15日
   - 时间段：1月1日到1月15日

4. **单据类型**：Task、Bug、Sub-task、Epic、Story、Release等
5. **需求字段**：用户指定返回的字段
6. **群标题信息**：特别是SP[字母]+-数字格式的JIRA单号

### 自然语言任务创建

系统还实现了基于自然语言的任务创建功能，位于`app01/ai_module/natural_task_creator.py`：

1. **自然语言解析**：解析用户意图，提取任务配置信息
2. **调度表达式解析**：将自然语言时间表达式转换为系统调度表达式
3. **多轮对话**：支持通过多轮对话补充缺失信息
4. **智能任务创建**：根据解析结果创建定时任务

## AI处理流程

当传统指令无法识别时，系统会调用`_process_ai_command`方法：

1. 调用`views.handle_ai_query`处理用户查询
2. 如果处理成功，返回AI响应
3. 如果处理失败，返回友好的错误信息

## 测试工具

系统提供了两种测试工具，用于验证指令处理逻辑：

### 1. 单元测试工具

`test_command_processor.py`是一个纯单元测试脚本，可以在本地环境运行，不需要启动Django服务器：

```python
def test_command_matching():
    """测试指令匹配系统"""
    
    # 测试用例
    test_cases = [
        # Bug查询指令
        {"command": "bug SPCB-54865", "group_name": None, "expected": "bug_query"},
        # 其他测试用例...
    ]
    
    # 运行测试并验证结果
    # ...
```

### 2. 集成测试工具

`test_command_with_mock_api.py`是一个集成测试脚本，需要在Django服务器运行的情况下执行：

```python
def test_with_mock_api():
    """使用mock_seatalk_api测试命令处理器"""
    
    # 测试用例
    test_cases = [
        # 测试Bug查询指令
        {
            "message_type": "group",
            "user_id": "liang.tang",
            "message": "@ChatbotAR bug",
            "group_title": "[SPCB-54865]机器人调试群",
            # 其他参数...
        },
        # 其他测试用例...
    ]
    
    # 向mock_seatalk_api发送请求并验证结果
    # ...
```

## 总结

系统采用了多层次的命令处理逻辑：
1. 首先区分群聊和私聊模式，并提取相应的上下文信息
2. 优先处理明确的AI查询指令
3. 尝试处理SPCPM专用指令
4. 使用指令模式匹配系统处理传统固定格式指令
5. 无法识别的指令交给AI处理

最新的优化通过引入指令模式匹配系统，解决了传统指令匹配过于宽泛的问题，使系统能够更精确地匹配传统指令，同时避免误触发。

## 定时提醒任务功能

系统支持两种类型的定时任务：

### 1. JIRA查询定时任务
- **功能**：定期执行JIRA查询并发送结果通知
- **示例**：`"每天 9:00 提醒我有哪些未完成的子任务"`
- **处理流程**：
  1. 解析时间表达式和查询需求
  2. 生成对应的JQL查询
  3. 创建定时任务，定期执行JIRA查询
  4. 将查询结果发送给用户

### 2. 纯文本提醒任务（非JQL查询）
- **功能**：定期发送纯文本提醒消息，不涉及JIRA查询
- **示例**：
  - `"每个工作日 10:18 提醒我查看邮件"`
  - `"每天 9:00 提醒我开会"`
  - `"每周一 14:00 提醒我写周报"`
  - `"每月1日 16:00 提醒我做月报"`

### 指令匹配和处理

#### 指令识别
系统通过以下方式识别定时任务指令：

1. **大模型意图识别**：使用LLM分析用户输入，识别为`schedule_task`意图
2. **传统模式匹配**：作为回退机制，匹配包含时间和任务关键词的指令
3. **关键词检测**：
   - 时间关键词：`工作日`、`每天`、`每周`、`每月`、时间格式（如`10:18`）
   - 任务关键词：`提醒`、`通知`、`推送`等

#### 处理流程
1. **指令匹配**：`CommandProcessor._find_best_command_match()` 识别为 `schedule_task`
2. **路由处理**：调用 `CommandProcessor._process_schedule_task_command()`
3. **参数构建**：提取用户信息（`seatalk_id`, `email`, `group_id`）和群组信息
4. **AI处理**：调用 `AIAssistant._handle_schedule_management()` 进行自然语言处理
5. **任务创建**：通过 `NaturalTaskCreator` 解析并创建定时任务

#### 技术实现细节

##### CommandProcessor 集成
```python
def _process_schedule_task_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
    """处理定时任务命令"""
    # 构建参数
    extracted_info = {
        'intent': 'schedule_management',
        'schedule_request': True
    }
    
    group_info = {}
    if group_id:
        group_info['group_id'] = group_id
    if hasattr(context, 'group_name') and context.group_name:
        group_info['group_name'] = context.group_name
    
    # 调用AI助手处理
    result = assistant._handle_schedule_management(
        extracted_info=extracted_info,
        user_query=command_text,
        user_email=user_email,
        user_id=user_id,
        group_info=group_info
    )
```

##### 支持的时间表达式格式
- **每日任务**：`每天 HH:MM`、`每日 HH:MM`
- **工作日任务**：`工作日 HH:MM`、`每个工作日 HH:MM`
- **每周任务**：`每周一 HH:MM`、`每周二三 HH:MM`
- **每月任务**：`每月15日 HH:MM`、`每月1号 HH:MM`

##### 任务类型区分
系统在数据库层面区分两种任务类型：

- **`task_type='jira_query'`**：需要执行JIRA查询的任务
  - 必需字段：`query_text`（JQL查询语句）
  - 可选字段：`reminder_message`
  
- **`task_type='reminder'`**：纯文本提醒任务
  - 必需字段：`reminder_message`（提醒内容）
  - 可选字段：`query_text`（通常为NULL）

##### 权限控制
```python
# 提醒任务权限检查
if query_analysis.get('scope') == 'reminder_task':
    if notification_analysis['type'] == 'private':
        return PermissionResult(
            allowed=True,
            reason="普通用户可以创建个人提醒任务",
            user_role='regular_user'
        )
    elif notification_analysis['type'] == 'group':
        return PermissionResult(
            allowed=True,
            reason="普通用户可以创建发送到群的提醒任务",
            user_role='regular_user'
        )
```

### 使用示例

#### 创建纯文本提醒
```
用户输入：每个工作日 10:18 提醒我查看邮件
系统响应：✅ 定时任务"工作日邮件检查提醒"创建成功
         📅 执行时间: 每个工作日 10:18
         📬 通知方式: 私聊通知
         🔔 提醒内容: 查看邮件
```

#### 创建JIRA查询任务
```
用户输入：工作日的 10:20 提醒我有哪些未完成的子任务
系统响应：✅ 定时任务"工作日未完成子任务提醒"创建成功
         📅 执行时间: 每个工作日 10:20
         📬 通知方式: 私聊通知
         🔍 查询内容: assignee = currentUser() AND issuetype = Sub-task AND status not in (Closed, Done, Icebox)
```

## 未来优化方向

1. **与AI意图分类集成**：在AI意图分类系统中添加`traditional_command`意图类型，支持识别用自然语言表达的传统指令
2. **增强上下文感知**：进一步利用群名等上下文信息进行更智能的指令匹配
3. **实体提取增强**：进一步完善实体提取系统，提高准确性和覆盖率
4. **定时任务增强**：
   - 支持更复杂的时间表达式（如"每两周"、"每季度"）
   - 增强自然语言理解，支持更多样化的表达方式
   - 添加任务模板功能，方便用户快速创建常用任务

## 最新修复记录（2025-07-25）

### 定时提醒任务关键问题修复

#### 修复的问题
1. **默认通知类型错误**：私聊任务被错误设置为'smart'类型，现在私聊默认为'private'，群聊默认为'group'
2. **提醒消息发送失败**：调用了不存在的`send_message`方法，现在使用正确的`send_text_message`方法
3. **响应信息不详细**：群聊任务创建只返回简单信息，现在统一返回详细的任务信息
4. **群组ID设置缺失**：群聊任务没有正确设置`target_group_id`，现在自动继承当前群组ID

#### 技术修复详情
1. **通知类型智能默认**：
   ```python
   # 私聊任务默认私聊通知，群聊任务默认群聊通知
   if group_id:
       notification_type = task_config.get('notification_type', 'group')
   else:
       notification_type = task_config.get('notification_type', 'private')
   ```

2. **私聊消息发送修复**：
   ```python
   # 使用正确的API方法发送私聊提醒
   await self.private_chat.send_text_message(
       employee_code=employee_code,
       content=f"🔔 **定时提醒**: {task.task_name}\n\n{reminder_message}",
       format_type=1  # Markdown格式
   )
   ```

3. **响应信息详细化**：
   - 提醒任务和JIRA任务都返回详细的创建信息
   - 包含任务ID、任务名称、调度时间、通知方式等
   - 提供任务管理命令提示

4. **群组ID自动设置**：
   ```python
   # 自动继承当前群组ID
   target_group_id = task_config.get('target_group_id') or group_id
   ```

#### 用户体验改进
- ✅ 私聊提醒任务默认发送到私聊，显示用户邮箱
- ✅ 群聊JIRA任务默认发送到当前群组
- ✅ 任务创建返回详细信息，便于用户确认
- ✅ 定时提醒消息内容准确，格式友好
- ✅ 消除重复发送问题，提高系统稳定性

### 建单指令误识别修复（2025-07-25 紧急修复）

#### 严重问题发现
建单指令被错误处理，导致核心功能无法正常使用：
- ❌ "在SPCB-54004 下add subtask：--case 设计 1d" 被错误识别为 `schedule_task` 
- ❌ 后来被修复为识别为 `jira_query`，但 `jira_query` 只支持查询不支持创建

#### 根本原因分析
1. **初始问题**：LLM将"add subtask"与"1d"时间表达式结合，错误理解为定时任务
2. **深层问题**：命令处理器的 `jira_query` 处理逻辑 (`_process_jira_query_command`) 只支持查询JIRA信息，不支持创建子任务
3. **历史正确流程**：以前建单指令不匹配任何传统指令 → 走AI处理流程 → 被识别为`jira_write` → 执行创建逻辑

#### 正确修复方案
**让建单指令不匹配任何传统指令，恢复原有的AI处理流程**：

1. **修改LLM提示词**：明确指出子任务创建指令应该返回"none"
2. **保持处理流程**：建单指令 → 不匹配传统指令 → 走AI处理 → 识别为`jira_write` → 创建子任务

#### 修复效果验证
```
✅ "在SPCB-54004 下建单：--case 设计 1d" → None (走AI处理)
✅ "在SPCB-54004 下add subtask：--case 设计 1d" → None (走AI处理)
✅ "SPCB-54004 的状态" → jira_query (查询功能正常)
✅ "每个工作日 10:30 提醒我查看邮件" → schedule_task (定时任务正常)
```

#### 技术细节
修改了命令处理器中的LLM提示词：
```python
**重要识别规则**：

1. **子任务创建指令不应该匹配传统指令**：
   - 包含JIRA单号 + 创建关键词（建单、建subtask、add subtask等）的指令应该返回"none"
   - 这些指令需要走AI处理流程，被识别为jira_write意图后执行创建逻辑
   - 示例："在SPCB-54004 下add subtask：--case 设计 1d" → none (让AI处理)

2. **JIRA查询指令**：
   - 仅限于纯查询JIRA单号信息的指令
   - 示例："SPCB-123的状态" → jira_query
```

#### 重要教训
不应该一味通过提高某类指令的优先级来解决问题，而应该：
1. **分析历史正确流程**：了解以前是如何正确处理的
2. **识别处理能力边界**：区分传统指令处理能力与AI处理能力  
3. **恢复正确流程**：让指令走到有处理能力的流程中

此修复确保了建单功能恢复到历史正确的处理流程，避免了错误的架构设计。

### bug_query模式误匹配修复（2025-07-25 关键修复）

#### 新发现的问题
在解决建单指令被错误识别为`schedule_task`后，又发现了新的问题：**包含"bug"关键词的建单指令被错误匹配为`bug_query`传统指令**。

#### 问题表现
```
❌ 在SPCB-51331下建单：Workee bug 修复 1d → bug_query (错误匹配)
❌ 在SPCB-51331下建子任务：Workee bug 修复 1d → bug_query (错误匹配)
✅ 在SPCB-54004 下add subtask：--需求测试 1d → None (正确走AI处理)
```

#### 根本原因
`bug_query`传统指令在回退匹配逻辑中使用了过于宽泛的字符串匹配：
```python
'bug_query': CommandPattern(
    'bug_query',
    patterns=[
        r'^bug\s+SP[A-Z]+-\d+$',
        r'^SP[A-Z]+-\d+\s+bug$',
        r'^bug$',
        'bug', '缺陷', 'BUG', '问题'  # ← 这里的问题！
    ]
)
```

简单字符串`'bug'`导致任何包含"bug"的指令都被匹配，包括创建包含"bug"内容的子任务。

#### 修复方案
1. **移除过于宽泛的字符串匹配**：移除简单的`'bug'`字符串模式
2. **只保留精确的正则表达式匹配**：仅匹配真正的bug查询格式
3. **添加严格的排除模式**：明确排除所有创建操作关键词

#### 修复后的模式
```python
'bug_query': CommandPattern(
    'bug_query',
    patterns=[
        r'^bug\s+SP[A-Z]+-\d+$',     # "bug SPCB-1234"
        r'^SP[A-Z]+-\d+\s+bug$',     # "SPCB-1234 bug"
        r'^bug$',                    # 纯"bug"（需要上下文）
        r'^缺陷\s+SP[A-Z]+-\d+$',   # "缺陷 SPCB-1234"
        r'^SP[A-Z]+-\d+\s+缺陷$'    # "SPCB-1234 缺陷"
    ],
    exclude_patterns=[
        'linkedissues',
        r'建单',         # 排除创建操作
        r'建子任务',     # 排除创建操作
        r'add\s+subtask', # 排除创建操作
        r'创建',         # 排除创建操作
        r'新建'          # 排除创建操作
    ]
)
```

#### 修复效果验证
```
✅ bug SPCB-54004 → 正确识别（LLM识别为jira_query也合理）
✅ 在SPCB-51331下建单：Workee bug 修复 1d → None (走AI处理)
✅ 在SPCB-51331下建子任务：Workee bug 修复 1d → None (走AI处理)
✅ 在SPCB-51331下add subtask：Workee bug 修复 1d → None (走AI处理)
✅ 创建bug修复任务 → None (走AI处理)
```

#### 核心价值
1. **彻底解决误匹配**：包含"bug"关键词的建单指令不再被错误拦截
2. **保持查询功能**：真正的bug查询指令依然能被正确处理
3. **架构清晰**：明确区分查询操作和创建操作的处理边界
4. **系统稳定**：排除模式确保了创建指令的稳定路由

这个修复是对指令匹配系统的重要完善，确保了不同类型指令能够被正确路由到合适的处理器。 
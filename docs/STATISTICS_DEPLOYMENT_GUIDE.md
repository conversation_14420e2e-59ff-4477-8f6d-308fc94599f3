# ChatBot AutoRelease 统计系统部署指南

## 🚀 部署概述

本指南将帮助您将ChatBot AutoRelease统计系统部署到生产环境。统计系统已通过全面测试，可以安全部署而不影响现有用户的正常使用。

## ✅ 部署前检查

### 系统要求
- Python 3.8+
- Django 3.2+
- PostgreSQL/MySQL数据库
- 足够的存储空间（建议预留至少1GB用于统计数据）

### 测试验证
运行以下命令确保系统正常：
```bash
python test_statistics_comprehensive.py
```
确保所有测试通过（成功率100%）。

## 📋 部署步骤

### 1. 数据库迁移

```bash
# 创建统计相关的数据表
python manage.py migrate

# 验证迁移成功
python manage.py showmigrations app01
```

### 2. 配置统计系统

在 `djangoProject/settings.py` 中添加或确认以下配置：

```python
# 统计系统基础配置
STATISTICS_ENABLED = True
STATISTICS_ASYNC = True
STATISTICS_MIDDLEWARE_ENABLED = True
STATISTICS_SERVICE_ENABLED = True
STATISTICS_REPORTS_ENABLED = True

# 数据保留配置
STATISTICS_DATA_RETENTION_DAYS = 90
STATISTICS_ACCESS_EVENT_RETENTION_DAYS = 30
STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS = 7

# 告警阈值配置
STATISTICS_ALERT_ERROR_RATE_THRESHOLD = 10.0
STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD = 5.0
STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD = 5

# 性能配置
STATISTICS_BUFFER_SIZE = 100
STATISTICS_FLUSH_INTERVAL = 60
STATISTICS_MAX_RESPONSE_CONTENT_LENGTH = 5000
STATISTICS_MAX_ERROR_MESSAGE_LENGTH = 1000
```

### 3. URL配置验证

确认 `djangoProject/urls.py` 中包含统计API路由：

```python
urlpatterns = [
    # ... 其他路由
    path('api/statistics/', include('app01.statistics.urls')),
    # ... 其他路由
]
```

### 4. 中间件配置（可选）

如需自动收集HTTP请求性能数据，在 `MIDDLEWARE` 中添加：

```python
MIDDLEWARE = [
    # ... 其他中间件
    'app01.statistics.middleware.StatisticsMiddleware',
    'app01.statistics.middleware.DatabaseQueryCountMiddleware',
    # ... 其他中间件
]
```

### 5. 重启服务

```bash
# 重启Django服务
sudo systemctl restart your-django-service

# 或者如果使用其他方式部署
# supervisorctl restart chatbot-ar-be
# pm2 restart chatbot-ar-be
```

## 🔧 配置定时任务（可选）

### 创建日志目录

```bash
sudo mkdir -p /var/log/chatbot-ar-statistics
sudo chown $USER:$USER /var/log/chatbot-ar-statistics
```

### 配置Crontab

参考 `scripts/statistics_crontab_example.txt` 文件，选择需要的任务：

```bash
# 编辑crontab
crontab -e

# 添加以下任务（根据需要选择）：

# 每15分钟执行系统健康检查
*/15 * * * * cd /path/to/project && python manage.py run_statistics_job health_check >> /var/log/chatbot-ar-statistics/health_check.log 2>&1

# 每小时执行统计汇总
0 * * * * cd /path/to/project && python manage.py run_statistics_job hourly_summary >> /var/log/chatbot-ar-statistics/hourly_summary.log 2>&1

# 每日凌晨1点执行日统计汇总
0 1 * * * cd /path/to/project && python manage.py run_statistics_job daily_summary >> /var/log/chatbot-ar-statistics/daily_summary.log 2>&1

# 每周日凌晨3点执行数据清理
0 3 * * 0 cd /path/to/project && python manage.py run_statistics_job data_cleanup >> /var/log/chatbot-ar-statistics/data_cleanup.log 2>&1
```

### 验证定时任务

```bash
# 查看crontab配置
crontab -l

# 手动测试任务
python manage.py run_statistics_job --list
python manage.py run_statistics_job health_check
```

## 🌐 访问监控面板

部署完成后，可以通过以下方式访问统计系统：

### Web监控面板
```
http://your-domain/api/statistics/dashboard/
```

### API接口
```bash
# 实时监控数据
curl http://your-domain/api/statistics/realtime/dashboard/

# 指令执行趋势
curl "http://your-domain/api/statistics/realtime/command-trends/?days=7"

# 日报（HTML格式）
curl "http://your-domain/api/statistics/reports/daily/?format=html"
```

## 📊 验证部署

### 1. 功能验证

```bash
# 运行全面测试
python test_statistics_comprehensive.py

# 检查API接口
curl -s http://localhost:8000/api/statistics/realtime/dashboard/ | python -m json.tool

# 访问监控面板
curl -s http://localhost:8000/api/statistics/dashboard/ | head -20
```

### 2. 数据收集验证

```bash
# 查看数据表
python manage.py dbshell
> SELECT COUNT(*) FROM bot_access_event;
> SELECT COUNT(*) FROM command_execution_record;
> \q

# 检查日志
tail -f /var/log/chatbot-ar-statistics/health_check.log
```

### 3. 性能验证

```bash
# 监控系统资源使用
top -p $(pgrep -f "manage.py")

# 检查数据库连接
python manage.py dbshell
```

## 🔍 监控和维护

### 日志监控

```bash
# 查看统计系统日志
tail -f /var/log/chatbot-ar-statistics/*.log

# 查看Django日志中的统计相关信息
grep "statistics" /path/to/django/logs/django.log
```

### 数据库维护

```bash
# 检查统计表大小
python manage.py dbshell
> SELECT 
    table_name, 
    pg_size_pretty(pg_total_relation_size(table_name::regclass)) as size
  FROM information_schema.tables 
  WHERE table_name LIKE '%statistics%' OR table_name LIKE 'bot_%' OR table_name LIKE 'command_%';

# 手动清理旧数据
python manage.py run_statistics_job data_cleanup
```

### 性能监控

```bash
# 检查统计系统性能影响
python -c "
import time
start = time.time()
# 执行一些业务操作
print(f'Processing time: {time.time() - start:.3f}s')
"
```

## ⚠️ 注意事项

### 安全考虑
1. 统计数据包含用户输入内容，确保符合隐私政策
2. 定期清理敏感数据
3. 限制统计API的访问权限

### 性能影响
1. 统计系统采用异步处理，对主业务影响最小
2. 可通过配置调整缓冲区大小和刷新频率
3. 监控数据库存储空间使用情况

### 故障处理
1. 如果统计功能异常，不会影响主业务
2. 可通过设置 `STATISTICS_ENABLED = False` 临时关闭
3. 查看日志文件排查问题

## 📞 技术支持

### 常见问题
1. **统计数据不更新** - 检查数据库连接和配置
2. **监控面板无法访问** - 验证URL配置和模板文件
3. **定时任务不执行** - 检查crontab配置和权限

### 联系方式
- **项目负责人**: <EMAIL>
- **文档位置**: docs/STATISTICS_SYSTEM_GUIDE.md
- **测试报告**: statistics_test_report.json

## 🎉 部署完成

恭喜！ChatBot AutoRelease统计系统已成功部署。系统将开始自动收集统计数据，您可以通过监控面板查看实时数据和生成报表。

### 下一步建议
1. 观察系统运行1-2天，确保稳定
2. 根据实际使用情况调整配置
3. 设置告警通知（如需要）
4. 定期查看统计报表，优化系统性能

---

**部署日期**: 2025-07-18  
**版本**: v1.0.0  
**状态**: ✅ 生产就绪

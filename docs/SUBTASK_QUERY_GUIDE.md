# 子任务查询功能使用指南

## 功能概述

子任务查询功能已经过全面优化，现在支持基于"Planned Start Date"和"Planned Due Date"字段的时间段交叉匹配，并在输出中包含Story Points工作量统计。

## 核心特性

### 1. 时间段交叉匹配
- 基于"Planned Start Date"（cf[16300]）和"Planned Due Date"（cf[16301]）字段
- 智能判断子任务的计划时间与查询时间段是否有交叉
- 支持本周、上周、本月等时间范围查询

### 2. Story Points统计
- 自动提取Story Points字段（cf[10100]或cf[10004]）
- 显示每个子任务的工作量估算
- 提供总工作量统计和覆盖率信息

### 3. 专用输出格式
- 针对子任务优化的显示格式
- 包含单号、描述、计划日期、Story Points等关键信息
- 提供直接的JIRA链接

## 使用示例

### 查询本周子任务
```
用户输入: "帮我查询这周有什么子任务"
```

**生成的JQL:**
```sql
assignee = currentUser() AND type = sub-task AND 
((cf[16300] >= startOfWeek() AND cf[16300] <= endOfWeek()) OR 
 (cf[16301] >= startOfWeek() AND cf[16301] <= endOfWeek()) OR 
 (cf[16300] <= startOfWeek() AND cf[16301] >= endOfWeek()))
```

**输出格式:**
```
📋 **子任务查询结果** (共3个):

🎯 **SPCB-1001** | In Progress
📌 实现用户登录功能
👤 经办人: <EMAIL>
📅 计划开始: 2025-01-13
📅 计划完成: 2025-01-17
⚡ Story Points: 5
🔗 [查看详情](https://jira.shopee.io/browse/SPCB-1001)

---

🎯 **SPCB-1002** | To Do
📌 编写API文档
👤 经办人: <EMAIL>
📅 计划开始: 2025-01-14
📅 计划完成: 2025-01-16
⚡ Story Points: 3
🔗 [查看详情](https://jira.shopee.io/browse/SPCB-1002)

📊 **工作量统计**: 总计 8 Story Points (2/2 个子任务有估算)
```

### 查询上周完成的子任务
```
用户输入: "我上周完成了哪些子任务"
```

**生成的JQL:**
```sql
assignee = currentUser() AND type = sub-task AND status in (Closed, Done) AND 
((cf[16300] >= startOfWeek(-1) AND cf[16300] <= endOfWeek(-1)) OR 
 (cf[16301] >= startOfWeek(-1) AND cf[16301] <= endOfWeek(-1)))
```

### 查询特定用户的子任务
```
用户输入: "查询liang.tang这周的子任务"
```

**生成的JQL:**
```sql
assignee = "<EMAIL>" AND type = sub-task AND 
((cf[16300] >= startOfWeek() AND cf[16300] <= endOfWeek()) OR 
 (cf[16301] >= startOfWeek() AND cf[16301] <= endOfWeek()) OR 
 (cf[16300] <= startOfWeek() AND cf[16301] >= endOfWeek()))
```

## 技术实现

### 1. 字段映射
- `customfield_16300`: Planned Start Date
- `customfield_16301`: Planned Due Date  
- `customfield_10100`: Story Points
- `customfield_10004`: Story Point Estimate (备用)

### 2. 时间段交叉逻辑
系统使用三种条件判断时间段交叉：
1. 计划开始时间在查询时间段内
2. 计划结束时间在查询时间段内
3. 计划开始时间在查询时间段前且计划结束时间在查询时间段后

### 3. 自动检测机制
系统通过以下方式自动识别子任务查询：
- JQL中包含`type = sub-task`
- 用户查询包含"子任务"、"subtask"等关键词
- 提取信息中的issuetype包含"sub-task"

## 支持的查询类型

1. **个人子任务查询**
   - "我的子任务"
   - "我这周的子任务"
   - "我上周完成的子任务"

2. **时间范围查询**
   - "本周的子任务"
   - "上周的子任务"
   - "本月的子任务"

3. **特定用户查询**
   - "liang.tang的子任务"
   - "查询guoxiaohong这周的子任务"

4. **状态筛选查询**
   - "已完成的子任务"
   - "进行中的子任务"
   - "待处理的子任务"

## 注意事项

1. **计划日期字段**: 确保JIRA中的子任务设置了Planned Start Date和Planned Due Date
2. **Story Points**: 系统会自动查找Story Points或Story Point Estimate字段
3. **时间计算**: 基于当前时间动态计算周、月等时间范围
4. **权限要求**: 需要有相应JIRA项目的查看权限

## 故障排除

如果子任务查询结果不准确，请检查：
1. JIRA中是否正确设置了计划日期字段
2. Story Points字段是否已配置
3. 用户是否有相应的查看权限
4. 时间范围表达是否明确

---

*最后更新: 2025-01-13* 
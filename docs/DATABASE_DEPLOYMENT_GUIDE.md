# ChatBot AutoRelease 统计系统数据库部署指南

## 🎯 部署概述

本指南提供ChatBot AutoRelease统计系统的详细数据库部署步骤，确保安全、可靠的生产环境部署。

**⚠️ 重要提醒**: 本次部署涉及数据库结构变更，请严格按照步骤执行。

## 📋 数据库变更概述

### 新增数据表
1. `bot_access_event` - 机器人访问事件记录
2. `command_execution_record` - 指令执行记录
3. `system_performance_metrics` - 系统性能指标
4. `cronjob_execution_monitor` - 定时任务监控
5. `user_activity_summary` - 用户活动汇总
6. `system_health_snapshot` - 系统健康快照

### 迁移文件
- `app01/migrations/0024_add_statistics_models.py`

## 🚀 详细部署步骤

### 阶段一：部署前准备 (15-20分钟)

#### 1.1 环境检查
```bash
# 检查当前环境
echo "=== 环境检查 ==="
python --version
python manage.py --version
psql --version  # 或 mysql --version

# 检查数据库连接
python manage.py dbshell -c "\q"
echo "数据库连接正常"
```

#### 1.2 代码准备
```bash
# 确保代码是最新的
git status
git pull origin main  # 或你的主分支

# 检查迁移文件
ls -la app01/migrations/0024_add_statistics_models.py
echo "迁移文件存在"
```

#### 1.3 数据库备份 (关键步骤)
```bash
# MySQL 备份 (您的项目使用MySQL)
mysqldump -u root -p chatbotcicd > backup_$(date +%Y%m%d_%H%M%S).sql

# 如果没有密码，使用：
# mysqldump -u root chatbotcicd > backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
ls -lh backup_*.sql
echo "数据库备份完成"
```

#### 1.4 磁盘空间检查
```bash
# 检查磁盘空间 (建议至少有1GB可用空间)
df -h
echo "磁盘空间检查完成"
```

#### 1.5 测试环境验证
```bash
# 运行统计系统测试
python test_statistics_comprehensive.py
echo "测试环境验证完成"
```

### 阶段二：数据库迁移 (5-10分钟)

#### 2.1 检查当前迁移状态
```bash
echo "=== 检查迁移状态 ==="
python manage.py showmigrations app01

# 应该看到类似输出：
# app01
#  [X] 0001_initial
#  [X] 0002_...
#  [X] 0023_add_group_id_to_spcpm_timeline_reminder
#  [ ] 0024_add_statistics_models
```

#### 2.2 预检查迁移 (重要)
```bash
echo "=== 预检查迁移 ==="
python manage.py migrate --plan

# 检查输出，确保只有0024迁移会被执行
# 如果有其他未执行的迁移，请先处理
```

#### 2.3 执行迁移 (关键步骤)
```bash
echo "=== 执行数据库迁移 ==="
echo "开始时间: $(date)"

# 执行迁移
python manage.py migrate app01 0024

echo "结束时间: $(date)"
echo "迁移执行完成"
```

#### 2.4 验证迁移结果
```bash
echo "=== 验证迁移结果 ==="

# 检查迁移状态
python manage.py showmigrations app01

# 检查新表是否创建成功 (MySQL)
python manage.py dbshell << EOF
SHOW TABLES LIKE 'bot_%';
SHOW TABLES LIKE 'command_%';
SHOW TABLES LIKE 'system_%';
SHOW TABLES LIKE 'user_%';
SHOW TABLES LIKE 'cronjob_%';
EXIT;
EOF

echo "迁移验证完成"
```

### 阶段三：应用配置 (5分钟)

#### 3.1 更新配置文件
```bash
echo "=== 更新配置文件 ==="

# 备份当前配置
cp djangoProject/settings.py djangoProject/settings.py.backup.$(date +%Y%m%d_%H%M%S)

# 添加统计系统配置到 settings.py
cat >> djangoProject/settings.py << 'EOF'

# ================================
# 统计系统配置
# ================================

# 基础配置
STATISTICS_ENABLED = True
STATISTICS_ASYNC = True
STATISTICS_SERVICE_ENABLED = True
STATISTICS_REPORTS_ENABLED = True

# 中间件配置 (可选，建议先不启用)
STATISTICS_MIDDLEWARE_ENABLED = False
STATISTICS_COLLECT_ALL_REQUESTS = False

# 数据保留配置
STATISTICS_DATA_RETENTION_DAYS = 90
STATISTICS_ACCESS_EVENT_RETENTION_DAYS = 30
STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS = 7

# 性能配置
STATISTICS_BUFFER_SIZE = 100
STATISTICS_FLUSH_INTERVAL = 60
STATISTICS_MAX_RESPONSE_CONTENT_LENGTH = 5000
STATISTICS_MAX_ERROR_MESSAGE_LENGTH = 1000

# 告警配置
STATISTICS_ALERT_ERROR_RATE_THRESHOLD = 10.0
STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD = 5.0
STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD = 5

EOF

echo "配置文件更新完成"
```

#### 3.2 验证配置
```bash
echo "=== 验证配置 ==="
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()
from django.conf import settings
print('STATISTICS_ENABLED:', getattr(settings, 'STATISTICS_ENABLED', False))
print('配置验证成功')
"
```

### 阶段四：服务重启 (2-5分钟)

#### 4.1 重启应用服务
```bash
echo "=== 重启应用服务 ==="

# 根据你的部署方式选择相应的重启命令

# 方式1: systemd
sudo systemctl restart your-django-service
sudo systemctl status your-django-service

# 方式2: supervisor
# sudo supervisorctl restart chatbot-ar-be
# sudo supervisorctl status chatbot-ar-be

# 方式3: PM2
# pm2 restart chatbot-ar-be
# pm2 status

# 方式4: 手动重启 (开发环境)
# pkill -f "manage.py runserver"
# nohup python manage.py runserver 0.0.0.0:8000 > server.log 2>&1 &

echo "服务重启完成"
```

#### 4.2 检查服务状态
```bash
echo "=== 检查服务状态 ==="

# 检查进程
ps aux | grep "manage.py"

# 检查端口
netstat -tlnp | grep :8000  # 或你的端口

# 检查日志
tail -f /var/log/your-app/django.log | head -20

echo "服务状态检查完成"
```

### 阶段五：功能验证 (10分钟)

#### 5.1 基础功能验证
```bash
echo "=== 基础功能验证 ==="

# 测试数据库连接 (MySQL)
python manage.py dbshell -e "SELECT COUNT(*) FROM bot_access_event;"

# 测试统计系统
python test_statistics_comprehensive.py

echo "基础功能验证完成"
```

#### 5.2 API接口验证
```bash
echo "=== API接口验证 ==="

# 测试监控面板
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/statistics/dashboard/

# 测试API接口
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/statistics/realtime/dashboard/

# 测试页面访问
python test_dashboard_access.py

echo "API接口验证完成"
```

#### 5.3 数据收集验证
```bash
echo "=== 数据收集验证 ==="

# 模拟一些用户活动，然后检查数据是否被收集
# 等待几分钟后检查 (MySQL)
python manage.py dbshell << EOF
SELECT COUNT(*) as bot_events FROM bot_access_event;
SELECT COUNT(*) as command_records FROM command_execution_record;
SELECT COUNT(*) as performance_metrics FROM system_performance_metrics;
EXIT;
EOF

echo "数据收集验证完成"
```

### 阶段六：监控设置 (5分钟)

#### 6.1 设置日志监控
```bash
echo "=== 设置日志监控 ==="

# 创建统计系统日志目录
sudo mkdir -p /var/log/chatbot-ar-statistics
sudo chown $USER:$USER /var/log/chatbot-ar-statistics

# 设置日志轮转
sudo tee /etc/logrotate.d/chatbot-ar-statistics << EOF
/var/log/chatbot-ar-statistics/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
}
EOF

echo "日志监控设置完成"
```

#### 6.2 设置健康检查
```bash
echo "=== 设置健康检查 ==="

# 创建健康检查脚本
cat > /tmp/health_check.sh << 'EOF'
#!/bin/bash
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/statistics/realtime/dashboard/)
if [ "$RESPONSE" = "200" ]; then
    echo "$(date): Statistics system is healthy"
else
    echo "$(date): Statistics system is unhealthy (HTTP $RESPONSE)"
fi
EOF

chmod +x /tmp/health_check.sh
/tmp/health_check.sh

echo "健康检查设置完成"
```

## 🔍 部署后验证清单

### ✅ 必须验证项目
- [ ] 数据库迁移成功执行
- [ ] 6个新表已创建
- [ ] 应用服务正常启动
- [ ] 监控面板可以访问
- [ ] API接口响应正常
- [ ] 现有功能未受影响

### ✅ 建议验证项目
- [ ] 数据收集功能正常
- [ ] 日志文件正常生成
- [ ] 系统性能无明显下降
- [ ] 数据库连接池正常

## 🚨 故障排查

### 常见问题及解决方案

#### 1. 迁移失败
```bash
# 检查错误信息
python manage.py migrate app01 0024 --verbosity=2

# 如果需要回滚
python manage.py migrate app01 0023

# 恢复数据库备份 (MySQL)
mysql -u root -p chatbotcicd < backup_YYYYMMDD_HHMMSS.sql
```

#### 2. 服务启动失败
```bash
# 检查配置文件语法
python manage.py check

# 检查导入错误
python -c "import djangoProject.settings"

# 查看详细错误日志
python manage.py runserver --verbosity=2
```

#### 3. API接口无法访问
```bash
# 检查URL配置
python manage.py show_urls | grep statistics

# 检查视图函数
python -c "from app01.statistics.views import realtime_dashboard; print('OK')"
```

## 🔄 回滚方案

### 紧急回滚步骤
```bash
echo "=== 紧急回滚 ==="

# 1. 禁用统计功能
sed -i 's/STATISTICS_ENABLED = True/STATISTICS_ENABLED = False/' djangoProject/settings.py

# 2. 重启服务
sudo systemctl restart your-django-service

# 3. 如果需要完全回滚数据库
python manage.py migrate app01 0023

# 4. 恢复配置文件
cp djangoProject/settings.py.backup.* djangoProject/settings.py

echo "回滚完成"
```

## 📊 部署时间估算

| 阶段 | 预计时间 | 说明 |
|------|----------|------|
| 部署前准备 | 15-20分钟 | 备份、检查、测试 |
| 数据库迁移 | 5-10分钟 | 创建新表 |
| 应用配置 | 5分钟 | 更新配置文件 |
| 服务重启 | 2-5分钟 | 重启应用服务 |
| 功能验证 | 10分钟 | 全面测试 |
| 监控设置 | 5分钟 | 日志和监控 |
| **总计** | **42-55分钟** | 建议预留1小时 |

## 📞 支持联系

**部署负责人**: <EMAIL>  
**紧急联系**: 如遇问题请立即联系  
**文档版本**: v1.0.0  
**更新日期**: 2025-07-18  

---

**⚠️ 重要提醒**: 
1. 请在业务低峰期执行部署
2. 确保数据库备份完成后再开始迁移
3. 如遇任何问题，立即执行回滚方案
4. 部署完成后持续监控24小时

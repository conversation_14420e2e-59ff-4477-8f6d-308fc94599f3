#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查计划任务状态和统计数据收集情况
"""

import os
import sys
import django
import subprocess
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from django.utils import timezone

def check_crontab_status():
    """检查系统crontab状态"""
    print("🔍 检查系统crontab状态")
    print("=" * 60)
    
    try:
        # 检查当前用户的crontab
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        
        if result.returncode == 0:
            crontab_content = result.stdout
            print("✅ 当前crontab内容:")
            print("-" * 40)
            
            # 查找统计相关的任务
            statistics_jobs = []
            lines = crontab_content.split('\n')
            
            for line in lines:
                if 'statistics' in line.lower() or 'app01.statistics' in line:
                    statistics_jobs.append(line.strip())
            
            if statistics_jobs:
                print("📊 找到统计相关的计划任务:")
                for i, job in enumerate(statistics_jobs, 1):
                    print(f"  {i}. {job}")
            else:
                print("❌ 没有找到统计相关的计划任务！")
                print("   这可能是数据为空的主要原因。")
            
            print(f"\n📝 总计划任务数: {len([l for l in lines if l.strip() and not l.startswith('#')])}")
            
        else:
            print("❌ 无法读取crontab:")
            print(f"   错误: {result.stderr}")
            
    except FileNotFoundError:
        print("❌ crontab命令不存在，可能系统不支持cron")
    except Exception as e:
        print(f"❌ 检查crontab时出错: {e}")

def check_django_crontab():
    """检查Django crontab配置"""
    print("\n🔍 检查Django crontab配置")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        if hasattr(settings, 'CRONJOBS'):
            cronjobs = settings.CRONJOBS
            print(f"✅ Django配置中有 {len(cronjobs)} 个计划任务")
            
            # 查找统计相关任务
            statistics_jobs = []
            for job in cronjobs:
                if len(job) >= 2 and 'statistics' in str(job[1]).lower():
                    statistics_jobs.append(job)
            
            if statistics_jobs:
                print("\n📊 统计相关的Django计划任务:")
                for i, job in enumerate(statistics_jobs, 1):
                    schedule = job[0]
                    function = job[1]
                    print(f"  {i}. {schedule} -> {function}")
            else:
                print("❌ Django配置中没有统计相关的计划任务")
                
        else:
            print("❌ Django配置中没有CRONJOBS设置")
            
    except Exception as e:
        print(f"❌ 检查Django crontab配置时出错: {e}")

def check_statistics_data():
    """检查统计数据表状态"""
    print("\n🔍 检查统计数据表状态")
    print("=" * 60)
    
    try:
        from app01.models import (
            BotAccessEvent, CommandExecutionRecord, 
            CronJobExecutionMonitor, SystemHealthSnapshot,
            UserActivitySummary
        )
        
        tables_info = [
            ('BotAccessEvent', BotAccessEvent),
            ('CommandExecutionRecord', CommandExecutionRecord),
            ('CronJobExecutionMonitor', CronJobExecutionMonitor),
            ('SystemHealthSnapshot', SystemHealthSnapshot),
            ('UserActivitySummary', UserActivitySummary),
        ]
        
        for table_name, model_class in tables_info:
            try:
                count = model_class.objects.count()
                if count > 0:
                    latest = model_class.objects.first()
                    print(f"✅ {table_name}: {count} 条记录 (最新: {latest.created_at})")
                else:
                    print(f"❌ {table_name}: 0 条记录")
            except Exception as e:
                print(f"❌ {table_name}: 检查失败 - {e}")
                
    except Exception as e:
        print(f"❌ 检查统计数据表时出错: {e}")

def manual_run_statistics():
    """手动运行统计任务"""
    print("\n🚀 手动运行统计任务")
    print("=" * 60)
    
    try:
        from app01.statistics.cron_jobs import (
            hourly_statistics_summary,
            daily_statistics_summary,
            statistics_health_check
        )
        
        print("1. 运行系统健康检查...")
        try:
            result = statistics_health_check()
            print(f"   结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
        
        print("\n2. 运行每小时统计汇总...")
        try:
            result = hourly_statistics_summary()
            print(f"   结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
        
        print("\n3. 运行每日统计汇总...")
        try:
            result = daily_statistics_summary()
            print(f"   结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            
    except ImportError as e:
        print(f"❌ 无法导入统计任务函数: {e}")
    except Exception as e:
        print(f"❌ 手动运行统计任务时出错: {e}")

def install_crontab():
    """安装Django crontab"""
    print("\n🔧 安装Django crontab")
    print("=" * 60)
    
    try:
        # 尝试安装crontab
        result = subprocess.run([
            sys.executable, 'manage.py', 'crontab', 'add'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Django crontab安装成功")
            print("输出:", result.stdout)
        else:
            print("❌ Django crontab安装失败")
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"❌ 安装Django crontab时出错: {e}")

def main():
    """主函数"""
    print("🔍 ChatBot AutoRelease 计划任务状态检查")
    print("=" * 80)
    
    # 检查crontab状态
    check_crontab_status()
    
    # 检查Django配置
    check_django_crontab()
    
    # 检查数据表状态
    check_statistics_data()
    
    print("\n" + "=" * 80)
    print("📋 建议操作:")
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
        
        if action == 'install':
            install_crontab()
        elif action == 'run':
            manual_run_statistics()
        elif action == 'check':
            check_statistics_data()
        else:
            print("❓ 未知操作")
    else:
        print("1. 安装计划任务: python check_cronjobs_status.py install")
        print("2. 手动运行统计: python check_cronjobs_status.py run")
        print("3. 重新检查数据: python check_cronjobs_status.py check")

if __name__ == '__main__':
    main()

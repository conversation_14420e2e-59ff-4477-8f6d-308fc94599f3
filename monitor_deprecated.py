#!/usr/bin/env python3
"""
废弃函数监控脚本 - 简化版
"""

import os
import subprocess
from datetime import datetime

def check_current_status():
    """检查当前状态"""
    print("🔍 废弃函数安全检查")
    print("=" * 40)
    
    # 检查是否有备份
    backups = [f for f in os.listdir('app01') if f.startswith('views.py.backup')]
    print(f"✅ 发现备份文件: {len(backups)} 个")
    for backup in backups[:3]:  # 只显示前3个
        print(f"   - {backup}")
    
    # 检查当前分支
    try:
        result = subprocess.run(['git', 'branch', '--show-current'], 
                              capture_output=True, text=True)
        branch = result.stdout.strip()
        print(f"✅ 当前分支: {branch}")
    except:
        print("❌ 无法获取Git信息")
    
    # 检查废弃标记是否添加成功
    try:
        result = subprocess.run(['grep', '-c', 'DEPRECATED', 'app01/views.py'], 
                              capture_output=True, text=True)
        deprecated_count = result.stdout.strip()
        print(f"✅ 废弃标记数量: {deprecated_count}")
    except:
        print("❌ 无法检查废弃标记")

def show_next_steps():
    """显示后续步骤"""
    print(f"\n📋 安全删除步骤指南")
    print("=" * 40)
    print("1. ✅ 已完成：创建备份 + 添加废弃标记")
    print("2. 🔄 进行中：监控期（建议1-2周）")
    print("   - 部署到测试环境")
    print("   - 观察日志中的DeprecationWarning")
    print("   - 检查系统运行状态")
    print("3. ⏳ 待执行：确认安全后删除函数")
    print("4. 🧪 最后：充分测试")
    
    print(f"\n⚠️  监控要点:")
    print("- 如果看到DeprecationWarning，说明函数仍被调用")
    print("- 无警告1-2周后，可考虑删除")
    print("- 建议先删除低风险函数（failuremsg系列）")
    print("- mergefromfeature函数最后处理")

def generate_deletion_command():
    """生成删除命令（供参考）"""
    print(f"\n🗑️  删除命令参考（请谨慎使用）:")
    print("=" * 40)
    functions = [
        ('timeformat', '最低风险，时间格式函数'),
        ('failuremsg_old', '低风险，已被替代'),  
        ('failuremsg', '低风险，已被替代'),
        ('MRnotdeal_20230726', '低风险，历史代码'),
        ('mergefromfeature', '中等风险，最后删除')
    ]
    
    for func, desc in functions:
        print(f"# 删除 {func} - {desc}")
        print(f"sed -i '/^def {func}(/,/^def /{{/^def {func}(/,/^def /d;}}' app01/views.py")
        print()

if __name__ == "__main__":
    check_current_status()
    show_next_steps()
    generate_deletion_command()
    
    print(f"\n💡 提示:")
    print("如需立即删除特定函数，建议手动编辑而非使用脚本")
    print("这样可以更精确地控制删除范围") 
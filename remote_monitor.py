#!/usr/bin/env python3
"""
远程服务器废弃函数监控脚本
适合在服务器上定期运行的简化版本
"""

import os
import subprocess
import sys
from datetime import datetime

def print_header():
    print("=" * 60)
    print("🔍 废弃函数监控报告")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

def check_basic_status():
    """检查基础状态"""
    print("\n📋 基础状态检查:")
    
    # 检查是否在正确目录
    if not os.path.exists('app01/views.py'):
        print("❌ 错误：未找到 app01/views.py，请在项目根目录运行")
        return False
    
    print("✅ 项目目录正确")
    
    # 检查废弃标记
    try:
        result = subprocess.run(['grep', '-c', 'DEPRECATED', 'app01/views.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            count = result.stdout.strip()
            print(f"✅ 废弃标记: {count} 个函数已标记")
        else:
            print("❌ 未找到废弃标记")
    except Exception as e:
        print(f"❌ 检查废弃标记失败: {e}")
    
    return True

def check_django_health():
    """检查Django健康状态"""
    print("\n🏥 Django健康检查:")
    
    try:
        # Django check
        result = subprocess.run(['python3', 'manage.py', 'check'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Django check 通过")
        else:
            print(f"⚠️  Django check 有警告:\n{result.stdout}")
    except Exception as e:
        print(f"❌ Django check 失败: {e}")
    
    try:
        # 测试导入views
        result = subprocess.run(['python3', '-c', 'import app01.views; print("导入成功")'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Views模块导入正常")
        else:
            print(f"❌ Views模块导入失败: {result.stderr}")
    except Exception as e:
        print(f"❌ Views导入测试失败: {e}")

def check_deprecation_warnings():
    """检查废弃警告"""
    print("\n⚠️  废弃警告检查:")
    
    log_dirs = ['logs/', './logs/', '/var/log/']
    warnings_found = False
    
    for log_dir in log_dirs:
        if os.path.exists(log_dir):
            print(f"📂 检查日志目录: {log_dir}")
            try:
                # 查找最近的废弃警告
                cmd = f"find {log_dir} -name '*.log' -mtime -7 -exec grep -l 'deprecat\\|DeprecationWarning' {{}} \\;"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    warnings_found = True
                    print("🚨 发现废弃警告，相关日志文件:")
                    for log_file in result.stdout.strip().split('\n'):
                        print(f"   - {log_file}")
                        # 显示最近几条警告
                        grep_cmd = f"grep -i 'deprecat\\|DeprecationWarning' {log_file} | tail -3"
                        grep_result = subprocess.run(grep_cmd, shell=True, capture_output=True, text=True)
                        if grep_result.stdout:
                            print(f"     最近警告: {grep_result.stdout.strip()}")
                else:
                    print(f"   ✅ {log_dir} 未发现废弃警告")
            except Exception as e:
                print(f"   ❌ 检查 {log_dir} 失败: {e}")
    
    if not warnings_found:
        print("🎉 恭喜！未发现任何废弃函数调用警告")
        return True
    return False

def show_next_steps(no_warnings):
    """显示下一步建议"""
    print(f"\n📋 下一步建议:")
    
    if no_warnings:
        print("✅ 未发现废弃警告，可以考虑删除函数")
        print("🔄 建议删除顺序（按风险等级）:")
        functions = [
            ('timeformat', '极低风险'),
            ('failuremsg_old', '低风险'),
            ('failuremsg', '低风险'),
            ('MRnotdeal_20230726', '低风险'),
            ('mergefromfeature', '中等风险，最后删除')
        ]
        for func, risk in functions:
            print(f"   • {func} - {risk}")
    else:
        print("⚠️  发现废弃函数仍在使用")
        print("📝 建议:")
        print("   1. 检查调用源并修改代码")
        print("   2. 继续监控1-2周")
        print("   3. 确认警告消失后再删除")
    
    print(f"\n⏰ 建议下次检查时间: {datetime.now().strftime('%Y-%m-%d')} + 3天")

def main():
    print_header()
    
    if not check_basic_status():
        sys.exit(1)
    
    check_django_health()
    no_warnings = check_deprecation_warnings()
    show_next_steps(no_warnings)
    
    print("\n" + "=" * 60)
    print("🏁 监控完成")

if __name__ == "__main__":
    main() 
"""
JIRA 字段映射自动更新的 Crontab 配置

添加到 Django settings.py 的 CRONJOBS 配置中：

CRONJOBS = [
    # 每天凌晨 2 点更新 JIRA 字段映射
    ('0 2 * * *', 'app01.ai_module.field_update_cron.daily_field_update'),
]

或者直接在系统 crontab 中添加：
0 2 * * * cd /path/to/chatbot-ar-be && python manage.py update_jira_fields >> /var/log/jira_field_update.log 2>&1
"""

import logging
import os
import sys
from django.conf import settings

# 确保 Django 设置已加载
if not settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_ar.settings')
    import django
    django.setup()

from app01.ai_module.field_mapper import field_mapper

logger = logging.getLogger(__name__)


def daily_field_update():
    """每日字段更新任务（用于 django-crontab）"""
    try:
        logger.info("开始执行定时字段映射更新任务...")
        
        # 导入异步模块
        import asyncio
        from django.utils import timezone
        
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            start_time = timezone.now()
            
            # 检查是否需要更新
            status = loop.run_until_complete(field_mapper.get_update_status())
            
            if not status['needs_update']:
                logger.info(f"字段映射仍然有效（上次更新: {status['last_update']}），跳过更新")
                return
            
            # 执行更新
            result = loop.run_until_complete(field_mapper.force_update())
            
            execution_time = (timezone.now() - start_time).total_seconds()
            
            if result['success']:
                logger.info(f"定时字段映射更新完成，耗时 {execution_time:.2f} 秒，"
                          f"总字段数: {result['total_fields']}")
            else:
                logger.error(f"定时字段映射更新失败: {result['error']}")
                
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"定时字段映射更新任务异常: {str(e)}")


if __name__ == "__main__":
    # 支持直接运行此脚本进行测试
    daily_field_update() 
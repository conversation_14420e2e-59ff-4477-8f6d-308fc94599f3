#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试统计API接口
"""

import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

def test_api_endpoint(url, description):
    """测试单个API端点"""
    print(f"\n🔍 测试 {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 成功 - 数据类型: {type(data)}")
                if isinstance(data, dict):
                    if 'success' in data:
                        print(f"   API成功: {data['success']}")
                        if data.get('success') and 'data' in data:
                            print(f"   数据键: {list(data['data'].keys()) if isinstance(data['data'], dict) else 'non-dict'}")
                        elif not data.get('success') and 'error' in data:
                            print(f"   ❌ API错误: {data['error']}")
                    else:
                        print(f"   数据键: {list(data.keys())}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_local_apis():
    """测试本地API"""
    base_url = "http://localhost:8000"
    
    apis = [
        (f"{base_url}/api/statistics/realtime/dashboard/", "实时监控面板"),
        (f"{base_url}/api/statistics/realtime/command-trends/", "指令执行趋势"),
        (f"{base_url}/api/statistics/realtime/user-activity/", "用户活动统计"),
    ]
    
    print("🚀 开始测试本地API接口...")
    print("=" * 60)
    
    success_count = 0
    for url, description in apis:
        if test_api_endpoint(url, description):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 测试完成！成功: {success_count}/{len(apis)}")

def test_production_apis():
    """测试生产环境API"""
    base_url = "https://autorelease.chatbot.shopee.io"
    
    apis = [
        (f"{base_url}/api/statistics/realtime/dashboard/", "实时监控面板"),
        (f"{base_url}/api/statistics/realtime/command-trends/", "指令执行趋势"),
        (f"{base_url}/api/statistics/realtime/user-activity/", "用户活动统计"),
    ]
    
    print("🚀 开始测试生产环境API接口...")
    print("=" * 60)
    
    success_count = 0
    for url, description in apis:
        if test_api_endpoint(url, description):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 测试完成！成功: {success_count}/{len(apis)}")

def test_django_services():
    """测试Django服务层"""
    print("🚀 开始测试Django服务层...")
    print("=" * 60)
    
    try:
        from app01.statistics.services import realtime_stats_service
        
        # 测试实时监控数据
        print("\n🔍 测试实时监控数据服务")
        dashboard_data = realtime_stats_service.get_realtime_dashboard_data()
        if 'error' in dashboard_data:
            print(f"❌ 错误: {dashboard_data['error']}")
        else:
            print(f"✅ 成功 - 数据键: {list(dashboard_data.keys())}")
        
        # 测试指令趋势数据
        print("\n🔍 测试指令趋势数据服务")
        trends_data = realtime_stats_service.get_command_trends(days=7)
        if 'error' in trends_data:
            print(f"❌ 错误: {trends_data['error']}")
        else:
            print(f"✅ 成功 - 数据键: {list(trends_data.keys())}")
        
        # 测试用户活动数据
        print("\n🔍 测试用户活动数据服务")
        activity_data = realtime_stats_service.get_user_activity_stats(days=7)
        if 'error' in activity_data:
            print(f"❌ 错误: {activity_data['error']}")
        else:
            print(f"✅ 成功 - 数据键: {list(activity_data.keys())}")
            
    except Exception as e:
        print(f"❌ Django服务测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == 'local':
            test_local_apis()
        elif mode == 'prod':
            test_production_apis()
        elif mode == 'django':
            test_django_services()
        else:
            print("用法:")
            print("  python test_statistics_api.py local    # 测试本地API")
            print("  python test_statistics_api.py prod     # 测试生产环境API")
            print("  python test_statistics_api.py django   # 测试Django服务层")
    else:
        print("🤔 请选择测试模式:")
        print("1. 测试本地API (python test_statistics_api.py local)")
        print("2. 测试生产环境API (python test_statistics_api.py prod)")
        print("3. 测试Django服务层 (python test_statistics_api.py django)")

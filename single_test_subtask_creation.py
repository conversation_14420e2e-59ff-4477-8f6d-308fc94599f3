#!/usr/bin/env python3
"""
单独测试子任务创建功能
用于调试和查看详细日志
"""

import requests
import json
import time
from datetime import datetime

def test_subtask_creation():
    """单独测试子任务创建功能"""

    base_url = "http://autorelease.chatbot.shopee.io"
    api_endpoint = "/api/mock-seatalk/webhook/"  # 使用测试专用的API端点
    
    print("🔧 单独测试：子任务创建功能")
    print("="*60)
    print(f"📍 测试服务器: {base_url}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 测试用例 - 使用测试接口格式
    test_case = {
        "name": "🤖 AI功能 - 子任务创建",
        "request": {
            "test_mode": True,  # 启用测试模式
            "message_type": "private",
            "user_id": "liang.tang",
            "message": "/ai 在SPCB-54865建单：测试任务 1d",
            "timestamp": datetime.now().isoformat()
        },
        "expected_keywords": ["创建", "成功", "SPCB-", "子任务"]
    }
    
    print(f"📋 测试用例: {test_case['name']}")
    print(f"📨 测试请求:")
    print(json.dumps(test_case['request'], ensure_ascii=False, indent=2))
    print(f"🎯 期望关键词: {', '.join(test_case['expected_keywords'])}")
    print()

    # 显示关键信息
    print(f"👤 用户信息:")
    print(f"   User ID: {test_case['request']['user_id']}")
    print(f"   Message Type: {test_case['request']['message_type']}")
    print(f"   Test Mode: {test_case['request']['test_mode']}")
    print(f"   消息内容: {test_case['request']['message']}")
    print()
    
    try:
        # 发送测试请求
        print("📤 发送测试请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{base_url}{api_endpoint}",
            json=test_case['request'],
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ 响应时间: {response_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ HTTP请求成功")
            
            # 解析响应
            try:
                result = response.json()
                print(f"📄 响应数据:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                # 检查拦截的消息
                intercepted_messages = result.get('intercepted_messages', [])
                print(f"\n📨 拦截到 {len(intercepted_messages)} 条消息:")
                
                if intercepted_messages:
                    for i, msg in enumerate(intercepted_messages):
                        msg_text = msg.get('text', '')
                        msg_type = msg.get('type', 'unknown')
                        msg_time = msg.get('timestamp', '')
                        
                        print(f"\n   消息 {i+1} ({msg_type}) - {msg_time}")
                        print(f"   内容长度: {len(msg_text)} 字符")
                        print(f"   内容:")
                        print(f"   ┌─────────────────────────┐")
                        for line in msg_text.split('\n'):
                            print(f"   │ {line}")
                        print(f"   └─────────────────────────┘")
                        
                        # 检查关键词匹配
                        print(f"\n   🔍 关键词匹配检查:")
                        for keyword in test_case['expected_keywords']:
                            matched = keyword.lower() in msg_text.lower()
                            match_icon = "✅" if matched else "❌"
                            print(f"      {match_icon} '{keyword}' - {'匹配' if matched else '未匹配'}")
                else:
                    print("   ❌ 没有拦截到任何消息")
                
                # 总体评估
                print(f"\n📊 测试结果评估:")
                if intercepted_messages:
                    all_text = ' '.join([msg.get('text', '') for msg in intercepted_messages])
                    matched_keywords = []
                    for keyword in test_case['expected_keywords']:
                        if keyword.lower() in all_text.lower():
                            matched_keywords.append(keyword)
                    
                    if matched_keywords:
                        print(f"   ✅ 匹配的关键词: {', '.join(matched_keywords)}")
                        print(f"   📈 匹配率: {len(matched_keywords)}/{len(test_case['expected_keywords'])} ({len(matched_keywords)/len(test_case['expected_keywords'])*100:.1f}%)")
                        
                        if len(matched_keywords) == len(test_case['expected_keywords']):
                            print(f"   🎉 测试通过！")
                        else:
                            print(f"   ⚠️ 部分匹配，可能需要调整期望关键词")
                    else:
                        print(f"   ❌ 没有匹配任何期望关键词")
                        print(f"   💡 建议检查AI处理逻辑或调整期望关键词")
                else:
                    print(f"   ❌ 测试失败：没有拦截到消息")
                    print(f"   💡 可能的原因：")
                    print(f"      • AI处理失败")
                    print(f"      • 消息发送函数未被拦截")
                    print(f"      • 权限问题导致创建失败")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {str(e)}")
                print(f"原始响应: {response.text}")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    
    print("\n" + "="*60)
    print("🔚 单独测试完成")
    print("💡 请检查服务器日志以获取更多详细信息")
    print("="*60)

if __name__ == "__main__":
    test_subtask_creation()

#!/usr/bin/env python3
"""
废弃函数监控脚本 - chatbot-ar-be项目专用
检查添加废弃标记后的函数状态
"""

import os
import subprocess
from datetime import datetime

def check_project_status():
    """检查项目状态"""
    print("🔍 chatbot-ar-be 项目废弃函数检查")
    print("=" * 50)
    
    # 确认我们在正确的项目中
    if not os.path.exists('app01/views.py'):
        print("❌ 错误：未找到 app01/views.py，请确认在 chatbot-ar-be 项目根目录")
        return False
    
    print("✅ 确认在 chatbot-ar-be 项目中")
    
    # 检查备份文件
    try:
        backups = [f for f in os.listdir('app01') if f.startswith('views.py.backup')]
        print(f"✅ 备份文件: {len(backups)} 个")
        for backup in backups:
            stat = os.stat(f'app01/{backup}')
            size_mb = stat.st_size / (1024*1024)
            mtime = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')
            print(f"   - {backup} ({size_mb:.1f}MB, {mtime})")
    except Exception as e:
        print(f"❌ 检查备份失败: {e}")
    
    # 检查废弃标记
    try:
        result = subprocess.run(['grep', '-c', 'DEPRECATED', 'app01/views.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            count = result.stdout.strip()
            print(f"✅ 废弃标记: {count} 个函数已标记")
        else:
            print("❌ 未找到废弃标记")
    except Exception as e:
        print(f"❌ 检查废弃标记失败: {e}")
    
    # 检查具体标记的函数
    deprecated_functions = [
        'MRnotdeal_20230726', 'failuremsg', 'failuremsg_old', 
        'mergefromfeature', 'timeformat'
    ]
    
    print(f"\n📋 废弃函数状态:")
    for func in deprecated_functions:
        try:
            result = subprocess.run(['grep', '-A10', f'def {func}(', 'app01/views.py'], 
                                  capture_output=True, text=True)
            if 'DEPRECATED' in result.stdout:
                print(f"   ✅ {func} - 已标记废弃")
            else:
                print(f"   ❌ {func} - 未找到废弃标记")
        except:
            print(f"   ❓ {func} - 检查失败")
    
    return True

def show_monitoring_guide():
    """显示监控指南"""
    print(f"\n🛡️ 安全监控指南")
    print("=" * 50)
    print("1. 部署检查:")
    print("   python3 manage.py check  # Django检查")
    print("   python3 -c \"import app01.views\"  # 导入检查")
    
    print("\n2. 日志监控:")
    print("   tail -f logs/*.log | grep -i deprecat")
    print("   # 观察是否有 DeprecationWarning")
    
    print("\n3. 测试运行:")
    print("   # 运行主要功能，观察1-2周")
    print("   # 如无警告，可考虑删除")
    
    print("\n4. 删除顺序（按风险等级）:")
    risk_order = [
        ('timeformat', '极低风险', '有明确bug说明'),
        ('failuremsg_old', '低风险', '已被新版本替代'),
        ('failuremsg', '低风险', '已被新版本替代'),
        ('MRnotdeal_20230726', '低风险', '历史遗留代码'),
        ('mergefromfeature', '中等风险', '涉及Git操作，最后删除')
    ]
    
    for func, risk, reason in risk_order:
        print(f"   {func:<20} {risk:<8} - {reason}")

def check_git_status():
    """检查Git状态"""
    print(f"\n📊 Git状态")
    print("=" * 30)
    try:
        # 当前分支
        result = subprocess.run(['git', 'branch', '--show-current'], 
                              capture_output=True, text=True)
        print(f"当前分支: {result.stdout.strip()}")
        
        # 最近提交
        result = subprocess.run(['git', 'log', '--oneline', '-3'], 
                              capture_output=True, text=True)
        print("最近提交:")
        for line in result.stdout.strip().split('\n')[:3]:
            print(f"  {line}")
            
        # 工作区状态
        result = subprocess.run(['git', 'status', '--porcelain'], 
                              capture_output=True, text=True)
        if result.stdout.strip():
            print("未提交更改:")
            print(result.stdout.strip())
        else:
            print("✅ 工作区干净")
            
    except Exception as e:
        print(f"❌ Git检查失败: {e}")

if __name__ == "__main__":
    print("🚀 chatbot-ar-be 废弃函数监控器")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    if check_project_status():
        check_git_status()
        show_monitoring_guide()
        
        print(f"\n💡 下一步建议:")
        print("1. 如果还没部署，先部署到测试环境")
        print("2. 观察1-2周，确认无DeprecationWarning")
        print("3. 从timeformat函数开始删除（风险最低）")
        print("4. 每删除一个函数后进行测试")
        print("\n⚠️  删除前记得再次运行此脚本确认状态") 
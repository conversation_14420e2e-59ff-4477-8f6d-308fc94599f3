#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清除测试统计数据
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from app01.models import (
    BotAccessEvent, CommandExecutionRecord, 
    CronJobExecutionMonitor, SystemHealthSnapshot,
    SystemPerformanceMetrics, UserActivitySummary
)

def clear_all_statistics():
    """清除所有统计数据"""
    print("🧹 开始清除所有统计数据...")
    
    models_to_clear = [
        ('用户访问事件', BotAccessEvent),
        ('指令执行记录', CommandExecutionRecord),
        ('定时任务监控', CronJobExecutionMonitor),
        ('系统健康快照', SystemHealthSnapshot),
        ('系统性能指标', SystemPerformanceMetrics),
        ('用户活动汇总', UserActivitySummary),
    ]
    
    total_deleted = 0
    
    for model_name, model_class in models_to_clear:
        try:
            count = model_class.objects.count()
            if count > 0:
                deleted_count = model_class.objects.all().delete()[0]
                print(f"✅ {model_name}: 删除了 {deleted_count} 条记录")
                total_deleted += deleted_count
            else:
                print(f"ℹ️  {model_name}: 没有数据需要删除")
        except Exception as e:
            print(f"❌ {model_name}: 删除失败 - {e}")
    
    print(f"\n🎉 清除完成！总共删除了 {total_deleted} 条记录")
    print("📊 现在监控面板将显示真实的统计数据")

def clear_test_data_only():
    """只清除测试数据（用户ID包含test_user的数据）"""
    print("🧹 开始清除测试数据...")
    
    # 清除测试用户的访问事件
    test_access_count = BotAccessEvent.objects.filter(
        user_id__contains='test_user'
    ).delete()[0]
    print(f"✅ 测试用户访问事件: 删除了 {test_access_count} 条记录")
    
    # 清除测试用户的指令执行记录
    test_command_count = CommandExecutionRecord.objects.filter(
        user_id__contains='test_user'
    ).delete()[0]
    print(f"✅ 测试用户指令记录: 删除了 {test_command_count} 条记录")
    
    # 清除测试相关的用户活动汇总
    test_summary_count = UserActivitySummary.objects.filter(
        user_id__contains='test_user'
    ).delete()[0]
    print(f"✅ 测试用户活动汇总: 删除了 {test_summary_count} 条记录")
    
    total_deleted = test_access_count + test_command_count + test_summary_count
    print(f"\n🎉 测试数据清除完成！总共删除了 {total_deleted} 条记录")
    print("📊 保留了系统健康快照等非用户相关数据")

def show_current_statistics():
    """显示当前统计数据概况"""
    print("📊 当前统计数据概况:")
    print("-" * 40)
    
    models_info = [
        ('用户访问事件', BotAccessEvent),
        ('指令执行记录', CommandExecutionRecord),
        ('定时任务监控', CronJobExecutionMonitor),
        ('系统健康快照', SystemHealthSnapshot),
        ('系统性能指标', SystemPerformanceMetrics),
        ('用户活动汇总', UserActivitySummary),
    ]
    
    for model_name, model_class in models_info:
        try:
            count = model_class.objects.count()
            print(f"{model_name}: {count} 条记录")
        except Exception as e:
            print(f"{model_name}: 查询失败 - {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        action = sys.argv[1]
        if action == 'all':
            clear_all_statistics()
        elif action == 'test':
            clear_test_data_only()
        elif action == 'show':
            show_current_statistics()
        else:
            print("❌ 未知操作")
            print("用法:")
            print("  python clear_test_statistics.py all    # 清除所有统计数据")
            print("  python clear_test_statistics.py test   # 只清除测试数据")
            print("  python clear_test_statistics.py show   # 显示当前数据概况")
    else:
        print("🤔 请选择操作:")
        print("1. 清除所有统计数据 (python clear_test_statistics.py all)")
        print("2. 只清除测试数据 (python clear_test_statistics.py test)")
        print("3. 显示当前数据概况 (python clear_test_statistics.py show)")

#!/bin/bash

# 检查系统中是否存在 Python3.10 命令
if ! command -v python3.10 &> /dev/null
then
    echo "Python3.10 is not installed. Please install it and try again."
    exit
fi

# 循环监控 manage.py 进程
while true
do
    # 查找所有的 manage.py 进程，并检查它们的 Python 版本是否为 Python3.10
    pids=`ps aux | grep manage.py | grep python3.10 | grep -v grep | awk '{print $2}'`

    if [ -z "$pids" ]
    then
        # 如果没有符合条件的进程，则自动重启 manage.py
        echo "manage.py process not found. Restarting..."
        python3.10 manage.py runserver &
    else
        echo "manage.py process is running. PIDs: $pids"
    fi

    # 每 30 秒检查一次进程
    sleep 600
done


#!/usr/bin/env python3
"""
智能通知任务管理工具
用于生产环境的智能通知任务管理、测试和维护
"""

import asyncio
import sys
import os
import argparse
from datetime import datetime
import json

# Django环境设置
import django
from django.conf import settings
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from app01.ai_module.advanced_task_manager import advanced_task_manager
from app01.ai_module.task_scheduler import task_scheduler
from app01.models import UserScheduledTask, AdvancedTaskFeatureWhitelist


class SmartNotificationManager:
    """智能通知任务管理工具"""
    
    def __init__(self):
        self.manager = advanced_task_manager
        self.scheduler = task_scheduler
    
    async def list_smart_tasks(self, user_id: str = None):
        """列出所有智能通知任务"""
        try:
            from asgiref.sync import sync_to_async
            
            def get_tasks():
                query = UserScheduledTask.objects.filter(notification_type='smart')
                if user_id:
                    query = query.filter(user_id=user_id)
                return list(query.order_by('-created_at'))
            
            get_tasks_async = sync_to_async(get_tasks)
            tasks = await get_tasks_async()
            
            if not tasks:
                print("📋 没有找到智能通知任务")
                return
            
            print(f"📋 智能通知任务列表 (共 {len(tasks)} 个):")
            print("=" * 80)
            
            # 获取群组名称映射
            from app01.models import SeatalkGroup
            from asgiref.sync import sync_to_async
            
            def get_group_mapping():
                group_id_to_name = {}
                for group in SeatalkGroup.objects.all():
                    group_id_to_name[group.group_id] = group.group_name
                return group_id_to_name
            
            get_groups_async = sync_to_async(get_group_mapping)
            group_id_to_name = await get_groups_async()
            
            for task in tasks:
                status_icon = "✅" if task.status == 'active' else "⏸️" if task.status == 'paused' else "❌"
                
                print(f"{status_icon} ID: {task.id} | {task.task_name}")
                print(f"   👤 用户: {task.user_email}")
                print(f"   🔍 查询: {task.query_text}")
                print(f"   ⏰ 调度: {task.frequency} at {task.schedule_time}")
                print(f"   📊 执行统计: {task.successful_executions}/{task.total_executions}")
                
                if task.next_execution:
                    print(f"   ⏭️  下次执行: {task.next_execution.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 解析智能配置
                try:
                    config = json.loads(task.target_group_id) if task.target_group_id else {}
                    target_type = config.get('target_type', 'auto')
                    
                    # 显示通知目标信息
                    if target_type == 'private':
                        print(f"   🎯 通知目标: 仅私聊")
                    elif target_type == 'group':
                        print(f"   🎯 通知目标: 仅群聊")
                        
                        # 显示群组映射信息
                        if 'group_mapping' in config:
                            print(f"   📢 群组映射:")
                            for project, group_id in config['group_mapping'].items():
                                group_name = group_id_to_name.get(group_id, "未知群组")
                                print(f"      • {project}: {group_name} ({group_id})")
                    elif target_type == 'both':
                        print(f"   🎯 通知目标: 私聊+群聊")
                    else:
                        print(f"   🎯 通知目标: 自动判断")
                    
                    print(f"   🧠 合并消息: {config.get('merge_messages', True)}")
                except Exception as e:
                    print(f"   🧠 智能配置: 默认 (解析失败: {str(e)})")
                
                print("-" * 80)
                
        except Exception as e:
            print(f"❌ 获取任务列表失败: {str(e)}")
    
    async def test_smart_task(self, task_id: int):
        """测试智能通知任务"""
        try:
            from asgiref.sync import sync_to_async
            
            get_task = sync_to_async(
                lambda: UserScheduledTask.objects.get(id=task_id, notification_type='smart')
            )
            task = await get_task()
            
            print(f"🧪 测试智能任务: {task.task_name}")
            print("=" * 50)
            
            # 模拟执行任务（不实际发送通知）
            from app01.ai_module.ai_assistant import ai_assistant
            
            print("🔍 执行JQL查询...")
            ai_result = await ai_assistant.process_query(
                user_query=task.query_text,
                user_id=task.user_id,
                user_email=task.user_email
            )
            
            if ai_result['success']:
                # 提取JIRA结果
                jira_results = self.scheduler._extract_jira_results(ai_result)
                
                print(f"📊 查询结果: 找到 {len(jira_results)} 个ticket")
                
                if jira_results:
                    # 分组处理
                    from app01.ai_module.advanced_task_manager import JiraResultProcessor
                    processor = JiraResultProcessor()
                    grouped_tickets = processor.group_tickets_by_assignee(jira_results)
                    
                    print(f"👥 按assignee分组: {len(grouped_tickets)} 个组")
                    for assignee, tickets in grouped_tickets.items():
                        print(f"   • {assignee}: {len(tickets)} 个ticket")
                    
                    # 生成消息预览
                    from app01.ai_module.advanced_task_manager import SmartMessageGenerator
                    generator = SmartMessageGenerator()
                    
                    print("\n💬 消息预览:")
                    merged_messages = await generator.generate_merged_messages(
                        grouped_tickets, task.task_name, task.task_description or ""
                    )
                    
                    for assignee, message in list(merged_messages.items())[:2]:  # 只显示前2个
                        print(f"\n👤 发送给 {assignee}:")
                        print("-" * 30)
                        print(message[:200] + "..." if len(message) > 200 else message)
                    
                    if len(merged_messages) > 2:
                        print(f"\n... 还有 {len(merged_messages) - 2} 个接收者")
                
                else:
                    print("✅ 查询无结果")
            else:
                print(f"❌ 查询失败: {ai_result.get('error', '未知错误')}")
                
        except UserScheduledTask.DoesNotExist:
            print(f"❌ 未找到ID为 {task_id} 的智能任务")
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    async def create_smart_task_interactive(self):
        """交互式创建智能通知任务"""
        print("🧠 交互式创建智能通知任务")
        print("=" * 40)
        
        try:
            # 收集基本信息
            user_email = input("👤 用户邮箱: ").strip()
            if not user_email:
                print("❌ 用户邮箱不能为空")
                return
            
            user_id = user_email.split('@')[0]
            task_name = input("📝 任务名称: ").strip()
            if not task_name:
                print("❌ 任务名称不能为空")
                return
            
            jql_query = input("🔍 JQL查询: ").strip()
            if not jql_query:
                print("❌ JQL查询不能为空")
                return
            
            schedule_time = input("⏰ 执行时间 (HH:MM): ").strip()
            if not schedule_time:
                schedule_time = "10:00"
            
            frequency = input("📅 频率 (daily/weekly/monthly) [daily]: ").strip()
            if not frequency:
                frequency = "daily"
            
            # 智能配置
            print("\n🧠 智能通知配置:")
            target_type = input("🎯 通知类型 (private/group/both/auto) [auto]: ").strip()
            if not target_type:
                target_type = "auto"
            
            merge_messages = input("📋 是否合并消息 (y/n) [y]: ").strip().lower()
            merge_messages = merge_messages != 'n'
            
            message_template = input("📄 消息模板 (daily_reminder/bug_alert/subtask_reminder/general) [general]: ").strip()
            if not message_template:
                message_template = "general"
            
            # 构建配置
            notification_config = {
                'type': 'assignee_based',
                'target_type': target_type,
                'merge_messages': merge_messages,
                'smart_content': True,
                'message_template': message_template,
                'group_mapping': {}
            }
            
            print("\n🔄 创建任务中...")
            
            # 创建任务
            result = await self.manager.create_smart_notification_task(
                user_id=user_id,
                user_email=user_email,
                task_name=task_name,
                jql_query=jql_query,
                schedule_time=schedule_time,
                frequency=frequency,
                notification_config=notification_config
            )
            
            if result['success']:
                print(f"✅ 智能任务创建成功!")
                print(f"🆔 任务ID: {result['task_id']}")
                print(f"📝 任务名称: {task_name}")
                print(f"🎯 通知模式: {target_type}")
            else:
                print(f"❌ 创建失败: {result.get('error', '未知错误')}")
                
        except KeyboardInterrupt:
            print("\n❌ 操作已取消")
        except Exception as e:
            print(f"❌ 创建失败: {str(e)}")
    
    async def manage_whitelist(self, action: str, user_id: str = None):
        """管理智能通知白名单"""
        try:
            from asgiref.sync import sync_to_async
            
            if action == 'list':
                get_whitelists = sync_to_async(
                    lambda: list(AdvancedTaskFeatureWhitelist.objects.filter(is_active=True))
                )
                whitelists = await get_whitelists()
                
                if not whitelists:
                    print("📋 没有找到白名单用户")
                    return
                
                print(f"📋 智能通知白名单用户 (共 {len(whitelists)} 个):")
                print("=" * 60)
                
                for whitelist in whitelists:
                    features = whitelist.features if whitelist.features else {}
                    has_smart = features.get('smart_notification', False)
                    
                    status_icon = "✅" if has_smart else "❌"
                    print(f"{status_icon} {whitelist.user_id} | {whitelist.user_email}")
                    print(f"   权限: {list(features.keys()) if features else '无'}")
                    print("-" * 60)
            
            elif action == 'add' and user_id:
                # 添加用户到白名单
                def add_to_whitelist():
                    whitelist, created = AdvancedTaskFeatureWhitelist.objects.get_or_create(
                        user_id=user_id,
                        defaults={
                            'user_email': f"{user_id}@shopee.com",
                            'features': {'smart_notification': True},
                            'is_active': True
                        }
                    )
                    
                    if not created:
                        features = whitelist.features or {}
                        features['smart_notification'] = True
                        whitelist.features = features
                        whitelist.is_active = True
                        whitelist.save()
                    
                    return created
                
                add_whitelist = sync_to_async(add_to_whitelist)
                created = await add_whitelist()
                
                print(f"✅ 已将用户 {user_id} 添加到智能通知白名单")
            
            elif action == 'remove' and user_id:
                # 从白名单移除用户
                def remove_from_whitelist():
                    try:
                        whitelist = AdvancedTaskFeatureWhitelist.objects.get(user_id=user_id)
                        features = whitelist.features or {}
                        if 'smart_notification' in features:
                            del features['smart_notification']
                            whitelist.features = features
                            whitelist.save()
                        return True
                    except AdvancedTaskFeatureWhitelist.DoesNotExist:
                        return False
                
                remove_whitelist = sync_to_async(remove_from_whitelist)
                success = await remove_whitelist()
                
                if success:
                    print(f"✅ 已将用户 {user_id} 从智能通知白名单移除")
                else:
                    print(f"❌ 用户 {user_id} 不在白名单中")
            
            else:
                print("❌ 无效的操作或缺少用户ID")
                
        except Exception as e:
            print(f"❌ 白名单管理失败: {str(e)}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能通知任务管理工具')
    parser.add_argument('command', choices=['list', 'test', 'create', 'whitelist'], 
                       help='执行的命令')
    parser.add_argument('--user-id', help='用户ID')
    parser.add_argument('--task-id', type=int, help='任务ID')
    parser.add_argument('--action', choices=['list', 'add', 'remove'], help='白名单操作')
    
    args = parser.parse_args()
    
    manager = SmartNotificationManager()
    
    try:
        if args.command == 'list':
            await manager.list_smart_tasks(args.user_id)
        
        elif args.command == 'test':
            if not args.task_id:
                print("❌ 请指定任务ID: --task-id")
                return
            await manager.test_smart_task(args.task_id)
        
        elif args.command == 'create':
            await manager.create_smart_task_interactive()
        
        elif args.command == 'whitelist':
            if not args.action:
                print("❌ 请指定操作: --action [list|add|remove]")
                return
            await manager.manage_whitelist(args.action, args.user_id)
        
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🧠 智能通知任务管理工具")
    print("=" * 40)
    
    if len(sys.argv) == 1:
        print("""
使用方法:
  python smart_notification_manager.py list [--user-id USER_ID]     # 列出智能任务
  python smart_notification_manager.py test --task-id TASK_ID       # 测试任务
  python smart_notification_manager.py create                       # 交互式创建任务
  python smart_notification_manager.py whitelist --action list      # 查看白名单
  python smart_notification_manager.py whitelist --action add --user-id USER_ID    # 添加白名单
  python smart_notification_manager.py whitelist --action remove --user-id USER_ID # 移除白名单

示例:
  python smart_notification_manager.py list
  python smart_notification_manager.py test --task-id 123
  python smart_notification_manager.py whitelist --action add --user-id john
        """)
    else:
        asyncio.run(main()) 

📊 测试汇总统计
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
总测试数: 15
通过测试: 14 ✅
失败测试: 1 ❌
成功率: 93.3%
测试时间: 2025-07-15 22:21:52


📂 TRADITIONAL 类别测试 (4/4 - 100.0%)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 1] ✅ 🔧 传统指令 - Bug查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "group",
  "user_id": "liang.tang",
  "message": "@ChatbotAR bug SPCB-54865"
}

🎯 期望关键词: bug, Bug, SPCB-54865

📨 拦截到 1 条消息:

   消息 1 (group_message) - 2025-07-15T22:20:05.286989
   ┌─ 内容 (471 字符) ─┐
   │ 需求"【SPCB-54865】Workee 测试需求"的BUG统计信息：
   │ 总计: 5个BUG，解决率: 80.0%
   │ 
   │ 各状态分布：
   │ TO DO: 1个
   │ DONE: 3个
   │ CLOSED: 1个
   │ 
   │ 【To Do】状态的BUG：
   │ 1. 【P1】CLONE - 测试 Workee 的 bug 单
   │ 🔗 SPCB-54866: https://jira.shopee.io/browse/SPCB-54866
   │ 处理人: <mention-tag target="seatalk://user?email=<EMAIL>"/>
   │ ------------------------------------------------
   │ 
   │ 🔗 查看所有相关BUG:
   │ https://jira.shopee.io/issues/?jql=issue%20in%20linkedIssues%28%22SPCB-54865%22%2C%20%22is%20blocked%20by%22%29%20AND%20type%20%3D%20bug
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'bug'
   ✅ 'Bug'
   ✅ 'SPCB-54865'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['bug', 'Bug', 'SPCB-54865']
   响应时间: 1.90秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 2] ✅ 🔧 传统指令 - Timeline查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "group",
  "user_id": "liang.tang",
  "message": "@ChatbotAR timeline SPCB-54865"
}

🎯 期望关键词: timeline, 时间线, SPCB-54865

📨 拦截到 1 条消息:

   消息 1 (group_message) - 2025-07-15T22:20:07.742358
   ┌─ 内容 (716 字符) ─┐
   │ 📊 **需求时间线概览 | SPCB-54865** 📊
   │ 
   │ 📌 **Workee 测试需求**
   │ 🔗 https://jira.shopee.io/browse/SPCB-54865
   │ 📒 Fix Version/s: **Adhoc-250708**
   │ 
   │ ⏰ **关键日期:━━━━━━━━━━━━━**
   │ • PRD Review Start Date: **2025-06-04**
   │ • PRD Review End Date: **2025-06-04**
   │ • Planned Dev Start Date: **2025-06-09**
   │ • Planned Dev Due Date: **2025-06-20**
   │ • Planned QA Start Date: **2025-06-23**
   │ • Planned QA Due Date: **2025-06-25**
   │ • Planned Release Date: **2025-07-08**
   │ 
   │ 👥 **团队成员:━━━━━━━━━━━━━**
   │ • PM: <mention-tag target="seatalk://user?email=<EMAIL>"/>
   │ • Tech PIC: <mention-tag target="seatalk://user?email=<EMAIL>"/>
   │ • QA: <mention-tag target="seatalk://user?email=<EMAIL>"/>
   │ 
   │ 💡 需查看此需求下的bug状态，请发送: `@ChatbotAR bug`
   └─────────────────────────┘

🔍 关键词匹配分析:
   ❌ 'timeline'
   ✅ '时间线'
   ✅ 'SPCB-54865'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['时间线', 'SPCB-54865']
   响应时间: 20.68秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 3] ✅ 🔧 传统指令 - Checklist确认 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "group",
  "user_id": "liang.tang",
  "message": "@ChatbotAR SPCB-54865 SO"
}

🎯 期望关键词: confirmed, 确认, Signed off

📨 拦截到 1 条消息:

   消息 1 (group_message) - 2025-07-15T22:20:29.836640
   ┌─ 内容 (23 字符) ─┐
   │ Signed off，confirmed成功！
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'confirmed'
   ❌ '确认'
   ✅ 'Signed off'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['confirmed', 'Signed off']
   响应时间: 1.81秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 4] ✅ 🔧 传统指令 - JIRA单号查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "group",
  "user_id": "liang.tang",
  "message": "@ChatbotAR SPCB-54865"
}

🎯 期望关键词: SPCB-54865, 状态

📨 拦截到 1 条消息:

   消息 1 (group_message) - 2025-07-15T22:20:33.247506
   ┌─ 内容 (111 字符) ─┐
   │ 📋 **SPCB-54865：** 【High】 【Waiting】 
   │ 📌 Workee 测试需求
   │ 🔗 https://jira.shopee.io/browse/SPCB-54865
   │  经办人: Pengbin Feng
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'SPCB-54865'
   ❌ '状态'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['SPCB-54865']
   响应时间: 2.99秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


📂 AI_CRITICAL 类别测试 (3/3 - 100.0%)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 1] ✅ 🤖 AI功能 - 未完成子任务查询(修复验证) - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai 我有哪些未完成的子任务"
}

🎯 期望关键词: sub-task, SPCB-, 经办人

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:20:47.240493
   ┌─ 内容 (479 字符) ─┐
   │ 根据您的查询条件，找到了3个相关的结果:
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54899
   │ 📌 测试任务
   │ 经办人: <EMAIL>
   │ ----------------------------------
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54898
   │ 📌 测试任务
   │ 经办人: <EMAIL>
   │ ----------------------------------
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54848
   │ 📌 完善 EasyWork 工具的接口测试组件
   │ 经办人: <EMAIL>
   │ 
   │ 🔍 **执行的JQL查询:**
   │ ```
   │ assignee = currentUser() AND type = sub-task AND status not in (Closed, Done, Icebox)
   │ ```
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'sub-task'
   ✅ 'SPCB-'
   ✅ '经办人'

📊 验证结果:
   状态: 通过
   验证信息: 验证通过：找到关键词 'status not in (Closed, Done, Icebox)' | 匹配关键词: ['sub-task', 'SPCB-', '经办人']
   响应时间: 12.44秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 2] ✅ 🤖 AI功能 - 我的子任务(多种表述) - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai 我的子任务"
}

🎯 期望关键词: assignee, currentUser, 子任务

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:20:57.263488
   ┌─ 内容 (479 字符) ─┐
   │ 根据您的查询条件，找到了3个相关的结果:
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54899
   │ 📌 测试任务
   │ 经办人: <EMAIL>
   │ ----------------------------------
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54898
   │ 📌 测试任务
   │ 经办人: <EMAIL>
   │ ----------------------------------
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54848
   │ 📌 完善 EasyWork 工具的接口测试组件
   │ 经办人: <EMAIL>
   │ 
   │ 🔍 **执行的JQL查询:**
   │ ```
   │ assignee = currentUser() AND type = sub-task AND status not in (Closed, Done, Icebox)
   │ ```
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'assignee'
   ✅ 'currentUser'
   ❌ '子任务'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['assignee', 'currentUser']
   响应时间: 9.14秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 3] ✅ 🤖 AI功能 - 待处理的子任务 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai 待处理的子任务"
}

🎯 期望关键词: sub-task, SPCB-, 经办人

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:21:06.365373
   ┌─ 内容 (480 字符) ─┐
   │ 根据您的查询条件，找到了3个相关的结果:
   │ 
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54899
   │ 📌 测试任务
   │ 经办人: <EMAIL>
   │ ----------------------------------
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54898
   │ 📌 测试任务
   │ 经办人: <EMAIL>
   │ ----------------------------------
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54848
   │ 📌 完善 EasyWork 工具的接口测试组件
   │ 经办人: <EMAIL>
   │ 
   │ 🔍 **执行的JQL查询:**
   │ ```
   │ assignee = currentUser() AND type = sub-task AND status not in (Closed, Done, Icebox)
   │ ```
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'sub-task'
   ✅ 'SPCB-'
   ✅ '经办人'

📊 验证结果:
   状态: 通过
   验证信息: 验证通过：找到关键词 'status not in (Closed, Done, Icebox)' | 匹配关键词: ['sub-task', 'SPCB-', '经办人']
   响应时间: 7.94秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


📂 AI 类别测试 (3/4 - 75.0%)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 1] ✅ 🤖 AI功能 - Bug查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai 查询我的bug"
}

🎯 期望关键词: assignee, Bug, currentUser

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:21:13.716947
   ┌─ 内容 (234 字符) ─┐
   │ 根据您的查询条件，找到了1个相关的结果:
   │ 📋【P1】【TO DO】https://jira.shopee.io/browse/SPCB-54866
   │ 📌 CLONE - 测试 Workee 的 bug 单
   │ 经办人: <EMAIL>
   │ 
   │ 🔍 **执行的JQL查询:**
   │ ```
   │ assignee = currentUser() AND type = Bug AND status not in (Closed, Done, Icebox)
   │ ```
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'assignee'
   ✅ 'Bug'
   ✅ 'currentUser'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['assignee', 'Bug', 'currentUser']
   响应时间: 6.35秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 2] ✅ 🤖 AI功能 - 项目进度查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai SPCB-54865的进度如何"
}

🎯 期望关键词: SPCB-54865, 进度

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:21:21.648851
   ┌─ 内容 (161 字符) ─┐
   │ 根据您的查询条件，找到了1个相关的结果:
   │ 📋【无优先级】【Waiting】https://jira.shopee.io/browse/SPCB-54865
   │ 📌 Workee 测试需求
   │ 经办人: <EMAIL>
   │ 
   │ 🔍 **执行的JQL查询:**
   │ ```
   │ key = SPCB-54865
   │ ```
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'SPCB-54865'
   ❌ '进度'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['SPCB-54865']
   响应时间: 6.93秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 3] ❌ 🤖 AI功能 - 子任务创建 - 失败
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai 在SPCB-54865建单：测试任务 1d"
}

🎯 期望关键词: 创建, 成功, SPCB-

📨 拦截到 1 条消息:

   消息 1 (mock_message) - 2025-07-15T22:21:24.805319
   ┌─ 内容 (23 字符) ─┐
   │ 这是一条模拟消息，实际消息可能通过其他渠道发送
   └─────────────────────────┘

🔍 关键词匹配分析:
   ❌ '创建'
   ❌ '成功'
   ❌ 'SPCB-'

📊 验证结果:
   状态: 失败
   验证信息: 验证失败：未找到任何预期关键词 ['创建', '成功', 'SPCB-']
   响应时间: 2.14秒

💡 可能的问题分析:
   • 关键词不匹配 - 未找到: 创建, 成功, SPCB-
   • 建议检查AI响应内容是否使用了不同的表述

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 4] ✅ 🤖 AI功能 - 定时任务查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai 我的定时任务有哪些"
}

🎯 期望关键词: 定时任务, 列表, 您的

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:21:30.564426
   ┌─ 内容 (147 字符) ─┐
   │ 需要更多信息来处理您的请求。
   │ 
   │ 💡 **常用功能：**
   │ • `/ai help` - 查看完整帮助
   │ • `/ai schedule` - 定时任务管理
   │ • `/ai schedule create` - 创建智能通知任务
   │ • `/ai fields search 关键词` - 搜索JIRA字段
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ '定时任务'
   ❌ '列表'
   ✅ '您的'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['定时任务', '您的']
   响应时间: 4.76秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


📂 SPCPM 类别测试 (2/2 - 100.0%)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 1] ✅ 🔧 SPCPM - Timeline查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "group",
  "user_id": "liang.tang",
  "message": "@ChatbotAR timeline SPCPM-103235"
}

🎯 期望关键词: timeline, SPCPM-103235, 时间线

📨 拦截到 1 条消息:

   消息 1 (group_message) - 2025-07-15T22:21:32.891948
   ┌─ 内容 (247 字符) ─┐
   │ Project Timeline for SPCPM-103235:
   │ 
   │ PRD: 2025-06-23, 2025-06-24
   │ Tech Design: 2025-06-30, 2025-07-08
   │ Development: 2025-07-04, 2025-07-07
   │ Integration: 2025-07-07, 2025-07-08
   │ QA: 2025-07-28, 2025-07-30
   │ UAT: 2025-07-28, 2025-07-30
   │ Release: 2025-08-07
   │ 
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ 'timeline'
   ✅ 'SPCPM-103235'
   ❌ '时间线'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['timeline', 'SPCPM-103235']
   响应时间: 1.31秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 2] ✅ 🔧 SPCPM - 角色@功能 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "group",
  "user_id": "liang.tang",
  "message": "@ChatbotAR qa SPCPM-103235"
}

🎯 期望关键词: QA, SPCPM-103235, 角色

📨 拦截到 1 条消息:

   消息 1 (group_message) - 2025-07-15T22:21:36.830179
   ┌─ 内容 (100 字符) ─┐
   │ SPCPM-103235 Project Team Members:
   │ <mention-tag target="seatalk://user?email=<EMAIL>"/>
   └─────────────────────────┘

🔍 关键词匹配分析:
   ❌ 'QA'
   ✅ 'SPCPM-103235'
   ❌ '角色'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['SPCPM-103235']
   响应时间: 2.94秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


📂 ERROR 类别测试 (2/2 - 100.0%)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 1] ✅ ⚠️ 错误处理 - 无效JIRA单号 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai INVALID-12345的状态"
}

🎯 期望关键词: 未找到, 符合条件, 结果

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:21:47.017755
   ┌─ 内容 (59 字符) ─┐
   │ 抱歉，未找到符合条件的结果。
   │ 
   │ 🔍 **执行的JQL查询:**
   │ ```
   │ key = INVALID-12345
   │ ```
   └─────────────────────────┘

🔍 关键词匹配分析:
   ✅ '未找到'
   ✅ '符合条件'
   ✅ '结果'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['未找到', '符合条件', '结果']
   响应时间: 9.76秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


[ 2] ✅ ⚠️ 错误处理 - 空AI查询 - 通过
────────────────────────────────────────────────────────────────────────────────────────────────────

📋 测试请求:
{
  "test_mode": true,
  "message_type": "private",
  "user_id": "liang.tang",
  "message": "/ai"
}

🎯 期望关键词: 帮助, 功能, 问题

📨 拦截到 1 条消息:

   消息 1 (sent_aio_message) - 2025-07-15T22:21:52.440564
   ┌─ 内容 (60 字符) ─┐
   │ 你好！请问有什么可以帮您的吗？如果您有关于AI（人工智能）的问题，或者需要技术支持、代码示例、学习资料等，欢迎随时提问！
   └─────────────────────────┘

🔍 关键词匹配分析:
   ❌ '帮助'
   ❌ '功能'
   ✅ '问题'

📊 验证结果:
   状态: 通过
   验证信息: 基本验证通过 | 匹配关键词: ['问题']
   响应时间: 3.87秒

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━


#!/bin/bash

# ChatbotAR 服务停止脚本
# 停止Django服务和定时任务调度器

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/task_scheduler.pid"

echo "🛑 停止 ChatbotAR 所有服务..."
echo ""

# 停止Django服务
echo "📝 停止Django服务..."
if pgrep -f "manage.py runserver.*8081" > /dev/null; then
    echo "   找到Django进程，正在停止..."
    pkill -f "manage.py runserver.*8081"
    
    # 等待进程停止
    for i in {1..5}; do
        if ! pgrep -f "manage.py runserver.*8081" > /dev/null; then
            echo "   ✅ Django服务已停止"
            break
        fi
        echo "   ⏳ 等待Django停止... ($i/5)"
        sleep 1
    done
    
    # 如果还在运行，强制终止
    if pgrep -f "manage.py runserver.*8081" > /dev/null; then
        echo "   ❌ Django未响应，强制终止..."
        pkill -9 -f "manage.py runserver.*8081"
        sleep 1
        if pgrep -f "manage.py runserver.*8081" > /dev/null; then
            echo "   ❌ 无法停止Django服务"
        else
            echo "   ✅ Django服务已强制停止"
        fi
    fi
else
    echo "   ℹ️  Django服务未运行"
fi

echo ""

# 停止定时任务调度器
echo "⏰ 停止定时任务调度器..."
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    
    if ps -p $PID > /dev/null 2>&1; then
        echo "   找到调度器进程 (PID: $PID)"
        
        # 发送TERM信号
        kill -TERM $PID
        
        # 等待进程停止
        for i in {1..5}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "   ✅ 定时任务调度器已停止"
                rm -f "$PID_FILE"
                break
            fi
            echo "   ⏳ 等待调度器停止... ($i/5)"
            sleep 1
        done
        
        # 如果还在运行，强制终止
        if ps -p $PID > /dev/null 2>&1; then
            echo "   ❌ 调度器未响应，强制终止..."
            kill -KILL $PID
            sleep 1
            
            if ps -p $PID > /dev/null 2>&1; then
                echo "   ❌ 无法停止调度器"
            else
                echo "   ✅ 调度器已强制停止"
                rm -f "$PID_FILE"
            fi
        fi
    else
        echo "   ❌ PID文件中的进程不存在，清理PID文件"
        rm -f "$PID_FILE"
    fi
else
    echo "   ℹ️  未找到PID文件"
    
    # 尝试查找运行中的调度器进程
    RUNNING_PIDS=$(ps aux | grep "run_scheduled_tasks" | grep -v grep | awk '{print $2}')
    
    if [ ! -z "$RUNNING_PIDS" ]; then
        echo "   🔍 发现运行中的调度器进程，正在停止..."
        kill -TERM $RUNNING_PIDS
        sleep 2
        
        # 检查是否成功停止
        STILL_RUNNING=$(ps aux | grep "run_scheduled_tasks" | grep -v grep | awk '{print $2}')
        if [ ! -z "$STILL_RUNNING" ]; then
            echo "   强制终止调度器进程..."
            kill -KILL $STILL_RUNNING
        fi
        echo "   ✅ 调度器进程已停止"
    else
        echo "   ℹ️  没有发现运行中的调度器进程"
    fi
fi

echo ""
echo "🎯 服务状态检查:"
echo "   Django: $(pgrep -f 'manage.py runserver.*8081' >/dev/null && echo '❌ 仍在运行' || echo '✅ 已停止')"
echo "   调度器: $(ps aux | grep 'run_scheduled_tasks' | grep -v grep >/dev/null && echo '❌ 仍在运行' || echo '✅ 已停止')"
echo ""
echo "✅ 停止操作完成" 
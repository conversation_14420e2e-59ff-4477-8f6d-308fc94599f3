#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库中的统计数据
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from django.utils import timezone
from app01.models import BotAccessEvent, CommandExecutionRecord, CronJobExecutionMonitor, SystemHealthSnapshot

def check_database_status():
    """检查数据库中各表的数据状态"""
    print("🔍 检查数据库数据状态")
    print("=" * 60)
    
    # 检查时间范围
    now = timezone.now()
    today = now.date()
    thirty_minutes_ago = now - timedelta(minutes=30)
    one_hour_ago = now - timedelta(hours=1)
    
    print(f"📅 当前时间: {now}")
    print(f"📅 今日日期: {today}")
    print(f"📅 30分钟前: {thirty_minutes_ago}")
    print(f"📅 1小时前: {one_hour_ago}")
    print()
    
    # 1. 检查 BotAccessEvent 表
    print("1️⃣ BotAccessEvent (用户访问事件)")
    print("-" * 40)
    
    total_access_events = BotAccessEvent.objects.count()
    recent_access_events = BotAccessEvent.objects.filter(created_at__gte=thirty_minutes_ago).count()
    active_users = BotAccessEvent.objects.filter(created_at__gte=thirty_minutes_ago).values('user_id').distinct().count()
    
    print(f"   总访问事件数: {total_access_events}")
    print(f"   最近30分钟事件数: {recent_access_events}")
    print(f"   最近30分钟活跃用户数: {active_users}")
    
    if recent_access_events > 0:
        recent_events = BotAccessEvent.objects.filter(
            created_at__gte=thirty_minutes_ago
        ).order_by('-created_at')[:5]
        
        print("   最近的访问事件:")
        for event in recent_events:
            print(f"     - {event.created_at}: {event.user_id} ({event.event_type})")
    print()
    
    # 2. 检查 CommandExecutionRecord 表
    print("2️⃣ CommandExecutionRecord (指令执行记录)")
    print("-" * 40)
    
    total_commands = CommandExecutionRecord.objects.count()
    today_commands = CommandExecutionRecord.objects.filter(created_at__date=today).count()
    recent_commands = CommandExecutionRecord.objects.filter(created_at__gte=one_hour_ago).count()
    
    print(f"   总指令记录数: {total_commands}")
    print(f"   今日指令数: {today_commands}")
    print(f"   最近1小时指令数: {recent_commands}")
    
    if total_commands > 0:
        latest_commands = CommandExecutionRecord.objects.order_by('-created_at')[:5]
        print("   最新的指令记录:")
        for cmd in latest_commands:
            print(f"     - {cmd.created_at}: {cmd.user_id} ({cmd.command_type}) - {'成功' if cmd.success else '失败'}")
    else:
        print("   ❌ 没有找到任何指令执行记录！")
    print()
    
    # 3. 检查 CronJobExecutionMonitor 表
    print("3️⃣ CronJobExecutionMonitor (定时任务监控)")
    print("-" * 40)
    
    total_cronjobs = CronJobExecutionMonitor.objects.count()
    running_cronjobs = CronJobExecutionMonitor.objects.filter(status='running').count()
    
    print(f"   总定时任务记录数: {total_cronjobs}")
    print(f"   运行中的任务数: {running_cronjobs}")
    
    if total_cronjobs > 0:
        latest_jobs = CronJobExecutionMonitor.objects.order_by('-created_at')[:3]
        print("   最新的任务记录:")
        for job in latest_jobs:
            print(f"     - {job.created_at}: {job.job_name} ({job.status})")
    print()
    
    # 4. 检查 SystemHealthSnapshot 表
    print("4️⃣ SystemHealthSnapshot (系统健康快照)")
    print("-" * 40)
    
    total_snapshots = SystemHealthSnapshot.objects.count()
    print(f"   总健康快照数: {total_snapshots}")
    
    if total_snapshots > 0:
        latest_snapshot = SystemHealthSnapshot.objects.first()
        print(f"   最新快照时间: {latest_snapshot.created_at}")
        print(f"   系统状态: {latest_snapshot.overall_status}")
    print()
    
    # 5. 数据分析和建议
    print("📊 数据分析和建议")
    print("-" * 40)
    
    if active_users > 0 and today_commands == 0:
        print("⚠️  发现问题: 有活跃用户但没有指令记录")
        print("   可能原因:")
        print("   1. 用户只是访问了聊天室但没有发送指令")
        print("   2. 指令执行记录没有正确保存")
        print("   3. 统计数据收集功能未启用")
        print()
        print("🔧 建议解决方案:")
        print("   1. 检查指令处理逻辑是否正确保存执行记录")
        print("   2. 手动发送一些AI指令测试")
        print("   3. 检查统计数据收集是否启用")
    
    elif total_commands == 0:
        print("⚠️  发现问题: 数据库中没有任何指令执行记录")
        print("🔧 建议解决方案:")
        print("   1. 生成一些测试数据")
        print("   2. 检查指令处理流程")
        print("   3. 确认统计功能是否正常工作")
    
    else:
        print("✅ 数据状态正常")

def generate_test_data():
    """生成一些测试数据"""
    print("\n🚀 生成测试数据...")
    print("=" * 60)
    
    from django.utils import timezone
    import uuid
    
    # 生成一些指令执行记录
    test_commands = [
        {
            'user_id': 'liang.tang',
            'command_type': 'ai_query',
            'raw_input': '/ai 今天天气怎么样',
            'success': True,
            'processing_time': 1.2,
            'response_content': '今天天气晴朗，温度适宜。'
        },
        {
            'user_id': 'nicholas.tiongkh',
            'command_type': 'jira_query',
            'raw_input': '/jira SPCPM-103235',
            'success': True,
            'processing_time': 0.8,
            'response_content': 'JIRA任务详情...'
        },
        {
            'user_id': 'liang.tang',
            'command_type': 'schedule_management',
            'raw_input': '/ai 我的定时任务有哪些',
            'success': True,
            'processing_time': 0.5,
            'response_content': '您当前有3个定时任务...'
        }
    ]
    
    created_count = 0
    for cmd_data in test_commands:
        try:
            cmd = CommandExecutionRecord.objects.create(
                execution_id=uuid.uuid4(),
                user_id=cmd_data['user_id'],
                command_type=cmd_data['command_type'],
                raw_input=cmd_data['raw_input'],
                success=cmd_data['success'],
                processing_time=cmd_data['processing_time'],
                response_content=cmd_data['response_content'],
                response_length=len(cmd_data['response_content']),
                start_time=timezone.now(),
                end_time=timezone.now()
            )
            created_count += 1
            print(f"✅ 创建指令记录: {cmd.user_id} - {cmd.command_type}")
        except Exception as e:
            print(f"❌ 创建失败: {e}")
    
    print(f"\n🎉 成功创建 {created_count} 条测试数据")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'generate':
        generate_test_data()
    else:
        check_database_status()
        print("\n💡 提示: 运行 'python check_database_data.py generate' 可以生成测试数据")

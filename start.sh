#!/bin/bash

# 设置部署路径
export VUE_ADMIN_DIR="/data/chatbot-ar-fe"
export AUTODEPLOY_DIR="/data/chatbot-ar-be"

# 创建日志目录
mkdir -p /data/autodeploy_logs

# 检查并停止已有的Django服务（更安全的方式）
if pgrep -f "manage.py runserver.*8081" > /dev/null; then
    echo "Stopping existing Django service on port 8081..."
    pkill -f "manage.py runserver.*8081"
    sleep 2
fi

# 检查并停止已有的定时任务调度器
TASK_SCHEDULER_PID_FILE="task_scheduler.pid"
if [ -f "$TASK_SCHEDULER_PID_FILE" ]; then
    OLD_SCHEDULER_PID=$(cat "$TASK_SCHEDULER_PID_FILE")
    if ps -p $OLD_SCHEDULER_PID > /dev/null 2>&1; then
        echo "Stopping existing task scheduler (PID: $OLD_SCHEDULER_PID)..."
        kill -TERM $OLD_SCHEDULER_PID
        sleep 3
        
        # 检查是否成功停止
        if ps -p $OLD_SCHEDULER_PID > /dev/null 2>&1; then
            echo "Force killing task scheduler..."
            kill -KILL $OLD_SCHEDULER_PID
        fi
        echo "Task scheduler stopped"
    fi
    rm -f "$TASK_SCHEDULER_PID_FILE"
fi

# 激活虚拟环境
source venv/bin/activate

# 启动Django服务（直接输出到日志目录）
cd /data/chatbot-ar-be
nohup python -u manage.py runserver 0.0.0.0:8081 >/data/autodeploy_logs/djo.out 2>&1 &

# 等待Django服务启动
sleep 3

# 启动定时任务调度器（使用新的调度器）
echo "Starting task scheduler..."
./start_task_scheduler_daemon.sh &
SCHEDULER_PID=$!

# 保存调度器PID
echo $SCHEDULER_PID > "$TASK_SCHEDULER_PID_FILE"

# 等待调度器启动
sleep 2
if ps -p $SCHEDULER_PID > /dev/null 2>&1; then
    echo "Task scheduler started successfully (PID: $SCHEDULER_PID)"
else
    echo "Failed to start task scheduler"
fi

# 创建日志软链接（仅用于向后兼容）
ln -sf /data/autodeploy_logs/djo.out djo.out
ln -sf /data/chatbot-ar-be/logs/task_scheduler.log /data/autodeploy_logs/task_scheduler.log
ln -sf /data/chatbot-ar-be/logs/task_scheduler_daemon.log /data/autodeploy_logs/task_scheduler_daemon.log
ln -sf /data/chatbot-ar-be/logs/task_scheduler.log task_scheduler.log
ln -sf /data/autodeploy_logs/crontab.log crontab.log
ln -sf /data/autodeploy_logs/crontab_db_update.log crontab_db_update.log
ln -sf /data/autodeploy_logs/crontab_release_save.log crontab_release_save.log
ln -sf /data/autodeploy_logs/crontab_saveCalendarJiraReleaseList.log crontab_saveCalendarJiraReleaseList.log
ln -sf /data/autodeploy_logs/crontab_save_JIRA_Release_List_Details.log crontab_save_JIRA_Release_List_Details.log
ln -sf /data/autodeploy_logs/crontab_update_db_data.log crontab_update_db_data.log
ln -sf /data/autodeploy_logs/watchdog.log watchdog.log
ln -sf /data/autodeploy_logs/manage_monitor.log manage_monitor.log
ln -sf /data/autodeploy_logs/get_unreleased_versions.log get_unreleased_versions.log
ln -sf /data/autodeploy_logs/cronjob_auto_seatalk_router.log cronjob_auto_seatalk_router.log

# 设置日志目录权限
chmod -R 755 /data/autodeploy_logs

# 显示启动状态
echo ""
echo "🚀 ChatbotAR Services Started Successfully!"
echo "=" * 50
echo "📝 Django Service:"
echo "   - Log: /data/autodeploy_logs/djo.out"
echo "   - Status: ps aux | grep 'manage.py runserver'"
echo "   - View logs: tail -f /data/autodeploy_logs/djo.out"
echo ""
echo "⏰ Task Scheduler:"
echo "   - PID file: task_scheduler.pid"
echo "   - Main Log: /data/chatbot-ar-be/logs/task_scheduler.log"
echo "   - Daemon Log: /data/chatbot-ar-be/logs/task_scheduler_daemon.log"
echo "   - Status: ps aux | grep 'task_scheduler'"
echo "   - View logs: tail -f /data/chatbot-ar-be/logs/task_scheduler.log"
echo ""
echo "🛑 To stop all services:"
echo "   - Django: pkill -f 'manage.py runserver'"
echo "   - Scheduler: pkill -f 'start_task_scheduler_daemon'"
echo "   - Or use: kill \$(cat task_scheduler.pid)"
echo "=" * 50 
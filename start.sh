#!/bin/bash

# 设置部署路径
export VUE_ADMIN_DIR="/data/chatbot-ar-fe"
export AUTODEPLOY_DIR="/data/chatbot-ar-be"

# 停止已有的 Python 进程
killall python3 || true

# 激活虚拟环境
source venv/bin/activate

# 启动服务
cd /data/chatbot-ar-be
nohup python -u manage.py runserver 0.0.0.0:8081 >djo.out 2>&1 &

# 创建日志软链接
mkdir -p /data/autodeploy_logs
ln -sf /data/autodeploy_logs/djo.out djo.out
ln -sf /data/autodeploy_logs/crontab.log crontab.log
ln -sf /data/autodeploy_logs/crontab_db_update.log crontab_db_update.log
ln -sf /data/autodeploy_logs/crontab_release_save.log crontab_release_save.log
ln -sf /data/autodeploy_logs/crontab_saveCalendarJiraReleaseList.log crontab_saveCalendarJiraReleaseList.log
ln -sf /data/autodeploy_logs/crontab_save_JIRA_Release_List_Details.log crontab_save_JIRA_Release_List_Details.log
ln -sf /data/autodeploy_logs/crontab_update_db_data.log crontab_update_db_data.log
ln -sf /data/autodeploy_logs/watchdog.log watchdog.log
ln -sf /data/autodeploy_logs/manage_monitor.log manage_monitor.log
ln -sf /data/autodeploy_logs/get_unreleased_versions.log get_unreleased_versions.log
ln -sf /data/autodeploy_logs/cronjob_auto_seatalk_router.log cronjob_auto_seatalk_router.log

# 设置日志目录权限
chmod -R 755 /data/autodeploy_logs 
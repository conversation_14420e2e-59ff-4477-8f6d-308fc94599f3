.DS_Store
.vscode/settings.json

# 依赖目录
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# 本地环境文件
.env.local
.env.*.local

# 缓存文件
.cache/
.npm/
.yarn/

# 编译输出目录
/build/
/out/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 测试覆盖率目录
/coverage/

# Cypress 测试结果
/cypress/videos/
/cypress/screenshots/

# 类型检查文件
*.tsbuildinfo

# 临时文件
.temp
.tmp
temp/
tmp/

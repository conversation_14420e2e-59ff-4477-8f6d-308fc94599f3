#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试统计系统定时任务
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from app01.statistics.cron_jobs import (
    system_health_check,
    hourly_statistics_summary,
    daily_statistics_summary,
    weekly_statistics_summary,
    monthly_statistics_summary,
    cleanup_old_statistics_job
)

def test_all_jobs():
    """测试所有统计任务"""
    jobs = [
        ('系统健康检查', system_health_check),
        ('小时统计汇总', hourly_statistics_summary),
        ('日统计汇总', daily_statistics_summary),
        ('周统计汇总', weekly_statistics_summary),
        ('月统计汇总', monthly_statistics_summary),
        ('数据清理', cleanup_old_statistics_job),
    ]
    
    print("🚀 开始测试统计系统定时任务...")
    print("=" * 50)
    
    for job_name, job_func in jobs:
        print(f"\n📊 测试 {job_name}...")
        try:
            result = job_func()
            status = "✅ 成功" if result else "❌ 失败"
            print(f"   结果: {status}")
        except Exception as e:
            print(f"   结果: ❌ 异常 - {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")

def test_single_job(job_name):
    """测试单个任务"""
    job_map = {
        'health': ('系统健康检查', system_health_check),
        'hourly': ('小时统计汇总', hourly_statistics_summary),
        'daily': ('日统计汇总', daily_statistics_summary),
        'weekly': ('周统计汇总', weekly_statistics_summary),
        'monthly': ('月统计汇总', monthly_statistics_summary),
        'cleanup': ('数据清理', cleanup_old_statistics_job),
    }
    
    if job_name not in job_map:
        print(f"❌ 未知任务: {job_name}")
        print(f"可用任务: {', '.join(job_map.keys())}")
        return
    
    job_display_name, job_func = job_map[job_name]
    print(f"🚀 测试 {job_display_name}...")
    
    try:
        result = job_func()
        status = "✅ 成功" if result else "❌ 失败"
        print(f"结果: {status}")
    except Exception as e:
        print(f"结果: ❌ 异常 - {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        # 测试单个任务
        job_name = sys.argv[1]
        test_single_job(job_name)
    else:
        # 测试所有任务
        test_all_jobs()

#!/bin/bash

# 快速回滚脚本 - 用于紧急情况
# 简化版本，快速回滚到上一个提交并重启服务

set -e

PROJECT_DIR="/data/chatbot-ar-be"
LOG_FILE="/data/chatbot-ar-be/logs/quick_rollback.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "开始快速回滚..."

# 进入项目目录
cd "$PROJECT_DIR" || exit 1

# 检查Git状态
if [ -n "$(git status --porcelain)" ]; then
    log "警告: 发现未提交的更改，将丢失这些更改"
fi

# 获取当前提交信息
CURRENT_COMMIT=$(git rev-parse --short HEAD)
log "当前提交: $CURRENT_COMMIT"

# 执行回滚
log "执行 git reset --hard HEAD~1"
git reset --hard HEAD~1

# 清理未跟踪文件
git clean -fd

# 获取回滚后的提交信息
NEW_COMMIT=$(git rev-parse --short HEAD)
log "回滚到提交: $NEW_COMMIT"

# 重启服务
log "重启服务..."

# 停止服务
if [ -f "stop.sh" ]; then
    ./stop.sh
else
    pkill -f "python.*manage.py" || true
    pkill -f "gunicorn" || true
fi

sleep 3

# 启动服务
if [ -f "start.sh" ]; then
    ./start.sh
else
    log "错误: 找不到 start.sh"
    exit 1
fi

sleep 5

# 检查服务状态
if pgrep -f "python.*manage.py" > /dev/null || pgrep -f "gunicorn" > /dev/null; then
    log "服务启动成功"
    echo -e "${GREEN}快速回滚完成！${NC}"
    echo -e "${GREEN}从 $CURRENT_COMMIT 回滚到 $NEW_COMMIT${NC}"
else
    log "服务启动失败"
    echo -e "${RED}服务启动失败，请检查日志${NC}"
    exit 1
fi 
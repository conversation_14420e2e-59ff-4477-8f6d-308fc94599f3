#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接修复脚本
临时解决MySQL连接丢失问题
"""

import os
import re

def backup_settings():
    """备份原始settings.py"""
    settings_file = 'djangoProject/settings.py'
    backup_file = 'djangoProject/settings.py.backup'
    
    if not os.path.exists(backup_file):
        with open(settings_file, 'r') as src, open(backup_file, 'w') as dst:
            dst.write(src.read())
        print(f"✅ 已备份原始配置到: {backup_file}")

def fix_database_settings():
    """修复数据库连接设置"""
    settings_file = 'djangoProject/settings.py'
    
    # 读取当前配置
    with open(settings_file, 'r') as f:
        content = f.read()
    
    # 查找DATABASES配置
    databases_pattern = r"DATABASES\s*=\s*{[^}]*'default'\s*:\s*{[^}]*}[^}]*}"
    
    # 新的数据库配置
    new_db_config = """DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'chatbotcicd',
        'USER': 'root',
        'PASSWORD': '',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
            'autocommit': True,
            'connect_timeout': 10,
            'read_timeout': 120,      # 增加到2分钟
            'write_timeout': 120,     # 增加到2分钟
        },
        'CONN_MAX_AGE': 0,           # 禁用连接池，每次都新建连接
        'CONN_HEALTH_CHECKS': False, # 禁用健康检查
        'TEST': {
            'CHARSET': 'utf8mb4',
            'COLLATION': 'utf8mb4_unicode_ci',
            'MIGRATE': True,
            'MIRROR': None,
            'NAME': None,
        },
        'ATOMIC_REQUESTS': False,
        'AUTOCOMMIT': True,
        'TIME_ZONE': None,
    }
}"""

    # 替换配置
    if re.search(databases_pattern, content, re.MULTILINE | re.DOTALL):
        new_content = re.sub(databases_pattern, new_db_config, content, flags=re.MULTILINE | re.DOTALL)
    else:
        print("❌ 未找到DATABASES配置，请手动修改")
        return False
    
    # 写入新配置
    with open(settings_file, 'w') as f:
        f.write(new_content)
    
    print("✅ 数据库配置已修复:")
    print("  - CONN_MAX_AGE: 300 → 0 (禁用连接池)")
    print("  - read_timeout: 30 → 120 (增加读取超时)")
    print("  - write_timeout: 30 → 120 (增加写入超时)")
    print("  - CONN_HEALTH_CHECKS: True → False (禁用健康检查)")
    
    return True

def test_connection():
    """测试数据库连接"""
    import django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
    django.setup()
    
    from django.db import connection
    try:
        cursor = connection.cursor()
        cursor.execute('SELECT 1')
        result = cursor.fetchone()
        print(f"✅ 数据库连接测试成功: {result}")
        
        # 显示新的配置
        config = connection.settings_dict
        print(f"📊 新的连接配置:")
        print(f"  - CONN_MAX_AGE: {config.get('CONN_MAX_AGE')}")
        print(f"  - read_timeout: {config['OPTIONS'].get('read_timeout')}")
        print(f"  - write_timeout: {config['OPTIONS'].get('write_timeout')}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def main():
    print("🔧 开始修复数据库连接配置...")
    
    # 1. 备份原始配置
    backup_settings()
    
    # 2. 修复数据库设置
    if fix_database_settings():
        print("\n🧪 测试新的数据库配置...")
        if test_connection():
            print("\n🎉 数据库连接修复完成！")
            print("\n📋 后续步骤:")
            print("1. 重启Django应用服务")
            print("2. 监控是否还有连接丢失错误")
            print("3. 如果问题解决，可以保留此配置")
            print("4. 如果需要回滚，运行: cp djangoProject/settings.py.backup djangoProject/settings.py")
        else:
            print("\n❌ 修复后仍有问题，请检查MySQL服务器状态")
    else:
        print("❌ 配置修复失败")

if __name__ == "__main__":
    main() 
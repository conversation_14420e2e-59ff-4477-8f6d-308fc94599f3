{"name": "my-vue-app", "version": "1.0.0", "author": {"name": "weibin.fang", "email": "<EMAIL>", "url": "https://github.com/weibinfang"}, "scripts": {"lint": "eslint \"src/**/*.{js,vue}\"", "serve": "cross-env NODE_ENV=development vite", "build": "cross-env NODE_ENV=production vite build", "preview": "cross-env vite preview", "build:preview": "vite build --mode production && vite preview", "dev:mock": "cross-env USE_MOCK=true vite", "build:mock": "cross-env USE_CHUNK_MOCK=true vite build && vite preview", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "deploy-gh": "GH=1 bash deploy-gh.sh"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "@icon-park/vue-next": "^1.4.2", "@soerenmartius/vue3-clipboard": "^0.1.2", "@types/echarts": "^4.9.22", "axios": "^0.21.1", "circular-json": "^0.5.9", "echarts": "^5.5.1", "element-plus": "^2.3.14", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "qs": "^6.10.1", "screenfull": "^5.1.0", "sortable.js": "^0.3.0", "sortablejs": "^1.15.0", "typescript": "^5.0.4", "vue": "^3.2.33", "vue-i18n": "^9.1.10", "vue-router": "^4.0.15", "vue-svgicon": "^3.3.2", "vue3-count-to": "^1.0.11", "vuedraggable": "^4.1.0", "vuex": "^4.0.2"}, "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@intlify/vite-plugin-vue-i18n": "^3.4.0", "@types/node": "^16.7.1", "@types/sortablejs": "^1.15.6", "@vitejs/plugin-legacy": "^1.5.3", "@vitejs/plugin-vue": "^1.9.2", "@vue/compiler-sfc": "^3.2.11", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "^10.3.1", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.15.0", "faker": "^6.6.6", "lint-staged": "^11.1.1", "naive-ui": "^2.34.3", "postcss": "^8.3.6", "prettier": "^2.3.2", "sass": "^1.37.0", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.4.0", "stylelint-config-standard": "^22.0.0", "svg-sprite-loader": "^6.0.9", "terser": "^5.31.3", "unplugin-auto-import": "^0.7.2", "unplugin-icons": "^0.14.3", "unplugin-vue-components": "^0.19.5", "vfonts": "^0.0.3", "vite": "5", "vite-plugin-babel-import": "^2.0.5", "vite-plugin-element-plus": "^0.0.12", "vite-plugin-mock": "^2.9.4", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-style-import": "^1.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/weibinfang/vue3-admin-element-template.git"}, "license": "MIT", "bugs": {"url": "https://github.com/weibinfang/vue3-admin-element-template/issues"}, "homepage": "https://github.com/weibinfang/vue3-admin-element-template", "engines": {"node": "^12 || >=14"}, "vite": {"optimizeDeps": {"include": ["@element-plus/icons-vue", "@icon-park/vue-next", "@icon-park/vue-next/es/all", "@soerenmartius/vue3-clipboard", "axios", "circular-json", "echarts/charts", "echarts/components", "echarts/core", "echarts/renderers", "element-plus", "element-plus/es", "element-plus/es/components/avatar/style/css", "element-plus/es/components/avatar/style/index", "element-plus/es/components/backtop/style/css", "element-plus/es/components/backtop/style/index", "element-plus/es/components/breadcrumb-item/style/css", "element-plus/es/components/breadcrumb-item/style/index", "element-plus/es/components/breadcrumb/style/css", "element-plus/es/components/breadcrumb/style/index", "element-plus/es/components/button-group/style/css", "element-plus/es/components/button-group/style/index", "element-plus/es/components/button/style/css", "element-plus/es/components/button/style/index", "element-plus/es/components/calendar/style/index", "element-plus/es/components/card/style/css", "element-plus/es/components/card/style/index", "element-plus/es/components/cascader/style/index", "element-plus/es/components/checkbox-button/style/css", "element-plus/es/components/checkbox-button/style/index", "element-plus/es/components/checkbox-group/style/css", "element-plus/es/components/checkbox-group/style/index", "element-plus/es/components/checkbox/style/css", "element-plus/es/components/checkbox/style/index", "element-plus/es/components/col/style/css", "element-plus/es/components/col/style/index", "element-plus/es/components/config-provider/style/css", "element-plus/es/components/config-provider/style/index", "element-plus/es/components/container/style/css", "element-plus/es/components/container/style/index", "element-plus/es/components/date-picker/style/css", "element-plus/es/components/date-picker/style/index", "element-plus/es/components/descriptions-item/style/css", "element-plus/es/components/descriptions-item/style/index", "element-plus/es/components/descriptions/style/css", "element-plus/es/components/descriptions/style/index", "element-plus/es/components/dialog/style/index", "element-plus/es/components/divider/style/css", "element-plus/es/components/divider/style/index", "element-plus/es/components/drawer/style/css", "element-plus/es/components/drawer/style/index", "element-plus/es/components/dropdown-item/style/css", "element-plus/es/components/dropdown-item/style/index", "element-plus/es/components/dropdown-menu/style/css", "element-plus/es/components/dropdown-menu/style/index", "element-plus/es/components/dropdown/style/css", "element-plus/es/components/dropdown/style/index", "element-plus/es/components/empty/style/index", "element-plus/es/components/form-item/style/css", "element-plus/es/components/form-item/style/index", "element-plus/es/components/form/style/css", "element-plus/es/components/form/style/index", "element-plus/es/components/header/style/css", "element-plus/es/components/header/style/index", "element-plus/es/components/icon/style/css", "element-plus/es/components/icon/style/index", "element-plus/es/components/image/style/index", "element-plus/es/components/input/style/css", "element-plus/es/components/input/style/index", "element-plus/es/components/link/style/index", "element-plus/es/components/loading/style/index", "element-plus/es/components/main/style/css", "element-plus/es/components/main/style/index", "element-plus/es/components/menu-item/style/css", "element-plus/es/components/menu-item/style/index", "element-plus/es/components/menu/style/css", "element-plus/es/components/menu/style/index", "element-plus/es/components/message/style/css", "element-plus/es/components/option/style/css", "element-plus/es/components/option/style/index", "element-plus/es/components/pagination/style/css", "element-plus/es/components/pagination/style/index", "element-plus/es/components/popconfirm/style/index", "element-plus/es/components/popover/style/css", "element-plus/es/components/popover/style/index", "element-plus/es/components/progress/style/css", "element-plus/es/components/progress/style/index", "element-plus/es/components/radio-button/style/css", "element-plus/es/components/radio-button/style/index", "element-plus/es/components/radio-group/style/css", "element-plus/es/components/radio-group/style/index", "element-plus/es/components/radio/style/css", "element-plus/es/components/radio/style/index", "element-plus/es/components/result/style/index", "element-plus/es/components/row/style/css", "element-plus/es/components/row/style/index", "element-plus/es/components/scrollbar/style/css", "element-plus/es/components/scrollbar/style/index", "element-plus/es/components/select/style/css", "element-plus/es/components/select/style/index", "element-plus/es/components/statistic/style/index", "element-plus/es/components/step/style/index", "element-plus/es/components/steps/style/index", "element-plus/es/components/sub-menu/style/css", "element-plus/es/components/sub-menu/style/index", "element-plus/es/components/switch/style/css", "element-plus/es/components/switch/style/index", "element-plus/es/components/tab-pane/style/css", "element-plus/es/components/tab-pane/style/index", "element-plus/es/components/table-column/style/css", "element-plus/es/components/table-column/style/index", "element-plus/es/components/table/style/css", "element-plus/es/components/table/style/index", "element-plus/es/components/tabs/style/css", "element-plus/es/components/tabs/style/index", "element-plus/es/components/tag/style/index", "element-plus/es/components/text/style/index", "element-plus/es/components/time-picker/style/index", "element-plus/es/components/tooltip/style/index", "element-plus/es/components/tree/style/index", "element-plus/es/locale/lang/en", "element-plus/es/locale/lang/zh-cn", "js-cookie", "lodash/debounce", "nprogress", "qs", "screenfull", "sortablejs", "vue", "vue-i18n", "vue-router", "vue-svgicon", "vue3-count-to", "vuedraggable", "vuex"]}}}
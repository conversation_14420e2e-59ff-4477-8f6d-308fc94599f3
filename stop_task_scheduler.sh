#!/bin/bash

# ChatbotAR 定时任务调度器停止脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/task_scheduler.pid"

echo "🛑 停止 ChatbotAR 定时任务调度器..."

if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    
    if ps -p $PID > /dev/null 2>&1; then
        echo "📍 找到调度器进程 (PID: $PID)"
        echo "🔄 正在停止进程..."
        
        # 发送TERM信号
        kill -TERM $PID
        
        # 等待进程停止
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "✅ 调度器已成功停止"
                rm -f "$PID_FILE"
                exit 0
            fi
            echo "⏳ 等待进程停止... ($i/10)"
            sleep 1
        done
        
        # 如果还在运行，强制终止
        if ps -p $PID > /dev/null 2>&1; then
            echo "❌ 进程未响应TERM信号，强制终止..."
            kill -KILL $PID
            sleep 1
            
            if ps -p $PID > /dev/null 2>&1; then
                echo "❌ 无法停止进程"
                exit 1
            else
                echo "✅ 进程已被强制终止"
                rm -f "$PID_FILE"
            fi
        fi
    else
        echo "❌ 进程 $PID 不存在，清理PID文件"
        rm -f "$PID_FILE"
    fi
else
    echo "❌ 未找到PID文件，可能调度器未运行"
    
    # 尝试查找运行中的调度器进程
    RUNNING_PIDS=$(ps aux | grep "run_scheduled_tasks" | grep -v grep | awk '{print $2}')
    
    if [ ! -z "$RUNNING_PIDS" ]; then
        echo "🔍 发现运行中的调度器进程:"
        ps aux | grep "run_scheduled_tasks" | grep -v grep
        echo "💡 手动停止: kill $RUNNING_PIDS"
    else
        echo "ℹ️  没有发现运行中的调度器进程"
    fi
fi 
#!/bin/bash

# 任务调度器守护进程启动脚本
# 启动任务调度器并在后台运行

LOG_FILE="logs/task_scheduler_daemon.log"
TIMESTAMP=$(date "+%Y-%m-%d %H:%M:%S")
DEBUG_GROUP_ID="NzQzMzAxODcyMjAy"  # 调试群组ID
HOSTNAME=$(hostname)

# 确保日志目录存在
mkdir -p logs

echo "[$TIMESTAMP] 🚀 启动任务调度器守护进程..." >> $LOG_FILE

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    echo "[$TIMESTAMP] 🔄 激活虚拟环境..." >> $LOG_FILE
    source venv/bin/activate
fi

# 使用项目环境中的Python
PYTHON_CMD="python3"
if [ -f "venv/bin/python" ]; then
    PYTHON_CMD="venv/bin/python"
    echo "[$TIMESTAMP] 🐍 使用虚拟环境Python: $PYTHON_CMD" >> $LOG_FILE
fi

# 发送启动通知到调试群
send_debug_message() {
    local message="$1"
    $PYTHON_CMD -c "import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings'); import django; django.setup(); from app01.seatalk_group_manager import send_message_to_group; send_message_to_group('$DEBUG_GROUP_ID', '$message')" >> $LOG_FILE 2>&1
}

# 检查Python环境
which $PYTHON_CMD >> $LOG_FILE 2>&1
PYTHON_STATUS=$?
if [ $PYTHON_STATUS -ne 0 ]; then
    ERROR_MSG="[$TIMESTAMP] ❌ 未找到Python，请确保Python已安装"
    echo "$ERROR_MSG" >> $LOG_FILE
    # 不使用send_debug_message，因为Python可能不可用
    exit 1
fi

# 检查Django设置
echo "[$TIMESTAMP] 🔍 检查Django环境..." >> $LOG_FILE
$PYTHON_CMD -c "import django; print(f'Django版本: {django.__version__}')" >> $LOG_FILE 2>&1
DJANGO_STATUS=$?
if [ $DJANGO_STATUS -ne 0 ]; then
    ERROR_MSG="[$TIMESTAMP] ❌ Django环境检查失败，请确保依赖已安装"
    echo "$ERROR_MSG" >> $LOG_FILE
    exit 1
fi

# 检查数据库连接
echo "[$TIMESTAMP] 🔍 检查数据库连接..." >> $LOG_FILE
$PYTHON_CMD -c "import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings'); import django; django.setup(); from django.db import connection; cursor = connection.cursor(); cursor.execute('SELECT 1'); print('数据库连接正常')" >> $LOG_FILE 2>&1
DB_STATUS=$?
if [ $DB_STATUS -ne 0 ]; then
    WARNING_MSG="[$TIMESTAMP] ⚠️ 数据库连接检查失败，但仍将继续启动"
    echo "$WARNING_MSG" >> $LOG_FILE
fi

# 停止现有进程
echo "[$TIMESTAMP] 🛑 尝试停止现有任务调度器..." >> $LOG_FILE
./stop_task_scheduler.sh >> $LOG_FILE 2>&1

# 启动任务调度器
echo "[$TIMESTAMP] 🚀 启动新的任务调度器..." >> $LOG_FILE
nohup $PYTHON_CMD start_scheduler.py > logs/task_scheduler.log 2>&1 &
PID=$!

# 检查进程是否成功启动
sleep 2
if ps -p $PID > /dev/null; then
    SUCCESS_MSG="[$TIMESTAMP] ✅ 任务调度器守护进程已启动，PID: $PID"
    echo "$SUCCESS_MSG" >> $LOG_FILE
    echo $PID > .scheduler_pid
    
    # 监控日志中的错误
    sleep 5
    if [ -f "logs/task_scheduler.log" ]; then
        ERROR_COUNT=$(grep -c "ERROR" logs/task_scheduler.log 2>/dev/null || echo "0")
        if [ "$ERROR_COUNT" -gt 0 ]; then
            WARNING_MSG="[$TIMESTAMP] ⚠️ 启动后发现 $ERROR_COUNT 个错误，请检查日志"
            echo "$WARNING_MSG" >> $LOG_FILE
            ERRORS=$(grep "ERROR" logs/task_scheduler.log 2>/dev/null | tail -5)
            if [ "$DB_STATUS" -eq 0 ]; then
                send_debug_message "⚠️ 任务调度器启动警告\n⏰ 时间: $TIMESTAMP\n🖥️ 主机: $HOSTNAME\n\nPID: $PID\n\n$WARNING_MSG\n\n最近的错误:\n$ERRORS"
            fi
        else
            echo "[$TIMESTAMP] ✅ 启动检查完成，未发现明显错误" >> $LOG_FILE
            if [ "$DB_STATUS" -eq 0 ]; then
                send_debug_message "✅ 任务调度器启动成功\n⏰ 时间: $TIMESTAMP\n🖥️ 主机: $HOSTNAME\n\nPID: $PID\n\n未发现明显错误，调度器正常运行中"
            fi
        fi
    else
        echo "[$TIMESTAMP] ⚠️ 日志文件不存在，无法检查错误" >> $LOG_FILE
    fi
else
    ERROR_MSG="[$TIMESTAMP] ❌ 任务调度器启动失败"
    echo "$ERROR_MSG" >> $LOG_FILE
    exit 1
fi

echo "[$TIMESTAMP] 📝 任务调度器守护进程启动完成" >> $LOG_FILE
echo "[$TIMESTAMP] 📊 日志文件: logs/task_scheduler.log" >> $LOG_FILE 
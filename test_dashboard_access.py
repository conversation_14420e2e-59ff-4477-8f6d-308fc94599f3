#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试统计监控面板访问
"""

import os
import sys
import django
from django.test import Client
from django.urls import reverse

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

def test_dashboard_access():
    """测试监控面板访问"""
    print("🌐 测试统计监控面板访问...")
    
    try:
        # 创建测试客户端
        client = Client()
        
        # 测试统计面板页面
        response = client.get('/api/statistics/dashboard/')
        
        print(f"📊 统计面板访问测试:")
        print(f"   URL: /api/statistics/dashboard/")
        print(f"   状态码: {response.status_code}")
        print(f"   内容类型: {response.get('Content-Type', 'N/A')}")
        print(f"   响应长度: {len(response.content)} bytes")
        
        if response.status_code == 200:
            print("   ✅ 页面访问成功")
            
            # 检查页面内容
            content = response.content.decode('utf-8')
            if 'ChatBot AutoRelease 统计监控面板' in content:
                print("   ✅ 页面内容正确")
            else:
                print("   ⚠️  页面内容可能有问题")
                
        else:
            print(f"   ❌ 页面访问失败: HTTP {response.status_code}")
            
        # 测试API接口
        api_endpoints = [
            '/api/statistics/realtime/dashboard/',
            '/api/statistics/realtime/command-trends/',
            '/api/statistics/reports/daily/',
        ]
        
        print(f"\n🔌 API接口访问测试:")
        for endpoint in api_endpoints:
            try:
                api_response = client.get(endpoint)
                status = "✅ 正常" if api_response.status_code == 200 else f"❌ {api_response.status_code}"
                print(f"   {endpoint}: {status}")
            except Exception as e:
                print(f"   {endpoint}: ❌ 错误 - {e}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试统计监控面板访问")
    print("=" * 60)
    
    success = test_dashboard_access()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试通过！监控面板可以正常访问")
        print("\n📋 访问说明:")
        print("1. 启动Django服务器: python manage.py runserver")
        print("2. 访问监控面板: http://localhost:8000/api/statistics/dashboard/")
        print("3. 生产环境访问: http://your-domain/api/statistics/dashboard/")
    else:
        print("⚠️  测试失败，请检查配置")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())

---
alwaysApply: true
---
patterns:
  # 禁止本地 runserver
  - pattern: "manage.py runserver"
    message: "请勿在本地运行 Django 服务"
    severity: error

  # 禁止使用 sqlite3、mysql 等连接本地数据库

  - pattern: "MySQLdb.connect"
    message: "不要连接本地 MySQL 数据库"
    severity: warning

  # 禁止对数据库进行写操作（基本场景）
  - pattern: ".save()"
    message: "本地环境不要写入数据库"
    severity: warning

  - pattern: ".delete()"
    message: "本地环境不要删除数据库数据"
    severity: warning
---
description:
globs:
alwaysApply: false
---
# SeaTalk通知系统指南

## 系统概述
SeaTalk是内部通讯工具，用于发送自动化通知消息给不同的用户组。通知系统主要由以下部分组成：
- 高优先级问题通知
- JIRA工单状态通知
- 错误日志通知

## 重要函数
- `test_for_seatalk_bot` - 用于发送消息到SeaTalk群组
- `send_jira_issues_to_seatalk` - 查询JIRA工单并发送到指定SeaTalk群组

## 群组ID配置
- 机器人调试群: `NzQzMzAxODcyMjAy`
- Chat&Chatbot管理群: 在代码中定义为 `chat_chatbot_mgmt_group_id`
- Chatbot部署平台消息通知群: `OTUyNDEwMzE2Mjk0`

## 通知格式规范
通知消息格式包含:
- 问题优先级分组（Highest/High）
- 问题详细信息（标题、链接、创建时间、状态等）
- @提及相关人员
- JIRA查询链接

## 错误处理
系统会捕获所有异常并将错误信息推送到调试群：
```python
except Exception as e:
    print(f"发送JIRA通知失败: {str(e)}")
    test_for_seatalk_bot(f"发送JIRA通知失败: {str(e)}", False, "NzQzMzAxODcyMjAy")
    return False
```

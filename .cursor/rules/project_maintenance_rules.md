# Chatbot-AR-BE 项目维护规则

## 🔍 函数分析规则

### 废弃函数标识
- 使用 `DEPRECATED` 标记废弃函数
- 添加 `warnings.warn()` 运行时警告
- 记录废弃原因和替代方案

### 函数删除流程
1. 标记为废弃 → 2. 监控1-2周 → 3. 确认无使用 → 4. 删除代码

## 📊 代码质量指标

### 文件大小限制
- 单个文件不超过2000行
- 单个函数不超过100行
- views.py需要拆分 (当前5,345行)

### 函数调用分析
- URL路由函数：31个
- Crontab定时任务：20个  
- 内部调用函数：106个
- 废弃函数：5个 (已标记)

### 待修复问题
- 缺失函数：MRnotdeal_data, update_service_live
- 废弃函数：timeformat (优先删除)

## 🔧 重构优先级

### 高优先级 (立即执行)
- [ ] 修复缺失的crontab函数
- [ ] 删除已确认废弃的函数
- [ ] 拆分过大的函数 (>200行)

### 中优先级 (1-2个月)
- [ ] 文件拆分：views.py → 多个模块文件
- [ ] 提取重复代码为公共函数
- [ ] 统一错误处理机制

### 低优先级 (长期规划)
- [ ] 引入Service层架构
- [ ] 添加单元测试
- [ ] 完善API文档

## 📈 监控规则

### 性能监控
- 高频crontab任务 (每分钟执行) 需要监控执行时间
- API响应时间超过5秒需要告警
- 数据库查询优化 (避免N+1问题)

### 错误监控  
- 废弃函数调用告警
- 外部API调用失败率
- Crontab任务执行失败

## 🚀 部署规则

### 代码变更检查
- 大型重构需要分支开发
- 废弃函数删除需要备份
- 数据库迁移需要回滚方案

### 测试要求
- 关键API需要集成测试
- 定时任务需要单元测试  
- 废弃函数删除前需要确认监控无告警 
---
description:
globs:
alwaysApply: false
---
# JIRA错误处理指南

## 常见JIRA API错误

### HTTP 503错误 (Service Unavailable)
- 表示JIRA服务器暂时无法处理请求
- 常见于服务器维护或过载情况
- 响应头中通常包含"Retry-After"参数，指示应该等待多少秒后重试

### 处理策略
1. 实现指数退避重试机制
2. 捕获异常并记录详细日志
3. 通过SeaTalk等渠道通知管理员

## 相关代码
JIRA通知发送失败处理位于[app01/views.py](mdc:chatbot-ar-be/app01/views.py):
```python
try:
    # JIRA API调用
    # ...
except Exception as e:
    print(f"发送JIRA通知失败: {str(e)}")
    # 如果发送失败，把错误日志推送到机器人调试群
    test_for_seatalk_bot(f"发送JIRA通知失败: {str(e)}", False, "NzQzMzAxODcyMjAy")
    return False
```

## 改进建议
1. 添加重试机制
2. 针对不同错误码采取不同处理策略
3. 完善日志记录，包括请求参数和完整响应信息

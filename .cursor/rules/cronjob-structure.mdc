---
description:
globs:
alwaysApply: false
---
# 定时任务(<PERSON>ron<PERSON><PERSON>)结构指南

## 定时任务命名规范
- 所有定时任务函数必须以`cronjob_`前缀命名
- 命名应清晰反映任务功能，如`cronjob_SPS_live_bug_of_chatbot_reminder`

## 主要定时任务
系统包含多个定时任务，主要用于：
1. 监控JIRA工单状态
2. 发送通知到不同SeaTalk群组
3. 执行自动化操作

### 重要定时任务示例

#### Bug提醒类
- `cronjob_SPS_live_bug_of_chatbot_reminder` - Chatbot产品线Live Bug提醒
- `cronjob_new_SPS_live_bug_of_chatbot_mirror` - 新SPS Live Bug监控
- `cronjob_SPCB_live_bug_reminder` - SPCB Live Bug提醒
- `cronjob_SPS_live_bug_of_chat_reminder` - Chat产品线Live Bug提醒

#### 其他自动化任务
- `cronjob_auto_seatalk_router` - 自动SeaTalk路由
- `cronjob_chatbot_adhoc_reminder` - Chatbot临时版本提醒

## 定时任务结构模板
```python
def cronjob_task_name():
    """
    任务说明注释
    """
    jql = '...'  # JIRA查询语句
    seatalk_group_id = "..."  # 目标群组ID
    description = "..."  # 通知描述
    
    send_jira_issues_to_seatalk(jql, seatalk_group_id, description)
```

## 相关代码
所有定时任务定义在[app01/views.py](mdc:chatbot-ar-be/app01/views.py)文件中

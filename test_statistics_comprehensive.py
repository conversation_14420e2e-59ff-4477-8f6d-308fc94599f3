#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ChatBot AutoRelease 统计系统全面测试脚本
包括功能测试、性能测试、数据准确性测试等
"""

import os
import sys
import time
import json
import threading
from datetime import datetime, timedelta
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
import django
django.setup()


class StatisticsSystemTester:
    """统计系统测试器"""
    
    def __init__(self):
        self.test_results = []
        self.performance_results = []
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始ChatBot AutoRelease统计系统全面测试")
        print("=" * 80)
        
        # 功能测试
        self.test_models()
        self.test_collectors()
        self.test_services()
        self.test_decorators()
        self.test_api_views()
        self.test_reports()
        self.test_cron_jobs()
        self.test_management_commands()
        
        # 性能测试
        self.test_performance()
        
        # 数据准确性测试
        self.test_data_accuracy()
        
        # 生成测试报告
        self.generate_test_report()
        
        return self.get_overall_result()
    
    def test_models(self):
        """测试数据模型"""
        print("\n📊 测试数据模型...")
        
        try:
            from app01.models import (
                BotAccessEvent, CommandExecutionRecord, 
                SystemPerformanceMetrics, CronJobExecutionMonitor,
                UserActivitySummary, SystemHealthSnapshot
            )
            
            # 测试模型字段和方法
            models_to_test = [
                BotAccessEvent, CommandExecutionRecord, 
                SystemPerformanceMetrics, CronJobExecutionMonitor,
                UserActivitySummary, SystemHealthSnapshot
            ]
            
            for model in models_to_test:
                # 检查模型字段
                fields = [f.name for f in model._meta.fields]
                assert len(fields) > 0, f"{model.__name__} 没有字段"
                
                # 检查Meta配置
                assert hasattr(model._meta, 'db_table'), f"{model.__name__} 缺少db_table配置"
                
                print(f"  ✅ {model.__name__} - {len(fields)} 个字段")
            
            self.test_results.append(("数据模型测试", True, "所有模型结构正常"))
            
        except Exception as e:
            self.test_results.append(("数据模型测试", False, str(e)))
            print(f"  ❌ 数据模型测试失败: {e}")
    
    def test_collectors(self):
        """测试数据收集器"""
        print("\n📥 测试数据收集器...")
        
        try:
            from app01.statistics.collectors import (
                bot_access_collector, command_execution_collector
            )
            
            # 测试访问事件收集器
            test_event = {
                'event_id': 'test-123',
                'event_type': 'user_enter_chatroom_with_bot',
                'timestamp': int(timezone.now().timestamp()),
                'event': {
                    'seatalk_id': 'test-user',
                    'email': '<EMAIL>'
                }
            }
            
            # 测试数据处理逻辑（不实际写入数据库）
            assert bot_access_collector.is_enabled(), "访问事件收集器未启用"
            assert command_execution_collector.is_enabled(), "指令执行收集器未启用"
            
            print("  ✅ 访问事件收集器正常")
            print("  ✅ 指令执行收集器正常")
            
            self.test_results.append(("数据收集器测试", True, "所有收集器正常"))
            
        except Exception as e:
            self.test_results.append(("数据收集器测试", False, str(e)))
            print(f"  ❌ 数据收集器测试失败: {e}")
    
    def test_services(self):
        """测试统计服务"""
        print("\n🔧 测试统计服务...")
        
        try:
            from app01.statistics.services import (
                realtime_stats_service,
                performance_analysis_service,
                cronjob_monitoring_service
            )
            
            # 测试服务状态
            services = [
                ("实时统计服务", realtime_stats_service),
                ("性能分析服务", performance_analysis_service),
                ("定时任务监控服务", cronjob_monitoring_service)
            ]
            
            for name, service in services:
                assert service.is_enabled(), f"{name}未启用"
                print(f"  ✅ {name}正常")
            
            self.test_results.append(("统计服务测试", True, "所有服务正常"))
            
        except Exception as e:
            self.test_results.append(("统计服务测试", False, str(e)))
            print(f"  ❌ 统计服务测试失败: {e}")
    
    def test_decorators(self):
        """测试装饰器"""
        print("\n🎯 测试装饰器...")
        
        try:
            from app01.statistics.decorators import (
                track_command_execution,
                track_cronjob_execution,
                create_command_tracker
            )
            
            # 测试装饰器应用
            @track_command_execution('test_command')
            def test_function():
                return "测试成功"
            
            @track_cronjob_execution('test_job')
            def test_cronjob():
                return "任务成功"
            
            # 测试上下文管理器
            tracker = create_command_tracker(
                user_id='test-user',
                command_type='test',
                raw_input='test input'
            )
            
            assert tracker is not None, "指令跟踪器创建失败"
            
            print("  ✅ 指令执行装饰器正常")
            print("  ✅ 定时任务装饰器正常")
            print("  ✅ 指令跟踪器正常")
            
            self.test_results.append(("装饰器测试", True, "所有装饰器正常"))
            
        except Exception as e:
            self.test_results.append(("装饰器测试", False, str(e)))
            print(f"  ❌ 装饰器测试失败: {e}")
    
    def test_api_views(self):
        """测试API视图"""
        print("\n🌐 测试API视图...")
        
        try:
            from app01.statistics.views import (
                realtime_dashboard,
                command_trends,
                performance_metrics,
                daily_report,
                statistics_dashboard_page
            )
            
            # 检查视图函数是否可调用
            views_to_test = [
                ("实时监控面板", realtime_dashboard),
                ("指令趋势", command_trends),
                ("性能指标", performance_metrics),
                ("日报", daily_report),
                ("统计面板页面", statistics_dashboard_page)
            ]
            
            for name, view_func in views_to_test:
                assert callable(view_func), f"{name}视图不可调用"
                print(f"  ✅ {name}视图正常")
            
            self.test_results.append(("API视图测试", True, "所有视图正常"))
            
        except Exception as e:
            self.test_results.append(("API视图测试", False, str(e)))
            print(f"  ❌ API视图测试失败: {e}")
    
    def test_reports(self):
        """测试报表生成"""
        print("\n📋 测试报表生成...")
        
        try:
            from app01.statistics.reports import (
                daily_report_generator,
                weekly_report_generator,
                alert_report_generator
            )
            
            # 测试报表生成器
            generators = [
                ("日报生成器", daily_report_generator),
                ("周报生成器", weekly_report_generator),
                ("告警报告生成器", alert_report_generator)
            ]
            
            for name, generator in generators:
                assert generator.is_enabled(), f"{name}未启用"
                print(f"  ✅ {name}正常")
            
            # 测试HTML报表生成
            html_report = daily_report_generator.generate_daily_report_html()
            assert len(html_report) > 0, "HTML报表生成失败"
            print("  ✅ HTML报表生成正常")
            
            self.test_results.append(("报表生成测试", True, "所有报表生成器正常"))
            
        except Exception as e:
            self.test_results.append(("报表生成测试", False, str(e)))
            print(f"  ❌ 报表生成测试失败: {e}")
    
    def test_cron_jobs(self):
        """测试定时任务"""
        print("\n⏰ 测试定时任务...")
        
        try:
            from app01.statistics.cron_jobs import (
                get_available_jobs,
                get_job_info,
                run_statistics_job
            )
            
            # 测试任务列表
            jobs = get_available_jobs()
            assert len(jobs) > 0, "没有可用的定时任务"
            print(f"  ✅ 发现 {len(jobs)} 个定时任务")
            
            # 测试任务信息
            job_info = get_job_info()
            assert len(job_info) == len(jobs), "任务信息不完整"
            print("  ✅ 任务信息完整")
            
            # 测试任务执行逻辑（不实际执行）
            for job in jobs:
                assert job in job_info, f"任务 {job} 缺少信息"
                print(f"  ✅ 任务 {job} 配置正常")
            
            self.test_results.append(("定时任务测试", True, f"{len(jobs)}个任务配置正常"))
            
        except Exception as e:
            self.test_results.append(("定时任务测试", False, str(e)))
            print(f"  ❌ 定时任务测试失败: {e}")
    
    def test_management_commands(self):
        """测试管理命令"""
        print("\n💻 测试管理命令...")
        
        try:
            from app01.statistics.management.commands.generate_statistics import Command as GenerateCommand
            from app01.statistics.management.commands.run_statistics_job import Command as RunJobCommand
            
            # 测试命令类
            commands = [
                ("生成统计命令", GenerateCommand),
                ("运行任务命令", RunJobCommand)
            ]
            
            for name, command_class in commands:
                command = command_class()
                assert hasattr(command, 'handle'), f"{name}缺少handle方法"
                print(f"  ✅ {name}正常")
            
            self.test_results.append(("管理命令测试", True, "所有管理命令正常"))
            
        except Exception as e:
            self.test_results.append(("管理命令测试", False, str(e)))
            print(f"  ❌ 管理命令测试失败: {e}")
    
    def test_performance(self):
        """性能测试"""
        print("\n⚡ 进行性能测试...")
        
        try:
            from app01.statistics.collectors import bot_access_collector
            
            # 测试数据收集性能
            start_time = time.time()
            
            # 模拟100个访问事件
            for i in range(100):
                test_event = {
                    'event_id': f'perf-test-{i}',
                    'event_type': 'user_enter_chatroom_with_bot',
                    'timestamp': int(timezone.now().timestamp()),
                    'event': {
                        'seatalk_id': f'test-user-{i}',
                        'email': f'test{i}@example.com'
                    }
                }
                # 只测试处理逻辑，不实际写入数据库
                
            processing_time = time.time() - start_time
            
            print(f"  ✅ 100个事件处理时间: {processing_time:.3f}秒")
            print(f"  ✅ 平均每个事件: {processing_time/100*1000:.1f}毫秒")
            
            self.performance_results.append({
                'test': '数据收集性能',
                'events': 100,
                'time': processing_time,
                'avg_per_event': processing_time/100*1000
            })
            
            self.test_results.append(("性能测试", True, f"100个事件处理耗时{processing_time:.3f}秒"))
            
        except Exception as e:
            self.test_results.append(("性能测试", False, str(e)))
            print(f"  ❌ 性能测试失败: {e}")
    
    def test_data_accuracy(self):
        """数据准确性测试"""
        print("\n🎯 进行数据准确性测试...")
        
        try:
            from app01.statistics.decorators import create_command_tracker
            
            # 测试指令跟踪的数据完整性
            test_data = {
                'user_id': 'accuracy-test-user',
                'command_type': 'test_command',
                'raw_input': 'test input for accuracy',
                'user_email': '<EMAIL>'
            }
            
            tracker = create_command_tracker(**test_data)
            
            # 验证数据完整性
            assert tracker is not None, "跟踪器创建失败"
            
            print("  ✅ 指令跟踪数据完整性正常")
            
            # 测试时间戳准确性
            now = timezone.now()
            timestamp = int(now.timestamp())
            converted_time = datetime.fromtimestamp(timestamp)

            # 转换为带时区的时间
            converted_time = timezone.make_aware(converted_time)

            time_diff = abs((now - converted_time).total_seconds())
            assert time_diff < 1, f"时间戳转换误差过大: {time_diff}秒"
            
            print("  ✅ 时间戳转换准确性正常")
            
            self.test_results.append(("数据准确性测试", True, "数据完整性和准确性正常"))
            
        except Exception as e:
            self.test_results.append(("数据准确性测试", False, str(e)))
            print(f"  ❌ 数据准确性测试失败: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for _, success, _ in self.test_results if success)
        failed_tests = total_tests - passed_tests
        
        # 生成报告
        report = {
            'test_time': timezone.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'success_rate': round((passed_tests / total_tests) * 100, 1) if total_tests > 0 else 0
            },
            'test_results': [
                {
                    'test_name': name,
                    'success': success,
                    'message': message
                }
                for name, success, message in self.test_results
            ],
            'performance_results': self.performance_results
        }
        
        # 保存报告
        with open('statistics_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ 测试报告已保存: statistics_test_report.json")
        
        return report
    
    def get_overall_result(self):
        """获取总体测试结果"""
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print("\n" + "=" * 80)
        print("📊 测试结果汇总")
        print("=" * 80)
        
        for test_name, success, message in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}: {message}")
        
        print(f"\n📈 总体结果: {passed}/{total} 通过")
        print(f"📈 成功率: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！统计系统可以部署。")
            print("\n📋 部署建议:")
            print("1. 运行数据库迁移: python manage.py migrate")
            print("2. 配置统计系统设置")
            print("3. 根据需要配置crontab任务")
            print("4. 访问监控面板: /api/statistics/dashboard/")
            return True
        else:
            print(f"\n⚠️  {total - passed} 个测试失败，请修复后再部署。")
            return False


def main():
    """主函数"""
    tester = StatisticsSystemTester()
    success = tester.run_all_tests()
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())

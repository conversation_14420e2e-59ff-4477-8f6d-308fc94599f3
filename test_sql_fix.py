#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试SQL修复
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

def test_command_trends():
    """测试指令趋势查询"""
    print("🔍 测试指令趋势查询")
    print("=" * 50)
    
    try:
        from app01.statistics.services import RealtimeStatsService
        
        service = RealtimeStatsService()
        result = service.get_command_trends(days=7)
        
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            return False
        else:
            print("✅ 成功获取指令趋势数据")
            print(f"📊 日期数量: {len(result.get('dates', []))}")
            print(f"📊 总指令数: {sum(result.get('total', []))}")
            print(f"📊 指令类型: {len(result.get('command_types', []))}")
            
            # 显示一些示例数据
            if result.get('dates'):
                print(f"📅 日期范围: {result['dates'][0]} 到 {result['dates'][-1]}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_activity():
    """测试用户活动查询"""
    print("\n🔍 测试用户活动查询")
    print("=" * 50)
    
    try:
        from app01.statistics.services import RealtimeStatsService
        
        service = RealtimeStatsService()
        result = service.get_user_activity_stats(days=7)
        
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            return False
        else:
            print("✅ 成功获取用户活动数据")
            print(f"📊 总用户数: {result.get('total_users', 'N/A')}")
            print(f"📊 总指令数: {result.get('total_commands', 'N/A')}")
            print(f"📊 活跃用户数据点: {len(result.get('active_users_data', []))}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_data():
    """测试仪表板数据"""
    print("\n🔍 测试仪表板数据")
    print("=" * 50)
    
    try:
        from app01.statistics.services import RealtimeStatsService
        
        service = RealtimeStatsService()
        result = service.get_realtime_dashboard_data()
        
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            return False
        else:
            print("✅ 成功获取仪表板数据")
            print(f"📊 活跃用户: {result.get('active_users', 'N/A')}")
            print(f"📊 总用户数: {result.get('total_users', 'N/A')}")
            print(f"📊 今日指令: {result.get('today_commands', 'N/A')}")
            print(f"📊 成功率: {result.get('success_rate', 'N/A')}%")
            print(f"📊 平均响应时间: {result.get('avg_response_time', 'N/A')}ms")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_raw_sql():
    """测试原生SQL查询"""
    print("\n🔍 测试原生SQL查询")
    print("=" * 50)
    
    try:
        from django.db import connection
        from datetime import datetime, timedelta
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=6)
        
        with connection.cursor() as cursor:
            # 测试指令统计查询
            cursor.execute("""
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as total,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed
                FROM app01_commandexecutionrecord 
                WHERE DATE(created_at) BETWEEN %s AND %s
                GROUP BY DATE(created_at)
                ORDER BY DATE(created_at)
            """, [start_date, end_date])
            
            rows = cursor.fetchall()
            print(f"✅ 指令统计查询成功，返回 {len(rows)} 行数据")
            
            for row in rows:
                date_str, total, success, failed = row
                print(f"   📅 {date_str}: 总计{total}, 成功{success}, 失败{failed}")
            
            # 测试用户活动查询
            cursor.execute("""
                SELECT 
                    DATE(created_at) as date,
                    COUNT(DISTINCT user_id) as active_users
                FROM app01_botaccessevent 
                WHERE DATE(created_at) BETWEEN %s AND %s
                GROUP BY DATE(created_at)
                ORDER BY DATE(created_at)
            """, [start_date, end_date])
            
            rows = cursor.fetchall()
            print(f"✅ 用户活动查询成功，返回 {len(rows)} 行数据")
            
            for row in rows:
                date_str, active_users = row
                print(f"   📅 {date_str}: 活跃用户{active_users}")
            
            return True
            
    except Exception as e:
        print(f"❌ 原生SQL测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 SQL修复验证测试")
    print("=" * 80)
    
    success_count = 0
    total_tests = 4
    
    # 测试原生SQL
    if test_raw_sql():
        success_count += 1
    
    # 测试指令趋势
    if test_command_trends():
        success_count += 1
    
    # 测试用户活动
    if test_user_activity():
        success_count += 1
    
    # 测试仪表板数据
    if test_dashboard_data():
        success_count += 1
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！SQL修复成功！")
        print("💡 现在可以重启Django服务并测试API了")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return success_count == total_tests

if __name__ == '__main__':
    main()

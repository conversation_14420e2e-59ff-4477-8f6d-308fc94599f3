# -*- coding: utf-8 -*-
"""
基于大模型的简化帮助系统
替换复杂的帮助系统架构，使用大模型生成个性化帮助回答
"""

import asyncio
import logging
from typing import Dict, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class LLMHelpManager:
    """基于大模型的帮助管理器"""
    
    def __init__(self):
        self.system_features_doc = self._load_system_features_doc()
        self.user_permissions = self._load_user_permissions()
    
    def _load_system_features_doc(self) -> str:
        """加载系统功能文档"""
        return """# ChatbotAR 系统功能完整文档

## 基本信息
- 机器人名称: ChatbotAR
- 支持群聊和私聊两种模式，可通过自然语言和固定指令两种方式交互，完成各种任务和操作
- 详细功能请见下面文档
- 群聊需要@ChatbotAR调用，私聊可直接发送命令

## 核心功能

### 1. JIRA查询功能
**功能说明:**
- 通过自然语言描述或者具体的JQL查询JIRA数据
- 在群内通过@群名可省略单号查询对应群组的JIRA数据
**固定指令查询:**
- jira单信息查询: SPCB-1234
- 阻塞 jira 单的Bug查询: bug SPCB-1234  
- jira 单时间线查询: timeline SPCB-1234

**自然语言查询:**
- 查询SPCB-1234的状态
- 我的任务 / 分配给zhang.san的bug
- 未完成的任务 / 已关闭的bug
- 上周完成的工作 / 本月新建的需求
- 高优先级bug / 紧急任务

**查询技巧:**
- 群名包含JIRA单号时，可省略单号参数
- 支持邮箱、姓名、employee_code多种格式
- 支持相对时间（本周、上月）和绝对时间
- 支持给出具体的JQL查询

### 2. JIRA操作功能
**子任务创建:**
**功能说明:**
- 通过机器人建工单，支持三级格式（通过--开头追加父任务标题）
- 基本格式: 在[父单号]下建子任务：[标题] [工作量]
- 示例: 在SPCB-1234下建子任务：完成API测试 2d
- 工作量格式: 1h/2h/4h (小时), 1d/2d/3d (天), 1w/2w (周)
- 支持追加 Task 标题的方式建单：在[父单号]下建子任务：--[标题] [工作量] ，会自动把--后面的内容追加到 Task 标题上作为任务标题。

**群组管理:**
- 创建群组: new group SPCB-1234
- 功能: 自动创建JIRA单号对应的SeaTalk群组，拉取相关人员加入群组

### 3. 定时任务功能
**功能说明:**
- 通过自然语言创建定时提醒任务，结合 JQL 查询到的内容，可做到私聊或者群聊提醒，为个人工作和项目管理提效
**基本命令:**
- 创建任务: schedule create "任务名" "查询内容" "时间安排"
- 查看任务: schedule list
- 暂停任务: schedule pause [任务ID]
- 恢复任务: schedule resume [任务ID]
- 删除任务: schedule delete [任务ID]

**时间安排格式:**
- 每日: daily 09:00
- 工作日: workdays 17:00
- 每周: weekly mon 10:00
- 每月: monthly 1 09:00

**创建示例:**
- schedule create "每日bug检查" "project = SPCB AND status = Open" "daily 09:00"
- schedule create "周报提醒" "assignee = currentUser() AND updated >= -1w" "weekly fri 17:00"
- 每个工作日早上 10 点提醒我有哪些未完成的子任务/

### 4. 文档处理功能
**功能说明:**
- 快速总结 confluence 上的 PRD 文档，适合用于快速理解需求
**支持的操作:**
- PRD总结:总结 confluence 上的PRD 文档

**使用示例:**
-  帮我总结这个 PRD: https://confluence.shopee.io/display/yyy
- 帮我总结这个PRD的背景、目标、验收标准、约束条件及技术细节

### 5. 通用AI能力（基于 ChatGPT 4.1)
**功能说明:**
- 可以把我当成一个完整功能的 AI 来用，可以支持 AI 的翻译、问答、写作、问题排查、bug 分析等功能
**支持的操作:**
- 翻译
- 问答
- 技术咨询
- 错误日志分析
- 写 JQL、SQL
- 文章创作等等

**使用示例:**
- 翻译：Hello World
- 翻译成英文：你好世界
- 写个 jql 查询
- 技术咨询：redis 的实现原理

### 6. SPCPM专用功能
**功能说明:**
- 专门为 SPCPM 项目的 PJ 开发的功能，可用于团队项目管理，提高团队协同效率
**基本命令:**
- 查看时间线: timeline SPCPM-XXXXX 或 tr SPCPM-XXXXX
- 团队提醒: [all|qa|dev|pm|pj|fe|be] SPCPM-XXXXX
- 添加提醒: tr+ SPCPM-XXXXX
- 移除提醒: tr- SPCPM-XXXXX
- 查看提醒: tr list

**团队角色说明:**
- all: 所有相关人员
- qa: QA团队成员
- dev: 开发团队成员
- pm: 产品经理
- pj: 项目经理
- fe: 前端开发
- be: 后端开发

### 7. 配置管理功能
**上下文管理:**
- 清除上下文: clear context
- 功能: 清除对话历史，重新开始

## 管理员专用功能 (仅超级管理员可用)

### AI模型AB测试管理
**基本命令:**
- 查看状态: abtest status
- 开启双模型: abtest enable dual
- 开启单模型: abtest enable single [qwen|gpt]
- 关闭测试: abtest disable
- 查看帮助: abtest help

**权限说明:**
- 仅超级管理员可使用AB测试功能
- AB测试用于对比不同AI模型的效果
- 测试期间可能影响响应质量

**重要提醒:**
- AB测试会影响AI回答质量
- 建议在非高峰期进行测试
- 测试完成后及时关闭恢复生产模式

## 用户权限说明

### 普通用户权限
- 可使用所有JIRA查询功能
- 可创建和管理自己的定时任务
- 可使用文档处理功能
- 可使用SPCPM专用功能
- 可进行基本配置管理

### 项目管理员权限
- 拥有普通用户的所有权限
- 可管理项目范围内的定时任务
- 可进行项目相关的高级操作

### 超级管理员权限
- 拥有所有用户权限
- 可使用AI模型AB测试管理功能
- 可管理所有用户的任务和配置

## 使用提示
- 群聊中需要@ChatbotAR来调用机器人
- 私聊中可直接发送命令
- 群名包含JIRA单号时，可省略单号参数
- 支持自然语言查询，机器人会智能理解用户意图
- 如有问题可联系: <EMAIL>"""

    def _load_user_permissions(self) -> Dict:
        """加载用户权限配置"""
        return {
            'super_admins': {
                '<EMAIL>',
                '<EMAIL>',
            },
            'project_admins': {
                'SPCB': {
                    '<EMAIL>',
                    '<EMAIL>',
                },
                'SPCT': {
                    '<EMAIL>',
                    '<EMAIL>',
                },
                'SPCPM': {
                    '<EMAIL>',
                }
            }
        }
    
    def _get_user_role(self, user_email: str) -> str:
        """获取用户角色"""
        if not user_email:
            return 'normal'
        
        if user_email in self.user_permissions['super_admins']:
            return 'super_admin'
        
        for project, admins in self.user_permissions['project_admins'].items():
            if user_email in admins:
                return 'project_admin'
        
        return 'normal'
    
    async def generate_help_response(self, 
                                   user_query: str,
                                   context_type: str = "group",
                                   user_email: str = "",
                                   user_role: str = "normal") -> str:
        """
        生成基于大模型的帮助回答
        
        Args:
            user_query: 用户查询
            context_type: 'group' 或 'private'
            user_email: 用户邮箱
            user_role: 用户角色
            
        Returns:
            个性化的帮助回答
        """
        try:
            # 如果提供了用户邮箱，重新确定用户角色
            if user_email:
                actual_role = self._get_user_role(user_email)
                if actual_role != 'normal':
                    user_role = actual_role
            
            # 构建系统提示词
            system_prompt = self._build_system_prompt(context_type, user_role)
            
            # 构建用户提示词
            user_prompt = self._build_user_prompt(user_query, context_type)
            
            # 调用大模型生成回答
            from ..ai_module.llm_client import LLMClient
            llm_client = LLMClient()
            
            result = await llm_client.generate_text(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.3,  # 降低随机性，确保回答一致性
                max_tokens=1500
            )
            
            if result['success']:
                return result['content']
            else:
                logger.error(f"大模型调用失败: {result.get('error', 'Unknown error')}")
                return self._get_fallback_help(context_type, user_role)
                
        except Exception as e:
            logger.error(f"生成帮助回答异常: {str(e)}")
            return self._get_fallback_help(context_type, user_role)
    
    def _build_system_prompt(self, context_type: str, user_role: str) -> str:
        """构建系统提示词"""
        role_description = {
            'normal': '普通用户',
            'project_admin': '项目管理员', 
            'super_admin': '超级管理员'
        }.get(user_role, '普通用户')
        
        context_description = {
            'group': '群聊环境（需要@ChatbotAR调用）',
            'private': '私聊环境（可直接发送命令）'
        }.get(context_type, '群聊环境')
        
        return f"""你是ChatbotAR智能助手的帮助系统。请根据用户的具体问题，从系统功能文档中提取相关信息，生成简洁、准确、有针对性的帮助回答。

当前用户信息:
- 用户角色: {role_description}
- 使用环境: {context_description}

系统功能文档:
{self.system_features_doc}

回答要求:
1. **必须严格基于系统功能文档回答**，不要使用文档外的信息
2. 根据用户角色过滤功能（普通用户不显示管理员功能）
3. 根据使用环境调整命令格式（群聊需要@ChatbotAR前缀）
4. **格式要求**：
   - 使用反引号包围示例命令：`示例命令`
   - 使用**加粗**标记功能分类标题
   - 使用编号或bullet点组织内容
   - 避免过多换行，保持紧凑
5. **为每个功能分类提供详细帮助指令**，格式如：
   详细帮助：输入 `如何使用JIRA查询` 或 `JIRA查询详细说明`
6. 示例命令格式：
   **1. 功能名称**
   • 功能描述：`示例命令`
   详细帮助：输入 `具体帮助指令`
7. 如果用户问题不明确，提供相关功能的概览
8. 不要提及不存在的功能或命令
9. 对于帮助相关的查询，必须基于功能文档回答
10. 回答长度控制在1000字符以内，信息密度高但包含详细帮助指引"""

    def _build_user_prompt(self, user_query: str, context_type: str) -> str:
        """构建用户提示词"""
        if not user_query or user_query.strip() in ['help', '帮助', '?', '？']:
            return f"用户在{context_type}环境中请求通用帮助信息，请提供系统功能概览。"
        else:
            return f"用户在{context_type}环境中询问: {user_query}\n\n请根据用户的具体问题提供相关的帮助信息。"
    
    def _get_fallback_help(self, context_type: str, user_role: str) -> str:
        """获取备用帮助信息"""
        bot_prefix = "@ChatbotAR " if context_type == "group" else ""
        
        basic_help = f"""👋 您好！我是ChatbotAR，您的智能工作助手！

📋 **核心功能**
🔍 **JIRA查询**: {bot_prefix}SPCB-1234 或 {bot_prefix}查询我的任务
✏️ **JIRA操作**: {bot_prefix}在SPCB-1234下建子任务：测试功能 2d
⏰ **定时任务**: {bot_prefix}schedule list
📄 **文档处理**: {bot_prefix}翻译：Hello World
🔄 **SPCPM专用**: {bot_prefix}SPCPM help

📮 **问题反馈**: <EMAIL>"""

        if user_role == 'super_admin':
            basic_help += f"\n\n🔐 **管理员功能**: {bot_prefix}abtest status"
        
        return basic_help

# 全局实例
llm_help_manager = LLMHelpManager()

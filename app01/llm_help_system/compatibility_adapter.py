# -*- coding: utf-8 -*-
"""
兼容性适配器
确保新的LLM帮助系统能够无缝替换现有的复杂帮助系统
"""

import asyncio
import logging
from typing import Dict, Optional, Tuple, Any
from enum import Enum

logger = logging.getLogger(__name__)

class UserRole(Enum):
    """用户角色枚举（保持与原系统兼容）"""
    NORMAL = "normal"
    PROJECT_ADMIN = "project_admin"
    SUPER_ADMIN = "super_admin"

class HelpCategory(Enum):
    """帮助分类枚举（保持与原系统兼容）"""
    GENERAL = "general"
    JIRA_QUERY = "jira_query"
    JIRA_WRITE = "jira_write"
    SCHEDULE = "schedule"
    DOCUMENT = "document"
    SPCPM = "spcpm"
    CONFIG = "config"
    ADMIN = "admin"

class CompatibilityAdapter:
    """兼容性适配器类"""
    
    def __init__(self):
        from .llm_help_manager import llm_help_manager
        self.llm_help_manager = llm_help_manager
    
    def get_help_message(self,
                        context_type: str = "group",
                        category: Optional[HelpCategory] = None,
                        user_role: UserRole = UserRole.NORMAL,
                        user_query: str = "",
                        user_email: str = "",
                        project_context: str = None) -> str:
        """
        获取帮助信息（兼容原HelpManager接口）
        
        Args:
            context_type: 'group' 或 'private'
            category: 帮助分类，None表示通用帮助
            user_role: 用户角色
            user_query: 用户查询（用于智能推荐）
            user_email: 用户邮箱（用于权限检查）
            project_context: 项目上下文
            
        Returns:
            格式化的帮助信息
        """
        try:
            # 构建查询内容
            if category is None:
                query = user_query if user_query else "help"
            else:
                category_queries = {
                    HelpCategory.JIRA_QUERY: "如何查询JIRA",
                    HelpCategory.JIRA_WRITE: "如何创建子任务和群组",
                    HelpCategory.SCHEDULE: "如何使用定时任务",
                    HelpCategory.DOCUMENT: "如何处理文档",
                    HelpCategory.SPCPM: "SPCPM专用功能",
                    HelpCategory.CONFIG: "如何进行配置管理",
                    HelpCategory.ADMIN: "管理员功能"
                }
                query = category_queries.get(category, "help")
            
            # 使用asyncio运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self.llm_help_manager.generate_help_response(
                        user_query=query,
                        context_type=context_type,
                        user_email=user_email,
                        user_role=user_role.value
                    )
                )
                return result
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"LLM帮助系统调用失败: {str(e)}")
            # 返回简单的备用帮助
            return self._get_simple_fallback_help(context_type, user_role)
    
    def detect_help_intent(self, user_query: str) -> Tuple[bool, Optional[HelpCategory]]:
        """
        检测用户查询是否为帮助请求（兼容原接口）
        
        Args:
            user_query: 用户查询
            
        Returns:
            (是否为帮助请求, 帮助分类)
        """
        user_query_lower = user_query.lower().strip()
        
        # 通用帮助触发词
        general_triggers = ['help', '帮助', '?', '？', 'usage', '使用方法', '怎么用', '如何使用']
        if any(trigger in user_query_lower for trigger in general_triggers):
            # 检查是否为特定分类帮助
            if any(word in user_query_lower for word in ['jira', '查询', '单号']):
                return True, HelpCategory.JIRA_QUERY
            elif any(word in user_query_lower for word in ['定时', 'schedule', '任务']):
                return True, HelpCategory.SCHEDULE
            elif any(word in user_query_lower for word in ['文档', 'document']):
                return True, HelpCategory.DOCUMENT
            elif any(word in user_query_lower for word in ['spcpm']):
                return True, HelpCategory.SPCPM
            elif any(word in user_query_lower for word in ['管理员', 'admin']):
                return True, HelpCategory.ADMIN
            else:
                return True, None
        
        return False, None
    
    def get_smart_help_recommendation(self, user_query: str, context: Dict) -> Optional[str]:
        """
        基于用户查询和上下文提供智能帮助推荐（兼容原接口）
        
        Args:
            user_query: 用户查询
            context: 上下文信息
            
        Returns:
            推荐的帮助信息，如果无推荐则返回None
        """
        user_query_lower = user_query.lower()
        
        # 基于关键词推荐相关帮助
        recommendations = []
        
        # JIRA相关推荐
        if any(word in user_query_lower for word in ['jira', '单号', '任务', 'bug']):
            recommendations.append("💡 **JIRA查询帮助**: 询问 'JIRA查询怎么用' 查看详细说明")
        
        # 定时任务推荐
        if any(word in user_query_lower for word in ['定时', '提醒', 'schedule', '每天', '每周']):
            recommendations.append("⏰ **定时任务帮助**: 询问 '定时任务怎么用' 查看详细说明")
        
        # 文档处理推荐
        if any(word in user_query_lower for word in ['文档', 'document', 'prd', 'trd']):
            recommendations.append("📄 **文档处理帮助**: 询问 '文档处理怎么用' 查看详细说明")
        
        if recommendations:
            return "\n".join(recommendations)
        
        return None
    
    def generate_contextual_help(self, user_query: str, context: Dict = None, user_role: str = "normal") -> str:
        """
        生成上下文相关的帮助信息（兼容原HelpIntentEnhancer接口）
        
        Args:
            user_query: 用户查询
            context: 上下文信息
            user_role: 用户角色
            
        Returns:
            帮助信息
        """
        # 检测帮助意图
        is_help, category = self.detect_help_intent(user_query)
        
        if not is_help:
            return ""
        
        # 转换用户角色
        role_mapping = {
            'normal': UserRole.NORMAL,
            'project_admin': UserRole.PROJECT_ADMIN,
            'super_admin': UserRole.SUPER_ADMIN
        }
        user_role_enum = role_mapping.get(user_role, UserRole.NORMAL)
        
        # 获取上下文类型
        context_type = context.get('type', 'group') if context else 'group'
        
        # 生成帮助信息
        return self.get_help_message(
            context_type=context_type,
            category=category,
            user_role=user_role_enum,
            user_query=user_query
        )
    
    def _get_simple_fallback_help(self, context_type: str, user_role: UserRole) -> str:
        """获取简单的备用帮助信息"""
        bot_prefix = "@ChatbotAR " if context_type == "group" else ""
        
        help_text = f"""👋 您好！我是ChatbotAR，您的智能工作助手！

📋 **核心功能**
🔍 **JIRA查询**: {bot_prefix}SPCB-1234
✏️ **JIRA操作**: {bot_prefix}在SPCB-1234下建子任务：测试功能 2d
⏰ **定时任务**: {bot_prefix}schedule list
📄 **文档处理**: {bot_prefix}翻译：Hello World

📮 **问题反馈**: <EMAIL>"""

        if user_role == UserRole.SUPER_ADMIN:
            help_text += f"\n\n🔐 **管理员功能**: {bot_prefix}abtest status"
        
        return help_text

# 创建全局实例以保持与原系统的兼容性
compatibility_adapter = CompatibilityAdapter()

# 为了保持完全兼容，创建与原系统相同的接口
class HelpManager:
    """兼容性HelpManager类"""
    
    def __init__(self):
        self.adapter = compatibility_adapter
    
    def get_help_message(self, *args, **kwargs):
        return self.adapter.get_help_message(*args, **kwargs)
    
    def detect_help_intent(self, *args, **kwargs):
        return self.adapter.detect_help_intent(*args, **kwargs)
    
    def get_smart_help_recommendation(self, *args, **kwargs):
        return self.adapter.get_smart_help_recommendation(*args, **kwargs)

class HelpIntentEnhancer:
    """兼容性HelpIntentEnhancer类"""
    
    def __init__(self):
        self.adapter = compatibility_adapter
    
    def detect_help_intent(self, *args, **kwargs):
        return self.adapter.detect_help_intent(*args, **kwargs)
    
    def generate_contextual_help(self, *args, **kwargs):
        return self.adapter.generate_contextual_help(*args, **kwargs)

# 创建全局实例
help_manager = HelpManager()
help_intent_enhancer = HelpIntentEnhancer()

# -*- coding: utf-8 -*-
"""
上下文感知的命令处理器
统一处理群聊和私聊的命令，支持上下文信息
"""

import re
import json
import logging
from typing import Dict, Optional, Any, List, Tuple
from icecream import ic

from .bot_config import bot_config
from .seatalk_group_manager import auto_create_group_for_jira

logger = logging.getLogger(__name__)


class CommandPattern:
    """指令模式定义类"""
    
    def __init__(self, name, patterns=None, context_aware=False, priority=0, exclude_patterns=None):
        """
        初始化指令模式
        
        Args:
            name: 指令名称
            patterns: 匹配模式列表（正则表达式或关键词组合）
            context_aware: 是否考虑上下文（如群名）
            priority: 优先级（数字越大优先级越高）
            exclude_patterns: 排除模式列表
        """
        self.name = name
        self.patterns = patterns or []
        self.context_aware = context_aware
        self.priority = priority
        self.exclude_patterns = exclude_patterns or []
    
    def match(self, command_text: str, context=None) -> float:
        """
        检查指令是否匹配此模式
        
        Args:
            command_text: 指令文本
            context: 消息上下文
            
        Returns:
            匹配分数（0表示不匹配，大于0表示匹配度）
        """
        if not command_text:
            return 0
            
        # 去除前后空格并转换为小写
        command_text = command_text.strip()
        command_lower = command_text.lower()
        
        # 检查排除模式
        for pattern in self.exclude_patterns:
            try:
                if isinstance(pattern, str):
                    if pattern in command_lower:
                        ic(f"排除模式匹配: '{pattern}' in '{command_lower}'")
                        return 0
                else:
                    if pattern.search(command_text):
                        ic(f"排除模式匹配: 正则 '{pattern.pattern}' 匹配 '{command_text}'")
                        return 0
            except Exception as e:
                ic(f"排除模式异常: {str(e)}")
        
        # 检查匹配模式
        score = 0
        for pattern in self.patterns:
            try:
                if isinstance(pattern, str):
                    # 尝试作为普通字符串匹配
                    if pattern in command_lower:
                        ic(f"字符串匹配成功: '{pattern}' in '{command_lower}'")
                        score += 1
                    # 尝试作为正则表达式匹配（不区分大小写）
                    else:
                        match = re.match(pattern, command_text, re.IGNORECASE)
                        if match:
                            ic(f"正则匹配成功: '{pattern}' 匹配 '{command_text}'")
                            score += 2  # 正则匹配给予更高分数
                else:
                    # 使用已编译的正则表达式对象（不区分大小写）
                    match = pattern.search(command_text)
                    if match:
                        ic(f"已编译正则匹配成功: '{pattern.pattern}' 匹配 '{command_text}'")
                        score += 2
            except Exception as e:
                ic(f"匹配异常: {str(e)}, 模式: {pattern}, 文本: {command_text}")
        
        # 考虑上下文
        if score > 0 and self.context_aware and context:
            # 如果指令中没有JIRA单号但群名中有，增加分数
            if context.group_name and not re.search(r'SP[A-Z]+-\d+', command_text):
                jira_keys = re.findall(r'SP[A-Z]+-\d+', context.group_name)
                if jira_keys:
                    ic(f"上下文匹配: 从群名 '{context.group_name}' 中找到JIRA单号 {jira_keys}")
                    score += 1
        
        final_score = score * (1 + self.priority * 0.1)  # 考虑优先级
        ic(f"最终分数: {final_score} (原始分数: {score}, 优先级: {self.priority})")
        return final_score


class MessageContext:
    """消息上下文类"""
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化消息上下文
        
        Args:
            data: Seatalk回调的原始数据
        """
        self.raw_data = data
        self.event_type = data.get("event_type")
        
        if self.event_type == "new_mentioned_message_received_from_group_chat":
            self._init_group_context(data)
        elif self.event_type == "message_from_bot_subscriber":
            self._init_private_context(data)
        else:
            self.is_valid = False
    
    def _init_group_context(self, data: Dict[str, Any]):
        """初始化群聊上下文"""
        self.type = 'group'
        self.is_valid = True
        self.has_context = True
        
        event = data["event"]
        message = event["message"]
        sender = message["sender"]
        
        self.text = message["text"]["plain_text"]
        self.group_id = event["group_id"]
        self.seatalk_id = sender["seatalk_id"]
        self.employee_code = sender["employee_code"]
        self.email = sender.get("email")  # 添加email属性
        
        # 获取群名作为上下文信息
        from .views import get_group_info
        group_info_result = get_group_info(self.group_id) if self.group_id else None
        self.group_name = group_info_result.get('group', {}).get('group_name') if group_info_result else None
        
        # 提取命令内容
        self.command_text = bot_config.extract_command_from_group_message(self.text)
        self.is_bot_mentioned = self.command_text is not None
    
    def _init_private_context(self, data: Dict[str, Any]):
        """初始化私聊上下文"""
        self.type = 'private'
        self.is_valid = True
        self.has_context = False
        
        event = data["event"]
        
        self.text = event["message"]["text"]["content"]
        self.group_id = None
        self.group_name = None
        self.seatalk_id = event["seatalk_id"]
        self.employee_code = event["employee_code"]
        self.email = event.get("email")  # 添加email属性
        
        # 提取命令内容
        self.command_text = bot_config.extract_command_from_private_message(self.text)
        self.is_bot_mentioned = True  # 私聊默认认为是对机器人说话
    
    def extract_jira_keys(self, text: str = None) -> list:
        """从文本中提取JIRA单号"""
        search_text = text or self.command_text or ""
        pattern = r'SP[A-Z]+-\d{1,6}'
        return re.findall(pattern, search_text)
    
    def get_jira_key_with_context(self, command_text: str = None) -> Optional[str]:
        """
        获取JIRA单号，支持上下文提取
        
        Args:
            command_text: 命令文本，如果为None则使用self.command_text
            
        Returns:
            Optional[str]: JIRA单号，如果找不到返回None
        """
        search_text = command_text or self.command_text or ""
        
        # 1. 先从命令文本中提取
        jira_keys = self.extract_jira_keys(search_text)
        if jira_keys:
            return jira_keys[0]
        
        # 2. 如果是群聊且有群名上下文，从群名中提取
        if self.has_context and self.group_name:
            jira_keys = self.extract_jira_keys(self.group_name)
            if jira_keys:
                return jira_keys[0]
        
        return None
    
    def is_pure_spcpm_group(self) -> tuple:
        """
        检查当前群组是否为纯SPCPM群组（群名中有且仅有SPCPM单号）
        
        Returns:
            tuple: (是否为纯SPCPM群组, SPCPM单号)
        """
        if not self.has_context or not self.group_name:
            return False, None
        
        # 检查群名是否包含SPCPM单号
        spcpm_pattern = r'SPCPM-\d+'
        spcpm_matches = re.findall(spcpm_pattern, self.group_name, re.IGNORECASE)
        
        if not spcpm_matches:
            return False, None
        
        # 检查是否包含其他项目单号
        other_pattern = r'SP[A-Z]+-\d+'
        all_matches = re.findall(other_pattern, self.group_name)
        other_matches = [m for m in all_matches if not m.upper().startswith('SPCPM-')]
        
        # 如果只有SPCPM单号，没有其他项目单号，则认为是纯SPCPM群组
        if len(other_matches) == 0:
            return True, spcpm_matches[0].upper()
        
        # 如果有其他项目单号，则不是纯SPCPM群组
        return False, None
        
    def get_context_info(self) -> Dict[str, Any]:
        """获取上下文信息供指令匹配使用"""
        context_info = {
            'type': self.type,
            'has_context': self.has_context,
            'group_id': self.group_id,
            'group_name': self.group_name,
            'jira_keys': []
        }
        
        # 提取上下文中的JIRA单号
        if self.group_name:
            context_info['jira_keys'] = self.extract_jira_keys(self.group_name)
            
        # 添加是否为纯SPCPM群组信息
        is_pure_spcpm, spcpm_id = self.is_pure_spcpm_group()
        context_info['is_pure_spcpm'] = is_pure_spcpm
        context_info['spcpm_id'] = spcpm_id
        
        return context_info


class CommandProcessor:
    """命令处理器"""
    
    def __init__(self):
        """初始化命令处理器"""
        self.command_patterns = [
            # Bug查询指令
            CommandPattern(
                name="bug_query",
                patterns=[
                    r'^bug\s+[A-Z]+-\d+$',     # "bug SPCB-1234"
                    r'^[A-Z]+-\d+\s+bug$',     # "SPCB-1234 bug"
                    r'^bug$',                    # 纯"bug"（需要上下文）
                    r'^缺陷\s+[A-Z]+-\d+$',   # "缺陷 SPCB-1234"
                    r'^[A-Z]+-\d+\s+缺陷$',   # "SPCB-1234 缺陷"
                    r'^查看\s*bug$',           # "查看bug" 或 "查看 bug"
                    r'^显示\s*bug$',           # "显示bug" 或 "显示 bug"
                    r'^查看\s+[A-Z]+-\d+$',   # "查看 SPCB-1234"
                    r'^显示\s+[A-Z]+-\d+$'    # "显示 SPCB-1234"
                ],
                context_aware=True,
                priority=3,  # 提高优先级，确保优先于checklist
                exclude_patterns=[
                    r'每[天周月]',
                    r'定时',
                    r'创建',
                    r'新建',
                    r'建单',
                    r'建子任务', 
                    r'add\s+subtask',
                    r'schedule',
                    r'linkedissues',
                    r'提醒',
                    r'通知'
                ]
            ),
            
            # Timeline查询指令
            CommandPattern(
                name="timeline_query",
                patterns=[
                    r'timeline\s+[A-Z]+-\d+',  # "timeline SPCB-1234"
                    r'[A-Z]+-\d+\s+timeline',  # "SPCB-1234 timeline"
                    r'^tl\s+[A-Z]+-\d+',       # "tl SPCB-1234"
                    r'^timeline$',               # 纯"timeline"（需要上下文）
                    r'^tl$'                      # 纯"tl"（需要上下文）
                ],
                context_aware=True,
                priority=2,
                exclude_patterns=[
                    r'每[天周月]',
                    r'定时',
                    r'提醒',
                    r'通知'
                ]
            ),
            
            # MR检查指令
            CommandPattern(
                name="mr_check",
                patterns=[
                    r'^chatbot\s+mr$',
                    r'^mr\s+chatbot$'
                ],
                priority=3  # 高优先级，因为模式非常明确
            ),
            
            # 创建群组指令
            CommandPattern(
                name="new_group",
                patterns=[
                    r'new\s+group\s+[A-Z]+-\d+',
                    r'[A-Z]+-\d+\s+new\s+group'
                ],
                priority=3
            ),
            
            # Checklist指令
            CommandPattern(
                name="checklist",
                patterns=[
                    r'SPCB-\d+\s+[SOCMDBsocmdb\\,]+'
                ],
                priority=2,  # 保持原有优先级
                exclude_patterns=[
                    r'bug'  # 添加排除模式，避免与bug_query冲突
                ]
            ),
            
            # JIRA单号查询
            CommandPattern(
                name="jira_query",
                patterns=[
                    r'^[A-Z]+-\d+$',  # 纯JIRA单号
                    r'^查看\s+[A-Z]+-\d+$',  # "查看 SPCB-1234"
                    r'^显示\s+[A-Z]+-\d+$'   # "显示 SPCB-1234"
                ],
                priority=1
            ),
            
            # 添加服务指令
            CommandPattern(
                name="add_service",
                patterns=[
                    r'add\s+service',
                    r'service\s+add',
                    r'^add\s+[\w\-\.]+',  # 添加这个模式，匹配以add开头后跟服务名称的模式
                    r'^add\s+shopee-[\w\-\.]+',  # 更具体的匹配shopee服务的模式
                    '添加服务', '新增服务', '增加服务', '添加项目', '新增项目', '增加项目'  # 保留原有中文关键词
                ],
                priority=2,
                exclude_patterns=[
                    r'reminder',
                    r'schedule'
                ]
            ),
            
            # 帮助指令
            CommandPattern(
                name="help",
                patterns=[
                    r'^help$',
                    r'^帮助$',
                    r'^\?$',
                    r'^你能做什么\?$',
                    r'^使用说明$',
                    r'^如何检查',
                    r'^怎么创建',
                    r'^如何使用',
                    r'^如何建群',
                    r'^查看.*步骤$',
                    r'^创建指南$',
                    r'^操作说明$',
                    r'^使用手册$',
                    r'^帮助文档$',
                    r'^具体操作步骤$',
                    r'^最佳实践$',
                    r'^常用示例$',
                    r'^注意事项$'
                ],
                priority=1
            ),
            
            # 定时任务指令
            CommandPattern(
                name="schedule_task",
                patterns=[
                    r'每[天周月].*提醒',
                    r'定时.*提醒',
                    r'提醒.*每[天周月]',
                    r'提醒.*定时',
                    r'每天.*提醒',
                    r'每周.*提醒',
                    r'每月.*提醒',
                    r'定时任务',
                    r'定时提醒',
                    r'定时通知'
                ],
                priority=2
            )
        ]
    
    def process_command(self, context: MessageContext) -> Dict[str, Any]:
        """
        处理命令的主入口
        
        Args:
            context: 消息上下文
            
        Returns:
            Dict: 处理结果
        """
        if not context.is_valid or not context.is_bot_mentioned:
            return self._generate_help_response(context)
        
        command_text = context.command_text
        if not command_text:
            return self._generate_help_response(context)
        
        # 1. 私聊AI查询处理（检查原始文本）
        if context.type == 'private' and context.text.startswith("/ai "):
            ai_query = context.text[4:]  # 从原始文本中提取AI查询内容
            return self._process_ai_command(ai_query, context)
        
        # 2. 群聊AI查询处理（检查命令文本）
        if context.type == 'group' and command_text.startswith("/ai "):
            return self._process_ai_command(command_text[4:], context)
        
        # 3. 先尝试交给SPCPM专用处理器，如果返回结果则直接输出
        spcpm_result = self._process_spcpm_command(command_text, context)
        if spcpm_result is not None:
            return spcpm_result
            
        # 4. 使用指令模式匹配系统找到最佳匹配的传统指令
        ic(f"使用指令模式匹配系统处理命令: {command_text}")
        best_match = self._find_best_command_match(command_text, context)
        
        if best_match:
            ic(f"找到最佳匹配指令: {best_match}")
            # 根据最佳匹配的指令类型调用相应处理方法
            if best_match == "bug_query":
                return self._process_bug_command(command_text, context)
            elif best_match == "timeline_query":
                return self._process_timeline_command(command_text, context)
            elif best_match == "mr_check":
                return self._process_mr_command(context)
            elif best_match == "new_group":
                return self._process_new_group_command(command_text, context)
            elif best_match == "checklist":
                return self._process_checklist_command(command_text, context)
            elif best_match == "jira_query":
                jira_keys = context.extract_jira_keys(command_text)
                if jira_keys:
                    return self._process_jira_query_command(jira_keys[0], context)
            elif best_match == "add_service":
                return self._process_add_service_command(command_text, context)
            elif best_match == "schedule_task":
                return self._process_schedule_task_command(command_text, context)
        
        # 5. 无法识别的命令，尝试使用AI处理
        ic("未匹配到传统指令，尝试使用AI处理")
        return self._process_ai_command(context.command_text or context.text, context)
    
    def _process_mr_command(self, context: MessageContext) -> Dict[str, Any]:
        """处理MR检查命令"""
        try:
            from .views import SeatalkProcess, MRnotdeal
            
            if SeatalkProcess.objects.filter(job_name='MRnotdeal').exists():
                seatalk_process = SeatalkProcess.objects.get(job_name='MRnotdeal')
                if seatalk_process.job_process != 1:
                    seatalk_process.job_process = 1
                    seatalk_process.save()
                    MRnotdeal()
                    message = "MR检查已经启动，请关注对应群的检查结果，谢谢！"
                    seatalk_process = SeatalkProcess.objects.get(job_name='MRnotdeal')
                    seatalk_process.job_process = 0
                    seatalk_process.save()
                    return {'type': 'success', 'message': message}
            else:
                SeatalkProcess.objects.create(job_name='MRnotdeal', job_process=0)
                return {'type': 'success', 'message': "MR检查进程已初始化"}
        except Exception as e:
            return {'type': 'error', 'message': f"MR检查失败: {str(e)}"}
    
    def _process_bug_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """处理Bug查询命令"""
        jira_key = context.get_jira_key_with_context(command_text)
        
        if not jira_key:
            if context.has_context:
                message = "未找到JIRA单号，请在消息中包含SP*-XXXXX或确保群名中包含单号"
            else:
                message = "私聊模式下请提供完整格式：bug SPCB-1234"
            return {'type': 'error', 'message': message}
        
        try:
            from .views import get_issues_in_epic
            bug_info = get_issues_in_epic(jira_key)
            return {'type': 'success', 'message': bug_info}
        except Exception as e:
            from .seatalk_group_manager import simplify_jira_error
            error_msg = simplify_jira_error(str(e))
            return {'type': 'error', 'message': f"获取Bug信息失败: {error_msg}"}
    
    def _process_timeline_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """处理Timeline查询命令"""
        jira_key = context.get_jira_key_with_context(command_text)
        
        if not jira_key:
            if context.has_context:
                message = "No JIRA issue found. Please include SP*-XXXXX in your message or ensure the group name contains an issue number."
            else:
                message = "In private chat mode, please provide the complete format: timeline SPCB-1234"
            return {'type': 'error', 'message': message}
        
        # 新增：SPCPM Request ID timeline整合
        if jira_key.startswith("SPCPM-"):
            try:
                from .spcpm_timeline_reminder import get_spcpm_timeline_and_people, format_spcpm_timeline_message
                from .seatalk_group_manager import simplify_jira_error
                
                timeline, people, ticket_keys = get_spcpm_timeline_and_people(jira_key)
                
                # 检查是否成功获取到时间线数据
                if not timeline:
                    return {'type': 'error', 'message': f"Failed to get timeline for {jira_key}. Please check if the issue exists and you have permission to access it."}
                
                msg = format_spcpm_timeline_message(jira_key, timeline)
                return {'type': 'success', 'message': msg}
            except Exception as e:
                # 简化错误信息
                error_msg = simplify_jira_error(str(e)) if 'HTTP 401' in str(e) else str(e)
                return {'type': 'error', 'message': f"Error retrieving timeline information: {error_msg}"}
        
        # 原有逻辑
        try:
            from .views import get_timeline_of_epic
            from .seatalk_group_manager import simplify_jira_error
            
            timeline_info = get_timeline_of_epic(jira_key)
            return {'type': 'success', 'message': timeline_info}
        except Exception as e:
            # 简化错误信息
            error_msg = simplify_jira_error(str(e)) if 'HTTP 401' in str(e) else str(e)
            return {'type': 'error', 'message': f"Error retrieving timeline information: {error_msg}"}
    
    def _process_new_group_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """处理创建群组命令"""
        jira_key = context.get_jira_key_with_context(command_text)
        
        if not jira_key:
            if context.has_context:
                message = f"未找到JIRA单号，请使用格式：@{bot_config.BOT_NAME} new group SP**-XXXXX"
            else:
                message = "私聊模式下请提供完整格式：new group SPCB-1234"
            return {'type': 'error', 'message': message}
        
        # 获取用户邮箱 - 优先使用seatalk_user_manager
        user_email = None
        try:
            from app01.ai_module.seatalk_user_manager import seatalk_user_manager
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                user_email = loop.run_until_complete(seatalk_user_manager.get_user_email(context.raw_data, context.employee_code))
                ic(f"🎯 用户邮箱获取结果: {user_email}")
            finally:
                loop.close()
        except Exception as e:
            ic(f"⚠️ 用户邮箱获取失败，使用fallback: {str(e)}")
            # 使用原有的fallback逻辑
            if context.employee_code:
                user_email = f"{context.employee_code}@shopee.com"
        
        # 如果无法获取用户邮箱，拒绝请求
        if not user_email:
            return {'type': 'error', 'message': "无法识别您的身份，建群功能会影响较多人员，只有白名单用户才能使用，请联系管理员: <EMAIL>"}
        
        # 项目权限映射
        project_permissions = {
            "SPCB": ["<EMAIL>", "<EMAIL>"],
            "SPCT": ["<EMAIL>", "<EMAIL>"],
            "SPCPM": ["<EMAIL>","<EMAIL>"],
            "SPB": ["<EMAIL>","<EMAIL>"],
            # 超级管理员，可以创建任何项目的群组
            "SUPER_ADMIN": ["<EMAIL>"]
        }
        
        # 提取项目前缀
        project_prefix = jira_key.split('-')[0]
        
        # 检查是否是超级管理员
        if user_email in project_permissions.get("SUPER_ADMIN", []):
            ic(f"超级管理员 {user_email} 创建群组: {jira_key}")
            # 超级管理员可以创建任何群组，继续执行
        # 检查项目权限
        elif project_prefix in project_permissions and user_email not in project_permissions[project_prefix]:
            ic(f"用户 {user_email} 尝试创建 {project_prefix} 项目群组，但没有权限")
            return {
                'type': 'error', 
                'message': f"您没有权限为 {project_prefix} 项目创建群组，请联系项目管理员"
            }
        
        # 检查群组是否已存在
        try:
            from .models import SeatalkGroup
            existing_groups = SeatalkGroup.objects.filter(group_name__contains=jira_key)
            if existing_groups.exists():
                return {
                    'type': 'info', 
                    'message': f"已存在包含 {jira_key} 的群组，无需重复创建"
                }
        except Exception as e:
            ic(f"检查现有群组失败: {str(e)}")
        
        # 创建群组
        try:
            result = auto_create_group_for_jira(jira_key)
            
            if result["success"]:
                success_message = f"群组创建成功！\n群组ID: {result['group_id']}"
                if "detail_message" in result:
                    success_message += f"\n\n{result['detail_message']}"
                return {'type': 'success', 'message': success_message}
            else:
                return {'type': 'error', 'message': f"群组创建失败：{result['message']}"}
        except Exception as e:
            return {'type': 'error', 'message': f"群组创建异常: {str(e)}"}
    
    def _process_checklist_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """处理Checklist命令"""
        ic(f"处理checklist命令: {command_text}")
        
        if "SPCB-" not in command_text:
            current_bot = bot_config.get_current_bot_name()
            message = f"缺少jira单号，checklist 确认信息参考格式：\n@{current_bot} SPCB-xxxxx SO\\CM\\CC\\DB\n（其中SO是Signed off，CM是Code Merged，CC是Config Changed，DB是DB Changed的缩写，兼容全小写和全称。输入对应缩写即表示对应项Confirmed,可一次确认单个或多个）"
            return {'type': 'error', 'message': message}
        
        try:
            from jira import JIRA
            from .views import JIRA_TOKEN
            
            jira_key = re.search(r'SPCB-\d+', command_text).group()
            ic(f"检测到JIRA单号: {jira_key}")
            
            jira = JIRA(server='https://jira.shopee.io', token_auth=JIRA_TOKEN)
            issue = jira.issue(jira_key)
            
            results = []
            
            # 处理各种checklist项
            checklist_items = [
                ('SO', 'Signed off', 'so', 'customfield_34003'),
                ('CM', 'Code Merged', 'cm', 'customfield_34002'),
                ('CC', 'Config Changed', 'cc', 'customfield_34001'),
                ('DB', 'DB Changed', 'db', 'customfield_34000')
            ]
            
            # 标准化命令文本，将反斜杠替换为逗号，方便统一处理
            normalized_command = command_text.replace('\\', ',')
            ic(f"标准化后的命令: {normalized_command}")
            
            for short, full, lower, field in checklist_items:
                if short in normalized_command or full in normalized_command or lower in normalized_command:
                    ic(f"检测到checklist项: {short}/{full}")
                    try:
                        issue.update(fields={field: {'value': 'Confirmed'}})
                        results.append(f"{full}，confirmed成功！")
                        ic(f"{full} confirmed成功")
                    except Exception as e:
                        results.append(f"{full}，未能confirmed！请重试或者去jira手动处理")
                        ic(f"{full} confirmed失败: {str(e)}")
            
            if results:
                return {'type': 'success', 'message': '\n'.join(results)}
            else:
                ic("未找到有效的checklist项目")
                return {'type': 'error', 'message': '未找到有效的checklist项目'}
                
        except Exception as e:
            ic(f"处理checklist失败: {str(e)}")
            return {'type': 'error', 'message': f"处理checklist失败: {str(e)}"}
    
    def _process_jira_query_command(self, jira_key: str, context: MessageContext) -> Dict[str, Any]:
        """处理JIRA单号查询命令"""
        try:
            from jira import JIRA
            from .views import JIRA_TOKEN
            
            jira = JIRA(server='https://jira.shopee.io', token_auth=JIRA_TOKEN)
            issue = jira.issue(jira_key)
            
            # 获取所需信息
            summary = issue.fields.summary
            status = issue.fields.status.name
            priority = issue.fields.priority.name if issue.fields.priority else "No Priority"
            assignee = issue.fields.assignee.displayName if issue.fields.assignee else "Unassigned"
            jira_url = f"https://jira.shopee.io/browse/{jira_key}"
            
            # 构建回复消息
            message = f"📋 **{jira_key}：** 【{priority}】 【{status}】 \n"
            message += f"📌 {summary}\n"
            message += f"🔗 {jira_url}\n"
            message += f" 经办人: {assignee}"
            
            return {'type': 'success', 'message': message}
        except Exception as e:
            return {'type': 'error', 'message': f"获取 JIRA 信息失败: {str(e)}"}
    
    def _process_add_service_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """处理添加服务命令"""
        try:
            from .views import extract_string, add_service_checklist
            service_name = extract_string(command_text)
            add_service_checklist(service_name, context.seatalk_id, context.group_id)
            return {'type': 'success', 'message': 'Service添加请求已处理'}
        except Exception as e:
            return {'type': 'error', 'message': f"添加服务失败: {str(e)}"}
    
    def _process_schedule_task_command(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """处理定时任务命令"""
        try:
            from app01.ai_module.ai_assistant import AIAssistant
            import asyncio
            
            # 创建AI助手实例
            assistant = AIAssistant()
            
            # 确定用户信息
            user_id = getattr(context, 'seatalk_id', None)  # 使用 seatalk_id 作为 user_id
            user_email = getattr(context, 'email', None)    # 使用 email 字段
            group_id = getattr(context, 'group_id', None)
            
            ic(f"🤖 处理定时任务命令 - 用户: {user_id}, 邮箱: {user_email}, 群组: {group_id}")
            
            # 构建extracted_info和group_info参数
            extracted_info = {
                'intent': 'schedule_management',
                'schedule_request': True
            }
            
            group_info = {}
            if group_id:
                group_info['group_id'] = group_id
            if hasattr(context, 'group_name') and context.group_name:
                group_info['group_name'] = context.group_name
            
            # 使用同步方式调用异步函数
            try:
                # 检查当前是否已有运行中的事件循环
                try:
                    current_loop = asyncio.get_running_loop()
                    import concurrent.futures
                    
                    # 在新线程中运行异步代码
                    def run_async():
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            result = new_loop.run_until_complete(
                                assistant._handle_schedule_management(
                                    extracted_info=extracted_info,
                                    user_query=command_text,
                                    user_email=user_email,
                                    user_id=user_id,
                                    group_info=group_info
                                )
                            )
                            return result
                        finally:
                            new_loop.close()
                    
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_async)
                        result = future.result(timeout=30)  # 30秒超时
                        
                except RuntimeError:
                    # 没有运行中的事件循环，可以直接使用run_until_complete
                    loop = asyncio.get_event_loop()
                    result = loop.run_until_complete(
                        assistant._handle_schedule_management(
                            extracted_info=extracted_info,
                            user_query=command_text,
                            user_email=user_email,
                            user_id=user_id,
                            group_info=group_info
                        )
                    )
                
                if result and result.get('success', False):
                    # 提取详细的响应信息
                    response_message = result.get('response', '')
                    
                    # 如果有详细响应，直接使用
                    if response_message and response_message != '定时任务处理完成':
                        return {
                            'type': 'info',
                            'message': response_message
                        }
                    
                    # 如果响应为空或太简单，构建详细信息
                    task_info = []
                    if result.get('task_id'):
                        task_info.append(f"📋 任务ID: {result['task_id']}")
                    if result.get('task_name'):
                        task_info.append(f"🏷️ 任务名: {result['task_name']}")
                    if result.get('schedule_info'):
                        task_info.append(f"⏰ 调度: {result['schedule_info']}")
                    
                    if task_info:
                        response_message = f"✅ **定时任务创建成功**\n\n" + "\n".join(task_info)
                        response_message += f"\n\n💡 使用 `schedule list` 查看所有任务"
                    else:
                        response_message = "✅ 定时任务创建成功"
                    
                    return {
                        'type': 'info',
                        'message': response_message
                    }
                else:
                    error_msg = result.get('error', '定时任务处理失败') if result else '定时任务处理失败'
                    return {
                        'type': 'error',
                        'message': f"❌ 定时任务处理失败: {error_msg}"
                    }
                    
            except Exception as e:
                ic(f"定时任务处理异常: {str(e)}")
                return {
                    'type': 'error',
                    'message': f"定时任务处理异常: {str(e)}"
                }
                
        except Exception as e:
            ic(f"定时任务命令处理失败: {str(e)}")
            return {
                'type': 'error',
                'message': f"定时任务命令处理失败: {str(e)}"
            }
    
    def _process_spcpm_general_help(self) -> Dict[str, Any]:
        """处理通用SPCPM帮助信息"""
        help_message = "SPCPM Commands:\n\n"
        help_message += f"1. View: `@{bot_config.BOT_NAME} timeline SPCPM-XXXXX` or `tr SPCPM-XXXXX`\n"
        help_message += f"2. Mention: `@{bot_config.BOT_NAME} [all|qa|dev|pm|pj|fe|be] SPCPM-XXXXX`\n"
        help_message += "3. Reminders: `tr+ SPCPM-XXXXX` (add), `tr- SPCPM-XXXXX` (remove), `tr list` (list all)\n"
        
        return {'type': 'info', 'message': help_message}

    def _generate_help_response(self, context: MessageContext, user_query: str = "") -> Dict[str, Any]:
        """生成帮助信息"""
        try:
            # 优先使用新的LLM帮助系统
            from app01.llm_help_system.llm_help_manager import llm_help_manager

            # 获取用户信息
            user_email = getattr(context, 'user_email', '')
            query = user_query or context.text or ""

            # 使用asyncio运行异步方法
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                help_message = loop.run_until_complete(
                    llm_help_manager.generate_help_response(
                        user_query=query,
                        context_type=context.type,
                        user_email=user_email,
                        user_role='normal'  # 默认普通用户，会根据邮箱自动调整
                    )
                )

                if help_message:
                    return {'type': 'help', 'message': help_message}
            finally:
                loop.close()

        except ImportError:
            # 如果LLM帮助系统不可用，尝试使用兼容性适配器
            try:
                from app01.llm_help_system.compatibility_adapter import help_intent_enhancer

                # 构建上下文信息
                help_context = {
                    'type': context.type,
                    'group_name': getattr(context, 'group_name', ''),
                    'user_id': getattr(context, 'user_id', ''),
                }

                # 生成上下文相关的帮助信息
                help_message = help_intent_enhancer.generate_contextual_help(
                    user_query=user_query or context.text or "",
                    context=help_context,
                    user_role='normal'  # 默认普通用户
                )

                if help_message:
                    return {'type': 'help', 'message': help_message}

            except ImportError:
                pass

        except Exception as e:
            logger.error(f"LLM帮助系统调用失败: {str(e)}")

        # 备用方案：使用传统帮助信息
        user_email = getattr(context, 'user_email', '')
        help_message = bot_config.generate_help_message(
            context_type=context.type,
            user_query=user_query or context.text or "",
            user_email=user_email
        )
        return {'type': 'help', 'message': help_message}

    def _process_spcpm_help(self, request_id: str) -> Dict[str, Any]:
        """处理SPCPM相关的帮助信息"""
        help_message = f"SPCPM Commands for {request_id}:\n\n"
        help_message += f"• View timeline: `@{bot_config.BOT_NAME} timeline {request_id}` or `tr {request_id}`\n"
        help_message += f"• Mention team: `@{bot_config.BOT_NAME} [all|qa|dev|pm|pj|fe|be] {request_id}`\n"
        help_message += f"• Add reminder: `tr+ {request_id}` or `add {request_id} timeline reminder`\n"
        help_message += f"• Remove reminder: `tr- {request_id}` or `remove {request_id} timeline reminder`\n"
        
        return {'type': 'info', 'message': help_message}

    def _process_add_spcpm_timeline_reminder(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """添加SPCPM项目到timeline reminder列表"""
        import re
        from .models import SPCPMTimelineReminder

        match = re.search(r'(spcpm-\d+)', command_text.lower())
        if not match:
            return {'type': 'error', 'message': 'SPCPM Request ID not recognized. Please use format:\n1. add SPCPM-xxxxxx timeline reminder\n2. tr+ SPCPM-xxxxxx'}

        request_id = match.group(1).upper()
        created_by = context.employee_code or ''
        group_id = context.group_id if context.type == 'group' else None

        if SPCPMTimelineReminder.objects.filter(request_id=request_id).exists():
            reminder = SPCPMTimelineReminder.objects.get(request_id=request_id)
            if group_id:
                reminder.group_id = group_id
                reminder.save()
                return {'type': 'info', 'message': f'{request_id} is already in the timeline reminder list, updated group ID'}
            return {'type': 'info', 'message': f'{request_id} is already in the timeline reminder list'}

        SPCPMTimelineReminder.objects.create(
            request_id=request_id,
            created_by=created_by,
            group_id=group_id
        )
        return {'type': 'success', 'message': f'{request_id} has been added to the timeline reminder list'}

    def _process_remove_spcpm_timeline_reminder(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """从timeline reminder列表移除SPCPM项目"""
        import re
        from .models import SPCPMTimelineReminder
        match = re.search(r'(spcpm-\d+)', command_text.lower())
        if not match:
            return {'type': 'error', 'message': 'SPCPM Request ID not recognized. Please use format:\n1. remove SPCPM-xxxxxx timeline reminder\n2. tr- SPCPM-xxxxxx'}

        request_id = match.group(1).upper()
        deleted, _ = SPCPMTimelineReminder.objects.filter(request_id=request_id).delete()
        if deleted:
            return {'type': 'success', 'message': f'{request_id} has been removed from the timeline reminder list'}
        else:
            return {'type': 'info', 'message': f'{request_id} is not in the timeline reminder list'}

    def _process_list_spcpm_timeline_reminder(self, context: MessageContext) -> Dict[str, Any]:
        """列出所有SPCPM timeline reminder项目"""
        from .models import SPCPMTimelineReminder
        reminders = SPCPMTimelineReminder.objects.all().order_by('-created_at')
        if not reminders:
            return {'type': 'info', 'message': 'There are no SPCPM projects in the timeline reminder list'}
        msg = 'SPCPM Timeline Reminder Projects:\n' + '\n'.join([f"{r.request_id}" for r in reminders])
        return {'type': 'success', 'message': msg}

    def _process_mention_spcpm_roles(self, command_text: str, context: MessageContext) -> Dict[str, Any]:
        """@SPCPM项目的指定角色人员"""
        import re
        from .spcpm_timeline_reminder import get_spcpm_timeline_and_people, SPCPM_PEOPLE_FIELDS
        spcpm_match = re.search(r'(spcpm-\d+)', command_text.lower())
        if not spcpm_match:
            return {'type': 'error', 'message': f'SPCPM Request ID not recognized. Please use format: @{bot_config.BOT_NAME} all/qa/dev/pm/pj SPCPM-23678'}
        request_id = spcpm_match.group(1).upper()
        role_mappings = {
            'developer': ['Developer'],
            'dev': ['Developer'],
            'qa': ['QA', 'QA List'],
            'fe': ['FE List'],
            'be': ['BE List'],
            'pm': ['Product Manager'],
            'pj': ['Project Manager'],
            'assignee': ['Assignee'],
            'all': list(SPCPM_PEOPLE_FIELDS.values())
        }
        roles = []
        for role_key in role_mappings.keys():
            if re.search(rf'\b{role_key}\b', command_text.lower()):
                roles.extend(role_mappings[role_key])
        if not roles:
            return self._process_spcpm_help(request_id)
        try:
            timeline, people, _ = get_spcpm_timeline_and_people(request_id)
            all_emails = set()
            for role in roles:
                if role in people and people[role]:
                    if isinstance(people[role], list):
                        all_emails.update(people[role])
                    else:
                        all_emails.add(people[role])
            if not all_emails:
                return {'type': 'info', 'message': f'No personnel found for the specified role(s) in project {request_id}'}
            msg = f"{request_id} Project Team Members:\n" + ' '.join([f'<mention-tag target="seatalk://user?email={e}"/>' for e in all_emails])
            return {'type': 'success', 'message': msg}
        except Exception as e:
            return {'type': 'error', 'message': f'Failed to get personnel information for project {request_id}: {str(e)}'}

    def _process_spcpm_command(self, command_text: str, context: MessageContext) -> Optional[Dict[str, Any]]:
        """统一处理所有与SPCPM相关的传统指令。
        返回 Dict 表示已处理；返回 None 表示不是 SPCPM 指令。"""
        import re
        command_lower = command_text.lower().strip()

        # 通用 list 命令（不需要单号，任何群均可执行）
        if command_lower.startswith("list timeline reminder") or command_lower.startswith("tr list") or command_lower.startswith("list tr"):
            return self._process_list_spcpm_timeline_reminder(context)

        # 判断群上下文
        is_pure_spcpm, spcpm_id_from_group = context.is_pure_spcpm_group()

        # tr+ / tr- 简写：添加 / 移除 reminder
        if command_lower.startswith("tr+") or command_lower.startswith("tr-"):
            add_mode = command_lower.startswith("tr+")
            match = re.search(r'spcpm-\d+', command_lower)
            if match:
                return self._process_add_spcpm_timeline_reminder(command_text, context) if add_mode else self._process_remove_spcpm_timeline_reminder(command_text, context)
            elif is_pure_spcpm:
                supplemented = f"{command_text} {spcpm_id_from_group}"
                return self._process_add_spcpm_timeline_reminder(supplemented, context) if add_mode else self._process_remove_spcpm_timeline_reminder(supplemented, context)
            else:
                return {'type': 'error', 'message': 'Please specify SPCPM Request ID, e.g. `tr+ SPCPM-123456`'}

        # add/remove timeline reminder 原始格式
        if (command_lower.startswith("add spcpm-") and "timeline reminder" in command_lower) or \
           (command_lower.startswith("add timeline reminder") and "spcpm-" in command_lower):
            return self._process_add_spcpm_timeline_reminder(command_text, context)
        if (command_lower.startswith("remove spcpm-") and "timeline reminder" in command_lower):
            return self._process_remove_spcpm_timeline_reminder(command_text, context)

        # 在纯SPCPM群使用 "add timeline reminder"
        if command_lower == "add timeline reminder" and is_pure_spcpm:
            supplemented = f"add {spcpm_id_from_group} timeline reminder"
            return self._process_add_spcpm_timeline_reminder(supplemented, context)

        # SPCPM 时间线查看：tr SPCPM-xxx 或 timeline/tl + SPCPM
        if command_lower.startswith("tr spcpm-"):
            return self._process_timeline_command(command_text, context)

        has_spcpm = re.search(r'spcpm-\d+', command_lower) is not None
        if ("timeline" in command_lower or "tl" in command_lower) and (has_spcpm or is_pure_spcpm):
            return self._process_timeline_command(command_text, context)

        # 角色 @ mention
        role_keywords = ['all', 'qa', 'dev', 'pm', 'pj', 'fe', 'be', 'developer', 'assignee']
        if any(re.search(rf'\b{role}\b', command_lower) for role in role_keywords):
            if has_spcpm or is_pure_spcpm:
                # 若无显式单号且为纯SPCPM群，则补单号
                if not has_spcpm and is_pure_spcpm:
                    command_text = f"{command_text} {spcpm_id_from_group}"
                return self._process_mention_spcpm_roles(command_text, context)

        # 输入仅为 SPCPM-xxxxx 或纯 SPCPM 群的无操作情况 -> 帮助
        if has_spcpm and len(command_lower.split()) <= 2:
            return self._process_spcpm_help(re.search(r'(spcpm-\d+)', command_lower).group(1).upper())
        if is_pure_spcpm and len(command_lower.split()) <= 2:
            return self._process_spcpm_help(spcpm_id_from_group)

        # spcpm 关键词帮助
        if command_lower in ["spcpm", "spcpm帮助", "spcpm help"]:
            return self._process_spcpm_general_help()

        # 如果以上均不匹配，则认为不是 SPCPM 指令
        return None

    def _process_ai_command(self, ai_query: str, context: MessageContext) -> Dict[str, Any]:
        """处理 AI 自然语言查询指令，调用 views.handle_ai_query 并包装结果"""
        try:
            from .views import handle_ai_query
            from django.http import JsonResponse
            from .seatalk_group_manager import send_message_to_group
            from app01.statistics.decorators import track_command_execution, create_command_tracker
            
            # 使用统计跟踪器记录AI指令执行
            with create_command_tracker(
                user_id=context.seatalk_id,
                command_type='ai_query',
                raw_input=ai_query,
                user_email=getattr(context, 'email', None),
                employee_code=getattr(context, 'employee_code', None),
                group_id=getattr(context, 'group_id', None),
                intent='natural_language_query'
            ) as tracker:
                # 添加备用发送机制，确保即使AI处理过程中出现错误，也能向用户发送回复
                try:
                    response = handle_ai_query(ai_query, context.seatalk_id, context.group_id, context.raw_data)
                    
                    # 如果是JsonResponse对象，提取其内容
                    if isinstance(response, JsonResponse):
                        ic("检测到JsonResponse对象，直接返回")
                        result = {
                            'type': 'ai_response',
                            'handled': True,
                            'response': response  # 保留原始响应对象
                        }
                        # 更新跟踪器结果
                        tracker.update_result(
                            response_content=str(response),
                            processed_command=ai_query
                        )
                        return result
                    else:
                        # 如果是字符串，直接使用
                        result = {
                            'type': 'success',
                            'handled': True,
                            'message': response
                        }
                        # 更新跟踪器结果
                        tracker.update_result(
                            response_content=response,
                            processed_command=ai_query
                        )
                        return result
                except Exception as inner_e:
                    ic(f"AI命令处理内部错误: {str(inner_e)}")
                    
                    # 更新跟踪器错误结果
                    tracker.update_result(
                        response_content=f"错误: {str(inner_e)}",
                        processed_command=ai_query
                    )
                    
                    # 尝试直接发送错误信息给用户
                    error_message = "🤖 **AI服务暂时不可用**\n\n系统正在处理您的请求时遇到了问题。\n\n💡 **建议操作：**\n• 等待1-2分钟后重新发送相同消息\n• 检查消息格式是否正确\n• 尝试简化请求内容"
                    
                    try:
                        if context.type == 'group' and context.group_id:
                            send_message_to_group(context.group_id, error_message)
                        # 私聊错误处理在外层异常中处理
                    except Exception:
                        pass
                    
                    # 重新抛出异常，让外层捕获
                    raise inner_e
                
        except Exception as e:
            ic(f"AI命令处理失败: {str(e)}")
            
            # 错误信息
            friendly_error = "AI功能暂时不可用，请稍后重试"
            
            # 尝试发送私聊错误信息
            if context.type == 'private' and context.employee_code:
                try:
                    from .views import sent_aio
                    sent_aio(friendly_error, context.employee_code)
                except:
                    pass
                    
            return {
                'type': 'error',
                'message': friendly_error
            }
            
    def _find_best_command_match(self, command_text: str, context: MessageContext) -> Optional[str]:
        """
        查找最佳匹配的指令模式，只匹配严格的传统固定指令格式
        
        Args:
            command_text: 命令文本
            context: 消息上下文
            
        Returns:
            最佳匹配的指令名称，如果没有匹配返回None
        """
        if not command_text:
            return None
            
        # 特殊处理: 检查是否是添加服务的指令
        if (command_text.lower().startswith('add ') or 
            command_text.startswith('添加') or 
            command_text.startswith('新增') or 
            command_text.startswith('增加')) and not any(keyword in command_text.lower() for keyword in ['subtask', '子任务', 'reminder', 'schedule', 'timeline']):
            # 检查是否是添加服务的模式
            service_name_match = re.match(r'^add\s+([\w\-\.]+)', command_text)
            # 检查中文添加服务指令
            if service_name_match:
                ic(f"检测到可能的服务添加指令: '{command_text}'")
                return 'add_service'
            elif any(keyword in command_text for keyword in ['服务', '项目']):
                ic(f"检测到可能的中文服务添加指令: '{command_text}'")
                return 'add_service'
        
        # 定义严格的传统指令模式
        patterns = {
            'bug_query': CommandPattern(
                'bug_query',
                patterns=[
                    r'^bug\s+[A-Z]+-\d+$',     # "bug SPCB-1234"
                    r'^[A-Z]+-\d+\s+bug$',     # "SPCB-1234 bug"
                    r'^bug$',                    # 纯"bug"（需要上下文）
                    r'^缺陷\s+[A-Z]+-\d+$',   # "缺陷 SPCB-1234"
                    r'^[A-Z]+-\d+\s+缺陷$'    # "SPCB-1234 缺陷"
                ],
                context_aware=True,
                priority=3,
                exclude_patterns=[
                    r'每[天周月]',
                    r'定时',
                    r'创建',
                    r'新建',
                    r'建单',
                    r'建子任务', 
                    r'add\s+subtask',
                    r'schedule',
                    r'linkedissues',
                    r'提醒',
                    r'通知'
                ]
            ),
            'timeline_query': CommandPattern(
                'timeline_query',
                patterns=[
                    r'timeline\s+[A-Z]+-\d+',  # "timeline SPCB-1234"
                    r'[A-Z]+-\d+\s+timeline',  # "SPCB-1234 timeline"
                    r'^tl\s+[A-Z]+-\d+',       # "tl SPCB-1234"
                    r'^timeline$',               # 纯"timeline"（需要上下文）
                    r'^tl$'                      # 纯"tl"（需要上下文）
                ],
                context_aware=True,
                priority=2,
                exclude_patterns=[
                    r'每[天周月]',
                    r'定时',
                    r'提醒',
                    r'通知'
                ]
            ),
            'mr_check': CommandPattern(
                'mr_check',
                patterns=[
                    r'^chatbot\s+mr$',
                    r'^mr\s+chatbot$',
                    r'^mr$',
                    r'^MR$'
                ],
                priority=3
            ),
            'new_group': CommandPattern(
                'new_group',
                patterns=[
                    r'new\s+group\s+[A-Z]+-\d+',
                    r'[A-Z]+-\d+\s+new\s+group',
                    r'^创建群\s+[A-Z]+-\d+$',
                    r'^新建群\s+[A-Z]+-\d+$',
                    r'^建群\s+[A-Z]+-\d+$'
                ],
                priority=3
            ),
            'checklist': CommandPattern(
                'checklist',
                patterns=[
                    r'SPCB-\d+\s+[SOCMDBsocmdb\\,]+'
                ],
                priority=2,
                exclude_patterns=[
                    r'bug'  # 添加排除模式，避免与bug_query冲突
                ]
            ),
            'jira_query': CommandPattern(
                'jira_query',
                patterns=[
                    r'^[A-Z]+-\d+$'  # 纯JIRA单号
                ],
                priority=1
            ),
            'add_service': CommandPattern(
                'add_service',
                patterns=[
                    r'add\s+service',
                    r'service\s+add',
                    r'^add\s+[\w\-\.]+',  # 添加这个模式，匹配以add开头后跟服务名称的模式
                    r'^add\s+shopee-[\w\-\.]+',  # 更具体的匹配shopee服务的模式
                    '添加服务', '新增服务', '增加服务', '添加项目', '新增项目', '增加项目'  # 保留原有中文关键词
                ],
                priority=2,
                exclude_patterns=[
                    r'reminder',
                    r'schedule',
                    r'todo',
                    r'todo list',
                ]
            )
        }
        
        # 计算每个模式的匹配分数
        best_match = None
        best_score = 0
        all_scores = {}
        
        for name, pattern in patterns.items():
            ic(f"尝试匹配模式: {pattern.name}")
            score = pattern.match(command_text, context)
            all_scores[name] = score
            
            if score > best_score:
                best_match = name
                best_score = score
        
        ic(f"指令匹配结果 - 命令: '{command_text}', 最佳匹配: {best_match}, 分数: {best_score}")
        ic(f"所有匹配分数: {all_scores}")
        
        # 设置一个最低匹配阈值，确保只有明确的传统指令才匹配
        if best_score < 0.5:  # 降低阈值，让更多指令能够匹配
            ic(f"最佳分数 {best_score} 低于阈值 0.5，返回None，交给AI处理")
            return None
        
        return best_match
        
    def test_command_match(self, command_text: str, group_name: str = None) -> Dict[str, Any]:
        """
        测试指令匹配系统
        
        Args:
            command_text: 指令文本
            group_name: 群名（可选）
            
        Returns:
            Dict: 测试结果
        """
        try:
            # 创建一个简单的模拟上下文对象
            class MockContext:
                def __init__(self, group_name=None):
                    self.group_name = group_name
                    self.type = 'group' if group_name else 'private'
                    self.has_context = group_name is not None
                
                def extract_jira_keys(self, text=None):
                    search_text = text or ""
                    pattern = r'SP[A-Z]+-\d{1,6}'
                    return re.findall(pattern, search_text)
            
            # 创建模拟上下文
            mock_context = MockContext(group_name)
            
            # 测试匹配
            ic(f"测试指令匹配 - 命令: '{command_text}', 群名: '{group_name}'")
            best_match = self._find_best_command_match(command_text, mock_context)
            
            # 收集所有匹配分数
            all_scores = {}
            for pattern in self.command_patterns:
                score = pattern.match(command_text, mock_context)
                if score > 0:
                    all_scores[pattern.name] = score
            
            # 返回测试结果
            return {
                "command": command_text,
                "group_name": group_name,
                "best_match": best_match,
                "all_scores": all_scores
            }
        except Exception as e:
            ic(f"测试异常: {str(e)}")
            import traceback
            ic(traceback.format_exc())
            return {
                "command": command_text,
                "group_name": group_name,
                "best_match": None,
                "all_scores": {},
                "error": str(e)
            }


# 单例实例
command_processor = CommandProcessor()
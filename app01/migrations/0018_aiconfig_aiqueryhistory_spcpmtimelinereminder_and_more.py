# Generated by Django 5.2.3 on 2025-07-03 10:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0017_calendarjirareleaselist'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'AI配置',
                'verbose_name_plural': 'AI配置',
                'db_table': 'ai_config',
            },
        ),
        migrations.CreateModel(
            name='AIQueryHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100)),
                ('group_id', models.CharField(blank=True, max_length=100, null=True)),
                ('employee_code', models.CharField(blank=True, max_length=100, null=True)),
                ('query', models.TextField()),
                ('intent', models.CharField(max_length=50)),
                ('jql', models.TextField()),
                ('result_count', models.IntegerField(default=0)),
                ('success', models.BooleanField(default=False)),
                ('processing_time', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'AI查询历史',
                'verbose_name_plural': 'AI查询历史',
                'db_table': 'ai_query_history',
            },
        ),
        migrations.CreateModel(
            name='SPCPMTimelineReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_id', models.CharField(help_text='SPCPM Request ID，如SPCPM-123456', max_length=32, unique=True)),
                ('created_by', models.CharField(blank=True, help_text='添加人employee_code', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'SPCPM里程碑提醒项目',
                'verbose_name_plural': 'SPCPM里程碑提醒项目',
                'db_table': 'spcpm_timeline_reminder',
            },
        ),
        migrations.CreateModel(
            name='TodoCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='分类名称', max_length=50, unique=True)),
                ('description', models.CharField(blank=True, help_text='分类描述', max_length=200)),
                ('color', models.CharField(default='#6B7280', help_text='分类颜色', max_length=7)),
                ('is_active', models.BooleanField(default=True, help_text='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': '代办事项分类',
                'verbose_name_plural': '代办事项分类',
                'db_table': 'todo_category',
            },
        ),
        migrations.CreateModel(
            name='UserConversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100)),
                ('group_id', models.CharField(blank=True, max_length=100, null=True)),
                ('employee_code', models.CharField(blank=True, max_length=100, null=True)),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('context', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '用户会话',
                'verbose_name_plural': '用户会话',
                'db_table': 'user_conversation',
            },
        ),
        migrations.CreateModel(
            name='UserJiraToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_email', models.EmailField(max_length=254, unique=True)),
                ('jira_token', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': '用户JIRA Token',
                'verbose_name_plural': '用户JIRA Token',
                'db_table': 'user_jira_token',
            },
        ),
        migrations.AlterModelOptions(
            name='seatalkgroup',
            options={'verbose_name': 'Seatalk群组', 'verbose_name_plural': 'Seatalk群组'},
        ),
        migrations.CreateModel(
            name='AdvancedTaskFeatureWhitelist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100)),
                ('user_email', models.CharField(blank=True, max_length=200, null=True)),
                ('employee_code', models.CharField(blank=True, max_length=100, null=True)),
                ('allowed_features', models.JSONField(default=list)),
                ('max_tasks', models.IntegerField(default=20)),
                ('max_templates', models.IntegerField(default=10)),
                ('is_active', models.BooleanField(default=True)),
                ('granted_by', models.CharField(blank=True, max_length=100, null=True)),
                ('granted_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'advanced_task_feature_whitelist',
                'indexes': [models.Index(fields=['user_id', 'is_active'], name='advanced_ta_user_id_fba6dd_idx'), models.Index(fields=['expires_at'], name='advanced_ta_expires_a0b56c_idx')],
                'unique_together': {('user_id',)},
            },
        ),
        migrations.CreateModel(
            name='ConditionalTrigger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100)),
                ('trigger_name', models.CharField(max_length=200)),
                ('trigger_type', models.CharField(choices=[('jira_event', 'JIRA事件'), ('time_based', '时间条件'), ('status_change', '状态变化'), ('field_change', '字段变化')], max_length=50)),
                ('condition_field', models.CharField(max_length=100)),
                ('condition_operator', models.CharField(choices=[('equals', '等于'), ('not_equals', '不等于'), ('contains', '包含'), ('greater_than', '大于'), ('less_than', '小于'), ('in_list', '在列表中')], max_length=50)),
                ('condition_value', models.TextField()),
                ('action_query', models.TextField()),
                ('notification_message', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('last_triggered', models.DateTimeField(blank=True, null=True)),
                ('trigger_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'conditional_trigger',
                'indexes': [models.Index(fields=['user_id', 'is_active'], name='conditional_user_id_897f73_idx'), models.Index(fields=['trigger_type', 'is_active'], name='conditional_trigger_0410a1_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProcessedMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_id', models.CharField(db_index=True, help_text='事件ID', max_length=100, unique=True)),
                ('message_id', models.CharField(db_index=True, help_text='消息ID', max_length=200)),
                ('seatalk_id', models.CharField(help_text='用户ID', max_length=50)),
                ('group_id', models.CharField(blank=True, help_text='群组ID', max_length=50, null=True)),
                ('query_content', models.TextField(help_text='查询内容')),
                ('processed_at', models.DateTimeField(auto_now_add=True, help_text='处理时间')),
                ('processing_duration', models.FloatField(default=0, help_text='处理耗时(秒)')),
            ],
            options={
                'verbose_name': '已处理消息',
                'verbose_name_plural': '已处理消息',
                'db_table': 'processed_messages',
                'indexes': [models.Index(fields=['event_id'], name='processed_m_event_i_a910e2_idx'), models.Index(fields=['message_id'], name='processed_m_message_3ac814_idx'), models.Index(fields=['processed_at'], name='processed_m_process_c3e35a_idx')],
            },
        ),
        migrations.CreateModel(
            name='TaskExecutionStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100)),
                ('date', models.DateField()),
                ('total_executions', models.IntegerField(default=0)),
                ('successful_executions', models.IntegerField(default=0)),
                ('failed_executions', models.IntegerField(default=0)),
                ('avg_execution_time', models.FloatField(default=0.0)),
                ('max_execution_time', models.FloatField(default=0.0)),
                ('min_execution_time', models.FloatField(default=0.0)),
                ('active_tasks_count', models.IntegerField(default=0)),
                ('paused_tasks_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'task_execution_statistics',
                'indexes': [models.Index(fields=['user_id', 'date'], name='task_execut_user_id_f8b42e_idx'), models.Index(fields=['date'], name='task_execut_date_ae49ae_idx')],
                'unique_together': {('user_id', 'date')},
            },
        ),
        migrations.CreateModel(
            name='TaskTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(choices=[('daily', '日常任务'), ('weekly', '周报相关'), ('monthly', '月度统计'), ('project', '项目管理'), ('bug_tracking', 'Bug跟踪'), ('custom', '自定义')], default='custom', max_length=50)),
                ('query_template', models.TextField()),
                ('default_schedule', models.CharField(max_length=100)),
                ('template_variables', models.JSONField(default=dict)),
                ('is_public', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.CharField(blank=True, max_length=100, null=True)),
                ('usage_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'task_template',
                'indexes': [models.Index(fields=['category', 'is_active'], name='task_templa_categor_cf8a39_idx'), models.Index(fields=['is_public', 'is_active'], name='task_templa_is_publ_4fc0ff_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserScheduledTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(max_length=100)),
                ('user_email', models.CharField(blank=True, max_length=200, null=True)),
                ('employee_code', models.CharField(blank=True, max_length=100, null=True)),
                ('task_name', models.CharField(max_length=200)),
                ('task_description', models.TextField(blank=True, null=True)),
                ('query_text', models.TextField()),
                ('frequency', models.CharField(choices=[('daily', '每天'), ('weekly', '每周'), ('monthly', '每月'), ('custom', '自定义')], default='daily', max_length=20)),
                ('schedule_time', models.TimeField()),
                ('schedule_days', models.JSONField(blank=True, default=list, null=True)),
                ('timezone', models.CharField(default='Asia/Singapore', max_length=50)),
                ('notification_type', models.CharField(choices=[('private', '私聊'), ('group', '群聊'), ('both', '私聊+群聊')], default='private', max_length=20)),
                ('target_group_id', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', '激活'), ('paused', '暂停'), ('disabled', '禁用')], default='active', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('total_executions', models.IntegerField(default=0)),
                ('successful_executions', models.IntegerField(default=0)),
                ('last_execution', models.DateTimeField(blank=True, null=True)),
                ('next_execution', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'user_scheduled_task',
                'indexes': [models.Index(fields=['user_id', 'status'], name='user_schedu_user_id_03fc26_idx'), models.Index(fields=['next_execution', 'is_active'], name='user_schedu_next_ex_594d39_idx'), models.Index(fields=['schedule_time'], name='user_schedu_schedul_e5bafa_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserServiceUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(help_text='用户ID', max_length=100)),
                ('user_email', models.CharField(blank=True, help_text='用户邮箱', max_length=200, null=True)),
                ('employee_code', models.CharField(blank=True, help_text='员工代码', max_length=100, null=True)),
                ('group_id', models.CharField(blank=True, help_text='群组ID', max_length=100, null=True)),
                ('service_type', models.CharField(choices=[('ai_query', 'AI查询'), ('jira_query', 'JIRA查询'), ('jira_write', 'JIRA写操作'), ('document_processing', '文档处理'), ('schedule_management', '定时任务'), ('todo_management', '代办事项'), ('statistics', '统计分析'), ('traditional_command', '传统命令')], help_text='服务类型', max_length=30)),
                ('intent', models.CharField(blank=True, help_text='识别的意图', max_length=50, null=True)),
                ('query_content', models.TextField(help_text='查询内容')),
                ('success', models.BooleanField(default=False, help_text='是否成功')),
                ('response_length', models.IntegerField(default=0, help_text='响应长度')),
                ('processing_time', models.FloatField(default=0.0, help_text='处理时间(秒)')),
                ('error_message', models.TextField(blank=True, help_text='错误信息')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='额外元数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='使用时间')),
            ],
            options={
                'verbose_name': '用户服务使用记录',
                'verbose_name_plural': '用户服务使用记录',
                'db_table': 'user_service_usage',
                'indexes': [models.Index(fields=['user_id', 'service_type'], name='user_servic_user_id_d58129_idx'), models.Index(fields=['user_id', 'created_at'], name='user_servic_user_id_0498a4_idx'), models.Index(fields=['service_type', 'created_at'], name='user_servic_service_ff57df_idx'), models.Index(fields=['success', 'created_at'], name='user_servic_success_d9123a_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserTodoItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(help_text='用户ID', max_length=100)),
                ('user_email', models.CharField(blank=True, help_text='用户邮箱', max_length=200, null=True)),
                ('employee_code', models.CharField(blank=True, help_text='员工代码', max_length=100, null=True)),
                ('title', models.CharField(help_text='标题', max_length=300)),
                ('description', models.TextField(blank=True, help_text='详细描述')),
                ('category', models.CharField(choices=[('personal', '个人'), ('work', '工作'), ('jira', 'JIRA'), ('meeting', '会议')], default='personal', help_text='分类', max_length=20)),
                ('priority', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('urgent', '紧急')], default='medium', help_text='优先级', max_length=20)),
                ('status', models.CharField(choices=[('pending', '待办'), ('in_progress', '进行中'), ('completed', '已完成'), ('cancelled', '已取消')], default='pending', help_text='状态', max_length=20)),
                ('due_date', models.DateField(blank=True, help_text='截止日期', null=True)),
                ('completed_at', models.DateTimeField(blank=True, help_text='完成时间', null=True)),
                ('completion_note', models.TextField(blank=True, help_text='完成备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='更新时间')),
            ],
            options={
                'verbose_name': '用户代办事项',
                'verbose_name_plural': '用户代办事项',
                'db_table': 'user_todo_item',
                'indexes': [models.Index(fields=['user_id', 'status'], name='user_todo_i_user_id_2515c7_idx'), models.Index(fields=['user_id', 'due_date'], name='user_todo_i_user_id_633756_idx'), models.Index(fields=['user_id', 'priority'], name='user_todo_i_user_id_c9865f_idx'), models.Index(fields=['created_at'], name='user_todo_i_created_d31fdc_idx')],
            },
        ),
        migrations.CreateModel(
            name='TaskExecutionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_time', models.DateTimeField(auto_now_add=True)),
                ('success', models.BooleanField(default=False)),
                ('query_result', models.JSONField(blank=True, null=True)),
                ('response_text', models.TextField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('execution_duration', models.FloatField(blank=True, null=True)),
                ('result_count', models.IntegerField(default=0)),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='execution_logs', to='app01.userscheduledtask')),
            ],
            options={
                'db_table': 'task_execution_log',
                'indexes': [models.Index(fields=['task', 'execution_time'], name='task_execut_task_id_ebb8ab_idx'), models.Index(fields=['success'], name='task_execut_success_d48ba3_idx')],
            },
        ),
    ]

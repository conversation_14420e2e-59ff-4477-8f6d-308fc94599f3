# Generated manually

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0024_add_statistics_models'),
    ]

    operations = [
        # 添加任务类型字段
        migrations.AddField(
            model_name='userscheduledtask',
            name='task_type',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('jira_query', 'JIRA查询任务'),
                    ('reminder', '提醒任务'),
                ],
                default='jira_query',
                help_text='任务类型：JIRA查询任务或纯提醒任务'
            ),
        ),
        
        # 添加提醒消息字段
        migrations.AddField(
            model_name='userscheduledtask',
            name='reminder_message',
            field=models.TextField(
                null=True,
                blank=True,
                help_text='提醒任务的消息内容'
            ),
        ),
        
        # 修改query_text字段，使其对提醒任务可选
        migrations.AlterField(
            model_name='userscheduledtask',
            name='query_text',
            field=models.TextField(
                null=True,
                blank=True,
                help_text='JIRA查询文本（JIRA查询任务必填，提醒任务可选）'
            ),
        ),
        
        # 添加索引
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userscheduledtask_task_type ON user_scheduled_task(task_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_userscheduledtask_task_type;"
        ),
    ] 
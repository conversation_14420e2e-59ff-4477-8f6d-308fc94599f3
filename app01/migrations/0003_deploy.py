# Generated by Django 4.1 on 2022-09-19 10:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0002_autotest_jenkins_pipeline_delete_stu'),
    ]

    operations = [
        migrations.CreateModel(
            name='Deploy',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('devName', models.Char<PERSON>ield(default=None, max_length=64)),
                ('startTime', models.DateTimeField(auto_now_add=True)),
                ('pipelineURL', models.CharField(default=None, max_length=128)),
                ('projectName', models.Char<PERSON>ield(default=None, max_length=128)),
                ('gitlabProjectURL', models.Char<PERSON>ield(default=None, max_length=128)),
                ('branchType', models.CharField(default=None, max_length=16)),
                ('callbackID', models.<PERSON>r<PERSON><PERSON>(default=None, max_length=16)),
                ('jenkinsDeployResult', models.Char<PERSON>ield(default=None, max_length=16)),
                ('jenkinsAutoTestResult', models.<PERSON>r<PERSON><PERSON>(default=None, max_length=16)),
                ('jenkinsAutoTestID', models.CharField(default=None, max_length=16)),
            ],
        ),
    ]

# Generated by Django 4.1 on 2022-09-20 03:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0004_alter_deploy_callbackid'),
    ]

    operations = [
        migrations.AlterField(
            model_name='deploy',
            name='branchType',
            field=models.CharField(max_length=16, null=True),
        ),
        migrations.AlterField(
            model_name='deploy',
            name='callbackID',
            field=models.CharField(max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name='deploy',
            name='devName',
            field=models.CharField(max_length=64, null=True),
        ),
        migrations.AlterField(
            model_name='deploy',
            name='gitlabProjectURL',
            field=models.CharField(max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name='deploy',
            name='jenkinsAutoTestID',
            field=models.Char<PERSON>ield(max_length=16, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='deploy',
            name='jenkinsAutoTestResult',
            field=models.Char<PERSON>ield(max_length=16, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='deploy',
            name='jenkinsDeployResult',
            field=models.CharField(max_length=16, null=True),
        ),
        migrations.AlterField(
            model_name='deploy',
            name='pipelineURL',
            field=models.CharField(max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name='deploy',
            name='projectName',
            field=models.CharField(max_length=128, null=True),
        ),
    ]

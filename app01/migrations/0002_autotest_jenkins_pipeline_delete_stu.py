# Generated by Django 4.1 on 2022-09-01 16:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutoTest',
            fields=[
                ('startTime', models.DateTimeField(auto_now_add=True)),
                ('env', models.Char<PERSON>ield(max_length=16)),
                ('pipelineURL', models.CharField(max_length=128)),
                ('projectName', models.CharField(max_length=128)),
                ('result', models.Char<PERSON>ield(max_length=16)),
                ('fail_count', models.Char<PERSON>ield(max_length=16)),
                ('if_shopee', models.Char<PERSON>ield(max_length=16)),
                ('qa_name', models.Char<PERSON>ield(max_length=128)),
                ('id', models.AutoField(primary_key=True, serialize=False)),
            ],
        ),
        migrations.CreateModel(
            name='<PERSON>',
            fields=[
                ('id', models.<PERSON><PERSON>ield(primary_key=True, serialize=False)),
                ('jenkinsID', models.Char<PERSON>ield(max_length=128)),
                ('projectURL', models.<PERSON>r<PERSON><PERSON>(max_length=128)),
                ('jenkinsProjectURL', models.CharField(max_length=128)),
                ('jobName', models.CharField(max_length=128)),
                ('deployTime', models.DateTimeField(auto_now_add=True)),
                ('env', models.CharField(max_length=16)),
                ('cid', models.CharField(max_length=128)),
                ('result', models.CharField(max_length=16)),
            ],
        ),
        migrations.CreateModel(
            name='PipeLine',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('dev', models.CharField(max_length=64)),
                ('startTime', models.DateTimeField(auto_now_add=True)),
                ('pipelineURL', models.CharField(max_length=128)),
                ('projectName', models.CharField(max_length=128)),
                ('gitlabProjectURL', models.CharField(max_length=128)),
                ('branchType', models.CharField(max_length=16)),
            ],
        ),
        migrations.DeleteModel(
            name='Stu',
        ),
    ]

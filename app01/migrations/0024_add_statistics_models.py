# Generated by Django 5.2.3 on 2025-07-18 07:40

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0023_add_group_id_to_spcpm_timeline_reminder'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userscheduledtask',
            name='notification_type',
            field=models.CharField(choices=[('private', '私聊'), ('group', '群聊')], default='private', max_length=20),
        ),
        migrations.CreateModel(
            name='BotAccessEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_id', models.CharField(help_text='事件唯一ID', max_length=100, unique=True)),
                ('event_type', models.CharField(choices=[('user_enter_chatroom_with_bot', '用户进入机器人聊天室'), ('user_leave_chatroom_with_bot', '用户离开机器人聊天室'), ('bot_added_to_group_chat', '机器人被添加到群聊'), ('bot_removed_from_group_chat', '机器人被移出群聊'), ('user_start_chat_with_bot', '用户开始与机器人私聊'), ('user_block_bot', '用户屏蔽机器人'), ('user_unblock_bot', '用户取消屏蔽机器人')], help_text='事件类型', max_length=50)),
                ('user_id', models.CharField(help_text='用户SeaTalk ID', max_length=100)),
                ('employee_code', models.CharField(blank=True, help_text='员工代码', max_length=100, null=True)),
                ('email', models.CharField(blank=True, help_text='用户邮箱', max_length=200, null=True)),
                ('group_id', models.CharField(blank=True, help_text='群组ID', max_length=100, null=True)),
                ('group_name', models.CharField(blank=True, help_text='群组名称', max_length=200, null=True)),
                ('app_id', models.CharField(blank=True, help_text='应用ID', max_length=100, null=True)),
                ('timestamp', models.BigIntegerField(help_text='原始时间戳')),
                ('event_time', models.DateTimeField(help_text='事件发生时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='额外元数据')),
            ],
            options={
                'verbose_name': '机器人访问事件',
                'verbose_name_plural': '机器人访问事件',
                'db_table': 'bot_access_event',
                'ordering': ['-event_time'],
                'indexes': [models.Index(fields=['user_id', 'event_type'], name='bot_access__user_id_ced8d3_idx'), models.Index(fields=['event_type', 'event_time'], name='bot_access__event_t_da57dc_idx'), models.Index(fields=['group_id', 'event_time'], name='bot_access__group_i_1b8aff_idx'), models.Index(fields=['event_time'], name='bot_access__event_t_1b63ef_idx'), models.Index(fields=['created_at'], name='bot_access__created_476608_idx')],
            },
        ),
        migrations.CreateModel(
            name='CommandExecutionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_id', models.UUIDField(default=uuid.uuid4, help_text='执行唯一ID', unique=True)),
                ('user_id', models.CharField(help_text='用户SeaTalk ID', max_length=100)),
                ('user_email', models.CharField(blank=True, help_text='用户邮箱', max_length=200, null=True)),
                ('employee_code', models.CharField(blank=True, help_text='员工代码', max_length=100, null=True)),
                ('group_id', models.CharField(blank=True, help_text='群组ID', max_length=100, null=True)),
                ('command_type', models.CharField(choices=[('ai_query', 'AI查询'), ('jira_query', 'JIRA查询'), ('jira_write', 'JIRA写操作'), ('mr_check', 'MR检查'), ('spcpm_query', 'SPCPM查询'), ('schedule_management', '定时任务管理'), ('todo_management', '代办事项管理'), ('traditional_command', '传统命令'), ('help_request', '帮助请求'), ('unknown', '未知指令')], help_text='指令类型', max_length=50)),
                ('raw_input', models.TextField(help_text='用户原始输入')),
                ('processed_command', models.TextField(blank=True, help_text='处理后的指令', null=True)),
                ('intent', models.CharField(blank=True, choices=[('query_tasks', '查询任务'), ('create_task', '创建任务'), ('update_task', '更新任务'), ('delete_task', '删除任务'), ('query_release', '查询发布'), ('query_mr', '查询MR'), ('query_bug', '查询Bug'), ('send_notification', '发送通知'), ('system_help', '系统帮助'), ('other', '其他')], help_text='识别的意图', max_length=50, null=True)),
                ('success', models.BooleanField(default=False, help_text='是否执行成功')),
                ('response_content', models.TextField(blank=True, help_text='返回给用户的内容', null=True)),
                ('response_length', models.IntegerField(default=0, help_text='响应内容长度')),
                ('error_message', models.TextField(blank=True, help_text='错误信息', null=True)),
                ('processing_time', models.FloatField(default=0.0, help_text='处理时间(秒)')),
                ('start_time', models.DateTimeField(help_text='开始处理时间')),
                ('end_time', models.DateTimeField(blank=True, help_text='结束处理时间', null=True)),
                ('api_calls', models.JSONField(blank=True, default=list, help_text='调用的API列表')),
                ('database_queries', models.IntegerField(default=0, help_text='数据库查询次数')),
                ('external_service_calls', models.JSONField(blank=True, default=dict, help_text='外部服务调用统计')),
                ('session_id', models.CharField(blank=True, help_text='会话ID', max_length=100, null=True)),
                ('thread_id', models.CharField(blank=True, help_text='线程ID', max_length=100, null=True)),
                ('message_id', models.CharField(blank=True, help_text='消息ID', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间')),
            ],
            options={
                'verbose_name': '指令执行记录',
                'verbose_name_plural': '指令执行记录',
                'db_table': 'command_execution_record',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user_id', 'created_at'], name='command_exe_user_id_671fa4_idx'), models.Index(fields=['command_type', 'created_at'], name='command_exe_command_5bfada_idx'), models.Index(fields=['success', 'created_at'], name='command_exe_success_ba0fa8_idx'), models.Index(fields=['intent', 'created_at'], name='command_exe_intent_809224_idx'), models.Index(fields=['group_id', 'created_at'], name='command_exe_group_i_d303ed_idx'), models.Index(fields=['start_time'], name='command_exe_start_t_040aaf_idx'), models.Index(fields=['processing_time'], name='command_exe_process_47704c_idx'), models.Index(fields=['created_at'], name='command_exe_created_4b1170_idx')],
            },
        ),
        migrations.CreateModel(
            name='CronJobExecutionMonitor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_name', models.CharField(help_text='任务名称', max_length=100)),
                ('execution_id', models.UUIDField(default=uuid.uuid4, help_text='执行ID')),
                ('start_time', models.DateTimeField(help_text='开始时间')),
                ('end_time', models.DateTimeField(blank=True, help_text='结束时间', null=True)),
                ('duration', models.FloatField(blank=True, help_text='执行时长(秒)', null=True)),
                ('status', models.CharField(choices=[('running', '运行中'), ('success', '成功'), ('failed', '失败'), ('timeout', '超时'), ('cancelled', '已取消')], default='running', help_text='执行状态', max_length=20)),
                ('success', models.BooleanField(default=False, help_text='是否成功')),
                ('output', models.TextField(blank=True, help_text='执行输出', null=True)),
                ('error_message', models.TextField(blank=True, help_text='错误信息', null=True)),
                ('exit_code', models.IntegerField(blank=True, help_text='退出码', null=True)),
                ('memory_peak', models.FloatField(blank=True, help_text='内存峰值(MB)', null=True)),
                ('cpu_time', models.FloatField(blank=True, help_text='CPU时间(秒)', null=True)),
                ('scheduled_time', models.DateTimeField(blank=True, help_text='计划执行时间', null=True)),
                ('trigger_type', models.CharField(default='cron', help_text='触发类型', max_length=20)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='额外元数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间')),
            ],
            options={
                'verbose_name': '定时任务执行监控',
                'verbose_name_plural': '定时任务执行监控',
                'db_table': 'cronjob_execution_monitor',
                'ordering': ['-start_time'],
                'indexes': [models.Index(fields=['job_name', 'start_time'], name='cronjob_exe_job_nam_61acfe_idx'), models.Index(fields=['status', 'start_time'], name='cronjob_exe_status_869e85_idx'), models.Index(fields=['success', 'start_time'], name='cronjob_exe_success_03451c_idx'), models.Index(fields=['start_time'], name='cronjob_exe_start_t_7357f0_idx'), models.Index(fields=['created_at'], name='cronjob_exe_created_9d0312_idx')],
            },
        ),
        migrations.CreateModel(
            name='SystemHealthSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('snapshot_time', models.DateTimeField(help_text='快照时间')),
                ('overall_status', models.CharField(choices=[('healthy', '健康'), ('warning', '警告'), ('critical', '严重'), ('down', '宕机')], help_text='整体状态', max_length=20)),
                ('total_users_today', models.IntegerField(default=0, help_text='今日总用户数')),
                ('active_users_now', models.IntegerField(default=0, help_text='当前活跃用户数')),
                ('total_commands_today', models.IntegerField(default=0, help_text='今日总指令数')),
                ('success_rate_today', models.FloatField(default=0.0, help_text='今日成功率(%)')),
                ('avg_response_time', models.FloatField(default=0.0, help_text='平均响应时间(秒)')),
                ('system_load', models.FloatField(default=0.0, help_text='系统负载')),
                ('memory_usage', models.FloatField(default=0.0, help_text='内存使用率(%)')),
                ('cpu_usage', models.FloatField(default=0.0, help_text='CPU使用率(%)')),
                ('database_status', models.CharField(default='healthy', help_text='数据库状态', max_length=20)),
                ('redis_status', models.CharField(default='healthy', help_text='Redis状态', max_length=20)),
                ('external_services_status', models.JSONField(default=dict, help_text='外部服务状态')),
                ('error_count_last_hour', models.IntegerField(default=0, help_text='最近1小时错误数')),
                ('warning_count_last_hour', models.IntegerField(default=0, help_text='最近1小时警告数')),
                ('cronjobs_running', models.IntegerField(default=0, help_text='运行中的定时任务数')),
                ('cronjobs_failed_today', models.IntegerField(default=0, help_text='今日失败的定时任务数')),
                ('details', models.JSONField(blank=True, default=dict, help_text='详细信息')),
                ('alerts', models.JSONField(blank=True, default=list, help_text='告警信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间')),
            ],
            options={
                'verbose_name': '系统健康快照',
                'verbose_name_plural': '系统健康快照',
                'db_table': 'system_health_snapshot',
                'ordering': ['-snapshot_time'],
                'indexes': [models.Index(fields=['snapshot_time'], name='system_heal_snapsho_b863dd_idx'), models.Index(fields=['overall_status', 'snapshot_time'], name='system_heal_overall_a55188_idx'), models.Index(fields=['created_at'], name='system_heal_created_e564af_idx')],
            },
        ),
        migrations.CreateModel(
            name='SystemPerformanceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_type', models.CharField(choices=[('api_response', 'API响应'), ('database_query', '数据库查询'), ('external_service', '外部服务调用'), ('system_resource', '系统资源'), ('business_metric', '业务指标')], help_text='指标类型', max_length=30)),
                ('metric_time', models.DateTimeField(help_text='指标时间')),
                ('api_endpoint', models.CharField(blank=True, help_text='API端点', max_length=200, null=True)),
                ('response_time', models.FloatField(default=0.0, help_text='响应时间(秒)')),
                ('status_code', models.IntegerField(blank=True, help_text='HTTP状态码', null=True)),
                ('cpu_usage', models.FloatField(blank=True, help_text='CPU使用率(%)', null=True)),
                ('memory_usage', models.FloatField(blank=True, help_text='内存使用率(%)', null=True)),
                ('disk_usage', models.FloatField(blank=True, help_text='磁盘使用率(%)', null=True)),
                ('database_connections', models.IntegerField(blank=True, help_text='数据库连接数', null=True)),
                ('active_users', models.IntegerField(default=0, help_text='活跃用户数')),
                ('concurrent_requests', models.IntegerField(default=0, help_text='并发请求数')),
                ('queue_length', models.IntegerField(default=0, help_text='队列长度')),
                ('error_count', models.IntegerField(default=0, help_text='错误数量')),
                ('warning_count', models.IntegerField(default=0, help_text='警告数量')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='额外元数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间')),
            ],
            options={
                'verbose_name': '系统性能指标',
                'verbose_name_plural': '系统性能指标',
                'db_table': 'system_performance_metrics',
                'ordering': ['-metric_time'],
                'indexes': [models.Index(fields=['metric_type', 'metric_time'], name='system_perf_metric__4c0904_idx'), models.Index(fields=['api_endpoint', 'metric_time'], name='system_perf_api_end_da062a_idx'), models.Index(fields=['metric_time'], name='system_perf_metric__f6c502_idx'), models.Index(fields=['created_at'], name='system_perf_created_adef32_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserActivitySummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.CharField(help_text='用户SeaTalk ID', max_length=100)),
                ('user_email', models.CharField(blank=True, help_text='用户邮箱', max_length=200, null=True)),
                ('employee_code', models.CharField(blank=True, help_text='员工代码', max_length=100, null=True)),
                ('period_type', models.CharField(choices=[('hourly', '小时'), ('daily', '日'), ('weekly', '周'), ('monthly', '月')], help_text='汇总周期', max_length=20)),
                ('period_start', models.DateTimeField(help_text='周期开始时间')),
                ('period_end', models.DateTimeField(help_text='周期结束时间')),
                ('total_commands', models.IntegerField(default=0, help_text='总指令数')),
                ('successful_commands', models.IntegerField(default=0, help_text='成功指令数')),
                ('failed_commands', models.IntegerField(default=0, help_text='失败指令数')),
                ('ai_queries', models.IntegerField(default=0, help_text='AI查询次数')),
                ('jira_operations', models.IntegerField(default=0, help_text='JIRA操作次数')),
                ('mr_checks', models.IntegerField(default=0, help_text='MR检查次数')),
                ('schedule_operations', models.IntegerField(default=0, help_text='定时任务操作次数')),
                ('avg_response_time', models.FloatField(default=0.0, help_text='平均响应时间(秒)')),
                ('max_response_time', models.FloatField(default=0.0, help_text='最大响应时间(秒)')),
                ('min_response_time', models.FloatField(default=0.0, help_text='最小响应时间(秒)')),
                ('peak_hour', models.IntegerField(blank=True, help_text='使用高峰小时', null=True)),
                ('active_days', models.IntegerField(default=0, help_text='活跃天数')),
                ('total_session_time', models.FloatField(default=0.0, help_text='总会话时间(分钟)')),
                ('group_interactions', models.IntegerField(default=0, help_text='群组交互次数')),
                ('private_interactions', models.IntegerField(default=0, help_text='私聊交互次数')),
                ('common_errors', models.JSONField(blank=True, default=list, help_text='常见错误列表')),
                ('error_rate', models.FloatField(default=0.0, help_text='错误率(%)')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录更新时间')),
            ],
            options={
                'verbose_name': '用户活动汇总',
                'verbose_name_plural': '用户活动汇总',
                'db_table': 'user_activity_summary',
                'ordering': ['-period_start'],
                'indexes': [models.Index(fields=['user_id', 'period_type', 'period_start'], name='user_activi_user_id_be2361_idx'), models.Index(fields=['period_type', 'period_start'], name='user_activi_period__bc7ddc_idx'), models.Index(fields=['period_start'], name='user_activi_period__392a79_idx'), models.Index(fields=['created_at'], name='user_activi_created_b3ded9_idx')],
                'unique_together': {('user_id', 'period_type', 'period_start')},
            },
        ),
    ]

# -*- coding: utf-8 -*-
"""
群组同步管理模块
负责Seatalk群组信息的同步和管理
"""

import requests
import time
import threading
from datetime import datetime
from icecream import ic
from typing import List, Dict

from .models import SeatalkGroup
from .config import get_seatalk_access_token

# 群组同步相关配置和缓存
GROUP_SYNC_LOCK = threading.Lock()  # 线程锁，防止并发调用
LAST_GROUP_SYNC_TIME = 0  # 上次群组同步时间戳
GROUP_SYNC_INTERVAL = 300  # 5分钟 = 300秒
SEATALK_API_BASE = "https://openapi.seatalk.io"

# 内存缓存：记录当天已经尝试过群组同步的情况
DAILY_SYNC_CACHE = {}  # 格式: {"2025-06-26": True}


def call_seatalk_api(endpoint, params=None):
    """
    调用Seatalk API的通用函数
    
    Args:
        endpoint: API端点
        params: 请求参数
        
    Returns:
        dict: API响应数据
    """
    access_token = get_seatalk_access_token()
    if not access_token:
        raise Exception("无法获取Seatalk访问令牌")
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{SEATALK_API_BASE}{endpoint}", 
                              headers=headers, params=params, timeout=30)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        ic(f"调用Seatalk API失败: {endpoint}, 错误: {str(e)}")
        raise


def get_all_joined_groups():
    """
    获取机器人加入的所有群组ID列表
    
    Returns:
        list: 群组ID列表
    """
    all_groups = []
    cursor = None
    
    while True:
        params = {'page_size': 100}
        if cursor:
            params['cursor'] = cursor
            
        try:
            result = call_seatalk_api('/messaging/v2/group_chat/joined', params)
            
            if result.get('code') != 0:
                ic(f"获取群组列表失败: {result}")
                break
                
            group_ids = result.get('joined_group_chats', {}).get('group_id', [])
            all_groups.extend(group_ids)
            
            cursor = result.get('next_cursor')
            if not cursor:
                break
                
        except Exception as e:
            ic(f"获取群组列表异常: {str(e)}")
            break
    
    ic(f"获取到 {len(all_groups)} 个群组ID")
    return all_groups


def get_group_info(group_id):
    """
    获取指定群组的详细信息
    
    Args:
        group_id: 群组ID
        
    Returns:
        dict: 群组信息，包含group_name等
    """
    try:
        result = call_seatalk_api('/messaging/v2/group_chat/info', 
                                {'group_id': group_id})
        
        if result.get('code') != 0:
            ic(f"获取群组信息失败: {group_id}, {result}")
            return None
            
        return result.get('group', {})
        
    except Exception as e:
        ic(f"获取群组信息异常: {group_id}, {str(e)}")
        return None


def sync_all_joined_groups():
    """
    同步所有机器人加入的群组信息到数据库
    
    Returns:
        bool: 同步是否成功
    """
    global LAST_GROUP_SYNC_TIME
    
    try:
        ic("开始同步群组信息...")
        
        # 获取所有群组ID
        group_ids = get_all_joined_groups()
        if not group_ids:
            ic("未获取到任何群组")
            return False
        
        # 批量获取群组详细信息
        updated_count = 0
        failed_count = 0
        
        for group_id in group_ids:
            try:
                group_info = get_group_info(group_id)
                if not group_info:
                    failed_count += 1
                    continue
                
                group_name = group_info.get('group_name', '')
                if not group_name:
                    failed_count += 1
                    continue
                
                # 兼容现有数据结构，只更新基本字段
                try:
                    # 先尝试获取现有记录
                    existing_group = SeatalkGroup.objects.get(group_id=group_id)
                    # 更新群组名称（可能已改变）
                    existing_group.group_name = group_name
                    existing_group.save()
                    ic(f"更新群组: {group_name} ({group_id})")
                except SeatalkGroup.DoesNotExist:
                    # 创建新记录
                    SeatalkGroup.objects.create(
                        group_id=group_id,
                        group_name=group_name
                    )
                    ic(f"新增群组: {group_name} ({group_id})")
                
                updated_count += 1
                    
                # 避免API调用过于频繁
                time.sleep(0.1)
                
            except Exception as e:
                ic(f"处理群组失败: {group_id}, {str(e)}")
                failed_count += 1
                continue
        
        # 更新同步时间
        LAST_GROUP_SYNC_TIME = time.time()
        
        ic(f"群组同步完成: 成功 {updated_count} 个, 失败 {failed_count} 个")
        return True
        
    except Exception as e:
        ic(f"群组同步异常: {str(e)}")
        return False


def should_sync_groups():
    """
    判断是否应该进行群组同步
    
    Returns:
        bool: 是否应该同步
    """
    global LAST_GROUP_SYNC_TIME
    
    current_time = time.time()
    
    # 检查是否超过5分钟间隔
    if current_time - LAST_GROUP_SYNC_TIME < GROUP_SYNC_INTERVAL:
        ic(f"距离上次同步未满5分钟，跳过同步")
        return False
    
    # 检查今天是否已经同步过
    today = datetime.now().strftime('%Y-%m-%d')
    if DAILY_SYNC_CACHE.get(today, False):
        ic(f"今天已经同步过群组，跳过")
        return False
    
    return True


def get_epic_project_group_id_with_fallback(epic_key):
    """
    根据Epic key从数据库中查询对应的群组ID，支持实时兜底查询
    
    Args:
        epic_key: Epic的key
        
    Returns:
        str: 群组ID
        
    Raises:
        Exception: 当找不到群组时抛出异常
    """
    try:
        # 先从数据库查找包含该JIRA号的群组
        groups = SeatalkGroup.objects.filter(group_name__contains=epic_key)
        ic(f"{epic_key} - 找到 {groups.count()} 个相关群组")
        
        if groups.exists():
            group = groups.first()
            group_id = group.group_id
            ic(f"{epic_key} - 使用群组: {group.group_name} (ID: {group_id})")
            return group_id
        
        # 如果找不到，尝试实时同步（有频率限制）
        if should_sync_groups():
            with GROUP_SYNC_LOCK:
                # 双重检查，避免并发问题
                if should_sync_groups():
                    ic(f"{epic_key} - 未找到群组，尝试实时同步...")
                    
                    if sync_all_joined_groups():
                        # 标记今天已同步
                        today = datetime.now().strftime('%Y-%m-%d')
                        DAILY_SYNC_CACHE[today] = True
                        
                        # 再次查询
                        groups = SeatalkGroup.objects.filter(group_name__contains=epic_key)
                        if groups.exists():
                            group = groups.first()
                            group_id = group.group_id
                            ic(f"{epic_key} - 同步后找到群组: {group.group_name} (ID: {group_id})")
                            return group_id
        
        # 最终还是找不到
        ic(f"{epic_key} - 未找到相关群组")
        raise Exception(f"未找到包含 {epic_key} 的群组")
        
    except Exception as e:
        if "未找到包含" not in str(e):
            ic(f"{epic_key} - 查询群组失败: {str(e)}")
        raise Exception(f"查询群组失败: {str(e)}")


def get_epic_project_groups_with_fallback(epic_key):
    """
    根据Epic key从数据库中查询对应的所有群组，支持实时兜底查询
    
    Args:
        epic_key: Epic的key
        
    Returns:
        QuerySet: 群组对象列表
        
    Raises:
        Exception: 当找不到群组时抛出异常
    """
    try:
        # 先从数据库查找包含该JIRA号的群组
        groups = SeatalkGroup.objects.filter(group_name__contains=epic_key)
        ic(f"{epic_key} - 找到 {groups.count()} 个相关群组")
        
        if groups.exists():
            # 打印所有找到的群组信息
            for group in groups:
                ic(f"{epic_key} - 匹配群组: {group.group_name} (ID: {group.group_id})")
            return groups
        
        # 如果找不到，尝试实时同步（有频率限制）
        if should_sync_groups():
            with GROUP_SYNC_LOCK:
                # 双重检查，避免并发问题
                if should_sync_groups():
                    ic(f"{epic_key} - 未找到群组，尝试实时同步...")
                    
                    if sync_all_joined_groups():
                        # 标记今天已同步
                        today = datetime.now().strftime('%Y-%m-%d')
                        DAILY_SYNC_CACHE[today] = True
                        
                        # 再次查询
                        groups = SeatalkGroup.objects.filter(group_name__contains=epic_key)
                        if groups.exists():
                            for group in groups:
                                ic(f"{epic_key} - 同步后找到群组: {group.group_name} (ID: {group.group_id})")
                            return groups
        
        # 最终还是找不到
        ic(f"{epic_key} - 未找到相关群组")
        raise Exception(f"未找到包含 {epic_key} 的群组")
        
    except Exception as e:
        if "未找到包含" not in str(e):
            ic(f"{epic_key} - 查询群组失败: {str(e)}")
        raise Exception(f"查询群组失败: {str(e)}")


def daily_sync_groups():
    """
    每日定时同步群组信息的任务函数
    应该通过Django定时任务在每天凌晨2点执行
    """
    try:
        ic("开始每日群组同步任务...")
        
        # 清理缓存
        global DAILY_SYNC_CACHE
        today = datetime.now().strftime('%Y-%m-%d')
        DAILY_SYNC_CACHE = {today: False}  # 重置今日缓存
        
        # 执行同步
        if sync_all_joined_groups():
            DAILY_SYNC_CACHE[today] = True
            ic("每日群组同步任务完成")
        else:
            ic("每日群组同步任务失败")
            
    except Exception as e:
        ic(f"每日群组同步任务异常: {str(e)}") 
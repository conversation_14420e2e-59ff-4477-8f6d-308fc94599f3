# -*- coding: utf-8 -*-  
# 部署失败消息私聊发送给提 MR的开发
import re
import os
import sys
# 这两行很重要，用来寻找项目根目录，os.path.dirname要写多少个根据要运行的python文件到根目录的层数决定
import time
import requests
import json
import re
sys.path.append(os.getcwd())
from config import Config
import jenkins
from datetime import datetime

# 获取gitlab上MR信息
def getlastMRMSG(project_id, env):
    rep = requests.get(
        'https://git.garena.com/api/v4/projects/{project_id}/'
        'merge_requests?private_token=kBV8bRxCbEqk2G8eyFyz&state=merged&scope=all&target_branch={env}'.format
        (project_id=project_id, env=env))

    json_result = json.loads(rep.text)

    return {"name": json_result[0]['author']['username'], "title": json_result[0]['title']}

# 获取seatalk app access token
def app_access_token():
    token_url = "https://openapi.seatalk.io/auth/app_access_token"
    headers = {
        'content-type': "application/json",
    }
    param = {
        "app_id": "NzM5MzEzODYyNzk5",
        "app_secret": "kDhXUHT1ZW-ABP8I0IF06YAvlW86ukoS"
    }
    r = requests.post(url=token_url, json=param, headers=headers)
    results = json.loads(r.text)
    return results["app_access_token"]

# seatalk私聊发送消息的简单方法，仅支持文本消息
def single_chat(text, code):
    access_token = app_access_token()
    # print(code)
    single_chat_url = "https://openapi.seatalk.io/messaging/v2/single_chat"
    headers = {
        'content-type': "application/json",
        'Authorization': "Bearer " + access_token
    }
    param = {
        "employee_code": code,
        "message": {
            "tag": "text",
            "text": {
                "content": text
            }
        }
    }
    r = requests.post(url=single_chat_url, json=param, headers=headers)
    result = json.loads(r.text)
    if result["code"] == 0:
        print("success")
    else:
        print("fail")

# 获取token
def gettoken():
    URL = "https://space.shopee.io/apis/uic/v2/auth/basic_login"
    headers = {
        'content-type': "application/json",
        'Authorization': 'Basic Y2hhdGJvdHFhOlNob3BlZTEyMw=='
    }
    try:
        r = requests.post(url=URL, headers=headers, timeout=5)
        results = json.loads(r.text)
        # print(results)
        return results
    except requests.exceptions.RequestException as e:
        print(e)

# 获取员工code
def get_employee_code(email):
    print("email")
    print(email)
    if email == "<EMAIL>":
        email = "<EMAIL>"
    access_token = app_access_token()
    get_code_url = "https://openapi.seatalk.io/contacts/v2/get_employee_code_with_email"
    headers = {
        'content-type': "application/json",
        'Authorization': "Bearer " + access_token
    }
    param = {
        "emails": email,
    }
    r = requests.post(url=get_code_url, json=param, headers=headers)
    json_result = json.loads(r.text)
    print(json_result)
    return json_result["employees"][0]["employee_code"]

# 部署失败消息私聊发送给提 MR的开发
def failuremsg():
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    seatalk_url = "https://openapi.seatalk.io/webhook/group/ZHWKU3hhQtyYD-kwIjQlYQ"
    srv_list = []
    result = gettoken()
    print("failuremsg begin!!")
    print(datetime.now())
    #print(time.strftime("%Y-%m-%d %H:%M:%S", t))
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    
    for value in Config.srvMap.values():
        
        for i in value:
            print(i)
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            rep = r.text.encode("utf-8").decode("latin1")
            print("00000")
            print(r)
            print(rep)
            result_rep = json.loads(rep)
            print(result_rep)            
            if result_rep["errmsg"] == "success":
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE':
                        srv_list.append(i)
    
    for value in Config.srvMap.values():
        
        for i in value:
            i = i.replace("test", "staging")

            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            rep = r.text.encode("utf-8").decode("latin1")
            print(rep)
            result_rep = json.loads(rep)
            
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE':
                        srv_list.append(i)

    if srv_list:
        text_all = ""
        srv_id = Config.srv2id
        blacklist = Config.msgBlackList

        for i in srv_list:
            if i in blacklist:
                continue
            msg_tmp = {}
            dev_name = ""
            title = ""
            code = ""
            list_dev = []
            if "-test" in i:
                if i.replace('-test', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-test', '')], 'test')
            if "-uat" in i:
                if i.replace('-uat', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-uat', '')], 'uat')
            if "-staging" in i:
                if i.replace('-staging', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-staging', '')], 'master')
            if msg_tmp:
                dev_name = msg_tmp['name'] + "@shopee.com"
                title = msg_tmp['title']
                list_dev.append(dev_name)
                code = get_employee_code(list_dev)
            
            
            text_all = text_all + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n"
            single_chat(text_all, code)
        if not text_all:
            return
        text_all = "最后部署失败的服务列表为：\n" + text_all
        param = {
            "tag": "text",
            "text": {
                "content": text_all
            }
        }
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        r = requests.post(url=seatalk_url, json=param, headers=headers)
        print(json.loads(r.text))
    else:
        print("success!!")

    # https://git.garena.com/api/v4/projects/49709/repository/commits?id=49709&ref_name=test

failuremsg()

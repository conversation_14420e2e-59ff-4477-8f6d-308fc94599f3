import requests
def addjenkinsjob():
   new_token = "MDYxNDgxOTU5NTgwOkXJSWQx9CAxhnN3OdncWxRuFMrE"
   addjoburl = "https://jira.shopee.io/rest/shopee_release_plugin_jenkins_job/latest/jenkins_job/add_jobs"
   headers = {
       "Authorization": f"Bearer {new_token}",
       "content-type": "application/json"
   }
   body = {
       "jenkins_job": ["shopee-chatbot-websocketgwy-live"]
   }
   r = requests.post(url=addjoburl, headers=headers, json=body)
   print(r)

if __name__ == '__main__':
   addjenkinsjob()
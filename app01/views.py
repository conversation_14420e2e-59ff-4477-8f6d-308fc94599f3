# -*- coding: utf-8 -*-
"""

    后台代码
"""
import inspect
import re
import concurrent.futures
import asyncio
from django.shortcuts import render
from django.shortcuts import HttpResponse, render, Http404
from django.http import JsonResponse
from django.db import transaction
import time
import jira
import requests
import json
import re
from urllib.parse import quote
from app01.models import ReleaseTitle
from app01.config import Config
from app01.models import Deploy
from app01.models import Autorelease
from .models import SeatalkGroup
from .seatalk_group_manager import auto_create_group_for_jira, check_timeline_changes, simplify_jira_error
from app01.models import SeatalkProcess
from app01.models import JIRAReleaseListDetails
from app01.models import CalendarJiraReleaseList
import jenkins
from jira import JIRA, JIRAError
from app01.statistics.decorators import track_cronjob_execution
from datetime import datetime, timedelta
import datetime as dt
import sqlite3
from icecream import ic
from asgiref.sync import sync_to_async
from app01.common_tool.get_config_from_CC import get_timeout_config_form_CC
from django.views.decorators.csrf import csrf_exempt
import os
from pathlib import Path
from django.conf import settings

# AI 模块导入
try:
    from app01.ai_module.ai_assistant import ai_assistant
    from app01.ai_module.typing_status import typing_manager, TypingContext
    from app01.ai_module.private_chat import private_chat_client
    from app01.ai_module.message_deduplication import deduplicate_message
    AI_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"AI模块导入失败: {e}")
    AI_MODULE_AVAILABLE = False
    # 创建一个空的装饰器作为fallback
    def deduplicate_message(func):
        return func

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

def generate_friendly_error_message(error_msg):
    """
    生成友好的错误提示信息
    """
    error_lower = error_msg.lower()
    
    # 检查是否包含error_type标记，如果是建单指南类型，直接返回原始错误信息
    if isinstance(error_msg, dict) and error_msg.get('error_type') == 'building_guide':
        return error_msg.get('error', error_msg)
        
    # 建单相关错误，直接返回原始错误信息
    if "建单" in error_msg or "创建子任务" in error_msg or "subtask" in error_lower:
        return error_msg
    
    # 请求超时错误
    if "请求超时" in error_msg or "timeout" in error_lower:
        return """⏰ **请求处理超时**

AI服务当前负载较高，请稍等片刻后重试。

💡 **建议操作：**
• 等待1-2分钟后重新发送相同请求
• 如果查询复杂，可以尝试简化查询条件
• 如果问题持续，请联系技术支持

🔄 **快速重试：** 直接重新发送刚才的消息即可"""
    
    # 网络连接错误
    elif "连接" in error_msg or "connection" in error_lower or "network" in error_lower:
        return """🌐 **网络连接异常**

与AI服务的连接暂时中断，请稍后重试。

💡 **建议操作：**
• 检查网络连接是否正常
• 等待30秒后重新尝试
• 如果问题持续，请联系技术支持

🔄 **重试提示：** 重新发送消息即可"""
    
    # JIRA相关错误
    elif "jira" in error_lower or "jql" in error_lower:
        return f"""🎯 **JIRA查询错误**

查询执行失败：{error_msg}

💡 **建议操作：**
• 检查JQL语法是否正确
• 确认JIRA字段名称是否准确
• 尝试简化查询条件后重试
• 使用 `fields search 关键词` 搜索jira可用字段的 ID

🔄 **重试提示：** 修正查询条件后重新发送"""
    
    # 权限相关错误
    elif "权限" in error_msg or "permission" in error_lower or "unauthorized" in error_lower:
        return """🔒 **权限不足**

您没有执行此操作的权限。

💡 **建议操作：**
• 确认您有相应的JIRA项目访问权限
• 联系项目管理员开通权限
• 检查是否使用了正确的用户账号

❓ **需要帮助？** 请联系技术支持"""
    
    # 服务不可用
    elif "不可用" in error_msg or "unavailable" in error_lower or "service" in error_lower:
        return """🚫 **服务暂时不可用**

AI服务正在维护或升级中。

💡 **建议操作：**
• 等待5-10分钟后重试
• 关注系统公告了解维护时间
• 如需紧急处理，请联系技术支持

⏰ **预计恢复时间：** 通常在30分钟内恢复正常"""
    
    # 通用错误
    else:
        return f"""❌ **处理失败**

{error_msg}

💡 **建议操作：**
• 稍等片刻后重试相同操作
• 检查输入格式是否正确
• 尝试简化请求内容
• 使用 `help` 查看使用说明

🔄 **重试提示：** 重新发送消息即可

❓ **需要帮助？** 如问题持续出现，请联系技术支持"""

# AI 功能处理函数
@deduplicate_message
async def handle_ai_query_async(ai_query: str, seatalk_id: str, group_id: str, data: dict):
    """处理AI查询的异步函数"""
    if not AI_MODULE_AVAILABLE:
        return JsonResponse({
            'message': 'AI功能暂不可用，请稍后重试',
            'success': False
        })
    
    try:
        # 提取用户信息
        user_id = seatalk_id
        group_id = group_id if group_id else None
        employee_code = None
        user_email = None
        
        # 获取employee_code（私聊时需要）
        if data.get("event_type") == "message_from_bot_subscriber":
            employee_code = data.get("event", {}).get("employee_code")
        
        # 使用新的用户信息管理器获取邮箱
        try:
            from app01.ai_module.seatalk_user_manager import seatalk_user_manager
            user_email = await seatalk_user_manager.get_user_email(data, employee_code)
            ic(f"🎯 用户邮箱获取结果: {user_email}")
        except Exception as e:
            ic(f"⚠️ 用户邮箱获取失败，使用fallback: {str(e)}")
            # 使用原有的fallback逻辑
            if data.get("event_type") == "message_from_bot_subscriber":
                if employee_code:
                    user_email = f"{employee_code}@shopee.com"
            else:
                sender_info = data.get("event", {}).get("message", {}).get("sender", {})
                user_email = sender_info.get("email")
        
        # 使用Typing状态包装处理
        async with TypingContext(group_id=group_id, employee_code=employee_code):
            # 获取群组标题（如果是群聊）
            group_title = None
            if group_id:
                try:
                    group_info_result = get_group_info(group_id)
                    if group_info_result and group_info_result.get('code') == 0 and group_info_result.get('group'):
                        group_title = group_info_result.get('group', {}).get('group_name')
                        ic(f"📋 获取到群组标题: {group_title}")
                except Exception as e:
                    ic(f"❌ 获取群组标题失败: {str(e)}")
            
            # 处理AI查询
            result = await ai_assistant.process_query(
                user_query=ai_query,
                user_id=user_id,
                group_id=group_id,
                employee_code=employee_code,
                user_email=user_email,
                group_title=group_title
            )
        
        ic(f"🎯 AI处理结果: success={result['success']}")
        if result['success']:
            ic(f"📊 处理时间: {result.get('processing_time', 0)}秒")
            ic(f"🎯 意图: {result.get('intent', 'unknown')}")
            ic(f"🔄 需要澄清: {result.get('needs_clarification', False)}")
        
        if result['success']:
            # 检查是否需要澄清
            if result.get('needs_clarification'):
                clarification = result.get('clarification', {})
                
                # 根据澄清类型提供更明确的提示
                clarification_type = clarification.get('type', 'general')
                
                if clarification_type == 'schedule_create':
                    response_text = """📅 **创建定时任务需要更多信息**

**标准格式：**
`schedule create "任务名称" "JQL查询" "时间表达式"`

**智能通知格式：**
`schedule create "任务名称" "JQL查询" "时间表达式" smart`

**示例：**
• `schedule create "每日Bug检查" "assignee = currentUser() AND status != Closed" "daily 09:00"`
• `schedule create "智能通知" "project = SPCB AND status = Open" "workdays 17:00" smart`

**时间表达式：**
• `daily 09:00` - 每天上午9点
• `workdays 18:00` - 工作日下午6点  
• `weekly monday 14:00` - 每周一下午2点

请按照格式提供完整信息。"""
                elif clarification_type == 'jql_syntax':
                    response_text = """🔍 **JQL查询语法说明**

**常用字段：**
• `assignee = currentUser()` - 分配给当前用户
• `project = SPCB` - 项目为SPCB
• `status in (Open, "In Progress")` - 状态为开放或进行中
• `priority in (High, Critical)` - 高优先级或紧急

**智能通知JQL示例：**
• `assignee = currentUser() AND status != Closed`
• `project = SPCB AND issuetype = Bug AND status = Open`
• `assignee in (<EMAIL>, <EMAIL>)`

请提供正确的JQL查询语句。"""
                elif clarification_type == 'smart_notification_config':
                    response_text = """🧠 **智能通知配置说明**

**启用智能通知：**
在命令末尾添加 `smart` 关键词

**智能通知特性：**
✅ 自动按assignee分组发送
✅ 每人只收到自己的ticket
✅ 支持消息合并
✅ 智能生成通知内容

**配置选项：**
• `notification_mode: assignee_private` - 私聊通知
• `notification_mode: assignee_group` - 群聊通知
• `merge_messages: true` - 合并消息

**完整示例：**
`schedule create "Bug提醒" "assignee = currentUser() AND priority = High" "daily 09:00" smart`"""
                else:
                    # 通用澄清信息
                    response_text = clarification.get('question', '需要更多信息来处理您的请求。')
                    response_text += "\n\n💡 **常用功能：**\n"
                    response_text += "• `help` - 查看完整帮助\n"
                    response_text += "• `schedule help` - 定时任务管理\n"
                    response_text += "• `jira help` - 创建智能通知任务\n"
                
                # 添加选项
                if clarification.get('options'):
                    response_text += "\n\n**可选操作：**\n" + "\n".join(clarification['options'])
            else:
                response_text = result.get('response', '处理完成')
            
            ic(f"📝 准备发送回复，长度: {len(response_text)}")
            ic(f"🎯 回复模式: {'群聊' if group_id else '私聊'}")
            
            # 发送响应 - 添加错误处理
            try:
                if group_id:
                    # 群聊响应
                    ic(f"📢 发送群聊消息到: {group_id}")
                    test_for_seatalk_bot(response_text, [seatalk_id], group_id)
                    ic("✅ 群聊消息发送完成")
                elif employee_code:
                    # 私聊响应
                    ic(f"💬 发送私聊消息到: {employee_code}")
                    try:
                        send_result = await private_chat_client.send_text_message(employee_code, response_text)
                        ic(f"🔍 私聊发送结果: {send_result}")
                        if send_result.get('success'):
                            ic("✅ 私聊消息发送成功")
                        else:
                            ic(f"❌ 私聊消息发送失败: {send_result.get('error', 'Unknown error')}")
                    except Exception as e:
                        ic(f"💥 私聊消息发送异常: {str(e)}")
                        # 尝试使用备用发送方法
                        try:
                            ic("🔄 尝试使用备用发送方法")
                            sent_aio(response_text, employee_code)
                            ic("✅ 备用发送方法执行完成")
                        except Exception as e2:
                            ic(f"💥 备用发送方法也失败: {str(e2)}")
                else:
                    ic("⚠️ 无法确定发送目标")
            except Exception as e:
                # 捕获发送消息时可能出现的错误，包括intercepted_messages未定义的错误
                error_log = f"消息处理失败，已清理记录: {str(e)}"
                ic(error_log)
                
                # 尝试直接发送错误信息，不使用测试框架的变量
                try:
                    if group_id:
                        from app01.seatalk_group_manager import send_message_to_group
                        send_message_to_group(group_id, response_text)
                except Exception as send_error:
                    ic("发送错误信息也失败了")
            
            # 清理线程上下文，避免上下文泄漏
            try:
                from app01.seatalk_group_manager import ThreadReplyContext
                ThreadReplyContext.clear_thread_context()
                ic("✅ AI处理完成后清理线程上下文")
            except Exception as e:
                ic(f"❌ 清理线程上下文失败: {str(e)}")
            
            return JsonResponse({
                'message': '查询成功',
                'success': True,
                'intent': result.get('intent'),
                'needs_clarification': result.get('needs_clarification', False),
                'processing_time': result.get('processing_time', 0)
            })
        else:
            # 生成友好的错误消息
            error_msg = generate_friendly_error_message(result['error'])
            
            # 发送错误响应
            if group_id:
                test_for_seatalk_bot(error_msg, [seatalk_id], group_id)
            elif employee_code:
                await private_chat_client.send_text_message(employee_code, error_msg)
            
            return JsonResponse({
                'message': error_msg,
                'success': False,
                'processing_time': result.get('processing_time', 0)
            })
    
    except Exception as e:
        error_msg = f"AI查询处理异常: {str(e)}"
        ic(error_msg)
        
        # 生成友好的异常消息
        friendly_error = """🤖 **AI服务暂时不可用**

系统正在处理您的请求时遇到了问题。

💡 **建议操作：**
• 等待1-2分钟后重新发送相同消息
• 检查消息格式是否正确
• 尝试简化请求内容

🔄 **重试提示：** 直接重新发送刚才的消息即可

⏰ **如果问题持续出现，请联系技术支持**"""
        
        # 发送异常响应
        if group_id:
            test_for_seatalk_bot(friendly_error, [seatalk_id], group_id)
        elif employee_code:
            try:
                await private_chat_client.send_text_message(employee_code, friendly_error)
            except:
                pass
        
        return JsonResponse({
            'message': error_msg,
            'success': False
        })


def handle_ai_query(ai_query: str, seatalk_id: str, group_id: str, data: dict):
    """AI查询处理函数的同步包装器"""
    from app01.statistics.decorators import track_bot_access_event
    
    # 记录用户访问事件
    track_bot_access_event({
        'event_id': data.get('event_id', f"manual_{int(time.time())}"),
        'event_type': 'ai_query',
        'timestamp': int(time.time()),
        'event': {
            'seatalk_id': seatalk_id,
            'employee_code': data.get('employee_code'),
            'email': data.get('email'),
            'group_id': group_id
        }
    })
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(handle_ai_query_async(ai_query, seatalk_id, group_id, data))
    finally:
        loop.close()


#打印日志的时候带上时间
def time_prefix(*args):
    # 返回一个字符串，而不是元组
    return f'{datetime.now()} | '

ic.configureOutput(prefix=time_prefix)

def retry_on_failure(max_retries=3, delay=10):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:  # 最后一次尝试
                        raise Exception(f"JIRA连接失败，已重试{max_retries}次。最后一次错误: {str(e)}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3, delay=10)
def get_jira_connection():
    return JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)


BASE_URL = "https://git.garena.com/api/v4/projects/"
jira_base_url = "https://jira.shopee.io"
#JIRA_TOKEN = "MDczMTIwMTAyODQyOuCFA/6xwPWDKQbEZmN+3QoxE0Ur"
#JIRA_TOKEN = "NDc0MzAyOTk0NzIxOsLGbXT2nYSeBpofk6kqS2xBFpek"
JIRA_TOKEN = "NTA4ODQ0NTE5OTQ2OpMq9xXh446Fs5eBqdDx9mM4tLaj" #liang.tang's
#zhuli的 gitlab token
#GITLAB_PRIVATE_TOKEN = "ef6M2p-3htssUbbLkgkK"
#liang.tang的 gitlab token
GITLAB_PRIVATE_TOKEN = "pCkAzptSzLpiv4tNKRma"
EMAIL = "<EMAIL>"
API_KEY = "lgu_1oC5Q05jXtrxcHAj5LyqyZR4eNtf7NxAjGa7"
LOG_URL = "https://carbon.log.test.shopee.io/openapi/v1/query/search"
BLACK_LIST = ["requestAiPlatform error|error=rpc error: code = DeadlineExceeded", "call dm spex failed",
              "call spex failed", "call getReturnRefundList get errCode: 10004",
              "getReturnRefundList get errCode: 15100003",
              "internal error, botmsgs Unmarshal error | msg_content: | err:unexpected end of JSON input"]
LOG_BASIC_URL = "shopee.chat_and_chatbot.cs_chatbot.engineer"
SHOPEE_APP = [f"{LOG_BASIC_URL}.platform.botapi",
              f"{LOG_BASIC_URL}.platform.intent",
              f"{LOG_BASIC_URL}.platform.agentcontrol",
              f"{LOG_BASIC_URL}.platform.dm",
              f"{LOG_BASIC_URL}.platform.nlu",
              f"{LOG_BASIC_URL}.platform.intentclarification",
              f"{LOG_BASIC_URL}.platform.asynctask",
              f"{LOG_BASIC_URL}.platform.adminservice",
              f"{LOG_BASIC_URL}.platform.featurecenter",
              f"{LOG_BASIC_URL}.platform.modelgw"
              ]
COMMON_APP = [f"{LOG_BASIC_URL}.chatbotcommon.botapi",
              f"{LOG_BASIC_URL}.chatbotcommon.intent",
              f"{LOG_BASIC_URL}.chatbotcommon.agentcontrol",
              f"{LOG_BASIC_URL}.chatbotcommon.dm",
              f"{LOG_BASIC_URL}.chatbotcommon.nlu",
              f"{LOG_BASIC_URL}.chatbotcommon.adminservice",
              f"{LOG_BASIC_URL}.chatbotcommon.featurecenter",
              f"{LOG_BASIC_URL}.chatbotcommon.adminasynctask"
              ]

SHOPEE_LOG_URL_BASE = "https://log.test.shopee.io/log-search?search-type=pipeline&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi&time-zone=8&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.dm&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intent&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.agentcontrol&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.nlu&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.intentclarification&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.asynctask&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.adminservice&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.featurecenter&" \
                      "selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.modelgw&" \
                      "pipeline-search-input="

COMMON_LOG_URL_BASE = "https://log.test.shopee.io/log-search?search-type=pipeline&selected-applications=shopee.chat_and_chatbot.cs_chatbot.engineer.platform.botapi&time-zone=8&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.botapi&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.intent&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.agentcontrol&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.dm&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.nlu&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminservice&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.featurecenter&" \
                      "selected-applications=shopee.chat_and_chatbot.shop_chatbot.engineer.chatbotcommon.adminasynctask&" \
                      "pipeline-search-input="

DATA_DEV_LIST = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                 "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                 "<EMAIL>", "<EMAIL>", "<EMAIL>",
                 "<EMAIL>"]
CHATBOT_BE_ALPHA = ["<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>"]
CHATBOT_BE_DELTA = ["<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>"]
CHATBOT_BE_BETA = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>"]
CHATBOT_FE = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
              "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"]
CHANNEL_DEV_LIST = ["<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>"]
CHATBOT_PM = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>",
              "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>",
              "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>",
              "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
              "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>",
              "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>",
              "<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>",
              "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
CHATBOT_DS = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>", "<EMAIL>"]

"""
    中间件，判断是否为同一天
"""
def is_same_day(date_str):
    # 获取当前本地日期
    current_date = datetime.now().date()

    # 将输入的日期字符串转换为datetime对象
    input_date = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S").date()

    # 判断是否为同一天
    if current_date == input_date:
        return True
    else:
        return False

"""
    部署失败服务的处理函数，通过crontab控制运行时间
"""
def failuremsg_final():
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] failuremsg_final 函数开始执行")
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    seatalk_url = "https://openapi.seatalk.io/webhook/group/ZHWKU3hhQtyYD-kwIjQlYQ"
    srv_list = {}
    srv_list_staging = {}
    result = gettoken()
    t = time.gmtime()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    for value in Config.srvMap_failmsg_chatbot.values():
        for i in value:
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            if not r.text:
                r = requests.post(url=history_url, json=parameters, headers=headers)
            result_rep = json.loads(r.text)
            if result_rep["errmsg"] == "success":
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']

                    if build_result == 'FAILURE' or build_result == 'ABORTED':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        end_time = result_rep["data"]["list"][0]['end_time']
                        timeArray = time.localtime(end_time / 1000)
                        formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                               "end_time": formatTime
                                               }
                        else:
                            srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                           "end_time": formatTime
                                           }
    for value in Config.srvMap_failmsg_chatbot.values():
        for i in value:
            i = i.replace("test", "uat")
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE' or build_result == 'ABORTED':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        end_time = result_rep["data"]["list"][0]['end_time']
                        timeArray = time.localtime(end_time / 1000)
                        formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                               "end_time": formatTime
                                               }
                        else:
                            srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                           "end_time": formatTime
                                           }

    for value in Config.srvMap_failmsg_chatbot.values():
        for i in value:
            i = i.replace("test", "staging")
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE' or build_result == 'ABORTED':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        end_time = result_rep["data"]["list"][0]['end_time']
                        timeArray = time.localtime(end_time / 1000)
                        formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                srv_list_staging[i] = {"branch": parameter["FROM_BRANCH"],
                                                       "end_time": formatTime
                                                       }
                        else:
                            srv_list_staging[i] = {"branch": parameter["FROM_BRANCH"],
                                                   "end_time": formatTime
                                                   }

    if srv_list:
        text_all = ""
        srv_id = Config.srv2id
        blacklist = Config.msgBlackList
        list_dev_all = []
        for i in srv_list.keys():
            if i in blacklist:
                continue
            msg_tmp = {}
            dev_name = ""
            title = ""
            code = ""
            list_dev = []
            branch_name_tmp = srv_list[i]["branch"].replace("origin/", "")
            if "-test" in i:
                if i.replace('-test', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-test', '')], branch_name_tmp)
            if "-uat" in i:
                if i.replace('-uat', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-uat', '')], branch_name_tmp)
            if "-staging" in i:
                if i.replace('-staging', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-staging', '')], branch_name_tmp)
            if msg_tmp:
                if "liujian1" in msg_tmp['name']:
                    dev_name = msg_tmp['name'].replace("liujian1", "liujian") + "@shopee.com"
                else:
                    dev_name = msg_tmp['name'] + "@shopee.com"
                title = msg_tmp['title']
                list_dev.append(dev_name)
                code = get_employee_code(list_dev)
                text_single = "服务部署失败，请高优先级处理\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                              srv_list[i]["end_time"] + "\n"
                if is_same_day(srv_list[i]["end_time"]):
                    single_chat(text_single, code)
                else:
                    if dev_name not in list_dev_all:
                        list_dev_all.append(dev_name)
                    if i == len(srv_list) - 1:
                        text_all = text_all + "=============================\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                                   srv_list[i]["end_time"]
                    else:
                        text_all = text_all + "=============================\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                                   srv_list[i]["end_time"] + "\n"
        if not text_all:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 没有找到失败的测试服务，函数结束")
            return
        text_all = "服务部署失败，请高优先级处理\n" + text_all
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 发送测试环境失败消息: {text_all}")
        test_for_seatalk_bot(text_all, list_dev_all, "MDk0NDQ0MjA2MDQ3")

    if srv_list_staging:
        text_all = ""
        srv_id = Config.srv2id
        blacklist = Config.msgBlackList
        list_dev_all = []
        for i in srv_list_staging.keys():
            if i in blacklist:
                continue
            msg_tmp = {}
            dev_name = ""
            title = ""
            code = ""
            list_dev = []
            branch_name_tmp = srv_list_staging[i]["branch"].replace("origin/", "")
            if "-staging" in i:
                if i.replace('-staging', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-staging', '')], branch_name_tmp)
            if msg_tmp:
                if "liujian1" in msg_tmp['name']:
                    dev_name = msg_tmp['name'].replace("liujian1", "liujian") + "@shopee.com"
                else:
                    dev_name = msg_tmp['name'] + "@shopee.com"
                title = msg_tmp['title']
                list_dev.append(dev_name)
                code = get_employee_code(list_dev)
                text_single = "服务部署失败，请高优先级处理\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                              srv_list_staging[i]["end_time"] + "\n"
                if is_same_day(srv_list_staging[i]["end_time"]):
                    single_chat(text_single, code)
                else:
                    if dev_name not in list_dev_all:
                        list_dev_all.append(dev_name)
                    text_all = text_all + "=============================\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                               srv_list_staging[i]["end_time"] + "\n"
        if not text_all:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 没有找到失败的staging服务，函数结束")
            return
        text_all = "\n服务部署失败，请高优先级处理\n" + text_all
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 发送staging环境失败消息: {text_all}")
        test_for_seatalk_bot(text_all, list_dev_all, "NDY0MzgxNDI4OTEw")
    
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] failuremsg_final 函数执行完成")

"""
    中间件，获取JIRA单标题格式内容，兼容多个项目
"""
def extract_content_JIRA(text):
    pattern = r"(SPCB|SPCSP|SPCT)-\d+"
    match = re.search(pattern, text)
    if match:
        content = match.group()
        return content
    else:
        return None

"""
    AR平台-提MR到release的按钮处理函数
"""
def ar_start_merge(request):
    if (request.method == 'POST'):
        BASE_URL = "https://git.garena.com/api/v4/projects/"
        postBody = request.body
        json_result = json.loads(postBody)
        ic(json_result)
        mr_map = Config.srv2id
        mr_id_repo = Config.mrMap
        title = json_result['title']
        services_list = json_result['services_list']
        if_merge = json_result['if_merge']
        repo_mr = {}
        new_repo_mr = []
        repo_list = set()
        for item in services_list:
            if item in mr_map.keys():
                repo_list.add(mr_map[item])
        ic(repo_list)
        repo_list = list(repo_list)
        new_array = {}
        for item in repo_list:
            for key, value in mr_id_repo.items():
                if item == value:
                    new_array[key] = value
        ic(new_array)
        if not if_merge:
            for key, value in new_array.items():
                autotag(title, value)
            return_data = {
                "msg": "success",
                "repo": new_repo_mr,
            }
            return HttpResponse(json.dumps(return_data))
        for key, value in new_array.items():
            real_url = BASE_URL + '{repo_id}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&source_branch=master&target_branch=release&title={title}'. \
                format(repo_id=value, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, title=title)
            ic(real_url)
            get_feedback_isue = requests.post(real_url)
            text_all = json.loads(get_feedback_isue.text)
            ic(text_all)
            if 'web_url' in text_all.keys():
                web_url = text_all['web_url']
                ic(web_url)
                result_merge = checkmergestatus(int(value), int(text_all['iid']))
                if result_merge:
                    autotag(title, value)
                    new_repo_mr.append(
                        {
                            "repo": key,
                            "url": web_url,
                            "status": 'merged'
                        }
                    )


                else:
                    new_repo_mr.append(
                        {
                            "repo": key,
                            "url": web_url,
                            "status": 'fail,no code change!'
                        }
                    )
            else:
                project_url = BASE_URL + '{repo_id}?private_token={GITLAB_PRIVATE_TOKEN}'.format(repo_id=value, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN)
                res = requests.get(project_url)
                res = json.loads(res.text)
                merge_url_link = f"{res['web_url']}/-/merge_requests"
                new_repo_mr.append(
                    {
                        "repo": key,
                        "url": merge_url_link,
                        "status": 'fail,please check if another MR already exist'
                    }
                )
        return_data = {
            "msg": "success",
            "repo": new_repo_mr,
        }
        return HttpResponse(json.dumps(return_data))

"""
    CI-当开发提交了一个MR后到master后，调用这个函数并且发送seatalk通知
"""
def real_time_push_message(request):
    if request.method == "POST":
        data = json.loads(request.body)
        result_mail = []
        des = data["des"]
        owner = data["owner"]
        title = data["title"]

        project_name = data["project_name"]
        data_fe_repo = ["web-dashboard", "web-microfe-operation-portal", "web-microfe-tmc", "web-ssar"]
        match = re.search(r'<(.*?)@', owner)
        jira_key = ""
        if match:
            result = match.group(1)

        match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', owner)
        if match:
            result_mail.append(match.group())

        url = data["real_mr_url"]
        if result_mail[0] == "<EMAIL>":
            result_mail[0] = "<EMAIL>"
        sourceBranch = data["sourceBranch"]
        targetBranch = data["targetBranch"]
        title = data["title"]
        jira_key = extract_content_JIRA(title)
        jira_url = "无"
        if jira_key:
            jira_url = f"https://jira.shopee.io/browse/{jira_key}"
        if blacklist(title):
            return JsonResponse({"data": "200"})
        release_name = get_jira_release_name(extract_content(title))
        text_all = f"[AR实时监控]\n{result}在{project_name}提交了一个MR，内容如下：\n🏷标题：{title}\n🏷需求单：{jira_url}\n🏷MR地址：{url}\n🏷MR描述：{des}\n🏷所属发布单：{release_name}\n🏷原分支：{sourceBranch}\n🏷目标分支：{targetBranch}\n"
        if result_mail[0] in ["<EMAIL>", "<EMAIL>", "<EMAIL>",
                              "<EMAIL>", "<EMAIL>", "<EMAIL>",
                              "<EMAIL>", "<EMAIL>", "<EMAIL>",
                              "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            if "SPCB" in title and project_name in data_fe_repo:
                group_id = "MTMxMDAyNDEyMTk1"

            if "SPCB" in title and project_name not in data_fe_repo:
                griup_id = "NDY0MzgxNDI4OTEw"
            else:
                group_id = "MTMxMDAyNDEyMTk1"
        elif result_mail[0] in CHANNEL_DEV_LIST:
            group_id = "NDc0MDg0ODY3NjI5"
        else:
            text_all = text_all + "\n如果确定合入，@自己leader，谢谢。"
            group_id = "NDY0MzgxNDI4OTEw"

        test_for_seatalk_bot(text_all, result_mail, group_id)
        return JsonResponse({"data": "200"})

"""
    通过标题，查询JIRA信息返回
"""
def get_release_by_title(release_title):
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    query = f'type = Release AND summary ~ "{release_title}"'
    # 执行 JQL 查询
    issues = jira.search_issues(query)
    # 输出符合条件的问题单标题
    release_list = []
    for issue in issues:
        length = len(issue.fields.issuelinks)
        if not length:
            print("There is no jira ticket contained.")
        for link in issue.fields.issuelinks:
            if "outwardIssue" in link.raw and link.raw["type"]["outward"] == "Contains":
                key = link.raw["outwardIssue"]["key"]
                title = link.raw["outwardIssue"]["fields"]["summary"]
                issue_type = link.raw["outwardIssue"]["fields"]["issuetype"]["name"]
                temp_data = {
                    "release_title": release_title,
                    "jira_key": key,
                    "jira_title": title,
                    "jira_link": f"https://jira.shopee.io/browse/{key}",
                    "type": issue_type
                }
                release_list.append(temp_data)

    return release_list

"""
    新的机器人发送私聊给个人的函数
"""
def sent_aio(text, employee_code):
    if not text:
        return
    url = "https://openapi.seatalk.io/messaging/v2/single_chat"
    result = app_access_token()
    tokens = "Bearer " + result
    param = {
        "employee_code": employee_code,
        "message": {
            "tag": "text",
            "text": {
                "content": text,
            },
        }
    }
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    r = requests.post(url=url, json=param, headers=headers)

def add_service_checklist(service, seatalk_id, group_id):
    """
    添加服务到JIRA Jenkins Job
    
    Args:
        service: 服务名称
        seatalk_id: 用户SeaTalk ID
        group_id: 群组ID（私聊时为None）
    """
    #message="添加新服务到 jira 请移步这里操作：https://space.shopee.io/release/portal/config_centre/service_config"
    #test_for_seatalk_bot(message, [seatalk_id], group_id)
    
    new_token = "MDYxNDgxOTU5NTgwOkXJSWQx9CAxhnN3OdncWxRuFMrE"
    addjoburl = "https://jira.shopee.io/rest/shopee_release_plugin_jenkins_job/latest/jenkins_job/add_jobs"
    headers = {
        "Authorization": f"Bearer {JIRA_TOKEN}",
        "content-type": "application/json"
    }
    body = {
        "jenkins_job": [service]
    }
    r = requests.post(url=addjoburl, headers=headers, json=body)
    ic(r)
    
    # 准备响应消息
    if r.status_code == 200:
        success_message = "添加成功！如果在 jira 上看不到刚添加的服务需要手动去这个页面操作：https://space.shopee.io/release/portal/config_centre/service_config。请同时联系 liang.tang在 AR 平台做这个新服务的相关配置。"
    else:
        success_message = "添加失败，请移步这里手工操作：https://space.shopee.io/release/portal/config_centre/service_config。请同时联系 liang.tang在 AR 平台做这个新服务的相关配置。"
    
    # 根据是否为群聊选择合适的发送方式
    try:
        if group_id:
            # 群聊：使用群聊API
            ic(f"发送群聊消息到群组: {group_id}")
            test_for_seatalk_bot(success_message, [seatalk_id], group_id)
        else:
            # 私聊：使用私聊API
            ic(f"发送私聊消息到用户: {seatalk_id}")
            try:
                # 首先尝试通过employee_code发送私聊消息
                from .views import get_employee_code
                # 注意：在私聊场景下，seatalk_id实际上是employee_code
                sent_aio(success_message, seatalk_id)
                ic("私聊消息发送完成")
            except Exception as private_error:
                ic(f"私聊发送失败，尝试其他方式: {str(private_error)}")
                # 如果私聊发送失败，记录错误但不抛出异常
                ic(f"服务添加操作已完成，但消息发送失败: {str(private_error)}")
    except Exception as send_error:
        ic(f"消息发送失败: {str(send_error)}")
        # 即使消息发送失败，JIRA操作可能已经成功，所以不抛出异常



def extract_string(input_string):
    """
    从输入字符串中提取服务名称
    
    Args:
        input_string: 输入字符串，如"add shopee-chatbot-mmfchatbotconsole-live"
        
    Returns:
        提取的服务名称，如果无法提取则返回None
    """
    if not input_string:
        return None
        
    parts = input_string.split()
    
    # 处理以add开头的指令
    if len(parts) >= 2 and parts[0].lower() == 'add':
        # 直接返回add后面的部分作为服务名称
        return parts[1]
    # 处理以service add开头的指令
    elif len(parts) >= 3 and parts[0].lower() == 'service' and parts[1].lower() == 'add':
        return parts[2]
    # 处理其他情况
    elif len(parts) >= 2:
        return parts[1]
    else:
        return None
#通过 seatalk 的群 ID 获取群组信息
def get_group_info(group_id):
    """
    获取群组信息，包括群名称、成员列表等
    
    Args:
        group_id: 群组ID
        
    Returns:
        完整的群组信息对象，如果获取失败则返回None
    """
    access_token = app_access_token()
    single_chat_url = f"https://openapi.seatalk.io/messaging/v2/group_chat/info?group_id={group_id}"
    headers = {
        'content-type': "application/json",
        'Authorization': "Bearer " + access_token
    }
    r = requests.get(url=single_chat_url, headers=headers)
    result = json.loads(r.text)
    ic(result) 
    if result["code"] == 0:
        print("success")
        return result  # 返回完整的结果对象
    else:
        print("fail")
        return None
# 获取epic的时间线和人员信息
def get_timeline_of_epic(jira_key):
    jira_url = "https://jira.shopee.io"
    jira = JIRA(jira_url, token_auth=JIRA_TOKEN)
    
    try:
        jira.current_user()
    except Exception as e:
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        return f"JIRA连接失败: {simplified_error}，请重试或联系 liang.tang"
    
    epic_issue = jira.issue(jira_key)
    epic_title = epic_issue.fields.summary
    fixVersions = epic_issue.fields.fixVersions
    
    # 获取日期信息
    dates = {
        "Created": epic_issue.fields.created,
        "Updated": epic_issue.fields.updated,
    }
    
    # 获取自定义字段 - 日期类信息
    # 注意：实际字段ID可能需要根据具体JIRA配置调整
    date_custom_fields = {
        "PRD Review Start Date": "customfield_11545",  # 示例ID，需替换为实际ID
        "PRD Review End Date": "customfield_11546",
        "Planned Dev Start Date": "customfield_11520",
        "Planned Dev Due Date": "customfield_11509",
        "Planned Integration Start Date": "customfield_12634",
        "Planned Integration End Date": "customfield_12635",
        "Planned QA Start Date": "customfield_11521",
        "Planned QA Due Date": "customfield_11510",
        "Planned UAT Start Date": "customfield_11522",
        "Planned UAT Due Date": "customfield_11511",
        "Planned Release Date": "customfield_11513",
        "Release Date": "customfield_11513",
        "TRD/PRD URL": "customfield_16700",
        "Cross Team with": "customfield_23400"
    }
    
    # 获取所有可用字段
    all_fields = jira.fields()
    field_map = {field['name']: field['id'] for field in all_fields}
    
    # 动态获取日期字段值
    for date_name, field_id in date_custom_fields.items():
        try:
            # 尝试使用实际的字段ID
            actual_field_id = field_map.get(date_name, field_id)
            value = getattr(epic_issue.fields, actual_field_id, None)
            if value:
                dates[date_name] = value
        except:
            # 如果字段不存在或获取失败，设为None
            dates[date_name] = None
    
    # 获取人员信息
    people = {
        "Assignee": epic_issue.fields.assignee.displayName if epic_issue.fields.assignee else None,
        "Reporter": epic_issue.fields.reporter.emailAddress if epic_issue.fields.reporter else None,
    }
    
    # 获取自定义字段 - 人员类信息
    people_custom_fields = {
        "Product Manager": "customfield_10306",  # 示例ID，需替换为实际ID
        "FE List": "customfield_37801",
        "BE List": "customfield_37800",
        "QA List": "customfield_12202",
        "Developer": "customfield_10307",
        "QA": "customfield_10308",
        "Project Manager": "customfield_10600",
    }
    
    # 动态获取人员字段值
    for person_name, field_id in people_custom_fields.items():
        try:
            # 尝试使用实际的字段ID
            actual_field_id = field_map.get(person_name, field_id)
            value = getattr(epic_issue.fields, actual_field_id, None)
            if value:
                # 处理不同类型的人员字段（单人/多人）
                if isinstance(value, list):
                    people[person_name] = [item.emailAddress if hasattr(item, 'emailAddress') else str(item) for item in value]
                else:
                    people[person_name] = value.emailAddress if hasattr(value, 'emailAddress') else str(value)
        except:
            # 如果字段不存在或获取失败，设为None
            people[person_name] = None
    
    # 获取项目相关的自定义字段
    project_info = {}
    project_custom_fields = {
        "TRD/PRD URL": "customfield_16700",
        "Cross Team with": "customfield_23400",
    }
    
    # 动态获取项目相关字段值
    for field_name, field_id in project_custom_fields.items():
        try:
            value = getattr(epic_issue.fields, field_id, None)
            if value:
                # 处理不同类型的字段值
                if isinstance(value, list):
                    project_info[field_name] = [str(item) for item in value]
                else:
                    project_info[field_name] = str(value)
            else:
                project_info[field_name] = None
        except:
            # 如果字段不存在或获取失败，设为None
            project_info[field_name] = None
    
    # 获取投票和观察者
    votes = {
        "Votes": epic_issue.fields.votes.votes if hasattr(epic_issue.fields.votes, 'votes') else 0
    }
    
    watchers = {
        "Watchers": len(jira.watchers(epic_issue).watchers) if hasattr(jira.watchers(epic_issue), 'watchers') else 0
    }
    
    # 组合所有信息
    timeline_info = {
        "epic_key": jira_key,
        "epic_title": epic_title,
        "status": epic_issue.fields.status.name if epic_issue.fields.status else "Unknown",
        "dates": dates,
        "people": people,
        "votes": votes,
        "watchers": watchers,
        "project_info": project_info
    }
    # 创建美化的Seatalk消息
    jira_url = f"https://jira.shopee.io/browse/{jira_key}"
    
    message = f"📊 **需求时间线概览 | {jira_key}** 📊\n\n"
    message += f"📌 **{epic_title}**\n"
    message += f"🔗 {jira_url}\n"
    
    # 添加项目相关信息到JIRA链接后面
    if project_info.get("TRD/PRD URL"):
        trd_prd_url = project_info["TRD/PRD URL"]
        message += f"📄 TRD/PRD URL: {trd_prd_url}\n"
    
    if project_info.get("Cross Team with"):
        cross_team = project_info["Cross Team with"]
        if isinstance(cross_team, list):
            cross_team = ", ".join(cross_team)
        message += f"🤝 Cross Team with: {cross_team}\n"
    
    
    # 添加对fixVersions的检查
    if fixVersions and len(fixVersions) > 0:
        fix_versions_names = [version.name for version in fixVersions]
        message += f"📒 Fix Version/s: **{', '.join(fix_versions_names)}**\n\n"
    else:
        message += f"📒 Fix Version/s: **未设置**\n\n"
    # 日期部分
    message += f"⏰ **关键日期:━━━━━━━━━━━━━**\n"
    
    # 优先显示这些重要日期
    important_dates = [
        "PRD Review Start Date",
        "PRD Review End Date",
        "Planned Dev Start Date", 
        "Planned Dev Due Date", 
        "Planned Integration Start Date",
        "Planned Integration End Date",
        "Planned QA Start Date", 
        "Planned QA Due Date", 
        "Planned UAT Start Date", 
        "Planned UAT Due Date", 
        "Planned Release Date",
        "Release Date"
    ]
    
    # 先显示重要日期
    for date_name in important_dates:
        if date_name in dates and dates[date_name]:
            date_value = dates[date_name]
            # 将ISO格式日期转换为更友好的格式
            try:
                if isinstance(date_value, str) and 'T' in date_value:  # 处理带时间的ISO格式
                    date_obj = datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                    formatted_date = date_obj.strftime('%Y-%m-%d')
                else:  # 处理仅日期的格式
                    formatted_date = date_value
                message += f"• {date_name}: **{formatted_date}**\n"
            except:
                message += f"• {date_name}: **{date_value}**\n"
    message += "\n"
    
    # 人员部分
    message += f"👥 **团队成员:━━━━━━━━━━━━━**\n"
    
    # 优先显示这些重要角色
    important_roles = [
        "Product Manager",
        "Developer",
        "FE List",
        "BE List",
        "QA",
        "QA List",
        "Project Manager"
    ]
    
    # 角色显示名称映射
    role_display_names = {
        "Product Manager": "PM",
        "Developer": "Tech PIC",
        "FE List": "FE List",
        "BE List": "BE List",
        "QA": "QA",
        "QA List": "QA List",
        "Project Manager": "Project Manager"
    }
    
    # 先显示重要角色
    for person_name in important_roles:
        if person_name in people and people[person_name]:
            person_value = people[person_name]
            if isinstance(person_value, list):
                # 处理多人字段
                message += f"• {role_display_names[person_name]}: "
                for i, person in enumerate(person_value):
                    email = person if '@' in person else person
                    # 使用Seatalk的@功能
                    message += f"<mention-tag target=\"seatalk://user?email={email}\"/> "
                    if i < len(person_value) - 1:
                        message += ", "
                message += "\n"
            else:
                # 处理单人字段
                email = person_value if '@' in person_value else person_value
                message += f"• {role_display_names[person_name]}: <mention-tag target=\"seatalk://user?email={email}\"/>\n"
    message += "\n"
    
    from .bot_config import bot_config
    message += f"💡 需查看此需求下的bug状态，请发送: `@{bot_config.BOT_NAME} bug`"
    
    return message    
#获取 epic 下的 issues
def get_issues_in_epic(jira_key):
    import urllib.parse
    jira_url = "https://jira.shopee.io"
    jira = JIRA(jira_url, token_auth=JIRA_TOKEN)
    
    try:
        jira.current_user()
    except Exception as e:
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        return f"JIRA连接失败: {simplified_error}，请重试或联系 liang.tang"
    
    epic_issue = jira.issue(jira_key)
    epic_title = epic_issue.fields.summary

    jql = f'issue in linkedIssues("{jira_key}", "is blocked by") AND type = bug'
    issues = jira.search_issues(jql)
    
    if not issues:
        return f'"{epic_title}"此需求下目前没有待处理的BUG，恭喜。'

    # 统计不同状态的bug数量
    status_count = {}
    resolved_statuses = ['Done', 'Closed', 'Icebox']
    # 更新未解决状态列表，包含WAITING状态
    unresolved_statuses = ['TO DO', 'DOING', 'TESTING', 'WAITING']
    bugs_by_status = {status: [] for status in unresolved_statuses}

    total_bugs = len(issues)
    resolved_count = 0

    for issue in issues:
        status = issue.fields.status.name.upper()  # 将状态名转换为大写
        status_count[status] = status_count.get(status, 0) + 1
        
        if status in [s.upper() for s in resolved_statuses]:
            resolved_count += 1
        
        # 检查未解决状态时使用大写比较
        if status in unresolved_statuses:  # 直接比较大写状态名
            assignee = issue.fields.assignee.emailAddress if issue.fields.assignee else "Unassigned"
            priority = issue.fields.priority.name if issue.fields.priority else "No Priority"
            bug_info = {
                'key': issue.key,
                'url': f"https://jira.shopee.io/browse/{issue.key}",
                'title': issue.fields.summary,
                'priority': priority,
                'assignee': assignee
            }
            bugs_by_status[status].append(bug_info)

    # 计算解决率
    resolution_rate = (resolved_count / total_bugs * 100) if total_bugs > 0 else 0

    # 生成输出文本
    all_text = f'需求"【{jira_key}】{epic_title}"的BUG统计信息：\n'
    all_text += f'总计: {total_bugs}个BUG，'
    all_text += f'解决率: {resolution_rate:.1f}%\n'
    all_text += '\n各状态分布：\n'
    for status, count in status_count.items():
        all_text += f'{status}: {count}个\n'
    
    # 显示bug时使用原始状态名称的映射
    status_display = {
        'TO DO': 'To Do',
        'DOING': 'Doing', 
        'TESTING': 'Testing',
        'WAITING': 'Waiting'
    }
    
    # 按状态分组显示未解决的bug
    has_unresolved = False
    for status in unresolved_statuses:
        bugs = bugs_by_status[status]
        if bugs:
            has_unresolved = True
            all_text += f'\n【{status_display[status]}】状态的BUG：\n'
            for num, bug in enumerate(bugs, 1):
                all_text += f"{num}. 【{bug['priority']}】{bug['title']}\n"
                all_text += f"🔗 {bug['key']}: {bug['url']}\n"
                all_text += f"处理人: <mention-tag target=\"seatalk://user?email={bug['assignee']}\"/>\n"
                all_text += "------------------------------------------------\n"
    
    if not has_unresolved:
        all_text += '\n赞👍，所有BUG都已解决！'

    # 添加 JIRA 链接 - 使用纯文本格式
    # 对JQL进行完整的URL编码，处理所有特殊字符
    encoded_jql = urllib.parse.quote(jql, safe='')
    jira_search_url = f"https://jira.shopee.io/issues/?jql={encoded_jql}"
    all_text += f"\n🔗 查看所有相关BUG:\n{jira_search_url}"

    return all_text
"""
    seatalk回调函数，如果机器人被拉进新群，或者有私聊，会进行保存跟处理
    现在使用上下文感知的命令处理器统一处理群聊和私聊
"""
def get_seatalk_recall(request):
    if request.method == "POST":

        data = json.loads(request.body)
        ic(data)

        # 收集机器人访问事件统计数据
        try:
            from app01.statistics.decorators import track_bot_access_event

            # 收集所有类型的访问事件
            if data.get("event_type") in [
                "user_enter_chatroom_with_bot",
                "user_leave_chatroom_with_bot",
                "bot_added_to_group_chat",
                "bot_removed_from_group_chat",
                "user_start_chat_with_bot",
                "user_block_bot",
                "user_unblock_bot"
            ]:
                track_bot_access_event(data)
        except Exception as e:
            ic(f"统计数据收集失败: {e}")

        # 处理机器人加入群聊事件
        if data["event_type"] == "bot_added_to_group_chat":
            group_id = data["event"]["group"]["group_id"]
            group_name = data["event"]["group"]["group_name"]
            if SeatalkGroup.objects.filter(group_id=group_id).exists():
                return JsonResponse({})

            # 创建 SeatalkGroup 对象并保存到数据库
            seatalk_group = SeatalkGroup(group_id=group_id, group_name=group_name)
            seatalk_group.save()
            return JsonResponse({})
        
        # 使用新的上下文感知命令处理器
        if data["event_type"] in ["new_mentioned_message_received_from_group_chat", "message_from_bot_subscriber"]:
            try:
                # 处理线程ID和消息ID，如果存在
                if data.get("event_type") == "new_mentioned_message_received_from_group_chat":
                    message_data = data.get("event", {}).get("message", {})
                    group_id = data.get("event", {}).get("group_id")
                    thread_id = message_data.get("thread_id")
                    message_id = message_data.get("message_id")
                    
                    # 存储消息ID和线程ID
                    from app01.seatalk_group_manager import ThreadReplyContext
                    # 先清理可能存在的旧上下文
                    ThreadReplyContext.clear_thread_context()
                    
                    # 设置新的上下文，同时保存消息ID和线程ID
                    # 根据SeaTalk API文档，thread_id表示该消息所属的线程
                    ThreadReplyContext.set_thread_context(group_id, thread_id, message_id)
                    
                    # 记录详细日志，帮助调试
                    if thread_id:
                        if thread_id == message_id:
                            ic(f"收到线程根消息: 群组={group_id}, 线程ID={thread_id}, 消息ID={message_id}")
                        else:
                            ic(f"收到线程回复消息: 群组={group_id}, 线程ID={thread_id}, 消息ID={message_id}")
                    else:
                        ic(f"收到普通消息: 群组={group_id}, 消息ID={message_id}")
                
                from .command_processor import MessageContext, command_processor
                
                # 创建消息上下文
                context = MessageContext(data)

                # 如果上下文无效，发送帮助信息
                if not context.is_valid:
                    ic("上下文无效，发送帮助信息")
                    try:
                        from .bot_config import bot_config
                        if data.get("event_type") == "new_mentioned_message_received_from_group_chat":
                            help_message = bot_config.generate_help_message('group')
                            seatalk_id = data["event"]["message"]["sender"]["seatalk_id"]
                            group_id = data["event"]["group_id"]
                            test_for_seatalk_bot(help_message, [seatalk_id], group_id)
                        else:
                            help_message = bot_config.generate_help_message('private')
                            employee_code = data["event"]["employee_code"]
                            sent_aio(help_message, employee_code)
                    except Exception as e:
                        ic(f"发送帮助信息失败: {str(e)}")
                    return HttpResponse('200')

                # 开始指令执行跟踪
                execution_id = None
                try:
                    from app01.statistics.decorators import create_command_tracker

                    # 创建指令跟踪器
                    tracker = create_command_tracker(
                        user_id=context.seatalk_id,
                        command_type='unknown',  # 将在处理过程中更新
                        raw_input=context.text,
                        user_email=context.email,
                        employee_code=context.employee_code,
                        group_id=context.group_id
                    )

                    with tracker:
                        # 处理命令
                        result = command_processor.process_command(context)

                        # 更新跟踪信息
                        if result:
                            tracker.update_result(
                                response_content=str(result.get('message', ''))[:5000],
                                processed_command=result.get('processed_command', ''),
                                api_calls=result.get('api_calls', []),
                                database_queries=result.get('database_queries', 0),
                                external_service_calls=result.get('external_service_calls', {})
                            )

                except Exception as tracking_error:
                    ic(f"指令跟踪失败: {tracking_error}")
                    # 如果跟踪失败，仍然继续处理命令
                    result = command_processor.process_command(context)
                
                # 如果是AI响应且已处理，直接返回
                if result.get('type') == 'ai_response' and result.get('handled'):
                    ic("AI响应已处理，直接返回")
                    return result['response']
                
                # 发送响应消息（仅对非AI响应）
                if 'message' in result and result.get('type') != 'ai_response':
                    _send_contextual_response(context, result['message'])
                
                return HttpResponse('200')
                
            except Exception as e:
                ic(f"新命令处理器异常: {str(e)}")
                # 检查是否是JsonResponse类型错误
                if "JsonResponse" in str(e) and "has no len()" in str(e):
                    ic("检测到JsonResponse类型错误，可能是AI处理返回，跳过错误处理")
                    return HttpResponse('200')
                
                # 其他错误时发送错误信息
                error_message = f"处理消息时出现错误，请稍后重试。"
                try:
                    if data.get("event_type") == "new_mentioned_message_received_from_group_chat":
                        seatalk_id = data["event"]["message"]["sender"]["seatalk_id"]
                        group_id = data["event"]["group_id"]
                        
                        # 使用更可靠的消息发送方式
                        try:
                            from app01.seatalk_group_manager import send_message_to_group
                            send_message_to_group(group_id, error_message)
                        except Exception as send_error:
                            # 如果上面的方法失败，尝试直接调用原始函数
                            test_for_seatalk_bot(error_message, [seatalk_id], group_id)
                    else:
                        employee_code = data["event"]["employee_code"]
                        sent_aio(error_message, employee_code)
                except Exception as send_error:
                    ic(f"发送错误信息也失败了: {str(send_error)}")
                return HttpResponse('500')
        
        # 原有处理逻辑已被上面的新命令处理器替代
        # 以下代码保留作为注释，以备需要时参考
        """
        # 兼容处理：原有的消息处理逻辑（已被新的命令处理器替代）
        if data["event_type"] == "new_mentioned_message_received_from_group_chat" or data["event_type"] == "message_from_bot_subscriber":
            # 原有的处理逻辑...
        """
        if "seatalk_challenge" in data["event"]:
            seatalk_challenge = data["event"]["seatalk_challenge"]

            res_data = {
                "seatalk_challenge": seatalk_challenge
            }
        else:
            res_data = {}
    return JsonResponse(res_data)


def _send_contextual_response(context, message: str):
    """根据上下文发送响应消息"""
    result = False
    try:
        if context.type == 'group':
            # 获取当前上下文中的线程ID和消息ID（如果有）
            from app01.seatalk_group_manager import ThreadReplyContext
            _, thread_id, message_id = ThreadReplyContext.get_thread_context(context.group_id)
            ic(f"发送响应，群组={context.group_id}, 线程ID={thread_id}, 消息ID={message_id}")
            
            # 根据SeaTalk线程API文档的理解：
            # 1. 如果消息有thread_id，说明是在线程中，我们应该使用相同的thread_id回复
            # 2. 如果有message_id，可以使用quoted_message_id引用回复
            # 3. 可以同时使用thread_id和quoted_message_id
            if thread_id and message_id:
                # 在线程中回复，同时使用线程ID和引用消息ID
                ic(f"在线程中回复，同时使用线程ID: {thread_id} 和引用消息ID: {message_id}")
                result = test_for_seatalk_bot(message, [context.seatalk_id], context.group_id, thread_id, message_id)
            elif thread_id:
                # 在线程中回复，只使用线程ID
                ic(f"在线程中回复，使用线程ID: {thread_id}")
                result = test_for_seatalk_bot(message, [context.seatalk_id], context.group_id, thread_id, None)
            elif message_id:
                # 普通消息，使用引用消息ID回复
                ic(f"使用引用消息回复普通消息: {message_id}")
                result = test_for_seatalk_bot(message, [context.seatalk_id], context.group_id, None, message_id)
            else:
                # 没有线程ID和消息ID，发送普通消息
                ic("发送普通消息，不使用线程ID或引用消息ID")
                result = test_for_seatalk_bot(message, [context.seatalk_id], context.group_id)
        else:  # private
            result = sent_aio(message, context.employee_code)
        return result
    finally:
        # 仅在非AI响应处理时清理线程上下文
        # AI响应处理已经在handle_ai_query_async中清理了线程上下文
        pass


# 注意：原有的_handle_message_legacy函数由于缩进错误已被移除
# 如果需要后备处理逻辑，可以在get_seatalk_recall函数中直接实现
"""
    AR平台-前端接口，从DB拿到发布单数据返回给前端
"""
def get_all_jira_release_list_details(request):
    queryset = JIRAReleaseListDetails.objects.all().order_by('date')
    result = []
    for item in queryset:
        data = {
            'date': item.date,
            'url': item.url,
            'count': item.count,
            'name': item.name}
        result.append(data)
    return JsonResponse({"data": result})

"""
    AR平台-日历页面，保存发布单列表到DB
"""
def save_JIRA_Release_List_Details():
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    data_todo = 'status != CLOSED AND status !=DONE'
    query_todo = f"project= spcb and type = Release AND summary ~ 发布单 AND {data_todo} ORDER BY created DESC"
    issue_todo = jira.search_issues(query_todo)
    ic(issue_todo)
    for issue in issue_todo:
        key_url = "https://jira.shopee.io/browse/" + str(issue.key)
        summary=issue.raw["fields"]["summary"]
        ic(summary)
        real_data = format_date(summary)
        ic(real_data)
        release_list = get_release_by_title(issue.raw["fields"]["summary"])
        count_release = str(len(release_list))
        name = issue.raw["fields"]["summary"]
        try:
            obj = JIRAReleaseListDetails.objects.get(name=name)
            obj.date = real_data
            obj.url = key_url
            obj.count = count_release
            obj.save()
        except JIRAReleaseListDetails.DoesNotExist:
            JIRAReleaseListDetails.objects.create(
                date=real_data,
                url=key_url,
                count=count_release,
                name=name
            )

"""
    获取分支最后的commit的开发，用来精准定位
"""
def get_last_commit_with_branch(url, branch):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    mr_list = f"{BASE_URL}{url}/repository/commits?private_token={GITLAB_PRIVATE_TOKEN}&ref_name={branch}"
    res = requests.get(mr_list)
    res_text = json.loads(res.text)
    edit_name = ""
    if res_text:
        if type(res_text) == dict:
            edit_name = ""
            return edit_name
        if "author_email" in res_text[0]:
            edit_name = res_text[0]["author_email"].replace("@shopee.com", "")
            return edit_name
    return edit_name

"""
    通过key获取JIRA标题
"""
def get_jira_release_name(key):
    jira = JIRA(server='https://jira.shopee.io', token_auth=JIRA_TOKEN)
    if key:
        try:
            issue = jira.issue(key)
        except JIRAError as e:
            return "无"
    else:
        return "无"
    release_name = ""
    for link in issue.fields.issuelinks:
        if hasattr(link, "outwardIssue") and link.type.name.lower() == "release scope":
            outward_issue = link.outwardIssue
            release_name = outward_issue.fields.summary
        if hasattr(link, "inwardIssue") and link.type.name.lower() == "release scope":
            inward_issue = link.inwardIssue
            release_name = inward_issue.fields.summary
    if release_name:
        return release_name
    else:
        return "无"

"""
    日期转换函数
"""
def format_date(string):
# 匹配包含6位以上或正好6位的数字
    pattern = r'(\d{6,})'
    match = re.search(pattern, string)
    
    if match:
        # 提取匹配到的数字串
        number_str = match.group(1)
        # 如果长度超过6位，只取最后6位
        if len(number_str) > 6:
            number_str = number_str[-6:]
        
        # 提取年份、月份和日期
        year = "20" + number_str[:2]
        month = number_str[2:4]
        day = number_str[4:6]
        date_str = f"{year}-{month}-{day}"
        return date_str
    else:
        return None

"""
    AR平台-获取所有发布数据，包含完成跟todo的，计算数量返回给前端
"""
def getAllJiraReleaseList(request):
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    data_todo = 'status != CLOSED AND status !=DONE'
    # 扩展查询条件以支持SPCT项目
    query_todo = f"type = Release AND (summary ~ 发布单 OR project = SPCT) AND {data_todo} ORDER BY created DESC"
    release_count = {}
    # 增加maxResults避免前端显示不全
    issue_todo = jira.search_issues(query_todo, maxResults=500)
    releases = ReleaseTitle.objects.values_list('releaseTitle', flat=True)
    release_count["done"] = len(releases)
    release_count["todo"] = len(issue_todo)
    return JsonResponse(release_count)

"""
    AR平台-日历页面，从DB读数据返回给前端
"""
def getCalendarJiraReleaseList(request):
    queryset = CalendarJiraReleaseList.objects.all()
    result = {}
    for item in queryset:
        result[item.name] = item.data
    return JsonResponse(result)

"""
    AR平台-日历页面，保存日历发布信息到DB
"""
def saveCalendarJiraReleaseList():
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    today = datetime.now().strftime('%Y-%m-%d')
    next_day = (datetime.strptime(today, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
    last_month = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    date_range = 'created >= "{}" and created <= "{}"'.format(last_month, next_day)

    # 定义 JQL 查询语句，支持SPCT项目
    query = f"type = Release AND (summary ~ 发布单 OR project = SPCT) AND {date_range} ORDER BY created DESC"
    # 执行 JQL 查询，增加maxResults避免数据不全
    issues = jira.search_issues(query, maxResults=500)

    for issue in issues:
        key_url = "https://jira.shopee.io/browse/" + str(issue.key)
        real_data = format_date(issue.raw["fields"]["summary"])
        name = issue.raw["fields"]["summary"]
        try:
            obj = CalendarJiraReleaseList.objects.get(name=name)
            obj.data = {"date": real_data, "url": key_url}
            obj.save()
        except CalendarJiraReleaseList.DoesNotExist:
            CalendarJiraReleaseList.objects.create(
                data={"date": real_data, "url": key_url},
                name=name
            )

"""
    字符串转字典
"""
def strtodict(context):
    if isinstance(context, str):
        return json.loads(context)
    else:
        return context

"""
    AR平台-获取发布服务列表的函数
"""
def getchecklist(key):
    services_list_fe = []
    services_list_be = []
    new_token = "MDYxNDgxOTU5NTgwOkXJSWQx9CAxhnN3OdncWxRuFMrE"
    url = "https://jira.shopee.io/rest/shopee_release_plugin_checklist/latest/checklist/get_checklist"
    headers = {
        "Authorization": f"Bearer {new_token}",
        "content-type": "application/json"
    }
    params = {
        "issue_key": key,
    }
    
    try:
        r = requests.get(url=url, headers=headers, params=params)
        if "no_checklist_data_found" in r.text:
            ic(f"JIRA单 {key} 没有checklist数据")
            return {"services_list_fe": services_list_fe, "services_list_be": services_list_be}
        
        results = strtodict(r.text)
        if not results or not isinstance(results, dict):
            ic(f"JIRA单 {key} - strtodict(results) 返回无效数据: {results}")
            return {"services_list_fe": services_list_fe, "services_list_be": services_list_be}

        results_dict = strtodict(results)
        if not results_dict or not isinstance(results_dict, dict) or "checklist_data" not in results_dict:
            ic(f"JIRA单 {key} - 没有找到checklist_data字段")
            return {"services_list_fe": services_list_fe, "services_list_be": services_list_be}
            
        results_1 = results_dict["checklist_data"]
        results_1_dict = strtodict(results_1)
        if not results_1_dict or not isinstance(results_1_dict, dict) or "checklist" not in results_1_dict:
            ic(f"JIRA单 {key} - 没有找到checklist字段")
            return {"services_list_fe": services_list_fe, "services_list_be": services_list_be}

        for temp in results_1_dict['checklist']:
            try:
                temp_result = strtodict(temp)
                if not temp_result or not isinstance(temp_result, dict) or "content" not in temp_result:
                    continue
                    
                pattern = "shopee.*"
                result_fin = re.search(pattern, temp_result["content"])
                if not result_fin:
                    continue
                    
                service_name = result_fin[0]
                if "static" in service_name or "cschat-h5" in service_name or "cs-chat" in service_name or "shopee-chatbot-insights" in service_name or "shopee-chatbot-mmfchatbotconsole" in service_name:
                    result = re.sub(r'-live$', '', service_name)
                    services_list_fe.append(result)
                else:
                    result = re.sub(r'-live$', '', service_name)
                    services_list_be.append(result)
            except Exception as temp_e:
                ic(f"处理单个checklist项时出错 (JIRA: {key}): {str(temp_e)}")
                continue

    except Exception as e:
        ic(f"获取JIRA单 {key} 的checklist时出错: {str(e)}")
        return {"services_list_fe": services_list_fe, "services_list_be": services_list_be}

    return {"services_list_fe": services_list_fe, "services_list_be": services_list_be}

"""
    AR平台-从分支提MR到master的核心函数
"""
def mergefromfeature(branch, projectID):
    """
    ⚠️  DEPRECATED - 此函数已废弃，计划删除
    ❌ 此函数未被任何地方调用，为历史遗留代码
    📅 标记时间: 2025-06-11
    🔄 如果您看到此警告，请确认是否仍需要此函数
    """
    import warnings
    warnings.warn(f"Function 'mergefromfeature' is deprecated and will be removed", DeprecationWarning, stacklevel=2)
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    title = "[AutoRelease]AR From feature to master"
    real_url = BASE_URL + '{repo_id}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&source_branch={branch}&target_branch=master&title={title}'. \
        format(repo_id=projectID, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, branch=branch, title=title)
    get_feedback_isue = requests.post(real_url)
    text_all = json.loads(get_feedback_isue.text)
    iid = text_all["iid"]
    count = 0
    count_mr = 0
    single_url = f"{BASE_URL}{projectID}/merge_requests/{iid}?private_token={GITLAB_PRIVATE_TOKEN}"
    get_rule = f"{BASE_URL}{projectID}/merge_requests/{iid}/approval_rules?private_token={GITLAB_PRIVATE_TOKEN}"
    res = requests.get(get_rule)
    rule_list = json.loads(res.text)
    for rule in rule_list:
        if rule["approvals_required"] != 0:
            id = rule["id"]
            name = rule["name"]
            approval_rules = f"{BASE_URL}{projectID}/merge_requests/{iid}/approval_rules/{id}?private_token={GITLAB_PRIVATE_TOKEN}&approvals_required=0&name={name}"
            approval_put = requests.put(approval_rules)
    while count_mr <= 20:
        single_post = requests.get(single_url)
        new_status = json.loads(single_post.text)["merge_status"]
        if new_status == "cannot_be_merged":
            break
        if new_status == "can_be_merged":
            merge_url = f"{BASE_URL}{projectID}/merge_requests/{iid}/merge?private_token={GITLAB_PRIVATE_TOKEN}"
            put_merge = requests.put(merge_url)
            print(put_merge.text)
            break
        else:
            time.sleep(5)
            count_mr += 1

"""
    检查MR的状态
"""
def checkMRstate(url, web_url):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    segments = web_url.split("/")
    iid = segments[-1]
    real_url = f"{BASE_URL}{url}/merge_requests/{iid}?private_token={GITLAB_PRIVATE_TOKEN}"
    get_feedback_isue = requests.get(real_url)
    text_all = json.loads(get_feedback_isue.text)
    return text_all["state"]

"""
    AR平台-子表格里，对单个需求进行单个MR的按钮
"""
def start_single_ar(request):
    data = json.loads(request.body)
    jira_key = data["jira_key"]
    release_title = data["release_title"]
    release_key = data["release_key"]
    if data["merge_list"]:
        data_all = []
        for i in data["merge_list"]:
            if "merge_status" in i:
                if i["merge_status"] == "opened":
                    continue
                else:
                    url = i["repo_url"]
                    url = url.replace("https://git.garena.com/", "")
                    url = url.replace(".git", "")
                    repo_name = i["repo_name"]
                    url = url.replace("/", "%2f")
                    data_all.append(
                        get_jira_field_value(jira_key, release_title, release_key, if_merge=True, need_clean=True))

        return JsonResponse({"data": data_all})

    else:
        return JsonResponse({"data": "success"})

"""
    通过branch的方式获取MR信息，主要获取最后MR的开发，精准找到开发
"""
def get_MR_with_branch(url, source_branch):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    target_branch = "master"
    mr_list = f"{BASE_URL}{url}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&source_branch={source_branch}&target_branch={target_branch}"
    res = requests.get(mr_list)
    res_text = json.loads(res.text)
    last_mr = {}
    if isinstance(res_text, dict):
        if res_text["message"] == "404 Project Not Found":
            return last_mr
    for i in res_text:
        if i["state"] == "opened":
            last_mr["status"] = "opened"
            last_mr["web_url"] = i["web_url"]
            if i["author"]["username"] == "weibin.fang":
                last_mr["author"] = "ChatbotAR"
            else:
                last_mr["author"] = i["author"]["username"]
            break
        if i["state"] == "merged":
            last_mr["status"] = "merged"
            last_mr["web_url"] = i["web_url"]
            if i["author"]["username"] == "weibin.fang":
                last_mr["author"] = "ChatbotAR"
            else:
                last_mr["author"] = i["author"]["username"]
            break
        if i["state"] == "closed" and i["author"]["username"] == "weibin.fang":
            last_mr["author"] = "ChatbotAR"
            last_mr["status"] = "closed"
            last_mr["web_url"] = i["web_url"]
    return last_mr

"""
    中间件，对字段进行处理，并发送MR请求，并且返回merge的列表
"""
def sentMR(feature_title, key, url, branch, release_title, if_merge, issue_key, release_key):
    merge_status = {}
    merge_status["repo_url"] = url
    url = url.replace("https://git.garena.com/", "")
    url = url.replace(".git", "")
    last_slash_index = url.rfind("/")
    if last_slash_index != -1:
        repo_name = url[last_slash_index + 1:]
    else:
        repo_name = "None"
    url = url.replace("/", "%2f")
    merge_status["repo_name"] = repo_name
    merge_status["branch_name"] = branch
    if not if_merge:
        try:
            row = Autorelease.objects.get(releaseKey=release_key)
        except Autorelease.DoesNotExist:
            return
        feature_list = row.releaseData
        if not feature_list:
            return merge_status
        for i in feature_list:
            if i["feature_key"] == issue_key:
                if not i["merge_list"]:
                    return "None"
                if "merge_list" in i:
                    for merge_single in i["merge_list"]:
                        if not merge_single:
                            continue
                        pic_name = get_last_commit_with_branch(url, branch)
                        merge_status["pic"] = pic_name
                        if "web_url" in merge_single:
                            if repo_name == merge_single["repo_name"]:
                                if merge_single["merge_status"] == "merged" or merge_single["merge_status"] == "closed":
                                    merge_status["merge_status"] = merge_single["merge_status"]
                                if merge_single["merge_status"] != "merged" and merge_single[
                                    "merge_status"] != "closed":
                                    if "Another open merge request already exists" in merge_single["merge_status"]:
                                        merge_status["merge_status"] = merge_single["merge_status"]
                                    else:
                                        temp_state = checkMRstate(url, merge_single["web_url"])
                                        merge_status["merge_status"] = temp_state

                                merge_status["web_url"] = merge_single["web_url"]
                                last_mr = get_MR_with_branch(url, branch)
                                if not last_mr:
                                    continue
                                if last_mr["status"] != "closed":
                                    merge_status["web_url"] = last_mr["web_url"]
                                    merge_status["author"] = last_mr["author"]
                                    merge_status["merge_status"] = last_mr["status"]
                            else:
                                continue
                        else:
                            last_mr = get_MR_with_branch(url, branch)
                            if not last_mr:
                                continue
                            if last_mr["status"] != "closed":
                                merge_status["web_url"] = last_mr["web_url"]
                                merge_status["author"] = last_mr["author"]
                                merge_status["merge_status"] = last_mr["status"]
                            else:
                                merge_status["author"] = last_mr["author"]
                                merge_status["merge_status"] = last_mr["status"]
                                merge_status["web_url"] = last_mr["web_url"]
        return merge_status
    if if_merge:
        mr_title = f"[{issue_key}][AR]-{release_title}-[MR FROM {branch} TO MASTER]"
        real_url = BASE_URL + '{url}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&source_branch={branch}&target_branch=master&title={mr_title}'.format(
            url=url, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, branch=branch, mr_title=mr_title)
        get_feedback_isue = requests.post(real_url)
        text_all = json.loads(get_feedback_isue.text)
        pic_name = get_last_commit_with_branch(url, branch)
        merge_status["pic"] = pic_name
        if "state" in text_all.keys():
            merge_status["merge_status"] = text_all["state"]
            merge_status["web_url"] = text_all["web_url"]
            sent_mr_to_seatalk(feature_title, issue_key, release_title, branch, repo_name, text_all["web_url"],
                               text_all["project_id"])
        if "merge_status" not in text_all.keys():
            last_mr = get_MR_with_branch(url, branch)
            if not last_mr:
                merge_status["merge_status"] = text_all["message"][0]
                merge_status["web_url"] = "Something wrong with MR,<NAME_EMAIL>,thanks!"
            if last_mr["status"] != "closed":
                merge_status["web_url"] = last_mr["web_url"]
                merge_status["author"] = last_mr["author"]
                merge_status["merge_status"] = last_mr["status"]
    return merge_status

"""
    早期的群聊机器人发送函数
"""
def seatalk_bot_msg(text, mentionals):
    url = "https://openapi.seatalk.io/messaging/v2/group_chat"
    result = app_access_token()
    tokens = "Bearer " + result
    mentional_text = ""
    for mentional in mentionals:
        mentional_text = f"<mention-tag target=\"seatalk://user?email={mentional}\"/>" + mentional_text
    text_all = f"{mentional_text}\n{text}\n"
    param = {
        "group_id": "NDY0MzgxNDI4OTEw",
        "message": {
            "tag": "text",
            "text": {
                "content": text_all,
            },
        }
    }
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    r = requests.post(url=url, json=param, headers=headers)

"""
    获取分支的commit的名字的函数
"""
def get_branch_commit_name(project_id, branch_name):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    get_commit_url = f"{project_id}/repository/branches/{branch_name}?private_token={GITLAB_PRIVATE_TOKEN}"
    res = requests.get(BASE_URL + get_commit_url)
    res_text = json.loads(res.text)

    return (res_text["commit"]["author_email"])

"""
    AR平台-发送未处理MR到seatalk的按钮处理函数
"""
def sent_mr_to_seatalk(feature_title, issue_key, title, branch, repo_name, url, project_id):
    branch_path = branch.replace('/', '%2F')
    mention_dev = get_branch_commit_name(project_id, branch_path)
    text_all = f"\n✅AR已成功提MR，内容如下\n✅需求：{issue_key}-{feature_title}\n🏷发布单：{title}\n🏷分支为：{branch}\n🏷仓库为：{repo_name}\n🏷MR地址：{url}\n👤涉及的开发为：<mention-tag target=\"seatalk://user?email={mention_dev}\"/>\n请及时关注，感谢"
    seatalk_bot_msg(text_all, [mention_dev])

"""
    中间件，启动合并代码的函数
"""
def beginMR(feature_title, data, release_title, if_merge, issue_key, release_key):
    merge_list = []
    # 检查data是否为None或空
    if data is None or not data:
        ic(f"警告: beginMR 收到空的 data 参数，issue_key: {issue_key}")
        return merge_list
    
    for key, value in data.items():
        merge_list.append(
            sentMR(feature_title, key, value[1], value[0], release_title, if_merge, issue_key, release_key))

    return merge_list

"""
    获取JIRA的comments，处理关联分支的核心函数
"""
def get_jira_comments(jira_key):
    # 创建JIRA连接对象
    jira_url = "https://jira.shopee.io"
    jira = JIRA(jira_url, token_auth=JIRA_TOKEN)
    issue_id = jira_key
    # 获取JIRA问题的评论集合
    issue = jira.issue(issue_id, expand='comments')
    comments = issue.fields.comment.comments
    pattern = r"on branch.*?\[(.*?)\]"
    pattern_project = r".*/(.*)/-/tree/.*"
    pattern_branch = r"(.*)\|"
    pattern_url = r"^(.+)\|(https://git\.garena\.com/shopee/[^/]+/[^/]+)"
    match_real_branch = r"^feature/SPCB-\d+"
    srv_branch = {}
    # 遍历评论集合并查找中括号里的内容
    for comment in comments:
        body = comment.body
        match = re.search(pattern, body)
        if match:
            branch = match.group(1)
            project_url = re.search(pattern_url, branch)
            if project_url is None:
                ic(project_url)
                continue
            else:
                project_url = project_url.group(2)
            match_proj = re.search(pattern_project, branch)
            branch_proj = match_proj.group(1)
            match_branch = re.search(pattern_branch, branch)
            branch_name = match_branch.group(1)
            if "SPCB" not in branch_name:
                continue
            elif "test" in branch_name or "TEST" in branch_name or "uat" in branch_name or "UAT" in branch_name or "master" in branch_name or "MASTER" in branch:
                continue
            else:
                if re.match(match_real_branch, branch_name):
                    if branch_proj in project_url:
                        srv_branch[branch_proj] = [branch_name, f"{project_url}"]
                    else:
                        srv_branch[branch_proj] = [branch_name, f"{project_url}/{branch_proj}"]
                else:
                    continue
    return srv_branch

"""
    字典转成字符串
"""
def dict_to_str(d: dict) -> str:
    return '\n'.join([f'{k}: {v}' for k, v in d.items()])


"""
    通过key的方式，查询这个JIRA单的标题，返回标题
"""
def get_jira_title(jira_key):
    # 创建JIRA连接对象
    jira_url = "https://jira.shopee.io"
    jira = JIRA(jira_url, token_auth=JIRA_TOKEN)
    issue = jira.issue(jira_key)
    title = issue.fields.summary
    return title

"""
    通过标题的方式，查询这个JIRA单的key，返回key
"""
def get_release_key_by_title(release_title):
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    query = f"type = Release AND summary ~ '{release_title}' AND status != CLOSED AND status !=DONE"
    # 执行 JQL 查询
    issues = jira.search_issues(query)

    # 输出符合条件的问题单标题
    return issues[0].key

"""
    遍历一个services里是否有数据，有则返回true，没有则返回false
"""
def check_services_list(services):
    result = False
    if services["services_list_be"] or services["services_list_fe"]:
        result = True
    return result

"""
    AR平台-signed off按钮处理函数
"""
def signed_off_seatalk(request):
    if request.method == "POST":
        data = json.loads(request.body)
        release_title = data["jira_title"]
        release_key = get_release_key_by_title(release_title)
        release_db = Autorelease.objects.get(releaseKey=release_key)
        row_data = release_db.releaseData
        release_title = get_jira_title(release_key)
        
        print(f"🚀 === 开始执行 signed_off_seatalk ===")
        print(f"发布Key: {release_key}")
        print(f"发布标题: {release_title}")
        print(f"总条目数量: {len(row_data)}")
        
        # 获取角色配置（使用文件开头定义的常量）
        # CHATBOT_PM 和 CHATBOT_DS 已在文件开头定义
        
        # 按角色分组收集需要通知的条目
        dev_items_by_pm = {}
        pm_items_by_pm = {}
        ds_items_by_pm = {}
        
        for i in row_data:
            pm = f"{i['PM']}@shopee.com"
            should_notify = False
            
            if "signoff_status" not in i.keys():
                should_notify = True
            elif not i["signoff_status"]:
                should_notify = True
            
            if should_notify:
                if pm in CHATBOT_PM:
                    if pm not in pm_items_by_pm:
                        pm_items_by_pm[pm] = []
                    pm_items_by_pm[pm].append(i)
                elif pm in CHATBOT_DS:
                    if pm not in ds_items_by_pm:
                        ds_items_by_pm[pm] = []
                    ds_items_by_pm[pm].append(i)
                else:
                    if pm not in dev_items_by_pm:
                        dev_items_by_pm[pm] = []
                    dev_items_by_pm[pm].append(i)

        # 分别处理每个角色组的私聊通知，失败后回退到群组通知
        if dev_items_by_pm:
            failed_dev_items = send_private_notifications_with_fallback(dev_items_by_pm, release_title, 'dev')
            if failed_dev_items:
                print(f"🔄 DEV组私聊失败，回退到群组通知")
                print(f"   群组ID: NDY0MzgxNDI4OTEw")
                print(f"   失败条目数量: {len(failed_dev_items)}")
                print(f"   失败条目列表: {', '.join([item['feature_key'] for item in failed_dev_items])}")
                
                from .bot_config import bot_config
                fail_text_dev = f'{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。或者直接在群里"@{bot_config.BOT_NAME} SPCB-XXX SO"，即可 signoff。\n'
                for item in failed_dev_items:
                    fail_text_dev += f"{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={item['PM']}@shopee.com\"/>\n"
                
                print(f"   群组消息长度: {len(fail_text_dev)} 字符")
                test_for_seatalk_bot(fail_text_dev, False, "NDY0MzgxNDI4OTEw")
                print(f"✅ DEV组群组通知发送完成")
        
        if pm_items_by_pm:
            failed_pm_items = send_private_notifications_with_fallback(pm_items_by_pm, release_title, 'pm')
            if failed_pm_items:
                print(f"🔄 PM组私聊失败，回退到群组通知")
                print(f"   群组ID: NTU4OTEwMTY5OTM3")
                print(f"   失败条目数量: {len(failed_pm_items)}")
                print(f"   失败条目列表: {', '.join([item['feature_key'] for item in failed_pm_items])}")
                
                from .bot_config import bot_config
                fail_text_pm = f'{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。或者直接在群里"@{bot_config.BOT_NAME} SPCB-XXX SO"，即可 signoff。\n'
                for item in failed_pm_items:
                    fail_text_pm += f"{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={item['PM']}@shopee.com\"/>\n"
                
                print(f"   群组消息长度: {len(fail_text_pm)} 字符")
                test_for_seatalk_bot(fail_text_pm, False, "NTU4OTEwMTY5OTM3")
                print(f"✅ PM组群组通知发送完成")
        
        if ds_items_by_pm:
            failed_ds_items = send_private_notifications_with_fallback(ds_items_by_pm, release_title, 'ds')
            if failed_ds_items:
                print(f"🔄 DS组私聊失败，回退到群组通知")
                print(f"   群组ID: MTI2MTMxMzY5NjAz")
                print(f"   失败条目数量: {len(failed_ds_items)}")
                print(f"   失败条目列表: {', '.join([item['feature_key'] for item in failed_ds_items])}")
                
                from .bot_config import bot_config
                fail_text_ds = f'{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。或者直接在群里"@{bot_config.BOT_NAME} SPCB-XXX SO"，即可 signoff。\n'
                for item in failed_ds_items:
                    fail_text_ds += f"{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={item['PM']}@shopee.com\"/>\n"
                
                print(f"   群组消息长度: {len(fail_text_ds)} 字符")
                test_for_seatalk_bot(fail_text_ds, False, "MTI2MTMxMzY5NjAz")
                print(f"✅ DS组群组通知发送完成")

        print(f"🏁 === signed_off_seatalk 执行完成 ===")
        return JsonResponse({"data": "success"})

"""
    AR平台-checklist按钮处理函数
"""
def jira_seatalk(request):
    if request.method == "POST":
        data = json.loads(request.body)
        release_title = data["jira_title"]
        release_key = get_release_key_by_title(release_title)
        release_db = Autorelease.objects.get(releaseKey=release_key)
        row_data = release_db.releaseData
        release_title = get_jira_title(release_key)
        fail_text = ""
        for i in row_data:
            result = check_services_list(i["services_list"])
            dev_pic = f"{i['dev_pic']}@shopee.com"

            isChange = False
            if not i["config_center"] or not i['DB_Change'] or not result or not i["shopee_region"]:
                fail_text = f"{fail_text}{i['feature_key']} {i['feature_title']} <mention-tag target=\"seatalk://user?email={dev_pic}\"/>\n"
                isChange = True
            if not i["config_center"]:
                fail_text = f"{fail_text}\nConfig Changed：{i['config_center']}\n"
                isChange = True
            if not i['DB_Change']:
                fail_text = f"{fail_text}DB Changed：{i['DB_Change']}\n"
                isChange = True
            if not result:
                fail_text = f"{fail_text}Release Checklist：None\n"
                isChange = True
            if not i["shopee_region"]:
                fail_text = f"{fail_text}Shopee Region：None\n"
                isChange = True
            if isChange:
                fail_text = f"{fail_text}=======================分割线=======================\n"
        if fail_text:
            fail_text = f"{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更存在Checklist字段未完全确认的情况，请移步jira尽快确认，以免影响live发布：\n{fail_text}"
        else:
            fail_text = f"恭喜，当前班车名下所有需求字段都已更新！"
        test_for_seatalk_bot(fail_text, False, "NDY0MzgxNDI4OTEw")
        return JsonResponse({"data": "success"})

"""
    AR平台-处理各种字段，并且更新DB的核心函数
"""
def get_jira_field_value(issue_key, release_title, release_key, if_merge, need_clean):
    # 初始化JIRA对象
    jira = JIRA(server=jira_base_url, token_auth=JIRA_TOKEN)
    fields_key = {
        "customfield_34003": "signoff_status",
        "customfield_31103": "redis_check",
        "customfield_34001": "config_center",
        "customfield_11201": "shopee_region",
        "customfield_34000": "DB_Change",
        "customfield_34002": "Code_Merged",
        "customfield_10307": "dev_pic",
        "customfield_37800": "dev_pic_backup1",
        "customfield_37801": "dev_pic_backup2",
        "customfield_10308": "qa_pic",
        "customfield_10306": "PM",
        "reporter": "reporter",
        "customfield_16700": "TRD/PRD URL",
        "customfield_23400": "Cross Team with"
    }
    
    # 获取JIRA Issue对象
    issue = jira.issue(issue_key)
    feature_title = issue.fields.summary
    status = issue.fields.status
    fields_value = {}
    all_fields_found = "pass"
    merge_list = []
    
    
    # 特殊处理 dev_pic
    dev_pic_value = getattr(issue.fields, "customfield_10307", None)
    ic("Initial dev_pic_value from customfield_10307:", dev_pic_value)
    
    if dev_pic_value is None or not hasattr(dev_pic_value, 'name'):
        # 尝试从 customfield_37800 获取
        backup1 = getattr(issue.fields, "customfield_37800", None)
        ic("Backup1 from customfield_37800:", backup1)
        if backup1 and isinstance(backup1, list) and len(backup1) > 0:
            dev_pic_value = backup1[0]
            ic("Using first value from backup1:", dev_pic_value)
        else:
            # 尝试从 customfield_37801 获取
            backup2 = getattr(issue.fields, "customfield_37801", None)
            ic("Backup2 from customfield_37801:", backup2)
            if backup2 and isinstance(backup2, list) and len(backup2) > 0:
                dev_pic_value = backup2[0]
                ic("Using first value from backup2:", dev_pic_value)
    
    if dev_pic_value is not None and hasattr(dev_pic_value, 'name'):
        temp_name = ""
        ic("Dev pic value name:", dev_pic_value.name)
        if "@shopee.com" in dev_pic_value.name:
            temp_name = dev_pic_value.name.replace("@shopee.com", "")
        fields_value["dev_pic"] = temp_name
        ic("Final dev_pic value:", temp_name)
    else:
        fields_value["dev_pic"] = None
        all_fields_found = "fail"
        ic("Dev_pic set to None")

    # 处理其他字段
    for field_name, field_key in fields_key.items():
        if field_key == "dev_pic":
            continue  # 跳过 dev_pic 因为已经处理过了
            
        if hasattr(issue.fields, field_name):
            field_value = getattr(issue.fields, field_name)
            
            # 处理 User 类型的字段
            if hasattr(field_value, 'name'):
                temp_name = ""
                if "@shopee.com" in field_value.name:
                    temp_name = field_value.name.replace("@shopee.com", "")
                fields_value[field_key] = temp_name
            # 处理带有 value 属性的字段
            elif hasattr(field_value, 'value'):
                fields_value[field_key] = field_value.value
            # 处理列表类型的字段
            elif isinstance(field_value, list):
                temp_text = ''
                for i in field_value:
                    if hasattr(i, 'value'):
                        value = i.value
                    elif hasattr(i, 'name'):
                        value = i.name
                    else:
                        value = str(i)
                        
                    if not temp_text:
                        temp_text = value
                    else:
                        temp_text = temp_text + ',' + value
                fields_value[field_key] = temp_text
            # 处理其他类型
            else:
                fields_value[field_key] = str(field_value) if field_value is not None else None
        else:
            all_fields_found = "fail"
    
    # 处理 PM 字段
    if fields_value.get("PM"):
        # 如果 customfield_10306 有值，使用它
        pass
    elif fields_value.get("reporter"):
        # 如果 customfield_10306 为空，使用 reporter 的值
        fields_value["PM"] = fields_value["reporter"]
    else:
        # 如果两者都为空，设置为 None 或其他默认值
        fields_value["PM"] = None

    # 删除 reporter 字段，因为我们已经处理过了
    fields_value.pop("reporter", None)

    fields_value["status"] = status.name
    branch_name = get_jira_comments(issue_key)

    fields_value["feature_branch"] = branch_name
    
    # 添加 bug 解决率统计
    try:
        # 获取bug统计信息
        jql = f'issue in linkedIssues("{issue_key}", "is blocked by") AND type = bug'
        bugs = jira.search_issues(jql)
        total_bugs = len(bugs)
        resolved_count = 0
        resolved_statuses = ['Done', 'Closed', 'Icebox']
        
        for bug in bugs:
            bug_status = bug.fields.status.name.upper()
            if bug_status in [s.upper() for s in resolved_statuses]:
                resolved_count += 1
        
        # 计算解决率
        bug_resolution_rate = round((resolved_count / total_bugs * 100), 1) if total_bugs > 0 else 100
        fields_value["bug_resolution_rate"] = f"{bug_resolution_rate}%"
        fields_value["bug_total"] = total_bugs
        fields_value["bug_resolved"] = resolved_count
    except Exception as e:
        # 如果出现异常，设置默认值
        fields_value["bug_resolution_rate"] = "N/A"
        fields_value["bug_total"] = 0
        fields_value["bug_resolved"] = 0
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        ic(f"获取bug解决率时出错: {simplified_error}")
    
    for each_value in fields_value.values():
        if not each_value:
            all_fields_found = "fail"
    fields_value["result"] = all_fields_found
    if all_fields_found == "pass":
        merge_list = beginMR(feature_title, fields_value['feature_branch'], release_title, if_merge, issue_key,
                             release_key)
    else:
        merge_list = []
    fields_value["type"] = issue.fields.issuetype.name
    fields_value["services_list"] = getchecklist(issue_key)
    fields_value = {"result": fields_value.pop("result")} | fields_value
    fields_value["result_all"] = dict_to_str(fields_value)
    fields_value["feature_title"] = feature_title
    fields_value["release_title"] = release_title
    fields_value["merge_list"] = merge_list
    fields_value["release_key"] = release_key
    fields_value["feature_key"] = issue_key

    # 添加调试信息，特别关注这三个字段
    ic(f"调试信息 - issue_key: {issue_key}")
    ic(f"Code_Merged: {fields_value.get('Code_Merged')}")
    ic(f"config_center: {fields_value.get('config_center')}")
    ic(f"DB_Change: {fields_value.get('DB_Change')}")
    ic(f"signoff_status: {fields_value.get('signoff_status')}")
    
    with transaction.atomic():
        try:
            release_db = Autorelease.objects.get(releaseKey=release_key)
            row_data = release_db.releaseData

            if not row_data:
                release_db.releaseData = [fields_value]
                release_db.save()
            # 对象存在，执行更新操作
            else:
                indexes_to_remove = []
                for i, item in enumerate(row_data):
                    if item.get('feature_key') == issue_key:
                        indexes_to_remove.append(i)
                for index in reversed(indexes_to_remove):
                    row_data.pop(index)
                row_data.append(fields_value)
                release_db.releaseFeature = json.dumps(fields_value)
                release_db.servicesList = str(fields_value["services_list"])
                release_db.save()
        except Autorelease.DoesNotExist:
            temp_list_feature = [fields_value]
            # 对象不存在，执行创建操作
            release_db = Autorelease(releaseKey=release_key, releaseTitle=release_title,
                                     releaseFeature=json.dumps(fields_value), releaseData=temp_list_feature,
                                     servicesList=str(fields_value["services_list"]))
            release_db.save()
    return fields_value

"""
    调试函数，可忽略
"""
def seatalk(request):
    if request.method == "POST":

        return JsonResponse({"data": "success"})

"""
    中间件，动态删除非已经取消关联的需求单
"""
def delete_data_not_in_list(data_list):
    release_key = ""
    if data_list:
        release_key = data_list[0]["release_key"]
    if not release_key:
        return
    with transaction.atomic():
        try:
            obj = Autorelease.objects.get(releaseKey=release_key)
            json_data = obj.releaseData
            #ic(json_data)
            updated_data = [item for item in json_data if
                            item.get('feature_key') in [d.get('jira_key') for d in data_list]]

            if len(updated_data) != len(json_data):
                obj.releaseData = updated_data
                #ic(updated_data)
                obj.save()
        except Autorelease.DoesNotExist:
            pass


"""
    AR平台-MRseatalk通知按钮处理函数
"""
def mr_seatalk_msg(request):
    data = json.loads(request.body)
    release_title = ""
    release_key = ""

    if data:
        release_title = data["jira_title"]
        release_key = get_release_key_by_title(release_title)

    if not release_key:
        return JsonResponse({"data": 200})
    with transaction.atomic():
        try:
            obj = Autorelease.objects.get(releaseKey=release_key)
            json_data = obj.releaseData
            merge_string = ""
            for item in json_data:
                if not item["signoff_status"]:
                    continue
                if item["Code_Merged"]:
                    continue
                dev_pic = f"{item['dev_pic']}@shopee.com"
                merge_string += f"\n{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={dev_pic}\"/>"

            if merge_string:
                message = f"{json_data[0]['release_title']}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更请尽快合并代码到master分支，DEV PIC确认所属变更单的FE&BE&DS代码均已合并后修改jira单中的Code Merged字段为\"Confirmed\"：{merge_string}\n"
                test_for_seatalk_bot(message, False, "NDY0MzgxNDI4OTEw")

        except Autorelease.DoesNotExist:
            pass
    return JsonResponse({"data": 200})

"""
    AR平台-单需求进行seatalk通知按钮处理函数
"""
def mr_seatalk_single_feature_msg(request):
    data = json.loads(request.body)
    release_title = ""
    release_key = ""
    if data:
        jira_title = data["jira_title"]
        jira_key = data["jira_key"]
        msg = ""
        if not data["merge_list"]:
            return JsonResponse({"data": f'{jira_key} {jira_title}没检测到MR，请人工确认'})
        else:
            msg = f'{jira_key} {jira_title}\n'
            for mr in data["merge_list"]:
                if "merge_status" in mr:
                    if mr["merge_status"] == "opened":
                        web_url = mr["web_url"]
                        msg = f'{msg}{web_url}\n'
                else:
                    repo_name = mr["repo_name"]
                    msg = f'{msg}{repo_name}请确认是否要提MR'
        if msg == f'{jira_key} {jira_title}':
            test_for_seatalk_bot(msg, ["ALL"], "NDY0MzgxNDI4OTEw")
            return JsonResponse({"data": "success"})
        else:
            msg = f'{msg}\n以上MR需要检查是否合入'
            test_for_seatalk_bot(msg, ["ALL"], "NDY0MzgxNDI4OTEw")
            return JsonResponse({"data": "success"})

    else:
        return JsonResponse({"data": "success"})

"""
    AR平台-复制消息按钮处理函数
"""
def mr_copy_msg(request):
    data = json.loads(request.body)
    release_title = ""
    release_key = ""
    if data:
        jira_title = data["jira_title"]
        jira_key = data["jira_key"]
        msg = ""
        if not data["merge_list"]:
            return JsonResponse({"data": f'{jira_key} {jira_title}没检测到MR，请人工确认'})
        else:
            msg = f'{jira_key} {jira_title}\n'
            for mr in data["merge_list"]:
                if "merge_status" in mr:
                    if mr["merge_status"] == "opened":
                        web_url = mr["web_url"]
                        msg = f'{msg}{web_url}\n'
                else:
                    repo_name = mr["repo_name"]
                    msg = f'{msg}{repo_name}请确认是否要提MR'
        if msg == f'{jira_key} {jira_title}':
            return JsonResponse({"data": f'{jira_key} {jira_title}没有需要处理的MR，请人工确认'})
        else:
            msg = f'{msg}\n以上MR需要检查是否合入'
            return JsonResponse({"data": msg})

    else:
        return JsonResponse({"data": "AR动态检测，目前没有需要处理的MR"})


"""
    根据发布单标题跟key获取发布单关联的需求的数据
"""
def new_get_release_by_title(release_title, release_key):
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    query = f'type = Release AND summary ~ "{release_title}"'
    # 执行 JQL 查询
    issues = jira.search_issues(query)
    # 输出符合条件的问题单标题
    release_list = []
    for issue in issues:
        length = len(issue.fields.issuelinks)
        if not length:
            ic("There is no jira ticket contained.")
            continue
        for link in issue.fields.issuelinks:
            if "outwardIssue" in link.raw and link.raw["type"]["outward"] == "Contains":
                key = link.raw["outwardIssue"]["key"]
                title = link.raw["outwardIssue"]["fields"]["summary"]
                issue_type = link.raw["outwardIssue"]["fields"]["issuetype"]["name"]
                temp_data = {
                    "release_title": release_title,
                    "jira_key": key,
                    "jira_title": title,
                    "release_key": release_key,
                    "jira_link": f"https://jira.shopee.io/browse/{key}",
                    "type": issue_type
                }
                release_list.append(temp_data)

    return release_list

"""
    自动返回DB的发布单信息给前端，并且进行后台的数据更新
"""
def get_all_release_by_title_api(request):
    data = json.loads(request.body)
    release_key = data["release_key"]
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    issues = jira.issue(release_key)

    data = new_get_release_by_title(issues.fields.summary, release_key)
    for index, one_key in enumerate(data):
        if index == 0:
            get_jira_field_value(one_key["jira_key"], one_key["release_title"], one_key["release_key"],
                                 if_merge=False, need_clean=True)
        else:
            get_jira_field_value(one_key["jira_key"], one_key["release_title"], one_key["release_key"],
                                 if_merge=False, need_clean=False)
    delete_data_not_in_list(data)

"""
    获取最新的发布单数据，并且更新DB的数据
"""
def get_all_release_by_title():
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    data_todo = 'status != CLOSED AND status !=DONE'
    query_todo = f"type = Release AND summary ~ 发布单 AND {data_todo} ORDER BY created DESC"
    # 增加maxResults避免数据不全
    issues = jira.search_issues(query_todo, maxResults=500)
    for issue in issues:
        data = new_get_release_by_title(issue.fields.summary, issue.key)
        for index, one_key in enumerate(data):
            if index == 0:
                get_jira_field_value(
                    one_key["jira_key"], 
                    one_key["release_title"],
                    one_key["release_key"],
                    if_merge=False,
                    need_clean=True
                )
            else:
                get_jira_field_value(
                    one_key["jira_key"],
                    one_key["release_title"], 
                    one_key["release_key"],
                    if_merge=False,
                    need_clean=False
                )
        delete_data_not_in_list(data)

"""
    自动返回DB的发布单信息给前端，并且进行后台的数据更新
"""
def autochecknewdata(request):
    if request.method == "POST":
        data = json.loads(request.body)
        # 添加数据验证
        if not data or not isinstance(data, list):
            return JsonResponse({"error": "无效的输入数据格式"}, status=400)
        
        ic(f"autochecknewdata 收到 {len(data)} 个JIRA单")
        for item in data:
            ic(f"准备处理JIRA单: {item.get('jira_key', 'Unknown')}")
            
        data_all = []
        for index, one_key in enumerate(data):
            # 添加必要字段验证
            if not all(k in one_key for k in ["jira_key", "release_title", "release_key"]):
                continue
                
            try:
                ic(f"开始处理JIRA单 {one_key['jira_key']} (索引: {index})")
                if index == 0:
                    result = get_jira_field_value(
                        one_key["jira_key"], 
                        one_key["release_title"],
                        one_key["release_key"],
                        if_merge=False,
                        need_clean=True
                    )
                else:
                    result = get_jira_field_value(
                        one_key["jira_key"],
                        one_key["release_title"], 
                        one_key["release_key"],
                        if_merge=False,
                        need_clean=False
                    )
                if result:
                    ic(f"成功处理JIRA单 {one_key['jira_key']}")
                    data_all.append(result)
                else:
                    ic(f"JIRA单 {one_key['jira_key']} 返回空结果")
            except Exception as e:
                ic(f"处理JIRA单 {one_key['jira_key']} 时出错: {str(e)}")
                continue
                
        try:
            delete_data_not_in_list(data)
        except Exception as e:
            print(f"删除数据时出错: {str(e)}")
            
        return JsonResponse({"data": data_all})

"""
    自动返回DB的发布单信息给前端，不进行后台的数据更新
"""
def autocheckdata(request):
    if request.method == "POST":
        data = json.loads(request.body)
        if not data:
            return JsonResponse({"data": ""})
        
        # 批量获取所有release keys
        release_keys = [item["release_key"] for item in data]
        
        try:
            # 使用filter进行批量查询
            objs = Autorelease.objects.filter(releaseKey__in=release_keys)
            
            # 构建release_key到releaseData的映射
            release_data_map = {obj.releaseKey: obj.releaseData for obj in objs}
            
            # 按照请求的顺序返回数据
            result_data = release_data_map.get(data[0]["release_key"], "")
            
            return JsonResponse({"data": result_data})
        except Exception as e:
            return JsonResponse({"data": "", "error": str(e)})


"""
    启动自动发布流程
"""
def startAuto(request):
    if request.method == "POST":
        data = json.loads(request.body)
        data_all = []
        for index, one_key in enumerate(data):
            try:
                if index == 0:
                    result = get_jira_field_value(one_key["jira_key"], one_key["release_title"], one_key["release_key"],
                                                 if_merge=True, need_clean=True)
                    data_all.append(result)
                else:
                    result = get_jira_field_value(one_key["jira_key"], one_key["release_title"], one_key["release_key"],
                                                 if_merge=True, need_clean=False)
                    data_all.append(result)
            except Exception as e:
                ic(f"处理数据时出错: {str(e)}")
                ic(f"出错的JIRA单: {one_key.get('jira_key', 'Unknown')}")
                # 继续处理下一个，不中断整个流程
                continue
        delete_data_not_in_list(data)
        return JsonResponse({"data": data_all})

"""
    K8S POD更新函数，由于K8S迁移了，已经无效了，废弃
"""
def pod_update(request):
    if request.method == "POST":
        data = json.loads(request.body)

        db_path = os.path.join(BASE_DIR, 'app01', 'service.db')
        conn = sqlite3.connect(db_path)
        # 创建 'service_info' 表格（如果它尚未存在）
        for i in data["data"]:
            service_name = i["service_name"]
            pod_count = i["pod_count"]
            k8s_link = i["k8s_link"]
            # 向 'service_info' 插入新行或替换现有行
            conn.execute('''
                INSERT OR REPLACE INTO service_info (
                    service_name, pod_count, k8s_link
                ) VALUES (
                    ?, ?, ?
             )
            ''', (service_name, pod_count, k8s_link))

        conn.commit()

        # 关闭数据库连接
        conn.close()
        return JsonResponse({"data": "success"})

"""
    获取日志的函数
"""
def get_log(case_name, trace_list):
    if "saas" in case_name:
        app_list = COMMON_APP
        log_base = COMMON_LOG_URL_BASE
    else:
        app_list = SHOPEE_APP
        log_base = SHOPEE_LOG_URL_BASE
    # 获取当天日期
    today = dt.date.today()

    # 获取当天 0 点的 datetime 对象
    today_start = dt.datetime.combine(today, dt.time.min)

    # 获取当前时间戳
    current_timestamp = int(time.time() * 1000)
    # 获取当前时间并向前推一小时
    one_hour_ago = dt.datetime.now() - dt.timedelta(hours=1)

    # 转换为时间戳
    one_hour_ago_timestamp = int(one_hour_ago.timestamp() * 1000)

    # 获取当天 0 点的时间戳
    today_start_timestamp = int(today_start.timestamp() * 1000)

    headers = {
        "x-openapi-key": API_KEY,
    }
    log_info = {
        "bug_type": 0,
        "is_bug": 0,
        "log_info": "",
        "log_link": "",
    }
    trace_search = ""
    for index, trace_id in enumerate(trace_list):
        if index == len(trace_list) - 1:

            trace_search += f"\"{trace_id}\""
        else:
            trace_search += f"\"{trace_id}\" OR "
    param = {
        "applications": app_list,
        "start_date": today_start_timestamp,
        "end_date": current_timestamp,
        "query": f"({trace_search}) AND @severity=ERROR"
    }
    response = requests.get(LOG_URL, params=param, headers=headers)
    json_result = json.loads(response.text)
    if "data" not in json_result or not json_result["data"]:

        param = {
            "applications": app_list,
            "start_date": today_start_timestamp,
            "end_date": current_timestamp,
            "query": f"({trace_search}) AND @severity=INFO"
        }
        response_new = requests.get(LOG_URL, params=param, headers=headers)
        json_result_new = json.loads(response_new.text)
        if "data" in json_result_new and json_result_new["data"]:
            json_final = json_result_new["data"][0]["@message"]
    if "data" in json_result and json_result["data"]:
        json_final = json_result["data"][0]["@message"]
        result = any(re.search(pattern, json_final) for pattern in BLACK_LIST)
        if result:
            log_info["is_bug"] = 2
            log_info["bug_type"] = 2
            print("第三方问题")
        else:
            log_info["is_bug"] = 0
            log_info["bug_type"] = 0
            log_info["log_info"] += json_final + "\n"
    temp_link = log_base + f"{trace_search}"
    log_info["log_link"] = temp_link
    return log_info

"""
    日期排序
"""
def sort_by_date(arr):
    date_arr = []
    for item in arr:
        #ic(item)
        match =re.search(r'\d{8}', item)
        if match:
            date_str = match.group(0)
            date_obj = datetime.strptime(date_str, '%Y%m%d')
            date_arr.append((date_obj, item))
        else:
            date_arr.append((None, item))  # 如果找不到日期字符串，则返回元组(None, item)
    # 过滤掉含有 None 的元组，然后进行排序
    filtered_date_arr = [item for item in date_arr if item[0] is not None]
    filtered_date_arr.sort(reverse=True)
    # 将排序后的元组与剩余的元组合并
    date_arr = filtered_date_arr + [item for item in date_arr if item[0] is None]
    return [item[1] for item in date_arr]

"""
    从DB里获取发布单数据并且返回给前端
"""
def get_jira_release_list_finished(request):
    releases = ReleaseTitle.objects.values_list('releaseTitle', flat=True)
    new_data = list(releases)
    #ic(new_data)
    return JsonResponse({"data": new_data})

"""
    更新DB里的历史发布单数据
"""
@track_cronjob_execution('save_releases')
def save_releases():
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    #历史发布单只获取近 90 天的数据
    today = datetime.now().strftime('%Y-%m-%d')
    last_month = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    date_range = 'created >= "{}" and created <= "{}" and status = DONE'.format(last_month, today)
    #date_range = 'status in ("CLOSED","DONE")'
    # 定义 JQL 查询语句
    query = f"project= SPCB and type = Release AND summary ~ 发布单 AND {date_range}"
    # 执行 JQL 查询
    issues = jira.search_issues(query, maxResults=False)
    easy_release_list = []
    # 输出符合条件的问题单标题
    for issue in issues:
        easy_release_list.append(issue.raw["fields"]["summary"])
    releases = sort_by_date(easy_release_list)
    ic(releases)
    #清空数据库
    ReleaseTitle.objects.all().delete()
    #把新查询到的数据写入数据库
    for release_title in releases:
        ReleaseTitle.objects.get_or_create(releaseTitle=release_title)

"""
    排序函数
"""
def new_sort_by_date(arr):
    date_arr = []
    for item in arr:
        if "SPCB" in item['key']:
            date_match = re.search(r'\d{8}', item["title"])
            if date_match:
                date_str = date_match.group(0)
            else:
                date_match = re.search(r'\d{6}', item["title"])
                if date_match:
                    date_str = '20' + date_match.group(0)
                else:
                    continue  # 如果没有找到符合条件的日期字符串，跳过该项

            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                date_arr.append((date_obj, item))
            except ValueError:
                continue  # 如果日期字符串格式不正确，跳过该项

    sorted_arr = sorted(date_arr, key=lambda x: x[0])
    return [item[1] for item in sorted_arr]
# def new_sort_by_date(arr):
#     date_arr = []
#     ic(arr)
#     for item in arr:
#         if "SPCB" in item['key']:
#             date_str = re.search(r'\d{8}', item["title"]).group(0)
#             if not date_str:
#                 date_str = '20'+re.search(r'\d{6}', item["title"]).group(0)
#             date_obj = datetime.strptime(date_str, '%Y%m%d')
#             date_arr.append((date_obj, item))
#     sorted_arr = sorted(date_arr, key=lambda x: x[0])
#     return [item[1] for item in sorted_arr]

"""
    获取未发布的release单，并且排序/获取unreleased 的 version
"""
def get_key_jira_release_list(request):
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    
    #today = datetime.now().strftime('%Y-%m-%d')
    #last_month = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    # date_range = 'created >= "{}" and created <= "{}" and status != CLOSED and status != DONE'.format(last_month, today)
    date_range = 'status != CLOSED and status != DONE'
    # 定义 JQL 查询语句
    query = f"project=SPCB and type = Release AND summary ~ 发布单 AND {date_range}"
    #ic(query)
    # 执行 JQL 查询，增加maxResults避免前端显示不全
    issues = jira.search_issues(query, maxResults=500)
    ic(issues)
    new_easy_release_list = []

    for issue in issues:
        new_easy_release_list.append({"title": issue.raw["fields"]["summary"], "key": issue.key})
    ic(new_easy_release_list)
    key_new_data_sort = new_sort_by_date(new_easy_release_list)
    #调用一次更新发布单和关联需求单的接口
    # with concurrent.futures.ThreadPoolExecutor() as executor:
    #     # 提交任务并返回 future 对象，但不等待结果
    #     future = executor.submit(get_unreleased_versions(request=None))
    # print("正在后台更新发布单和关联需求单")
    return JsonResponse({"data": key_new_data_sort})

#获取未发布的release 单，本函数可以用于取代get_key_jira_release_list用于获取未发布的版本
#但是由于需要同步去jira获取信息导致速度较慢。
#所以把此处改成计划任务每五分钟运行一次，去建发布单和做需求关联，原来的老接口可以继续使用。
def get_unreleased_versions():    
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    # 获取未发布的版本信息
    versions = jira.project_versions('SPCB')
    unreleased_versions = [v.raw for v in versions if not v.released]
    # print('unreleased_versions:',unreleased_versions)
    releases_data = []
    adhoc_version_ids= {}
    # 通过 bus/adhoc/hotfix 类的版本去找发布单，如果找到则返回单的标题，如果找不到则新建，然后返回单的标题
    for version in unreleased_versions:
        if re.match(r'(?i)(bus|adhoc|hotfix)-', version['name']):
            project_key = "SPCB"

            # 检查是否存在相关的发布单
            jql = f'project = {project_key} AND summary ~ "{version["name"]}" AND issuetype = "Release"'
            try:
                existing_issues = jira.search_issues(jql)
                ic(existing_issues)
            except Exception as e:
                # 使用简化的错误信息
                simplified_error = simplify_jira_error(str(e))
                return JsonResponse({"error": simplified_error}, status=500)

            if existing_issues:
                release_issue = existing_issues[0]
            else:
                try:
                    release_issue = create_release_ticket(jira, project_key, version['name'])
                except Exception as e:
                    # 使用简化的错误信息
                    simplified_error = simplify_jira_error(str(e))
                    return JsonResponse({"error": simplified_error}, status=500)

            try:
                #获取当前版本下关联的需求单
                tickets = get_tickets_for_version(jira, version['id'])
                #把对应的需求单关联到发布单上
                link_tickets_to_release(jira, tickets, release_issue)
                # 收集发布单的 title 和 key
            except Exception as e:
                # 使用简化的错误信息
                simplified_error = simplify_jira_error(str(e))
                return JsonResponse({"error": simplified_error}, status=500)
            releases_data.append({
                'title': release_issue.fields.summary,
                'key': release_issue.key
            })

# 用 unreleased version 创建 release ticket
def create_release_ticket(jira, project_key, version_name):
    # # 提取日期部分
    # date_part = version_name.split('-')[-1]
    # # 转换为 datetime 对象
    # date_obj = datetime.strptime(date_part, '%d%m%y')
    # # 格式化为所需的格式
    # new_date_part = date_obj.strftime('%Y%m%d')
    # # 生成新的 version_name
    # version_name = f'Bus-{new_date_part}'
    issue_dict = {
        'project': {'key': project_key},
        'summary': f'【Release】{version_name} 发布单',
        'description': f'This is the release ticket for version {version_name}.',
        'issuetype': {'name': 'Release'},
    }
    ic(issue_dict)
    return jira.create_issue(fields=issue_dict)

# 获取特定版本的 tickets
def get_tickets_for_version(jira, version_id):
    # Query to get all tickets for the given version
    jql = f'fixVersion = {version_id}'
    return jira.search_issues(jql)

# 把 version 下的 ticket 关联到 release 单下
def link_tickets_to_release(jira, tickets, release_issue):
    # 获取当前所有需要关联的ticket keys
    new_ticket_keys = {ticket.key for ticket in tickets}
    print(f"New tickets to be linked: {new_ticket_keys}")
    
    try:
        # 获取当前所有已关联的tickets
        existing_links = jira.issue(release_issue.key).fields.issuelinks
        print(f"Found {len(existing_links)} existing links for {release_issue.key}")
        
        # 遍历现有关联关系
        for link in existing_links:
            print(f"\nProcessing link: {link.id}")
            print(f"Link type: {getattr(link.type, 'name', 'N/A')}")
            print(f"Link outward: {getattr(link.type, 'outward', 'N/A')}")
            
            # 检查 Contains 关系（从release视角看是Contains）
            if hasattr(link, 'outwardIssue') and getattr(link.type, 'outward', '') == 'Contains':
                existing_key = link.outwardIssue.key
                print(f"Found linked ticket: {existing_key}")
                
                # 如果现有ticket不在新列表中，删除关联
                if existing_key not in new_ticket_keys:
                    try:
                        jira.delete_issue_link(link.id)
                        print(f"Removed link between {release_issue.key} and {existing_key}")
                    except Exception as e:
                        print(f"Error removing link for {existing_key}: {e}")
                else:
                    # 如果已存在关联，从待处理列表中移除
                    new_ticket_keys.remove(existing_key)
                    print(f"Keeping existing link for {existing_key}")
        
        # 为剩余的tickets创建新的关联
        print(f"\nRemaining tickets to link: {new_ticket_keys}")
        for ticket in tickets:
            if ticket.key in new_ticket_keys:
                try:
                    jira.create_issue_link(
                        type='Contains',
                        inwardIssue=release_issue.key,
                        outwardIssue=ticket.key
                    )
                    print(f"Created new link between {release_issue.key} and {ticket.key}")
                except Exception as e:
                    print(f"Error linking {ticket.key} to {release_issue.key}: {e}")
                    
    except Exception as e:
        print(f"Error processing issue links for {release_issue.key}: {e}")

"""
    获取未关闭或者DONE的需求单
"""
def get_jira_release_list(request):
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    today = datetime.now().strftime('%Y-%m-%d')
    last_month = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    date_range = 'status != CLOSED and status != DONE'
    # 定义 JQL 查询语句
    query = f"type = Release AND summary ~ 发布单 AND {date_range}"
    # 执行 JQL 查询，增加maxResults避免数据不全
    issues = jira.search_issues(query, maxResults=500)
    easy_release_list = []
    new_easy_release_list = []
    # 输出符合条件的问题单标题
    for issue in issues:
        easy_release_list.append(issue.raw["fields"]["summary"])
    new_data = sort_by_date(easy_release_list)

    return JsonResponse({"data": new_data})



"""
    通过标题，获取单个发布单
"""
def get_single_release_by_title(request):
    data = json.loads(request.body)
    ic(data)
    release_title = data.get("title", [])
    ic(release_title)
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    #query = f'type = Release AND summary ~ "{release_title}" AND status != CLOSED AND status !=DONE'
    query = f'type = Release AND summary ~ "{release_title}"'

    # 执行 JQL 查询，增加maxResults避免数据不全
    issues = jira.search_issues(query, maxResults=100)
    # 输出符合条件的问题单标题
    release_list = []
    ic(f"找到 {len(issues)} 个发布单")
    for issue in issues:
        length = len(issue.fields.issuelinks)
        ic(f"发布单 {issue.key} 包含 {length} 个关联JIRA单")

        if not length:
            print("There is no jira ticket contained.")
        for link in issue.fields.issuelinks:
            if "outwardIssue" in link.raw and link.raw["type"]["outward"] == "Contains":
                key = link.raw["outwardIssue"]["key"]
                title = link.raw["outwardIssue"]["fields"]["summary"]
                issue_type = link.raw["outwardIssue"]["fields"]["issuetype"]["name"]
                ic(f"找到关联的JIRA单: {key} - {title}")
                temp_data = {
                    "release_key": issue.key,
                    "release_title": release_title,
                    "jira_key": key,
                    "jira_title": title,
                    "jira_link": f"https://jira.shopee.io/browse/{key}",
                    "type": issue_type
                }
                release_list.append(temp_data)
    
    ic(f"最终返回 {len(release_list)} 个JIRA单")
    response_data = {
        "data": release_list
    }
    return JsonResponse(response_data)

"""
    日志分析函数
"""
def log_analysis(request):
    if request.method == 'POST':
        # 获取请求数据
        data = json.loads(request.body)
        print('data:',data)
        case_list = data.get("cases_list", [])
        print('case_list:',case_list)
        dict_all = []
        for i in case_list:
            case_name = i["case_name"]
            case_id = i["case_id"]
            env = i["env"]
            trace_id = i["trace_id"]
            if trace_id:
                result_dict = get_log(case_name, trace_id)
            else:
                result_dict = {
                    "bug_type": 2,
                    "is_bug": 2,
                    "log_info": "",
                    "log_link": "",
                }
            is_bug = result_dict["is_bug"]
            bug_type = result_dict["bug_type"]
            log_link = result_dict["log_link"]
            log_info = result_dict["log_info"]
            dict_all.append({
                "is_bug": 1,
                "log_link": log_link,
                "case_name": case_name,
                "log_info": log_info,
                "case_id": case_id,
                "env": "test",
                "bug_type": bug_type,
            })

        response_data = {
            'code': 0,
            'message': 'success',
            'data': dict_all
        }

        return JsonResponse(response_data)

"""
    创建JIRA单
"""
# Create your views here.
def create_jira_ticket(request):
    if request.method == 'POST':
        postBody = request.body
        json_result = json.loads(postBody)
        summary = json_result['summary']
        description = json_result['description']
        env = json_result['env']
        reporter = json_result['reporter']
        developer = json_result['developer']
        token = json_result['token']
        assignee = json_result['assignee']
        # 创建JIRA连接对象
        jira_url = "https://jira.shopee.io"
        jira = JIRA(jira_url, token_auth=token)

        linked_issue_key = "SPCB-23584"

        # # 创建 bug 单
        issue_dict = {
            'project': {'key': 'SPCB'},
            'assignee': {'name': assignee},
            'summary': summary,  # bug 摘要
            'description': description,  # bug 描述
            'issuetype': {'name': 'Problem'},  # 指定 issue 类型为 bug
            'components': [{'name': '监控'}],  # 添加 Component/s 字段
            'customfield_10205': {'value': env},  # 添加 Server Environment 字段
            'customfield_10307': {'name': developer},  # 添加 Developer 字段
            'customfield_10308': {'name': reporter},  # 添加 QA 字段
            'labels': ['接口自动化'],
        }
        try:
            new_issue = jira.create_issue(fields=issue_dict)  # 创建 bug 单
            print(f'Created bug: {new_issue.key}')  # 输出创建的 bug 单号
            issue_key = new_issue.key

            # 添加issuelink
            jira.create_issue_link('Blocks', issue_key, linked_issue_key)

            response_data = {
                "code": 0,
                "message": "success",
                "data": {"ticket_key": new_issue.key, "ticket_url": f"{jira_url}/browse/{new_issue.key}"}
            }
        except Exception as ex:
            # 使用简化的错误信息
            simplified_error = simplify_jira_error(str(ex))
            response_data = {"code": 400, "message": simplified_error}
    else:
        response_data = {"code": 400, "message": "Invalid request method."}

    return JsonResponse(response_data)

"""
    读取本地的部署数据的json文件
"""
def new_read_data_json(request):
    if request.method == 'GET':
        json_path = os.path.join(BASE_DIR, 'app01', 'new_data.json')
        with open(json_path) as json_file:
            data = json.load(json_file)
            return JsonResponse(data, safe=False)

"""
    读取本地的部署数据的json文件
"""
def read_data_json(request):
    if request.method == 'GET':
        json_path = os.path.join(BASE_DIR, 'app01', 'new_deploy_data.json')
        with open(json_path) as json_file:
            data = json.load(json_file)
            return JsonResponse(data, safe=False)

"""
    UTC转北京时间的函数
"""
def UTC2BJS(UTC):
    UTC_format = "%Y-%m-%dT%H:%M:%S.%fZ"
    BJS_format = "%Y-%m-%d %H:%M:%S"
    UTC = datetime.strptime(UTC, UTC_format)
    # 格林威治时间+8小时变为北京时间
    BJS = UTC + timedelta(hours=8)
    BJS = BJS.strftime(BJS_format)
    return BJS

"""
    获取最后的MR的信息，用来拿到开发信息
"""
def getlastMRMSG(project_id, env):
    rep = requests.get(
        'https://git.garena.com/api/v4/projects/{project_id}/'
        'merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&state=merged&scope=all&target_branch={env}'.format
        (project_id=project_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, env=env))

    json_result = json.loads(rep.text)
    if json_result:
        return {"name": json_result[0]['author']['username'], "title": json_result[0]['title']}
    else:
        return None


"""
    seatalk的token获取函数，如果权限发生变化，请在这里修改相对应参数
"""
def app_access_token():
    token_url = "https://openapi.seatalk.io/auth/app_access_token"
    headers = {
        'content-type': "application/json",
    }
    param = {
        "app_id": "NzM5MzEzODYyNzk5",
        "app_secret": "3dcplTo_Q3zIQCJLOs_FLzKZNJASevOT"
    }
    r = requests.post(url=token_url, json=param, headers=headers)
    results = json.loads(r.text)
    return results["app_access_token"]

"""
    机器人私聊函数
"""
def single_chat(text, code):
    access_token = app_access_token()
    single_chat_url = "https://openapi.seatalk.io/messaging/v2/single_chat"
    headers = {
        'content-type': "application/json",
        'Authorization': "Bearer " + access_token
    }
    param = {
        "employee_code": code,
        "message": {
            "tag": "text",
            "text": {
                "content": text
            }
        }
    }
    r = requests.post(url=single_chat_url, json=param, headers=headers)
    result = json.loads(r.text)
    if result["code"] == 0:
        print("success")
        return True
    else:
        print(f"fail - API返回: {result}")
        # 抛出异常，让上层可以捕获并处理
        raise Exception(f"私聊发送失败: {result}")
    
    return False

"""
    获取space的token，如果权限发生了变化，请在这里修改token即可
"""
_token_cache = None
_token_expire_time = None

def gettoken():
    global _token_cache, _token_expire_time
    
    # 检查缓存的token是否还有效
    current_time = time.time() * 1000  # 转换为毫秒
    if _token_cache and _token_expire_time and current_time < _token_expire_time:
        return _token_cache
        
    URL = "https://space.shopee.io/apis/uic/v2/auth/basic_login"
    headers = {
        'content-type': "application/json",
        'Authorization': 'Basic Y2hhdGJvdHFhOlNob3BlZTEyMw=='
    }
    
    try:
        r = requests.post(url=URL, headers=headers, timeout=5)
        results = json.loads(r.text)
        
        # 更新缓存
        _token_cache = results
        _token_expire_time = results.get('expireAt')  # 使用API返回的过期时间
        
        return results
    except requests.exceptions.RequestException as e:
        print(f"获取token失败: {e}")
        return _token_cache  # 如果请求失败，返回缓存的token

"""
    发送MR监控消息的函数
"""
def MRmsg(text_all, botToken):
    seatalk_url = "https://openapi.seatalk.io/webhook/group/" + botToken
    srv_list = []
    email_list = []
    black_list = ["48901", "85497", "44700", "9674", "19635", "70469"]
    ningjing = ["ning.jing", "zhiyu.wu", "guangxin.chen", "zijian.li", "jasonbo.li", "wudong", "ziqiang.he", "ming.jin", "zhenye.chen", "jiale.huang", "danieldachao.wang", "ming.chen"]
    binwang = ["bin.wang", "jing.huang", "jiamin.he", "junshan.qin", "xinsen.hu", "liyan", "xingwei.zou", "wenxin", "hao.liulh", "shengsong.feng"]
    shuangtai = ["shuangtai.yin", "wuchao.yan", "pusheng.yang", "peidong.xu", "chang.wu", "ekko.Wang", "zijian.yan", "sen.xie", "xiechao"]
    if botToken == "gXxkbHktS7at0ADtXMRQ2w":
        for i in shuangtai:
            if i in text_all:
                email_list.append("<EMAIL>")
                break
        for i in binwang:
            #ic(i)
            #ic(type(i))
            if i in text_all:
                email_list.append("<EMAIL>")
                break
        for i in ningjing:
            #ic(i)
            #ic(type(i))
            if i in text_all:
                email_list.append("<EMAIL>")
                break

    if botToken == "Ujm2PEutT5mD61G2X97oAQ":
        email_list = ["<EMAIL>", "<EMAIL>"]
    if botToken == "Rrcd-KN-QcuTdkVsSNcZlQ":
        email_list = ["<EMAIL>"]
    result = gettoken()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    if text_all:
        text_all = "当前未处理的opened状态的master/release MR请求列表为：\n" + text_all
        print(len(text_all))
        if len(text_all) >= 3000:
            first_half = text_all[:len(text_all) // 2]
            second_half = text_all[len(text_all) // 2:]
            param = {
                "tag": "text",
                "text": {
                    "content": first_half,
                    "mentioned_email_list": email_list,
                }
            }
            headers = {
                'content-type': "application/json",
                'Authorization': tokens
            }

            r = requests.post(url=seatalk_url, json=param, headers=headers)
            param = {
                "tag": "text",
                "text": {
                    "content": second_half,
                    "mentioned_email_list": email_list,
                }
            }
            headers = {
                'content-type': "application/json",
                'Authorization': tokens
            }
            r = requests.post(url=seatalk_url, json=param, headers=headers)
        else:
            param = {
                "tag": "text",
                "text": {
                    "content": text_all,
                    "mentioned_email_list": email_list,
                }
            }
            headers = {
                'content-type': "application/json",
                'Authorization': tokens
            }
            r = requests.post(url=seatalk_url, json=param, headers=headers)
    else:
        print("success!!")

"""
    黑名单，如果MR标题里有如下字段，则不做监控
"""
def blacklist(title):
    black_list = ["WIP", "Draft", "code review", "CODE REVIEW", "draft"]
    for i in black_list:
        if i in title:
            return True
    return False

"""
    判断MR是否有冲突
"""
def hasconflicts(souce_branch, repo):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    get_commit_url = f"{repo}/repository/commits?private_token={GITLAB_PRIVATE_TOKEN}&ref_name={souce_branch}"
    res = requests.get(BASE_URL + get_commit_url)
    res_text = json.loads(res.text)
    if len(res_text) >= 1:
        return (res_text[1]["author_email"])
    else:
        return "can not find flict message,pls manual check,thanks."

"""
    chatbot的MR监控函数，如果没处理的话会进行通知，通过crontab设置为1小时运行一次
"""
def MRnotdeal():
    project_id_list = []
    json_path = os.path.join(BASE_DIR, 'app01', 'services_id.json')
    with open(json_path) as f:
        project_id = json.load(f)
    text_fin_all_master = ""
    text_fin_not_master = ""
    text_fin_all_release = ""
    text_fin_not_release = ""
    text_fin_all = ""
    text_fin_not = ""
    text_common_repo = ""
    text_common_repo_not_approved = ""
    fe_blacklist = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>"]
    for i in project_id.values():
        project_id_list.append(str(i))
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    for i in project_id_list:
        real_url_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=master&state=opened&scope=all&approved_by_ids=Any'
        real_url_not_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=master&state=opened&scope=all&approved_by_ids=None'
        get_feedback_isue_appr = requests.get(real_url_approved)
        get_feedback_isue_not_appr = requests.get(real_url_not_approved)
        real_not_approved = json.loads(get_feedback_isue_not_appr.text)
        text_appr = json.loads(get_feedback_isue_appr.text)
        if text_appr:
            for j in text_appr:
                if isinstance(j, str):
                    continue
                if j["project_id"] == 32154:
                    continue
                if not blacklist(j["title"]):
                    release_name = get_jira_release_name(extract_content(j["title"]))
                    if j["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        text_common_repo = text_common_repo + "===========✅已审批，可合入===========" + "\n"
                        text_common_repo = text_common_repo + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_common_repo = text_common_repo + "标题：" + j["title"] + "\n"
                        text_common_repo = text_common_repo + "所属release单：" + release_name + "\n"
                        text_common_repo = text_common_repo + "MR地址：" + j["web_url"] + "\n"
                        if j["has_conflicts"]:
                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                            text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_common_repo = text_common_repo + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                    if j["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        if j["author"]["username"] in fe_blacklist:
                            text_fin_all_master = text_fin_all_master + "===========✅已审批，可合入===========" + "\n"
                            text_fin_all_master = text_fin_all_master + "提交MR的author👤：" + j["author"][
                                "username"] + "\n"
                            text_fin_all_master = text_fin_all_master + "标题：" + j["title"] + "\n"
                            text_fin_all_master = text_fin_all_master + "所属release单：" + release_name + "\n"
                            text_fin_all_master = text_fin_all_master + "MR地址：" + j["web_url"] + "\n"
                            text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                            text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                            text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                            text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                            text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                            if j["has_conflicts"]:
                                commit_name = hasconflicts(j["source_branch"], j["project_id"])
                                text_fin_all_master = text_fin_all_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_all_master = text_fin_all_master + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                            text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                    else:
                        text_fin_all_master = text_fin_all_master + "===========✅已审批，可合入===========" + "\n"
                        text_fin_all_master = text_fin_all_master + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_fin_all_master = text_fin_all_master + "标题：" + j["title"] + "\n"
                        text_fin_all_master = text_fin_all_master + "所属release单：" + release_name + "\n"
                        text_fin_all_master = text_fin_all_master + "MR地址：" + j["web_url"] + "\n"
                        text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                        text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                        text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                        text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                        if j["has_conflicts"]:
                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                            text_fin_all_master = text_fin_all_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_fin_all_master = text_fin_all_master + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                        text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"

        if real_not_approved:
            for k in real_not_approved:
                if isinstance(k, str):
                    continue
                if k["project_id"] == 32154:
                    continue
                if not blacklist(k["title"]):
                    release_name = get_jira_release_name(extract_content(k["title"]))
                    if k["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        text_common_repo_not_approved = text_common_repo_not_approved + "===========✅已审批，可合入===========" + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "提交MR的author👤：" + \
                                                        k["author"][
                                                            "username"] + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "标题：" + k["title"] + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "所属release单：" + release_name + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "MR地址：" + k[
                            "web_url"] + "\n"
                        if k["has_conflicts"]:
                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                            text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "创建时间：" + UTC2BJS(
                            k["created_at"]) + "\n"

                    if k["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        if k["author"]["username"] in fe_blacklist:
                            text_fin_not_master = text_fin_not_master + "===========❌未审批===========" + "\n"
                            text_fin_not_master = text_fin_not_master + "提交MR的author👤：" + k["author"][
                                "username"] + "\n"
                            text_fin_not_master = text_fin_not_master + "标题：" + k["title"] + "\n"
                            text_fin_not_master = text_fin_not_master + "所属release单：" + release_name + "\n"
                            text_fin_not_master = text_fin_not_master + "MR地址：" + k["web_url"] + "\n"
                            text_fin_not = text_fin_not + "===========❌未审批===========" + "\n"
                            text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                            text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                            text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                            text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                            if k["has_conflicts"]:
                                commit_name = hasconflicts(k["source_branch"], k["project_id"])
                                text_fin_not_master = text_fin_not_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_not_master = text_fin_not_master + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                            text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                    else:
                        text_fin_not_master = text_fin_not_master + "===========❌未审批===========" + "\n"
                        text_fin_not_master = text_fin_not_master + "提交MR的author👤：" + k["author"]["username"] + "\n"
                        text_fin_not_master = text_fin_not_master + "标题：" + k["title"] + "\n"
                        text_fin_not_master = text_fin_not_master + "所属release单：" + release_name + "\n"
                        text_fin_not_master = text_fin_not_master + "MR地址：" + k["web_url"] + "\n"
                        text_fin_not = text_fin_not + "===========❌未审批===========" + "\n"
                        text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                        text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                        text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                        text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                        if k["has_conflicts"]:
                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                            text_fin_not_master = text_fin_not_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_fin_not_master = text_fin_not_master + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                        text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"

        real_url_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=release&state=opened&scope=all&approved_by_ids=Any'
        real_url_not_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=release&state=opened&scope=all&approved_by_ids=None'
        get_feedback_isue_appr = requests.get(real_url_approved)
        get_feedback_isue_not_appr = requests.get(real_url_not_approved)
        real_not_approved = json.loads(get_feedback_isue_not_appr.text)
        text_appr = json.loads(get_feedback_isue_appr.text)
        if text_appr:
            for j in text_appr:
                if isinstance(j, str):
                    continue
                if j["project_id"] == 32154:
                    continue
                if not blacklist(j["title"]):
                    release_name = get_jira_release_name(extract_content(j["title"]))
                    if j["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        text_common_repo = text_common_repo + "===========✅已审批，可合入===========" + "\n"
                        text_common_repo = text_common_repo + "提交MR的author👤：" + j["author"][
                            "username"] + "\n"
                        text_common_repo = text_common_repo + "标题：" + j["title"] + "\n"
                        text_common_repo = text_common_repo + "所属release单：" + release_name + "\n"
                        text_common_repo = text_common_repo + "MR地址：" + j["web_url"] + "\n"
                        if j["has_conflicts"]:
                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                            text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_common_repo = text_common_repo + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"

                    if j["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        if j["author"]["username"] in fe_blacklist:
                            text_fin_all_release = text_fin_all_release + "===========✅已审批，可合入===========" + "\n"
                            text_fin_all_release = text_fin_all_release + "提交MR的author👤：" + j["author"][
                                "username"] + "\n"
                            text_fin_all_release = text_fin_all_release + "标题：" + j["title"] + "\n"
                            text_fin_all_release = text_fin_all_release + "所属release单：" + release_name + "\n"
                            text_fin_all_release = text_fin_all_release + "MR地址：" + j["web_url"] + "\n"
                            text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                            text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                            text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                            text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                            text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                            if j["has_conflicts"]:
                                commit_name = hasconflicts(j["source_branch"], j["project_id"])
                                text_fin_all_release = text_fin_all_release + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_all_release = text_fin_all_release + "创建时间：" + UTC2BJS(
                                j["created_at"]) + "\n"
                            text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                    else:
                        text_fin_all_release = text_fin_all_release + "===========✅已审批，可合入===========" + "\n"
                        text_fin_all_release = text_fin_all_release + "提交MR的author👤：" + j["author"][
                            "username"] + "\n"
                        text_fin_all_release = text_fin_all_release + "标题：" + j["title"] + "\n"
                        text_fin_all_release = text_fin_all_release + "所属release单：" + release_name + "\n"
                        text_fin_all_release = text_fin_all_release + "MR地址：" + j["web_url"] + "\n"
                        text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                        text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                        text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                        text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                        if j["has_conflicts"]:
                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                            text_fin_all_release = text_fin_all_release + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_fin_all_release = text_fin_all_release + "创建时间：" + UTC2BJS(
                            j["created_at"]) + "\n"
                        text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
        if real_not_approved:
            for k in real_not_approved:
                if isinstance(k, str):
                    continue
                if k["project_id"] == 32154:
                    continue
                if not blacklist(k["title"]):
                    release_name = get_jira_release_name(extract_content(k["title"]))
                    if k["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        text_common_repo_not_approved = text_common_repo_not_approved + "===========✅已审批，可合入===========" + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "提交MR的author👤：" + \
                                                        k["author"][
                                                            "username"] + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "标题：" + k[
                            "title"] + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "所属release单：" + release_name + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "MR地址：" + k[
                            "web_url"] + "\n"
                        if k["has_conflicts"]:
                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                            text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "创建时间：" + UTC2BJS(
                            k["created_at"]) + "\n"

                    if k["project_id"] in [9674, 19635, 48901, 85497, 44700, 32154]:
                        if k["author"]["username"]:
                            text_fin_not_release = text_fin_not_release + "===========❌未审批===========" + "\n"
                            text_fin_not_release = text_fin_not_release + "提交MR的author👤：" + k["author"][
                                "username"] + "\n"
                            text_fin_not_release = text_fin_not_release + "标题：" + k["title"] + "\n"
                            text_fin_not_release = text_fin_not_release + "所属release单：" + release_name + "\n"
                            text_fin_not_release = text_fin_not_release + "MR地址：" + k["web_url"] + "\n"
                            text_fin_not = text_fin_not + "===========未审批===========" + "\n"
                            text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                            text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                            text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                            text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                            if k["has_conflicts"]:
                                commit_name = hasconflicts(k["source_branch"], k["project_id"])
                                text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                    else:
                        text_fin_not_release = text_fin_not_release + "===========❌未审批===========" + "\n"
                        text_fin_not_release = text_fin_not_release + "提交MR的author👤：" + k["author"][
                            "username"] + "\n"
                        text_fin_not_release = text_fin_not_release + "标题：" + k["title"] + "\n"
                        text_fin_not_release = text_fin_not_release + "所属release单：" + release_name + "\n"
                        text_fin_not_release = text_fin_not_release + "MR地址：" + k["web_url"] + "\n"
                        text_fin_not = text_fin_not + "===========❌未审批===========" + "\n"
                        text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                        text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                        text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                        text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                        if k["has_conflicts"]:
                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                            text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
    text_all=str(text_fin_all_master + text_fin_all_release + text_fin_not_master + text_fin_not_release)
    #ic(text_all)
    MRmsg(text_all,"gXxkbHktS7at0ADtXMRQ2w")
    
    MRmsg(text_common_repo + text_common_repo_not_approved, "Ujm2PEutT5mD61G2X97oAQ")


def MRnotdeal_20230726():
    """
    ⚠️  DEPRECATED - 此函数已废弃，计划删除
    ❌ 此函数未被任何地方调用，为历史遗留代码
    📅 标记时间: 2025-06-11
    🔄 如果您看到此警告，请确认是否仍需要此函数
    """
    import warnings
    warnings.warn(f"Function 'MRnotdeal_20230726' is deprecated and will be removed", DeprecationWarning, stacklevel=2)
    project_id_list = []
    json_path = os.path.join(BASE_DIR, 'app01', 'services_id.json')
    with open(json_path) as f:
        project_id = json.load(f)
    text_fin_all_master = ""
    text_fin_not_master = ""
    text_fin_all_release = ""
    text_fin_not_release = ""
    text_fin_all = ""
    text_fin_not = ""
    text_common_repo = ""
    text_common_repo_not_approved = ""
    fe_blacklist = ["bin.wang@shopee", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>"]
    for i in project_id.values():
        project_id_list.append(str(i))
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    for i in project_id_list:
        real_url_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=master&state=opened&scope=all&approved_by_ids=Any'
        real_url_not_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=master&state=opened&scope=all&approved_by_ids=None'
        get_feedback_isue_appr = requests.get(real_url_approved)
        get_feedback_isue_not_appr = requests.get(real_url_not_approved)
        real_not_approved = json.loads(get_feedback_isue_not_appr.text)
        text_appr = json.loads(get_feedback_isue_appr.text)
        if text_appr:
            for j in text_appr:
                if not blacklist(j["title"]):
                    release_name = get_jira_release_name(extract_content(j["title"]))
                    if j["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                        text_common_repo = text_common_repo + "===========✅已审批，可合入===========" + "\n"
                        text_common_repo = text_common_repo + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_common_repo = text_common_repo + "标题：" + j["title"] + "\n"
                        text_common_repo = text_common_repo + "所属release单：" + release_name + "\n"
                        text_common_repo = text_common_repo + "MR地址：" + j["web_url"] + "\n"
                        if j["has_conflicts"]:
                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                            text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_common_repo = text_common_repo + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                    if j["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                        if j["author"]["username"] in fe_blacklist:
                            text_fin_all_master = text_fin_all_master + "===========✅已审批，可合入===========" + "\n"
                            text_fin_all_master = text_fin_all_master + "提交MR的author👤：" + j["author"][
                                "username"] + "\n"
                            text_fin_all_master = text_fin_all_master + "标题：" + j["title"] + "\n"
                            text_fin_all_master = text_fin_all_master + "所属release单：" + release_name + "\n"
                            text_fin_all_master = text_fin_all_master + "MR地址：" + j["web_url"] + "\n"
                            text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                            text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                            text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                            text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                            text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                            if j["has_conflicts"]:
                                commit_name = hasconflicts(j["source_branch"], j["project_id"])
                                text_fin_all_master = text_fin_all_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_all_master = text_fin_all_master + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                            text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                    else:
                        text_fin_all_master = text_fin_all_master + "===========✅已审批，可合入===========" + "\n"
                        text_fin_all_master = text_fin_all_master + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_fin_all_master = text_fin_all_master + "标题：" + j["title"] + "\n"
                        text_fin_all_master = text_fin_all_master + "所属release单：" + release_name + "\n"
                        text_fin_all_master = text_fin_all_master + "MR地址：" + j["web_url"] + "\n"
                        text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                        text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                        text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                        text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                        text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                        if j["has_conflicts"]:
                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                            text_fin_all_master = text_fin_all_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_fin_all_master = text_fin_all_master + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                        text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"

        if real_not_approved:
            for k in real_not_approved:
                if not blacklist(k["title"]):
                    release_name = get_jira_release_name(extract_content(k["title"]))
                    if k["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                        text_common_repo_not_approved = text_common_repo_not_approved + "===========✅已审批，可合入===========" + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "提交MR的author👤：" + \
                                                        k["author"][
                                                            "username"] + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "标题：" + k["title"] + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "所属release单：" + release_name + "\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "MR地址：" + k[
                            "web_url"] + "\n"
                        if k["has_conflicts"]:
                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                            text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_common_repo_not_approved = text_common_repo_not_approved + "创建时间：" + UTC2BJS(
                            k["created_at"]) + "\n"

                    if k["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                        if k["author"]["username"] in fe_blacklist:
                            text_fin_not_master = text_fin_not_master + "===========❌未审批===========" + "\n"
                            text_fin_not_master = text_fin_not_master + "提交MR的author👤：" + k["author"][
                                "username"] + "\n"
                            text_fin_not_master = text_fin_not_master + "标题：" + k["title"] + "\n"
                            text_fin_not_master = text_fin_not_master + "所属release单：" + release_name + "\n"
                            text_fin_not_master = text_fin_not_master + "MR地址：" + k["web_url"] + "\n"
                            text_fin_not = text_fin_not + "===========❌未审批===========" + "\n"
                            text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                            text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                            text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                            text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                            if k["has_conflicts"]:
                                commit_name = hasconflicts(k["source_branch"], k["project_id"])
                                text_fin_not_master = text_fin_not_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_not_master = text_fin_not_master + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                            text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                    else:
                        text_fin_not_master = text_fin_not_master + "===========❌未审批===========" + "\n"
                        text_fin_not_master = text_fin_not_master + "提交MR的author👤：" + k["author"]["username"] + "\n"
                        text_fin_not_master = text_fin_not_master + "标题：" + k["title"] + "\n"
                        text_fin_not_master = text_fin_not_master + "所属release单：" + release_name + "\n"
                        text_fin_not_master = text_fin_not_master + "MR地址：" + k["web_url"] + "\n"
                        text_fin_not = text_fin_not + "===========❌未审批===========" + "\n"
                        text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                        text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                        text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                        text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                        if k["has_conflicts"]:
                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                            text_fin_not_master = text_fin_not_master + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                            text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                        text_fin_not_master = text_fin_not_master + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                        text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"

                    real_url_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=release&state=opened&scope=all&approved_by_ids=Any'
                    real_url_not_approved = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=release&state=opened&scope=all&approved_by_ids=None'
                    get_feedback_isue_appr = requests.get(real_url_approved)
                    get_feedback_isue_not_appr = requests.get(real_url_not_approved)
                    real_not_approved = json.loads(get_feedback_isue_not_appr.text)
                    text_appr = json.loads(get_feedback_isue_appr.text)
                    if text_appr:
                        for j in text_appr:
                            if not blacklist(j["title"]):
                                release_name = get_jira_release_name(extract_content(j["title"]))
                                if j["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                                    text_common_repo = text_common_repo + "===========✅已审批，可合入===========" + "\n"
                                    text_common_repo = text_common_repo + "提交MR的author👤：" + j["author"][
                                        "username"] + "\n"
                                    text_common_repo = text_common_repo + "标题：" + j["title"] + "\n"
                                    text_common_repo = text_common_repo + "所属release单：" + release_name + "\n"
                                    text_common_repo = text_common_repo + "MR地址：" + j["web_url"] + "\n"
                                    if j["has_conflicts"]:
                                        commit_name = hasconflicts(j["source_branch"], j["project_id"])
                                        text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                    text_common_repo = text_common_repo + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"

                                if j["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                                    if j["author"]["username"] in fe_blacklist:
                                        text_fin_all_release = text_fin_all_release + "===========✅已审批，可合入===========" + "\n"
                                        text_fin_all_release = text_fin_all_release + "提交MR的author👤：" + j["author"][
                                            "username"] + "\n"
                                        text_fin_all_release = text_fin_all_release + "标题：" + j["title"] + "\n"
                                        text_fin_all_release = text_fin_all_release + "所属release单：" + release_name + "\n"
                                        text_fin_all_release = text_fin_all_release + "MR地址：" + j["web_url"] + "\n"
                                        text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                                        text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"][
                                            "username"] + "\n"
                                        text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                                        text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                                        text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                                        if j["has_conflicts"]:
                                            commit_name = hasconflicts(j["source_branch"], j["project_id"])
                                            text_fin_all_release = text_fin_all_release + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                            text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                        text_fin_all_release = text_fin_all_release + "创建时间：" + UTC2BJS(
                                            j["created_at"]) + "\n"
                                        text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                                else:
                                    text_fin_all_release = text_fin_all_release + "===========✅已审批，可合入===========" + "\n"
                                    text_fin_all_release = text_fin_all_release + "提交MR的author👤：" + j["author"][
                                        "username"] + "\n"
                                    text_fin_all_release = text_fin_all_release + "标题：" + j["title"] + "\n"
                                    text_fin_all_release = text_fin_all_release + "所属release单：" + release_name + "\n"
                                    text_fin_all_release = text_fin_all_release + "MR地址：" + j["web_url"] + "\n"
                                    text_fin_all = text_fin_all + "===========✅已审批，可合入===========" + "\n"
                                    text_fin_all = text_fin_all + "提交MR的author👤：" + j["author"]["username"] + "\n"
                                    text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                                    text_fin_all = text_fin_all + "所属release单：" + release_name + "\n"
                                    text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                                    if j["has_conflicts"]:
                                        commit_name = hasconflicts(j["source_branch"], j["project_id"])
                                        text_fin_all_release = text_fin_all_release + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                        text_fin_all = text_fin_all + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                    text_fin_all_release = text_fin_all_release + "创建时间：" + UTC2BJS(
                                        j["created_at"]) + "\n"
                                    text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
                    if real_not_approved:
                        for k in real_not_approved:
                            if not blacklist(k["title"]):
                                release_name = get_jira_release_name(extract_content(k["title"]))
                                if k["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                                    text_common_repo_not_approved = text_common_repo_not_approved + "===========✅已审批，可合入===========" + "\n"
                                    text_common_repo_not_approved = text_common_repo_not_approved + "提交MR的author👤：" + \
                                                                    k["author"][
                                                                        "username"] + "\n"
                                    text_common_repo_not_approved = text_common_repo_not_approved + "标题：" + k[
                                        "title"] + "\n"
                                    text_common_repo_not_approved = text_common_repo_not_approved + "所属release单：" + release_name + "\n"
                                    text_common_repo_not_approved = text_common_repo_not_approved + "MR地址：" + k[
                                        "web_url"] + "\n"
                                    if k["has_conflicts"]:
                                        commit_name = hasconflicts(k["source_branch"], k["project_id"])
                                        text_common_repo = text_common_repo + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                    text_common_repo_not_approved = text_common_repo_not_approved + "创建时间：" + UTC2BJS(
                                        k["created_at"]) + "\n"

                                if k["project_id"] in [9674, 19635, 48901, 85497, 44700]:
                                    if k["author"]["username"]:
                                        text_fin_not_release = text_fin_not_release + "===========❌未审批===========" + "\n"
                                        text_fin_not_release = text_fin_not_release + "提交MR的author👤：" + k["author"][
                                            "username"] + "\n"
                                        text_fin_not_release = text_fin_not_release + "标题：" + k["title"] + "\n"
                                        text_fin_not_release = text_fin_not_release + "所属release单：" + release_name + "\n"
                                        text_fin_not_release = text_fin_not_release + "MR地址：" + k["web_url"] + "\n"
                                        text_fin_not = text_fin_not + "===========未审批===========" + "\n"
                                        text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"][
                                            "username"] + "\n"
                                        text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                                        text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                                        text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                                        if k["has_conflicts"]:
                                            commit_name = hasconflicts(k["source_branch"], k["project_id"])
                                            text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                        text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"
                                else:
                                    text_fin_not_release = text_fin_not_release + "===========❌未审批===========" + "\n"
                                    text_fin_not_release = text_fin_not_release + "提交MR的author👤：" + k["author"][
                                        "username"] + "\n"
                                    text_fin_not_release = text_fin_not_release + "标题：" + k["title"] + "\n"
                                    text_fin_not_release = text_fin_not_release + "所属release单：" + release_name + "\n"
                                    text_fin_not_release = text_fin_not_release + "MR地址：" + k["web_url"] + "\n"
                                    text_fin_not = text_fin_not + "===========❌未审批===========" + "\n"
                                    text_fin_not = text_fin_not + "提交MR的author👤：" + k["author"]["username"] + "\n"
                                    text_fin_not = text_fin_not + "标题：" + k["title"] + "\n"
                                    text_fin_not = text_fin_not + "所属release单：" + release_name + "\n"
                                    text_fin_not = text_fin_not + "MR地址：" + k["web_url"] + "\n"
                                    if k["has_conflicts"]:
                                        commit_name = hasconflicts(k["source_branch"], k["project_id"])
                                        text_fin_not = text_fin_not + "这个MR有冲突，冲突解决开发👤：<mention-tag target=\"seatalk://user?email=" + commit_name + "\"/>\n"
                                    text_fin_not = text_fin_not + "创建时间：" + UTC2BJS(k["created_at"]) + "\n"

    MRmsg(text_fin_all_master + text_fin_all_release + text_fin_not_master + text_fin_not_release,
          "gXxkbHktS7at0ADtXMRQ2w")
    MRmsg(text_common_repo + text_common_repo_not_approved, "Ujm2PEutT5mD61G2X97oAQ")

"""
    机器人发送seatalk通知的核心函数，通过group_id来进行字段区分
    当消息内容超过4000字符时会自动分段发送
"""
def test_for_seatalk_bot(text, mentionals, group_id, thread_id=None, quoted_message_id=None):
    if not text:
        return
    
    # 记录传入的线程ID和引用消息ID
    if thread_id:
        ic(f"test_for_seatalk_bot 收到线程ID: {thread_id}")
    if quoted_message_id:
        ic(f"test_for_seatalk_bot 收到引用消息ID: {quoted_message_id}")
    
    # 保存原始线程ID和引用消息ID，避免被清理
    original_thread_id = thread_id
    original_quoted_message_id = quoted_message_id
        
    def send_message(content, mentional_text=""):
        url = "https://openapi.seatalk.io/messaging/v2/group_chat"
        result = app_access_token()
        tokens = "Bearer " + result
        
        if mentional_text:
            content = f"{mentional_text}\n{content}"
            
        # 创建请求参数
        param = {
            "group_id": group_id,
            "message": {
                "tag": "text",
                "text": {
                    "content": content,
                },
            }
        }
        
        # 如果是线程中的消息，使用线程ID进行回复
        if original_thread_id:
            ic(f"添加线程ID到请求参数: {original_thread_id}")
            # 修正：根据SeaTalk API文档，线程回复参数应该在message对象内部
            param["message"]["thread_id"] = original_thread_id
            
            # 如果同时有引用消息ID，也添加引用消息ID
            if original_quoted_message_id:
                ic(f"在线程消息中同时添加引用消息ID: {original_quoted_message_id}")
                param["message"]["quoted_message_id"] = original_quoted_message_id
            
            ic(f"完整请求参数(线程回复): {json.dumps(param)}")
        # 如果不是线程中的消息但有引用消息ID，则使用引用消息ID
        elif original_quoted_message_id:
            ic(f"添加引用消息ID到请求参数: {original_quoted_message_id}")
            # 引用消息ID需要添加到message对象中
            param["message"]["quoted_message_id"] = original_quoted_message_id
            # 确保不添加thread_id，避免冲突
            if "thread_id" in param["message"]:
                del param["message"]["thread_id"]
            ic(f"完整请求参数(引用消息): {json.dumps(param)}")
            
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        try:
            r = requests.post(url=url, json=param, headers=headers, timeout=30)
            ic(f"SeaTalk API响应: {r.text}")
            response_json = json.loads(r.text)
            if response_json.get("code") != 0:
                ic(f"SeaTalk API错误: {response_json.get('message', '未知错误')}")
            return r.status_code == 200 and response_json.get("code") == 0
        except requests.exceptions.Timeout:
            print(time_prefix() + f"Seatalk API请求超时: {group_id}")
            return False
        except requests.exceptions.RequestException as e:
            print(time_prefix() + f"Seatalk API请求失败: {group_id}, 错误: {e}")
            return False
        
    # 处理@提及
    mentional_text = ""
    if mentionals:
        for mentional in mentionals:
            if mentional == "ALL":
                mentional_text = "<mention-tag target=\"seatalk://user?id=0\"/>"
                break
            if "@shopee.com" not in mentional:
                mentional_text = f"<mention-tag target=\"seatalk://user?id={mentional}\"/>" + mentional_text
            else:
                mentional_text = f"<mention-tag target=\"seatalk://user?email={mentional}\"/>" + mentional_text

    # 记录是否是线程回复
    if original_thread_id:
        ic(f"准备发送线程回复，线程ID: {original_thread_id}")
    
    # 如果消息长度超过4000，进行分段处理
    MAX_LENGTH = 4000
    result = False
    
    if len(text) > MAX_LENGTH:
        # 将文本按照换行符分割
        lines = text.split('\n')
        current_chunk = ""
        
        for line in lines:
            # 如果当前块加上新行不超过限制，就添加到当前块
            if len(current_chunk) + len(line) + 1 <= MAX_LENGTH:
                current_chunk += (line + '\n')
            else:
                # 发送当前块
                if current_chunk:
                    result = send_message(current_chunk.rstrip(), mentional_text if mentional_text else "")
                # 开始新的块
                current_chunk = line + '\n'
                
        # 发送最后一个块
        if current_chunk:
            result = send_message(current_chunk.rstrip(), mentional_text if mentional_text else "")
    else:
        # 消息长度在限制之内，直接发送
        result = send_message(text, mentional_text)
    
    return result

"""
    channel的MR监控函数，如果没处理的话会进行通知，通过crontab设置为1小时运行一次
"""
def MRnotdealchannel_new():
    project_id_list = []
    project_id = Config.mrMapChannel
    text_fin_all = ""
    black_list = ["weibin.fang", "jun.huang", "liyan", "xingwei.zou", "xinsen.hu", "jiamin.he"]
    for i in project_id.values():
        project_id_list.append(i)
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    for i in project_id_list:
        real_url = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=master&state=opened&scope=all'
        get_feedback_isue = requests.get(real_url)
        text_all = json.loads(get_feedback_isue.text)
        if text_all:
            for j in text_all:
                if not blacklist(j["title"]):
                    if j["author"]["username"] in black_list:
                        continue
                    text_fin_all = text_fin_all + "==============================" + "\n"
                    text_fin_all = text_fin_all + "提交MR的author：" + j["author"]["username"] + "\n"
                    text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                    text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                    text_fin_all = text_fin_all + "创建时间：" + UTC2BJS(j["created_at"]) + "\n"
        real_url = BASE_URL + i + f'/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&target_branch=release&state=opened&scope=all'
        get_feedback_isue = requests.get(real_url)
        text_all = json.loads(get_feedback_isue.text)
        if text_all:
            for j in text_all:
                if not blacklist(j["title"]):
                    if j["author"]["username"] in black_list:
                        continue
                    text_fin_all = text_fin_all + "==============================" + "\n"
                    text_fin_all = text_fin_all + "提交MR的author：" + j["author"]["username"] + "\n"
                    text_fin_all = text_fin_all + "标题：" + j["title"] + "\n"
                    text_fin_all = text_fin_all + "MR地址：" + j["web_url"] + "\n"
                    text_fin_all = text_fin_all + "创建时间：" + j["created_at"] + "\n"
    if text_fin_all:
        MRmsg(text_fin_all, "Rrcd-KN-QcuTdkVsSNcZlQ")

"""
    从DB里，拿到没部署成功的pipeline的callbackID，再去查询一次部署结果，如果成功则启动自动化测试，失败则继续更新状态
"""
@track_cronjob_execution('callback')
def callback():
    # 添加详细的调试信息
    import datetime
    import threading
    import os
    
    ic("=== Callback Debug Info ===")
    ic("Timestamp:", datetime.datetime.now())
    ic("Thread ID:", threading.get_ident())
    ic("Process ID:", os.getpid())
    
    # 获取待处理的记录数
    data_result = Deploy.objects.filter(jenkinsDeployResult__in=['doing', 'QUEUE', 'IN_PROGRESS'])
    count = data_result.count()
    ic("Number of records to process:", count)
    
    if count == 0:
        ic("No records to process, exiting early")
        return
        
    ic("Starting callback function")
    ic("Current file location:", __file__)
    ic("Current line number:", inspect.currentframe().f_lineno)
    detail_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/build/detail"
    list_result = list(data_result)
    server = jenkins.Jenkins('http://**************.:8080', username='wenjie', password='123456')
    
    MAX_RETRIES = 30  # 最大重试次数
    RETRY_INTERVAL = 10  # 重试间隔(秒)
    
    # 将 gettoken 移到循环外
    result = gettoken()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    
    for one in list_result:
        parameters = {
            "callback_id": one.callbackID,
        }
        r = requests.post(url=detail_url, json=parameters, headers=headers)
        time.sleep(2)
        result_rep = json.loads(r.text)
        if result_rep["data"]:
            one.jenkinsDeployResult = result_rep['data']['status']
        else:
            continue
        if result_rep['data']['status'] == "SUCCESS" and one.env == "test":
            if "chatbotcommon" in one.projectName:
                one.save()
                continue
            auto_map = Config.autoMap
            if one.gitlabProjectURL in auto_map.keys():
                for case in auto_map[one.gitlabProjectURL]:
                    number = server.build_job('API-AUTO-CICD', {
                        'dir_name': 'case/' + case, 
                        'env': 'test',
                        'cid': 'sg',
                        'gitlab_ci_url': one.pipelineURL
                    })
                    
                    # 检查队列状态
                    for i in range(10):
                        rep = server.get_queue_item(number=number)
                        if not rep['why']:
                            one.jenkinsAutoTestID = rep['executable']['number'] 
                            one.save()
                            
                            # 检查构建状态
                            retry_count = 0
                            while retry_count < MAX_RETRIES:
                                rep_build = server.get_build_info(
                                    name="API-AUTO-CICD",
                                    number=rep['executable']['number']
                                )
                                if rep_build.get("result") in ['UNSTABLE', 'FAILURE', 'SUCCESS']:
                                    break
                                retry_count += 1
                                time.sleep(RETRY_INTERVAL)
                            break
                        elif i == 9:
                            one.save()
                        else:
                            time.sleep(20)
            else:
                one.save()
        else:
            one.save()

"""
    获取employee的ID，通过email来查询，发私聊通知用
"""
def send_private_notifications_with_fallback(items_by_pm, release_title, role_type):
    """
    发送私聊通知给相关PM，如果失败则返回失败的条目用于群组通知
    
    Args:
        items_by_pm: 按PM分组的条目字典 {pm_email: [items]}
        release_title: 发布标题  
        role_type: 角色类型 ('dev', 'pm', 'ds')
    
    Returns:
        failed_items: 发送失败的条目列表，需要回退到群组通知
    """
    failed_items = []
    
    print(f"=== 开始发送私聊通知 ({role_type}组) ===")
    print(f"发布标题: {release_title}")
    print(f"需要通知的PM数量: {len(items_by_pm)}")
    
    for pm_email, pm_item_list in items_by_pm.items():
        try:
            print(f"正在处理PM: {pm_email}")
            
            # 尝试获取员工工号
            employee_code = get_employee_code(pm_email)
            
            # 检查是否成功获取员工工号
            if employee_code is None:
                print(f"❌ 无法获取员工工号: {pm_email}")
                print(f"   将回退到群组通知，涉及条目: {', '.join([item['feature_key'] for item in pm_item_list])}")
                failed_items.extend(pm_item_list)
                continue
            
            print(f"获取员工工号成功: {pm_email} -> {employee_code}")
            
            # 构建私聊消息内容
            message_items = []
            item_keys = []
            for item in pm_item_list:
                message_items.append(f"{item['feature_key']} {item['feature_title']}")
                item_keys.append(item['feature_key'])
            
            message = f"{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n"
            message += "以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。"
            from .bot_config import bot_config
            message += f"或者直接在群里\"@{bot_config.BOT_NAME} SO\"，即可 signoff。\n"
            message += "\n".join(message_items)
            
            print(f"私聊消息内容概要:")
            print(f"  - 收件人: {pm_email} (员工工号: {employee_code})")
            print(f"  - 条目数量: {len(pm_item_list)}")
            print(f"  - 条目列表: {', '.join(item_keys)}")
            print(f"  - 消息长度: {len(message)} 字符")
            
            # 尝试发送私聊
            single_chat(message, employee_code)
            print(f"✅ 私聊通知发送成功: {pm_email} ({role_type})")
            
        except Exception as e:
            print(f"❌ 私聊通知发送失败: {pm_email} ({role_type})")
            print(f"   错误详情: {e}")
            print(f"   将回退到群组通知，涉及条目: {', '.join([item['feature_key'] for item in pm_item_list])}")
            # 发送失败的条目添加到失败列表，用于群组通知
            failed_items.extend(pm_item_list)
    
    print(f"=== 私聊通知处理完成 ({role_type}组) ===")
    print(f"成功发送私聊数量: {len(items_by_pm) - len([pm for pm, items in items_by_pm.items() if any(item in failed_items for item in items)])}")
    print(f"需要回退到群组通知的条目数量: {len(failed_items)}")
    
    return failed_items

def get_employee_code(email):
    print("email:")
    print(email)
    if email == "<EMAIL>":
        email = "<EMAIL>"
    access_token = app_access_token()
    get_code_url = "https://openapi.seatalk.io/contacts/v2/get_employee_code_with_email"
    headers = {
        'content-type': "application/json",
        'Authorization': "Bearer " + access_token
    }
    param = {
        "emails": [email],  # 修复：使用数组格式而不是字符串
    }
    r = requests.post(url=get_code_url, json=param, headers=headers)
    json_result = json.loads(r.text)
    print(json_result)
    
    # 增加错误检查
    if json_result.get("code") != 0:
        print(f"❌ get_employee_code API错误: {json_result}")
        return None
    
    if not json_result.get("employees") or len(json_result["employees"]) == 0:
        print(f"❌ 未找到员工信息: {email}")
        return None
    
    employee = json_result["employees"][0]
    if employee.get("code") != 0:
        print(f"❌ 员工状态异常: {employee}")
        return None
    
    return employee["employee_code"]

"""
    时间格式转化函数
"""
def timeformat(local_time):
    """
    ⚠️  DEPRECATED - 此函数已废弃，计划删除
    ❌ 此函数未被任何地方调用，为历史遗留代码
    📅 标记时间: 2025-06-11
    🔄 如果您看到此警告，请确认是否仍需要此函数
    """
    import warnings
    warnings.warn(f"Function 'timeformat' is deprecated and will be removed", DeprecationWarning, stacklevel=2)
    # 警告：这个函数有时区转换错误，会多加8小时
    # 已在 failuremsg_final 等函数中修复，不再使用此函数
    # 建议使用 time.strftime("%Y-%m-%d %H:%M:%S", timeArray) 直接格式化
    local_timestamp = time.mktime(local_time)
    utc_timestamp = local_timestamp + 8 * 3600  # 这里错误地又加了8小时
    utc_time = time.localtime(utc_timestamp)
    formatTime = time.strftime("%Y-%m-%d %H:%M:%S", utc_time)
    return formatTime

"""
    用来匹配是否包含有SPCB的JIRA单格式类型的中间函数
"""
def extract_content(string):
    pattern = r'\[(.*?)\]'  # 匹配中括号内的内容
    match = re.search(pattern, string)  # 使用正则表达式进行匹配
    if match:
        return match.group(1)  # 返回匹配到的内容
    else:
        pattern = r'SPCB-\d+'
        match = re.search(pattern, string)
        if match:
            return match.group()
        else:
            return None  # 如果未匹配到内容，返回None

"""
    已废弃
"""
def failuremsg():
    """
    ⚠️  DEPRECATED - 此函数已废弃，计划删除
    ❌ 此函数未被任何地方调用，为历史遗留代码
    📅 标记时间: 2025-06-11
    🔄 如果您看到此警告，请确认是否仍需要此函数
    """
    import warnings
    warnings.warn(f"Function 'failuremsg' is deprecated and will be removed", DeprecationWarning, stacklevel=2)
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    seatalk_url = "https://openapi.seatalk.io/webhook/group/ZHWKU3hhQtyYD-kwIjQlYQ"
    srv_list = {}
    result = gettoken()
    print("failuremsg begin!!")

    t = time.gmtime()
    print(time.strftime("%Y-%m-%d %H:%M:%S", t))
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    for value in Config.srvMap.values():
        for i in value:
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep["errmsg"] == "success":
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']

                    if build_result == 'FAILURE' or build_result == 'ABORTED':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        end_time = result_rep["data"]["list"][0]['end_time']
                        timeArray = time.localtime(end_time / 1000)
                        formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        # formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                               "end_time": formatTime
                                               }
                        else:
                            srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                           "end_time": formatTime
                                           }
    for value in Config.srvMap.values():

        for i in value:
            i = i.replace("test", "uat")

            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE' or build_result == 'ABORTED':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        end_time = result_rep["data"]["list"][0]['end_time']
                        timeArray = time.localtime(end_time / 1000)
                        formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        # formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        print(parameter.keys())
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                # tmp = {i:parameter["FROM_BRANCH"]}
                                # srv_list.append(tmp)
                                srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                               "end_time": formatTime
                                               }
                        else:
                            srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                           "end_time": formatTime
                                           }

    for value in Config.srvMap.values():

        for i in value:
            i = i.replace("test", "staging")

            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE' or build_result == 'ABORTED':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        end_time = result_rep["data"]["list"][0]['end_time']
                        timeArray = time.localtime(end_time / 1000)
                        formatTime = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                               "end_time": formatTime
                                               }
                        else:
                            srv_list[i] = {"branch": parameter["FROM_BRANCH"],
                                           "end_time": formatTime
                                           }

    if srv_list:
        text_all = ""
        srv_id = Config.srv2id
        blacklist = Config.msgBlackList
        list_dev_all = []
        for i in srv_list.keys():
            if i in blacklist:
                continue
            msg_tmp = {}
            dev_name = ""
            title = ""
            code = ""
            list_dev = []
            branch_name_tmp = srv_list[i]["branch"].replace("origin/", "")
            if "-test" in i:
                if i.replace('-test', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-test', '')], branch_name_tmp)
            if "-uat" in i:
                if i.replace('-uat', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-uat', '')], branch_name_tmp)
            if "-staging" in i:
                if i.replace('-staging', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-staging', '')], branch_name_tmp)
            if msg_tmp:
                dev_name = msg_tmp['name'] + "@shopee.com"
                title = msg_tmp['title']
                list_dev.append(dev_name)
                list_dev_all.append(dev_name)
                code = get_employee_code(list_dev)
                text_single = "失败的服务信息，请关注并解决。\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                              srv_list[i]["end_time"] + "\n"
                single_chat(text_single, "159264")
            text_all = text_all + "=============================\n" + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n" + "失败时间：" + \
                       srv_list[i]["end_time"] + "\n"
        if not text_all:
            return
        text_all = "\n最后部署失败的服务列表为：\n" + text_all
        param = {
            "tag": "text",
            "text": {
                "content": text_all,
                "mentioned_email_list": list(set(list_dev_all)),
            }
        }
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        r = requests.post(url=seatalk_url, json=param, headers=headers)
        print(json.loads(r.text))
    else:
        print("success!!")

"""
    废弃，抽空删掉
"""
def failuremsg_old():
    """
    ⚠️  DEPRECATED - 此函数已废弃，计划删除
    ❌ 此函数未被任何地方调用，为历史遗留代码
    📅 标记时间: 2025-06-11
    🔄 如果您看到此警告，请确认是否仍需要此函数
    """
    import warnings
    warnings.warn(f"Function 'failuremsg_old' is deprecated and will be removed", DeprecationWarning, stacklevel=2)
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    seatalk_url = "https://openapi.seatalk.io/webhook/group/ZHWKU3hhQtyYD-kwIjQlYQ"
    srv_list = {}
    result = gettoken()
    print("failuremsg begin!!")

    t = time.gmtime()
    print(time.strftime("%Y-%m-%d %H:%M:%S", t))
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    for value in Config.srvMap.values():
        for i in value:
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep["errmsg"] == "success":
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                srv_list[i] = parameter["FROM_BRANCH"]
                        else:
                            srv_list[i] = parameter["FROM_BRANCH"]

    for value in Config.srvMap.values():

        for i in value:
            i = i.replace("test", "uat")

            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        print(parameter.keys())
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                # tmp = {i:parameter["FROM_BRANCH"]}
                                # srv_list.append(tmp)
                                srv_list[i] = parameter["FROM_BRANCH"]
                        else:
                            srv_list[i] = parameter["FROM_BRANCH"]

    for value in Config.srvMap.values():

        for i in value:
            i = i.replace("test", "staging")

            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = json.loads(r.text)
            # result_rep = eval(r.text)
            # print(result_rep)
            if result_rep['data']:
                if result_rep['data']['list']:
                    build_result = result_rep['data']['list'][0]['build_status']
                    if build_result == 'FAILURE':
                        parameter = json.loads(result_rep['data']['list'][0]['parameter'])
                        print(parameter.keys())
                        if "PFB" in parameter.keys():
                            if not parameter["PFB"]:
                                # tmp = {i:parameter["FROM_BRANCH"]}
                                # srv_list.append(tmp)
                                srv_list[i] = parameter["FROM_BRANCH"]
                        else:
                            srv_list[i] = parameter["FROM_BRANCH"]

    if srv_list:
        text_all = ""
        srv_id = Config.srv2id
        blacklist = Config.msgBlackList

        for i in srv_list.keys():
            if i in blacklist:
                continue
            msg_tmp = {}
            dev_name = ""
            title = ""
            code = ""
            list_dev = []
            branch_name_tmp = srv_list[i].replace("origin/", "")
            if "-test" in i:
                if i.replace('-test', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-test', '')], branch_name_tmp)
            if "-uat" in i:
                if i.replace('-uat', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-uat', '')], branch_name_tmp)
            if "-staging" in i:
                if i.replace('-staging', '') in srv_id.keys():
                    msg_tmp = getlastMRMSG(srv_id[i.replace('-staging', '')], branch_name_tmp)
            if msg_tmp:
                dev_name = msg_tmp['name'] + "@shopee.com"
                title = msg_tmp['title']
                list_dev.append(dev_name)
                code = get_employee_code(list_dev)

            text_all = text_all + "【服务名】" + i + "\n" + "涉及开发：" + dev_name + "\n" + "MR标题：" + title + "\n"
        if not text_all:
            return
        text_all = "最后部署失败的服务列表为：\n" + text_all
        param = {
            "tag": "text",
            "text": {
                "content": text_all
            }
        }
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        r = requests.post(url=seatalk_url, json=param, headers=headers)
        print(json.loads(r.text))
    else:
        print("success!!")


"""
    自动合代码，这个跟后面的merge的区分，这个为中间调用函数，由于功能不同就不放到一个函数了，而是做一个拆分
"""
def automerge(project_id, iid):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    approve_url = f"{BASE_URL}{project_id}/merge_requests/{iid}/approvals?private_token={GITLAB_PRIVATE_TOKEN}&approvals_required=0"


    # 从CC里获取超时配置，默认为300秒（5分钟）
    # https://config.shopee.io/group/customer_service_and_chatbot/project/chatbot_qa/cluster/nonlive/namespaces/ar_test_default
    timeout = get_timeout_config_form_CC()
    if project_id not in [9674, 19635, 48901, 85497]:
        app_url = requests.post(approve_url)
        real_url = BASE_URL + '{project_id}/merge_requests/{iid}/merge?private_token={GITLAB_PRIVATE_TOKEN}'.format(
            project_id=project_id, iid=iid, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN)
        rep = requests.put(url=real_url)
        rep_result = json.loads(rep.text)

        if "message" in rep_result.keys():
            single_count = 0
            while single_count <= timeout:
                single_rep = requests.put(url=real_url)
                single_rep_result = json.loads(single_rep.text)
                if "message" in single_rep_result.keys():
                    time.sleep(30)
                    single_count += 30
                else:
                    if "state" in single_rep_result.keys():
                        if single_rep_result['state'] == 'merged':
                            return True
                        else:
                            return False
                    return False
            return False
        if "state" in rep_result.keys():
            if rep_result['state'] == 'merged':
                return True
            else:
                return False
    else:
        return False

"""
    检查merge的状态，如果一直处于不可合入的状态，则等待5秒钟，轮询30次，gitlab有时候会比较坑，没办法
"""
def checkmergestatus(repo_id, iid):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    real_url = BASE_URL + '{repo_id}/merge_requests/{iid}?private_token={GITLAB_PRIVATE_TOKEN}'.format(repo_id=repo_id,
                                                                                                     iid=iid, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN)
    max_count = 0
    while (max_count <= 30):
        get_feedback_isue = requests.get(real_url)
        text_all = json.loads(get_feedback_isue.text)
        if text_all['merge_status'] == 'can_be_merged':
            result = automerge(repo_id, iid)
            if result:
                return True
            else:
                return False
        else:
            if not text_all["changes_count"]:
                return False
            ic(text_all['merge_status'])
            max_count += 1
            time.sleep(5)
    return False

"""
    自动打TAG函数，通过TAG名跟仓库ID来打TAG
"""
def autotag(tag_title, repo_id):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    real_url = BASE_URL + '{repo_id}/repository/tags?private_token={GITLAB_PRIVATE_TOKEN}&tag_name={title}&ref=release'.format(
        repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, title=tag_title)
    get_feedback_isue = requests.post(real_url)
    autobuild(str(repo_id), tag_title)

"""
    AR的合代码函数，通过前端请求拿到仓库的列表跟所属的项目，如chatbot或者markering，来进行代码的合入
"""
def merge(request):
    if (request.method == 'POST'):
        BASE_URL = "https://git.garena.com/api/v4/projects/"
        postBody = request.body
        json_result = json.loads(postBody)
        mrMap = Config.mrMap
        mrMap_channel = Config.mrMapChannel
        mrMap_data = Config.mrMapdata
        title = json_result['title']
        repo_list = json_result['repo_list']
        repo_from = json_result['repo_from']
        repo_mr = {}
        for repo in repo_list:
            if repo in mrMap.keys() and repo_from[repo_list.index(repo)] in ["Chatbot", "seller", "marketing"]:
                repo_id = mrMap[repo]
                real_url = BASE_URL + '{repo_id}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&source_branch=master&target_branch=release&title={title}'. \
                    format(repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, title=title)
                ic(real_url)
                get_feedback_isue = requests.post(real_url)
                text_all = json.loads(get_feedback_isue.text)
                ic(text_all)
                if 'web_url' in text_all.keys():
                    web_url = text_all['web_url']
                    ic(web_url)
                    result_merge = checkmergestatus(int(repo_id), int(text_all['iid']))
                    if result_merge:
                        autotag(title, repo_id)
                        repo_mr[repo] = [web_url, 'merged']
                    else:
                        repo_mr[repo] = [web_url, 'fail,no code change!']
                else:
                    project_url = BASE_URL + '{repo_id}?private_token={GITLAB_PRIVATE_TOKEN}'.format(repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN)
                    res = requests.get(project_url)
                    res = json.loads(res.text)
                    merge_url_link = f"{res['web_url']}/-/merge_requests"
                    repo_mr[repo] = [merge_url_link, 'fail,please check if another MR already exist']
            if repo in mrMap_data.keys() and repo_from[repo_list.index(repo)] in ["data"]:
                repo_id = mrMap_data[repo]
                real_url = BASE_URL + '{repo_id}/merge_requests?private_token={GITLAB_PRIVATE_TOKEN}&source_branch=master&target_branch=release&title={title}'. \
                    format(repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, title=title)
                ic(real_url)
                get_feedback_isue = requests.post(real_url)
                text_all = json.loads(get_feedback_isue.text)
                if 'web_url' in text_all.keys():
                    web_url = text_all['web_url']
                    ic(web_url)
                    result_merge = checkmergestatus(int(repo_id), int(text_all['iid']))
                    if result_merge:
                        autotag(title, repo_id)
                        repo_mr[repo] = [web_url, 'merged']
                    else:
                        repo_mr[repo] = [web_url, 'fail,no code change!']
                else:
                    project_url = BASE_URL + '{repo_id}?private_token={GITLAB_PRIVATE_TOKEN}'.format(repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN)
                    res = requests.get(project_url)
                    res = json.loads(res.text)
                    merge_url_link = f"{res['web_url']}/-/merge_requests"
                    repo_mr[repo] = [merge_url_link, 'fail,please check if another MR already exist']
            if repo in mrMap_channel.keys() and repo_from[repo_list.index(repo)] in ["channel-FE", "channel-BE"]:
                repo_id = mrMap_channel[repo]
                real_url = BASE_URL + '{repo_id}/merge_requests?private_token=YEvG7ACsWzGEAjUVEL8m&source_branch=master&target_branch=release&title={title}'. \
                    format(repo_id=repo_id, title=title)
                ic(real_url)
                get_feedback_isue = requests.post(real_url)
                text_all = json.loads(get_feedback_isue.text)
                if 'web_url' in text_all.keys():
                    web_url = text_all['web_url']
                    ic(web_url)
                    repo_mr[repo] = [web_url, 'ready']
                else:
                    repo_mr[repo] = ['fail,please check if another MR already exist!', 'fail']
        return_data = {
            "msg": "success",
            "repo_list": repo_mr,
        }
        return HttpResponse(json.dumps(return_data))

"""
    自动化回调处理函数，通过自动化后台请求拿到自动化的结果后，失败信息进行处理，发送seatalk通知
"""
def apiCallback(request):
    seatalk_url = "https://openapi.seatalk.io/webhook/group/ZHWKU3hhQtyYD-kwIjQlYQ"
    if (request.method == 'POST'):
        # print("the POST method")
        postBody = request.body
        json_result = json.loads(postBody)
        print(json_result)
        auto_result = json_result["pipeline_result"]
        env = json_result["env"]
        cids = json_result["cid"]
        total_case = json_result["total_case"]
        passed_cases_count = json_result["passed_cases_count"]
        failed_cases_count = json_result["failed_cases_count"]
        success_rate = json_result["success_rate"]
        skipped_cases_count = json_result["skipped_cases_count"]
        failed_case_map = json_result["failed_case_map"]
        pipe_url = json_result['gitlab_ci_url']
        deploy = Deploy()
        result_deploy = Deploy.objects.filter(pipelineURL=pipe_url)
        ic(result_deploy)
        result_shopee = list(result_deploy)[0]
        projectName = result_shopee.projectName
        build_number = result_shopee.jenkinsAutoTestID
        ic(build_number)
        print(type(success_rate))
        if auto_result == "fail" and failed_case_map:
            allure = "http://**************:8080/job/API-AUTO-CICD/{}/allure/".format(build_number)
            text_all = f"\n仓库名: " \
                       f"{projectName}\n" \
                       f"pipeline地址: " \
                       f"{pipe_url}\n" \
                       f"allure地址：" \
                       f"{allure}\n" \
                       f"执行环境：" \
                       f"{env}\n执行地区：" \
                       f"{cids}\n总用例数为：" \
                       f"{total_case}\n通过用例数为：" \
                       f"{passed_cases_count}\n失败用例数为：" \
                       f"{failed_cases_count}\n跳过用例数为：" \
                       f"{skipped_cases_count}\n通过率为(去除skipped用例后的结果)：" \
                       f"{success_rate}\n".format(projectName=projectName,
                                                  pipe_url=pipe_url,
                                                  allure=allure,
                                                  env=env,
                                                  cids=cids,
                                                  total_case=total_case,
                                                  passed_cases_count=passed_cases_count,
                                                  failed_cases_count=failed_cases_count,
                                                  skipped_cases_count=skipped_cases_count,
                                                  success_rate="%.2f" % (success_rate * 100) + "%")

            text_all = text_all + "失败的用例跟owner为：\n"
            owner_list = []
            for key in failed_case_map:
                owner_list.append(key)
                text_all = text_all + key + "：\n"
                for value in failed_case_map[key]:
                    text_all = text_all + value + "\n"
            param = {
                "tag": "text",
                "text": {
                    "content": text_all,
                    "mentioned_email_list": owner_list,
                }
            }
            headers = {
                'content-type': "application/json",
            }
            # single_chat(text_all)
            r = requests.post(url=seatalk_url, json=param, headers=headers)
        return HttpResponse("success!")
    else:
        return HttpResponse("not a post request!!!")

"""
    CD的核心函数，通过pipeline请求拿来的字段，对config.py里的一些配置文件进行查询，拿到仓库ID等，调用space接口进行预编译
"""
def deploy(request):
    ic('开始运行 deploy 函数，开始部署')
    if (request.method == 'POST'):
        postBody = request.body
        json_result = json.loads(postBody)
        projecturl = json_result['projecturl']
        pipelineURL = json_result['pipelineURL']
        devName = json_result['devName']
        env = json_result['env']
        message = json_result['message']
        build_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/build"
        result = gettoken()
        tokens = "Bearer " + result["token"]
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        if env != "test" and env != "master" and env != "uat":
            if message:
                if "pfb" in message:
                    config_test = "origin/" + env
                else:
                    return HttpResponse("no pfb message")
            else:
                config_test = Config.branch[env]
        else:
            config_test = Config.branch[env]
        svrMAP = Config.srvMap
        pattern_pfb = r"\[pfb-.*\]"
        cids = Config.cids
        de_cid = cids["single"]
        if_pfb = False
        if projecturl in svrMAP.keys():
            #自动部署功能，对不同服务默认部署地区的配置（自动部署功能只在弄 nonlive 环境使用）
            for job in svrMAP[projecturl]:
                de_cid = cids["single"]
                if "static" in job and "chatbotcommon" not in job:
                    de_cid = cids["all"]
                if "static" in job and "chatbotcommon" in job:
                    de_cid = cids["single"]
                if "dashboardstatic" in job:
                    de_cid = cids["single"]
                if "chatbotstatic" in job:
                    de_cid = "PL,ES,ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,CL"
                if "shopee-autotrainingportal-adminstatic" in job:
                    de_cid=cids["all"]+",CN"
                if "shopee-chatbot-api" in job or "shopee-csdata-ssarfe" in job or "shopee-chatbot-admin-" in job:
                    de_cid = cids["all"]
                if "shopee-chatbot-admin-" in job:
                    de_cid = "ID,PH,MY,TH,VN,TW,SG,BR,CN"
                if "cschat-h5" in job:
                    de_cid = "ID,BR,MY,CN,PH,VN,SG,TH,TW,MX,CO,CL,PL,ES,FR,IN,KR"
                if "shopee-taskflow-adminstatic" in job:
                    de_cid = "ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,ES,CL,CN"
                if "shopee-annotation-asynctask" in job or "shopee-annotation-timetask" in job or "shopee-annotation-dataproxy" in job or "shopee-annotation-admin" in job:
                    de_cid = "ID,MY,PH,SG,TW,TH,VN,MX,AR,BR,ES,CN,CN"
                if env=="test" and ("shopee-knowledgebase-admin" in job or "shopee-chatbot-autotraining" in job):
                    de_cid = "SG,BR"
                if "shopee-knowledgebase-api" in job:
                    de_cid = "SG,BR"
                if "shopee-taskflow-taskflowsop" in job or "shopee-taskflow-apiproxy" in job or "shopee-taskflow-variateserving" in job:
                    de_cid = "SG,BR"
                if "shopee-chatbotcommon-tfserving" in job or "shopee-chatbotcommon-tfapiproxy" in job or "shopee-chatbotcommon-tfvariateserving" in job:
                    de_cid = "SG,BR"
                if "shopee-knowledgebase-adminstatic" in job:
                    de_cid = "ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,ES,CL,CN"
                if "shopee-chatbot-adminstatic" in job:
                    de_cid = "ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,ES,CL,CN"
                if "shopee-csdata-ssarfe" in job or "shopee-csdata-webdashboard" in job:
                    de_cid = "ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,ES,CL,CN"
                if "shopee-taskflow-taskflowserving" in job:
                    de_cid = "BR,SG"
                if env == "master" and "dataproxy" in job:
                    job = job.replace('test', 'uat')
                if env == "master" and "dataproxy" not in job:
                    job = job.replace('test', 'staging')
                if env == "uat":
                    job = job.replace('test', 'uat')
                if env == "master" and "csdata" in job:
                    continue

                if message:
                    if "pfb" in message and message != "":
                        pfb_name = re.search(pattern_pfb, str(message)).group()
                        pfb_name = re.sub(r"[\[\]]", '', pfb_name)
                        if_pfb = True
                        parameters = {
                            "pipeline_name": job,
                            "parameters": {
                                'FROM_BRANCH': config_test,
                                'DEPLOY_CIDS': "SG",
                                "USE_SKI": "true",
                                "CANARY": "false",
                                "PFB": pfb_name,
                            },
                        }
                    else:
                        parameters = {
                            "pipeline_name": job,
                            "parameters": {
                                'FROM_BRANCH': config_test,
                                'DEPLOY_CIDS': de_cid,
                                "USE_SKI": "true",
                                "CANARY": "false",
                            },
                        }
                else:
                    parameters = {
                        "pipeline_name": job,
                        "parameters": {
                            'FROM_BRANCH': config_test,
                            'DEPLOY_CIDS': de_cid,
                            "USE_SKI": "true",
                            "CANARY": "false",
                        },
                    }
                #对 RN 项目的部署做特殊处理，需要传递更多的参数
                if "shopee-chatbot-chatbotrnstatic" in job:
                    #根据部署的环境自动选择 pfb 信息
                    if env == "test":
                        pfb_name = "pfb-web-chatbot-rn-test"
                    if env == "uat":
                        pfb_name = "pfb-web-chatbot-rn-uat"
                    if env == "master":
                        pfb_name = "pfb-web-chatbot-rn"
                    parameters={
                        "pipeline_name": "shopee-chatbot-chatbotrnstatic-test",
                            "parameters": {
                                "USE_SKI": "true",
                                "PFB": pfb_name,
                                "DEPLOY_CIDS":"SG",
                                "SOURCE_TYPE":"FROM_BRANCH",
                                "FROM_BRANCH":"origin/master",
                                "CANARY":"false",
                                "BUILD_ONLY":"false",
                                "DEPLOY_ONLY":"false",
                                "DEPLOY_AZS":"",
                                "BASE_BUNDLE_VERSION":"",
                                "ENABLE_AUTOMATION":"false",
                                "INJECT_BUNDLE_INFO_ALERT":"true",
                                "NODE":"rnslave",
                                "PLUGIN_CONFIG_SOURCE_BRANCH":"",
                                "RN_COMPOSE_MULTI_CONFIG":"",
                                "RN_PLUGIN_NAME":"@shopee-rn/web-chatbot-rn",
                                "SOURCEMAP_TAG":"",
                                "USE_REDUCE_SIZE_MODE":"false",
                                "RN_COMPOSE_COMPOSER_BRANCH":"origin/master",
                                "RN_COMPOSE_HOST_BRANCH":"origin/master",
                                "RN_PLUGIN_BRANCH":config_test,
                                #"RN_PLUGIN_BRANCH":"origin/test",
                                "TARGET_APP":"shopee",

                            },
                    }
                ic(parameters)
                r = requests.post(url=build_url, json=parameters, headers=headers)
                ic(r.headers)
                time.sleep(2)
                result_rep = json.loads(r.text)
                ic(result_rep)
                if result_rep["errmsg"] != "success":
                    continue
                deploy_param = Deploy()
                deploy_param.callbackID = result_rep['data']['callback_id']
                deploy_param.jenkinsDeployResult = "doing"
                deploy_param.branchType = config_test
                deploy_param.gitlabProjectURL = projecturl
                deploy_param.projectName = job
                deploy_param.pipelineURL = pipelineURL
                deploy_param.devName = devName
                deploy_param.env = env
                #rn项目默认只部署到 test 环境
                if "shopee-chatbot-chatbotrnstatic" in job:
                    deploy_param.env = "test"
                deploy_param.pfb = if_pfb
                deploy_param.save()
            return HttpResponse("SUCCESS!!")
        else:
            print("This projecturl is not the right one,please check again.")
            return HttpResponse("This projecturl is not the right one,please check again!!!")
    else:
        return HttpResponse("not a post request!!!")

"""
    预编译函数，通过仓库ID跟TAG的名字，调用space接口进行预编译，注意，这里面的CIDS，以后需要根据实际情况自己修改，或者根据实际情况，用其他方式替代也行，但性价比不高。
"""
def autobuild(repo_id, tag_name):
    ic("这里是预编译函数日志开始的位置")
    build_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/build"
    result = gettoken()
    build_list = []
    sr2id = Config.srv2id
    ic(repo_id)
    ic(tag_name)
    
    # 添加详细的Config.srv2id调试日志
    ic("Config.srv2id 配置内容:")
    for key, value in sr2id.items():
        ic(f"服务: {key} -> 仓库ID: {value}")
    
    # 添加匹配逻辑的调试日志
    ic(f"正在查找 repo_id '{repo_id}' 对应的服务...")
    matched_services = []
    for key, value in sr2id.items():
        if value == repo_id:
            service_name = key + "-live"
            build_list.append(service_name)
            matched_services.append(key)
            ic(f"✅ 匹配到服务: {key} -> {service_name}")
    
    if not build_list:
        ic(f"❌ 未找到 repo_id '{repo_id}' 对应的服务!")
        ic("请检查Config.srv2id配置是否正确")
        return
    
    ic(f"最终构建列表 build_list: {build_list}")
    ic(f"匹配到 {len(matched_services)} 个服务: {matched_services}")
    
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    ic(headers)
    for i in build_list:
        ic(f"🚀 开始处理服务: {i}")
        #预编译功能对不同服务的默认build地区的配置（预编译只在 live 环境使用）
        CIDS = "SG,BR"
        if "shopee-chatbotcommon-" in i or "shopee-autotrainingportal-adminstatic" in i:
            CIDS= "SG"
        if "shopee-csdata-operationadminstatic" in i:
            CIDS = "AR,BD,BO,BR,CL,CN,CO,EC,XX,VN,TW,ES,FR,GLOBAL,GT,ID,KR,IN,MA,MX,MY,PH,PK,PL,TH,TN"
        if "shopee-csdata-ssarfe" in i:
            CIDS = "ID,MY,PH,SG,TW,TH,VN,CN,BR,XX,MX,ES,CO,PL,AR,CL,FR"
        if "shopee-csdata-webdashboard" in i:
            CIDS = "SG,BR,ID,MY,PH,TW,TH,VN,CN,XX,MX,FR,CO,CL,ES,PL,AR"
        if "shopee-chatbot-tmcstatic-live" in i:
            CIDS = "SG,AR,BR,CL,CN,CO,ES,FR,ID,IN,MX,MY,PH,PL,TH,TW,VN"
        if "shopee-csdata-alert-live" in i:
            CIDS = "SG,CN"
        if "shopee-csinfra-dataservice" in i:
            CIDS = "SG,CN"
        if "shopee-chatbot-csatstatic" in i:
            CIDS = "BR,ID,MY,PH,TH,TW,VN,MX,CO,CL,SG"
        if "shopee-chatbotcommon-adminstatic-live" in i:
            CIDS = "SG,MY,PH,ID"
        if "shopee-chatbotcommon-tfeadminstatic-live" in i:
            CIDS = "SG,PH,ID,MY"
        if "shopee-chatbot-adminstatic-live" in i:
            CIDS = "CN,SG,ID,MY,PH,TW,TH,VN,BR,MX,CL,ES,CO,AR"
        if "cschat-h5" in i:
            CIDS = "SG,PL,TH,VN,CN,MY,MX,CO,BR,ES,PH,CL,TW,AR,ID"
        if "shopee-chatbot-chatbotstatic" in i:
            CIDS = "MY,PH,TW,TH,VN,BR,MX,ES,CL,PL,CO,ID,AR,SG"
        if "shopee-chatbot-api-live" in i:
            CIDS = "SG,ID,BR,MY,PH,TW,TH,VN,CO,CL,MX,PL,ES,AR"
        if "shopee-chatbot-admin-live" in i:
            CIDS = "ID,MY,PH,TW,TH,VN,MX,CN,BR,PL,CL,ES,CO,AR,SG"
        if "shopee-taskflow-adminstatic-live" in i:
            CIDS = "ID,MY,PH,TW,TH,CN,VN,BR,CO,MX,CL,AR,PL,ES,SG"
        if "shopee-knowledgebase-adminstatic-live" in i:
            CIDS = "ID,MY,PH,TW,TH,VN,BR,MX,CO,CL,AR,PL,ES,CN,SG"
        if "shopee-annotation-asynctask-live" in i:
            CIDS = "ID,MY,PH,SG,TW,TH,VN,BR,CO,CL,MX,AR,PL,ES,CN"
        if "shopee-annotation-admin-live" in i:
            CIDS = "ID,MY,PH,SG,TW,TH,VN,BR,CO,CL,MX,AR,PL,ES,CN"
        if "shopee-annotation-timetask-live" in i:
            CIDS = "ID,MY,PH,SG,TW,TH,VN,BR,CO,CL,MX,AR,PL,ES,CN"
        if "shopee-taskflow-taskflowsop-live" in i or "shopee-taskflow-apiproxy-live" in i or "shopee-taskflow-taskflowserving-live" in i or "shopee-taskflow-variateserving-live" in i or "shopee-chatbotcommon-tfvariateserving-live" in i or "shopee-chatbotcommon-tfapiproxy-live" in i or "shopee-chatbotcommon-tfserving-live" in i:
            CIDS = "SG,BR"
        # 记录最终的CIDS配置
        ic(f"📍 服务 {i} 的CIDS配置: {CIDS}")
        
        parameters = {
            "pipeline_name": i,
            "parameters": {
                'FROM_BRANCH': tag_name,
                'DEPLOY_CIDS': CIDS,
                "USE_SKI": "true",
                "CANARY": "false",
                "DEPLOY_ONLY": "false",
                "BUILD_ONLY": "true",
            },

        }
        
        ic(f"📤 发送构建请求参数: {parameters}")
        ic(f"🌐 请求URL: {build_url}")

        r = requests.post(url=build_url, json=parameters, headers=headers)
        
        # 详细记录响应信息
        ic(f"📥 HTTP状态码: {r.status_code}")
        ic(f"📥 响应头: {r.headers}")
        ic(f"📥 响应内容: {r.text}")
        
        try:
            result_rep = json.loads(r.text)
            ic(f"📊 解析后的响应: {result_rep}")
            
            # 分析响应结果
            if result_rep.get('errno') == 0:
                ic(f"✅ 服务 {i} 构建请求成功!")
                ic(f"   Callback ID: {result_rep.get('data', {}).get('callback_id')}")
                ic(f"   Service Names: {result_rep.get('data', {}).get('service_names')}")
            else:
                ic(f"❌ 服务 {i} 构建请求失败!")
                ic(f"   错误码: {result_rep.get('errno')}")
                ic(f"   错误信息: {result_rep.get('errmsg')}")
        except json.JSONDecodeError as e:
            ic(f"❌ JSON解析失败: {e}")
            ic(f"   原始响应: {r.text}")
        except Exception as e:
            ic(f"❌ 处理响应时出错: {e}")
    ic("这里是预编译函数日志结束的位置")
"""
    打TAG核心函数，且打完TAG后会直接调用autobuild进行自动预编译
"""
def tag(request):
    if (request.method == 'POST'):
        BASE_URL = "https://git.garena.com/api/v4/projects/"
        # private_token_chatbot = "kBV8bRxCbEqk2G8eyFyz"
        # private_token_channel = "YEvG7ACsWzGEAjUVEL8m"
        postBody = request.body
        json_result = json.loads(postBody)
        mrMap = Config.mrMap
        mrMap_channel = Config.mrMapChannel
        mrMap_data = Config.mrMapdata
        title = json_result['title']
        repo_list = json_result['repo_list']
        repo_from = json_result['repo_from']

        for repo in repo_list:
            if repo in mrMap.keys() and repo_from[repo_list.index(repo)] in ['Chatbot', 'marketing', 'seller']:
                repo_id = mrMap[repo]
                real_url = BASE_URL + '{repo_id}/repository/tags?private_token={GITLAB_PRIVATE_TOKEN}&tag_name={title}&ref=release'.format(
                    repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, title=title)
                get_feedback_isue = requests.post(real_url)
                # if str(repo_id) == "9674":
                autobuild(str(repo_id), title)
            if repo in mrMap_data.keys() and repo_from[repo_list.index(repo)] in ['data']:
                repo_id = mrMap_data[repo]
                real_url = BASE_URL + '{repo_id}/repository/tags?private_token={GITLAB_PRIVATE_TOKEN}&tag_name={title}&ref=release'.format(
                    repo_id=repo_id, GITLAB_PRIVATE_TOKEN=GITLAB_PRIVATE_TOKEN, title=title)
                get_feedback_isue = requests.post(real_url)

                autobuild(str(repo_id), title)
            if repo in mrMap_channel.keys() and repo_from[repo_list.index(repo)] in ['channel-FE', 'channel-BE']:
                repo_id = mrMap_channel[repo]
                real_url = BASE_URL + '{repo_id}/repository/tags?private_token=YEvG7ACsWzGEAjUVEL8m&tag_name={title}&ref=release'.format(
                    repo_id=repo_id, title=title)
                get_feedback_isue = requests.post(real_url)
        return HttpResponse("200")

"""
    signed off通知，搭配cronjob_auto_seatalk_router
"""
def cronjob_signed_off_seatalk(jira_title, jira_key):
    release_title = jira_title
    release_key = jira_key
    release_db = Autorelease.objects.get(releaseKey=release_key)
    row_data = release_db.releaseData
    
    print(f"⏰ === 开始执行 cronjob_signed_off_seatalk ===")
    print(f"发布Key: {release_key}")
    print(f"发布标题: {release_title}")
    print(f"总条目数量: {len(row_data)}")
    
    # 获取角色配置（使用文件开头定义的常量）
    # CHATBOT_PM 和 CHATBOT_DS 已在文件开头定义
    
    # 按角色分组收集需要通知的条目
    dev_items_by_pm = {}
    pm_items_by_pm = {}
    ds_items_by_pm = {}
    
    for i in row_data:
        pm = f"{i['PM']}@shopee.com"
        should_notify = False
        
        if "signoff_status" not in i.keys():
            should_notify = True
        elif not i["signoff_status"]:
            should_notify = True
        
        if should_notify:
            if pm in CHATBOT_PM:
                if pm not in pm_items_by_pm:
                    pm_items_by_pm[pm] = []
                pm_items_by_pm[pm].append(i)
            elif pm in CHATBOT_DS:
                if pm not in ds_items_by_pm:
                    ds_items_by_pm[pm] = []
                ds_items_by_pm[pm].append(i)
            else:
                if pm not in dev_items_by_pm:
                    dev_items_by_pm[pm] = []
                dev_items_by_pm[pm].append(i)

    # 分别处理每个角色组的私聊通知，失败后回退到群组通知
    if dev_items_by_pm:
        failed_dev_items = send_private_notifications_with_fallback(dev_items_by_pm, release_title, 'dev')
        if failed_dev_items:
            print(f"🔄 DEV组私聊失败，回退到群组通知 (cronjob)")
            print(f"   群组ID: NDY0MzgxNDI4OTEw")
            print(f"   失败条目数量: {len(failed_dev_items)}")
            print(f"   失败条目列表: {', '.join([item['feature_key'] for item in failed_dev_items])}")
            
            from .bot_config import bot_config
            fail_text_dev = f'{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。或者直接在群里"@{bot_config.BOT_NAME} SPCB-XXX SO"，即可 signoff。\n'
            for item in failed_dev_items:
                fail_text_dev += f"{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={item['PM']}@shopee.com\"/>\n"
            
            print(f"   群组消息长度: {len(fail_text_dev)} 字符")
            test_for_seatalk_bot(fail_text_dev, False, "NDY0MzgxNDI4OTEw")
            print(f"✅ DEV组群组通知发送完成 (cronjob)")
    
    if pm_items_by_pm:
        failed_pm_items = send_private_notifications_with_fallback(pm_items_by_pm, release_title, 'pm')
        if failed_pm_items:
            print(f"🔄 PM组私聊失败，回退到群组通知 (cronjob)")
            print(f"   群组ID: NTU4OTEwMTY5OTM3")
            print(f"   失败条目数量: {len(failed_pm_items)}")
            print(f"   失败条目列表: {', '.join([item['feature_key'] for item in failed_pm_items])}")
            
            from .bot_config import bot_config
            fail_text_pm = f'{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。或者直接在群里"@{bot_config.BOT_NAME} SPCB-XXX SO"，即可 signoff。\n'
            for item in failed_pm_items:
                fail_text_pm += f"{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={item['PM']}@shopee.com\"/>\n"
            
            print(f"   群组消息长度: {len(fail_text_pm)} 字符")
            test_for_seatalk_bot(fail_text_pm, False, "NTU4OTEwMTY5OTM3")
            print(f"✅ PM组群组通知发送完成 (cronjob)")
    
    if ds_items_by_pm:
        failed_ds_items = send_private_notifications_with_fallback(ds_items_by_pm, release_title, 'ds')
        if failed_ds_items:
            print(f"🔄 DS组私聊失败，回退到群组通知 (cronjob)")
            print(f"   群组ID: MTI2MTMxMzY5NjAz")
            print(f"   失败条目数量: {len(failed_ds_items)}")
            print(f"   失败条目列表: {', '.join([item['feature_key'] for item in failed_ds_items])}")
            
            from .bot_config import bot_config
            fail_text_ds = f'{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更需要今天Signed off，麻烦已经Signed off的在jira单里Confirmed 一下Signed off字段。或者直接在群里"@{bot_config.BOT_NAME} SPCB-XXX SO"，即可 signoff。\n'
            for item in failed_ds_items:
                fail_text_ds += f"{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={item['PM']}@shopee.com\"/>\n"
            
            print(f"   群组消息长度: {len(fail_text_ds)} 字符")
            test_for_seatalk_bot(fail_text_ds, False, "MTI2MTMxMzY5NjAz")
            print(f"✅ DS组群组通知发送完成 (cronjob)")

    print(f"🏁 === cronjob_signed_off_seatalk 执行完成 ===")

"""
    checklist通知，搭配cronjob_auto_seatalk_router
"""
def cronjob_checklist_seatalk(jira_title, key):
    release_title = jira_title
    release_key = key
    release_db = Autorelease.objects.get(releaseKey=release_key)
    row_data = release_db.releaseData
    fail_text = ""
    # for index, i in enumerate(row_data):
    for i in row_data:
        result = check_services_list(i["services_list"])
        dev_pic = f"{i['dev_pic']}@shopee.com"
        isChange = False
        if not i["config_center"] or not i['DB_Change'] or not result or not i["shopee_region"]:
            fail_text = f"{fail_text}{i['feature_key']} {i['feature_title']} <mention-tag target=\"seatalk://user?email={dev_pic}\"/>\n"
            isChange = True

        if not i["config_center"]:
            fail_text = f"{fail_text}Config Changed：{i['config_center']}\n"
            isChange = True
        if not i['DB_Change']:
            fail_text = f"{fail_text}DB Changed：{i['DB_Change']}\n"
            isChange = True
        if not result:
            fail_text = f"{fail_text}Release Checklist：None\n"
            isChange = True
        if not i["shopee_region"]:
            fail_text = f"{fail_text}Shopee Region：None\n"
            isChange = True
        if isChange:
            fail_text = f"{fail_text}=======================分割线=======================\n"
    if fail_text:
        fail_text = f"{release_title}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更存在Checklist字段未完全确认的情况，请移步jira尽快确认，以免影响live发布：\n=======================分割线=======================\n{fail_text}"

    test_for_seatalk_bot(fail_text, False, "NDY0MzgxNDI4OTEw")

"""
    mr通知，搭配cronjob_auto_seatalk_router
"""
def cronjob_mr_seatalk_msg(key):
    release_key = key
    print(time_prefix() + f"cronjob_mr_seatalk_msg开始: {release_key}")

    if not release_key:
        print(time_prefix() + f"release_key为空，返回")
        return
        
    with transaction.atomic():
        try:
            print(time_prefix() + f"开始查询数据库: {release_key}")
            obj = Autorelease.objects.get(releaseKey=release_key)
            print(time_prefix() + f"数据库查询成功: {release_key}")
            
            json_data = obj.releaseData
            merge_string = ""
            for item in json_data:
                if not item["signoff_status"]:
                    continue
                if item["Code_Merged"]:
                    continue
                dev_pic = f"{item['dev_pic']}@shopee.com"
                merge_string += f"\n{item['feature_key']} {item['feature_title']} <mention-tag target=\"seatalk://user?email={dev_pic}\"/>"

            if merge_string:
                message = f"{json_data[0]['release_title']}：https://autorelease.chatbot.shopee.io/releasecheck\n以下变更请尽快合并代码到master分支，DEV PIC确认所属变更单的FE&BE&DS代码均已合并后修改jira单中的Code Merged字段为\"Confirmed\"：{merge_string}\n"
                print(time_prefix() + f"准备发送Seatalk消息: {release_key}")
                test_for_seatalk_bot(message, False, "NDY0MzgxNDI4OTEw")
                print(time_prefix() + f"Seatalk消息发送完成: {release_key}")
            else:
                print(time_prefix() + f"没有需要发送的MR消息: {release_key}")

        except Autorelease.DoesNotExist:
            print(time_prefix() + f"数据库中未找到release_key: {release_key}")
            pass
        except Exception as e:
            print(time_prefix() + f"cronjob_mr_seatalk_msg执行时出错: {release_key}, 错误: {e}")
            
    print(time_prefix() + f"cronjob_mr_seatalk_msg完成: {release_key}")
    return

"""
    获取前N天，用来处理工作日的，搭配cronjob_auto_seatalk_router
"""
def get_previous_workday(date_str, n):
    if "bus" in date_str or "Bus" in date_str:
        date_pattern = r"\d{6}"  # 匹配6位数字的日期字符串
        date_string = ''
        match = re.search(date_pattern, date_str)
        ic(match)
        if match:
            date_string = "20"+match.group()
            ic(date_string)
        else:
            # 匹配不到日期时，打印日志并返回None
            print(time_prefix() + f"无法从标题中解析日期: {date_str}")
            return None
            
        try:
            date = dt.datetime.strptime(date_string, '%Y%m%d').date()
            ic(date)
        except ValueError as e:
            # 日期格式错误时，打印日志并返回None
            print(time_prefix() + f"日期格式错误: {date_string}, 错误: {e}")
            return None
            
        weekdays = [0, 1, 2, 3, 4]  # 工作日对应的数字，0表示星期一，1表示星期二，以此类推，4表示星期五

        if date.weekday() in weekdays:
            weekdays.remove(date.weekday())

        count = 0
        while count < n:
            date -= dt.timedelta(days=1)
            if date.weekday() in weekdays:
                count += 1

        previous_workday = date.strftime('%Y%m%d')
        ic(previous_workday)
        return previous_workday
    else:
        # 不包含bus的标题，打印日志并返回None
        print(time_prefix() + f"标题不包含bus关键词，跳过处理: {date_str}")
        return None


"""
    定时任务，通过这个定时任务，来判断需求单需要发送哪一类消息，前三天为signed off，前两天为code merged，前一天为checklist
"""
def cronjob_auto_seatalk_router():
    try:
        jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
        data_todo = 'status != CLOSED AND status != DONE'
        query_todo = f"project= spcb AND type = Release AND summary ~ '发布单' AND {data_todo} ORDER BY created DESC"
        # 增加maxResults参数，确保获取所有符合条件的发布单，避免遗漏新的发布单
        issue_todo = jira.search_issues(query_todo, maxResults=500)
        today = dt.date.today()
        
        print(time_prefix() + f"总共找到 {len(issue_todo)} 个发布单需要处理")
        for index, issue in enumerate(issue_todo):
            try:
                title = issue.raw["fields"]["summary"]
                key = issue.key
                print(time_prefix() + f"处理第 {index + 1}/{len(issue_todo)} 个发布单 - title: '{title}', key: '{key}'")
                
                if "bus" in title or "Bus" in title:
                    # 处理checklist
                    checklist_day = get_previous_workday(title, 1)
                    if checklist_day is not None:
                        ic(checklist_day)
                        try:
                            temp_day_checklist = dt.datetime.strptime(checklist_day, "%Y%m%d").date()
                            if temp_day_checklist == today:
                                cronjob_checklist_seatalk(title, key)
                        except ValueError as e:
                            print(time_prefix() + f"checklist日期转换错误: {checklist_day}, 错误: {e}")
                    
                    # 处理sign off
                    sign_off_day = get_previous_workday(title, 3)
                    if sign_off_day is not None:
                        ic(sign_off_day)
                        try:
                            temp_day_sign_off = dt.datetime.strptime(sign_off_day, "%Y%m%d").date()
                            if temp_day_sign_off == today:
                                cronjob_signed_off_seatalk(title, key)
                        except ValueError as e:
                            print(time_prefix() + f"sign_off日期转换错误: {sign_off_day}, 错误: {e}")
                    
                    # 处理MR
                    mr_day = get_previous_workday(title, 2)
                    if mr_day is not None:
                        ic(mr_day)
                        try:
                            temp_day_mr = dt.datetime.strptime(mr_day, "%Y%m%d").date()
                            if temp_day_mr == today:
                                print(time_prefix() + f"开始处理MR消息: {key}")
                                cronjob_mr_seatalk_msg(key)
                                print(time_prefix() + f"完成处理MR消息: {key}")
                        except ValueError as e:
                            print(time_prefix() + f"MR日期转换错误: {mr_day}, 错误: {e}")
                        except Exception as e:
                            print(time_prefix() + f"处理MR消息时出错: {key}, 错误: {e}")
                else:
                    print(time_prefix() + f"跳过非bus标题: {title}")
                
                print(time_prefix() + f"完成处理第 {index + 1}/{len(issue_todo)} 个发布单: {key}")
                    
            except Exception as e:
                print(time_prefix() + f"处理单个issue时出错: {key if 'key' in locals() else 'unknown'}, 标题: {title if 'title' in locals() else 'unknown'}, 错误: {e}")
                continue  # 继续处理下一个issue
                
    except Exception as e:
        print(time_prefix() + f"cronjob_auto_seatalk_router执行时出错: {e}")
        # 不抛出异常，让定时任务继续运行


#查询指定 jql 中的数据，并发送到指定的seatalk群组,发送的时候附带一段文字描述，并打印出 ticket 的创建时间，assignee 和 status.
def send_jira_issues_to_seatalk(jql, seatalk_group_id, description, cc_email=None):
    """
    通用函数：查询JIRA数据并发送到指定的Seatalk群组
    
    Args:
        jql: JIRA查询语句
        seatalk_group_id: Seatalk群组ID 
        description: 消息描述文字
        cc_email: 抄送人邮箱 (可选)
    """
    try:
        # 连接JIRA
        #jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
        jira = get_jira_connection()
        # 查询issues
        issues = jira.search_issues(jql)
        
        if not issues:
            ic("没有查询到符合 JQL 条件的issue")
            return
            
        # 按优先级分组
        priority_groups = {}
        priority_count = {}
        
        # 新增：收集Component/s为Shop Chatbot的单子
        shop_chatbot_issues = []
        non_shop_chatbot_issues = []
        
        for issue in issues:
            # 检查Component/s字段和issue type
            components = getattr(issue.fields, 'components', [])
            issue_type = str(issue.fields.issuetype.name).lower()
            is_shop_chatbot = False
            
            if components:
                for comp in components:
                    if hasattr(comp, 'name') and comp.name == "Shop Chatbot" and issue_type == "bug":
                        shop_chatbot_issues.append(issue)
                        is_shop_chatbot = True
                        break
            
            if not is_shop_chatbot:
                non_shop_chatbot_issues.append(issue)
                
            priority = str(issue.fields.priority)
            if priority not in priority_groups:
                priority_groups[priority] = []
                priority_count[priority] = 0
            priority_groups[priority].append(issue)
            priority_count[priority] += 1
        
        # 构建原始群组消息（不包含Shop Chatbot问题）
        original_message = f"{description}\n\n"
        
        # 添加优先级统计信息
        original_message += "【问题统计】\n"
        total_issues = len(issues)
        total_non_shop_issues = len(non_shop_chatbot_issues)
        original_message += f"总问题数: {total_non_shop_issues}\n"
        
        # 重新计算各优先级非Shop问题的数量
        non_shop_priority_groups = {}
        non_shop_priority_count = {}
        
        for issue in non_shop_chatbot_issues:
            priority = str(issue.fields.priority)
            if priority not in non_shop_priority_groups:
                non_shop_priority_groups[priority] = []
                non_shop_priority_count[priority] = 0
            non_shop_priority_groups[priority].append(issue)
            non_shop_priority_count[priority] += 1
        
        for priority, count in non_shop_priority_count.items():
            percentage = (count / total_non_shop_issues) * 100 if total_non_shop_issues > 0 else 0
            original_message += f"{priority}: {count} ({percentage:.1f}%)\n"
        original_message += "\n"
        
        # 按优先级顺序排序（Highest -> Lowest）
        priority_order = ["P0", "Highest", "High", "P1", "Medium", "P2", "Low", "P3", "Lowest", "P4"]
        
        # 按优先级分组显示非Shop问题
        for priority in priority_order:
            if priority in non_shop_priority_groups:
                original_message += f"【{priority}优先级】\n"
                for issue in non_shop_priority_groups[priority]:
                    # 获取创建时间并格式化
                    created_date = dt.datetime.strptime(issue.fields.created[:10], '%Y-%m-%d').strftime('%Y-%m-%d')
                    
                    # 获取经办人邮箱(如果有)
                    assignee_email = f"{issue.fields.assignee.emailAddress}" if issue.fields.assignee else "未分配"
                    
                    # 构建@标签
                    mention_tag = f"<mention-tag target=\"seatalk://user?email={assignee_email}\"/>" if "@shopee.com" in assignee_email else ""
                    
                    # 获取component信息
                    components = getattr(issue.fields, 'components', [])
                    component_names = [comp.name for comp in components if hasattr(comp, 'name')]
                    component_str = "，".join(component_names) if component_names else "无"
                    
                    # 添加issue信息
                    original_message += (
                        f"Product Line:【{issue.fields.customfield_11549}】\n"
                        f"标题: {issue.fields.summary}\n"
                        f"链接: https://jira.shopee.io/browse/{issue.key}\n"
                        f"创建时间: {created_date}\n"
                        f"状态: {issue.fields.status.name}\n"
                        f"component: {component_str}\n"
                        f"经办人: {mention_tag}\n"
                        f"--------------------------------------------\n"
                    )
                original_message += "\n"
        
        # 添加CC信息
        if cc_email:
            cc_mention_tag = f"<mention-tag target=\"seatalk://user?email={cc_email}\"/>" if "@shopee.com" in cc_email else ""
            original_message += f"CC: {cc_mention_tag}\n\n"
        
        # 添加JIRA查询链接，使用 urllib.parse.quote 进行URL编码
        encoded_jql = quote(jql, safe='')
        original_message += f"在JIRA中查看全部问题：https://jira.shopee.io/issues/?jql={encoded_jql}"
        ic(original_message)
                
        # 新增：如果有Shop Chatbot相关单子，推送到Shop外网问题处理小群
        if shop_chatbot_issues:
            shop_chatbot_group_id = "MzgzNTI0MjE0MjYz"  # Shop外网问题处理小群的群ID:MzgzNTI0MjE0MjYz
            shop_message = f"【Shop Chatbot相关问题推送】\n\n共{len(shop_chatbot_issues)}个问题：\n"
            for issue in shop_chatbot_issues:
                created_date = dt.datetime.strptime(issue.fields.created[:10], '%Y-%m-%d').strftime('%Y-%m-%d')
                assignee_email = f"{issue.fields.assignee.emailAddress}" if issue.fields.assignee else "未分配"
                mention_tag = f"<mention-tag target=\"seatalk://user?email={assignee_email}\"/>" if "@shopee.com" in assignee_email else ""
                
                # 获取component信息
                components = getattr(issue.fields, 'components', [])
                component_names = [comp.name for comp in components if hasattr(comp, 'name')]
                component_str = "，".join(component_names) if component_names else "无"
                
                shop_message += (
                    f"标题: {issue.fields.summary}\n"
                    f"链接: https://jira.shopee.io/browse/{issue.key}\n"
                    f"创建时间: {created_date}\n"
                    f"状态: {issue.fields.status.name}\n"
                    f"component: {component_str}\n"
                    f"经办人: {mention_tag}\n"
                    f"--------------------------------------------\n"
                )
            shop_message += f"\n在JIRA中查看全部Shop Chatbot相关问题：https://jira.shopee.io/issues/?jql={encoded_jql}"
            ic("推送Shop Chatbot相关问题到Shop外网问题处理小群")
            test_for_seatalk_bot(shop_message, False, shop_chatbot_group_id)
 
        #如果jql里含有"project in ("Shopee Production Support JSD") "，那么把级别为"Highest", "High"的单子单独推送到Chat&Chatbot 管理群
        if 'project in ("Shopee Production Support JSD")' in jql:
            # Chat&Chatbot 管理群的群组ID
            chat_chatbot_mgmt_group_id = "NzkzMzg0NTgzNjU2"
            
            # 检查是否有Highest或High优先级的单子
            high_priority_issues = []
            if "Highest" in priority_groups or "High" in priority_groups:
                high_priority_message = f"{description}\n\n【仅显示高优先级问题】\n\n"
                
                # 统计高优先级问题数
                high_count = 0
                if "Highest" in priority_groups:
                    high_count += len(priority_groups["Highest"])
                if "High" in priority_groups:
                    high_count += len(priority_groups["High"])
                
                high_priority_message += f"高优先级问题总数: {high_count}\n\n"
                
                # 添加Highest优先级问题
                if "Highest" in priority_groups:
                    high_priority_message += f"【Highest优先级】\n"
                    for issue in priority_groups["Highest"]:
                        created_date = dt.datetime.strptime(issue.fields.created[:10], '%Y-%m-%d').strftime('%Y-%m-%d')
                        assignee_email = f"{issue.fields.assignee.emailAddress}" if issue.fields.assignee else "未分配"
                        mention_tag = f"<mention-tag target=\"seatalk://user?email={assignee_email}\"/>" if "@shopee.com" in assignee_email else ""
                        
                        # 获取component信息
                        components = getattr(issue.fields, 'components', [])
                        component_names = [comp.name for comp in components if hasattr(comp, 'name')]
                        component_str = "，".join(component_names) if component_names else "无"
                        
                        high_priority_message += (
                            f"Product Line:【{issue.fields.customfield_11549}】\n"
                            f"标题: {issue.fields.summary}\n"
                            f"链接: https://jira.shopee.io/browse/{issue.key}\n"
                            f"创建时间: {created_date}\n"
                            f"状态: {issue.fields.status.name}\n"
                            f"component: {component_str}\n"
                            f"经办人: {mention_tag}\n"
                            f"--------------------------------------------\n"
                        )
                    high_priority_message += "\n"
                
                # 添加High优先级问题
                if "High" in priority_groups:
                    high_priority_message += f"【High优先级】\n"
                    for issue in priority_groups["High"]:
                        created_date = dt.datetime.strptime(issue.fields.created[:10], '%Y-%m-%d').strftime('%Y-%m-%d')
                        assignee_email = f"{issue.fields.assignee.emailAddress}" if issue.fields.assignee else "未分配"
                        mention_tag = f"<mention-tag target=\"seatalk://user?email={assignee_email}\"/>" if "@shopee.com" in assignee_email else ""
                        
                        # 获取component信息
                        components = getattr(issue.fields, 'components', [])
                        component_names = [comp.name for comp in components if hasattr(comp, 'name')]
                        component_str = "，".join(component_names) if component_names else "无"
                        
                        high_priority_message += (
                            f"Product Line:【{issue.fields.customfield_11549}】\n"
                            f"标题: {issue.fields.summary}\n"
                            f"链接: https://jira.shopee.io/browse/{issue.key}\n"
                            f"创建时间: {created_date}\n"
                            f"状态: {issue.fields.status.name}\n"
                            f"component: {component_str}\n"
                            f"经办人: {mention_tag}\n"
                            f"--------------------------------------------\n"
                        )
                    high_priority_message += "\n"
                
                # 为高优先级问题且description包含SPS添加多个固定的CC人员
                if "SPS" in description and "chat" in description and "chatbot" not in description:
                    # 定义固定的CC人员列表
                    sps_high_priority_cc_emails = [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>"
                    ]
                if "SPS" in description and "chatbot" in description:
                    sps_high_priority_cc_emails = [
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>"
                    ]
                    # 添加多个CC信息
                    high_priority_message += "CC: "
                    for email in sps_high_priority_cc_emails:
                        if "@shopee.com" in email:
                            cc_mention_tag = f"<mention-tag target=\"seatalk://user?email={email}\"/>"
                            high_priority_message += f"{cc_mention_tag} "
                    high_priority_message += "\n\n"
                # 如果不包含SPS但有cc_email，使用原来的cc_email
                elif cc_email:
                    cc_mention_tag = f"<mention-tag target=\"seatalk://user?email={cc_email}\"/>" if "@shopee.com" in cc_email else ""
                    high_priority_message += f"CC: {cc_mention_tag}\n\n"
                
                # 添加JIRA查询链接
                high_priority_message += f"在JIRA中查看全部问题：https://jira.shopee.io/issues/?jql={encoded_jql}"
                
                # 将高优先级问题推送到Chat&Chatbot管理群
                ic("推送高优先级问题到Chat&Chatbot管理群")
                test_for_seatalk_bot(high_priority_message, False, chat_chatbot_mgmt_group_id)
        
        # 只有当有非Shop Chatbot问题时，才发送到原Seatalk群组
        if non_shop_chatbot_issues:
            test_for_seatalk_bot(original_message, False, seatalk_group_id)
        return True
        
    except Exception as e:
        # 构建包含函数名和参数的详细错误信息
        function_name = "send_jira_issues_to_seatalk"
        params_info = f"jql='{jql}', seatalk_group_id='{seatalk_group_id}', description='{description}', cc_email='{cc_email}'"
        detailed_error = f"函数 {function_name} 执行失败，参数: {params_info}，错误: {str(e)}"
        
        print(detailed_error)
        #如果发送失败，把错误日志推送到机器人调试群
        # 简化错误信息
        from .seatalk_group_manager import simplify_jira_error
        simplified_error = simplify_jira_error(str(e))
        test_for_seatalk_bot(f"发送JIRA通知失败: 函数={function_name}, 参数={params_info}, 错误={simplified_error}", False, "NzQzMzAxODcyMjAy")
        return False

def cronjob_SPS_live_bug_of_chatbot_reminder():
    """
    查询【SPS】项目中，Chatbot 产品线doing 状态的Live Bug单，并发送通知到"chatbot 部署平台消息通知"群(在 crontab 里设置每天 10 点提醒一次)
    """
    jql = '''project in ("Shopee Production Support JSD") AND issuetype = Bug AND "Server Environment" = Live AND "Product Line" = "CS (Chatbot - Marketplace)" and status in (doing,"Pending (DEP)")'''
    #seatalk_group_id = "OTUyNDEwMzE2Mjk0" # Chatbot部署平台消息通知群
    seatalk_group_id = "MjAyMjkwNzAzNTY2" # CS Bot Live issue DOD 群
    description = "以下是【SPS】项目中，Chatbot 产品线doing 状态的Live Bug单，请尽快处理："
    cc_email = "<EMAIL>"

    send_jira_issues_to_seatalk(jql, seatalk_group_id, description, cc_email)

def cronjob_new_SPS_live_bug_of_chatbot_mirror():
    """
    监控【SPS】项目中,创建时间在 5分钟之内的新的SPS Live Bug单，并提醒到 "chatbot 部署平台消息通知"群(在 crontab 里设置每天 5 分钟监控一次)
    """
    jql = '''project in ("Shopee Production Support JSD") AND issuetype = Bug AND "Server Environment" = Live AND "Product Line" = "CS (Chatbot - Marketplace)" AND status in (Reviewing,Confirmed,ACKNOWLEDGED,Doing,"PENDING USER") AND created >= -5m'''
    #seatalk_group_id = "OTUyNDEwMzE2Mjk0" # Chatbot部署平台消息通知群
    seatalk_group_id = "MjAyMjkwNzAzNTY2" # CS Bot Live issue DOD 群
    description = "警报！警报！警报！【SPS】项目在 5 分钟内新增了 Chatbot 相关Live Bug单，请关注处理："
    cc_email = "<EMAIL>"
    send_jira_issues_to_seatalk(jql, seatalk_group_id, description, cc_email)

def cronjob_SPCB_live_bug_reminder():
    """
    查询【SPCB】项目中尚未解决的Live Bug单，并发送通知到"chatbot 部署平台消息通知"群(在 crontab 里设置每天 10 点提醒一次)
    """
    jql = '''project = "Shopee ChatBot" AND type = bug AND "Server Environment" = "Production (Live)"  AND status not in (Done,Closed,Icebox)'''
    #seatalk_group_id = "OTUyNDEwMzE2Mjk0" # SPCB部署平台消息通知群
    seatalk_group_id = "MjAyMjkwNzAzNTY2" # CS Bot Live issue DOD 群
    description = "以下是Chatbot 项目中尚未解决的Live Bug单，请尽快处理："
    cc_email = "<EMAIL>"

    # 使用自定义函数处理SPCB项目的Live Bug提醒，避免Shop Chatbot组件过滤逻辑
    send_spcb_live_bug_to_seatalk(jql, seatalk_group_id, description, cc_email)


def cronjob_SPS_live_bug_of_chat_reminder():
    """
    查询【SPS】项目中，Chat 产品线doing 状态的Live issue单，并发送通知到"Chat SPS Notify"群(在 crontab 里设置每天 10 点提醒一次)
    """
    jql = '''project in ("Shopee Production Support JSD") AND issuetype = Bug AND "Server Environment" = Live AND "Product Line" in ("Seller Services (Webchat)", "App Chat") AND status in (doing,"Pending (DEP)")'''
    seatalk_group_id = "NTg5MjEzMDIzMjQ1" # Chat SPS Notify
    description = "以下是【SPS】项目中，Chat 产品线 doing 和 Pending (DEP) 状态的Live issue单，请尽快处理："
    #cc_email = "<EMAIL>"
    send_jira_issues_to_seatalk(jql, seatalk_group_id, description)

def cronjob_new_SPS_live_bug_of_chat_mirror():
    """
    监控【SPS】项目中,创建时间在 5分钟之内的新的SPS Live issue单，并提醒到 "Chat SPS Notify"群(在 crontab 里设置每天 5 分钟监控一次)
    """
    jql = '''project in ("Shopee Production Support JSD") AND issuetype = Bug AND "Server Environment" = Live AND "Product Line" in ("Seller Services (Webchat)", "App Chat") AND status in (Reviewing,Confirmed,ACKNOWLEDGED,Doing,"PENDING USER") AND created >= -5m'''
    seatalk_group_id = "NTg5MjEzMDIzMjQ1" # Chat SPS Notify
    description = "警报！警报！警报！【SPS】项目在 5 分钟内新增了 Chat 相关Live issue单，请关注处理："
    #cc_email = "<EMAIL>"
    send_jira_issues_to_seatalk(jql, seatalk_group_id, description)

def send_spcb_live_bug_to_seatalk(jql, seatalk_group_id, description, cc_email=None):
    """
    专门处理SPCB项目的Live Bug提醒，避免Shop Chatbot组件过滤逻辑

    Args:
        jql: JIRA查询语句
        seatalk_group_id: Seatalk群组ID
        description: 消息描述文字
        cc_email: 抄送人邮箱 (可选)
    """
    # 使用print代替logger
    import logging
    logger = logging.getLogger(__name__)
    try:
        # 连接JIRA
        jira = get_jira_connection()
        # 查询issues
        issues = jira.search_issues(jql)

        if not issues:
            ic("没有查询到符合 JQL 条件的issue")
            return

        # 按优先级分组
        priority_groups = {}
        priority_count = {}

        for issue in issues:
            priority = str(issue.fields.priority)
            if priority not in priority_groups:
                priority_groups[priority] = []
                priority_count[priority] = 0
            priority_groups[priority].append(issue)
            priority_count[priority] += 1

        # 构建消息
        message = f"{description}\n\n"

        # 添加优先级统计信息
        message += "【问题统计】\n"
        total_issues = len(issues)
        message += f"总问题数: {total_issues}\n"

        for priority, count in priority_count.items():
            percentage = (count / total_issues) * 100 if total_issues > 0 else 0
            message += f"{priority}: {count} ({percentage:.1f}%)\n"
        message += "\n"

        # 按优先级顺序排序（Highest -> Lowest）
        priority_order = ["P0", "Highest", "High", "P1", "Medium", "P2", "Low", "P3", "Lowest", "P4"]

        # 按优先级分组显示问题
        for priority in priority_order:
            if priority in priority_groups:
                message += f"【{priority}优先级】\n"
                for issue in priority_groups[priority]:
                    # 获取创建时间并格式化
                    created_date = dt.datetime.strptime(issue.fields.created[:10], '%Y-%m-%d').strftime('%Y-%m-%d')

                    # 获取经办人邮箱(如果有)
                    assignee_email = f"{issue.fields.assignee.emailAddress}" if issue.fields.assignee else "未分配"

                    # 构建@标签
                    mention_tag = f"<mention-tag target=\"seatalk://user?email={assignee_email}\"/>" if "@shopee.com" in assignee_email else ""

                    # 获取component信息
                    components = getattr(issue.fields, 'components', [])
                    component_names = [comp.name for comp in components if hasattr(comp, 'name')]
                    component_str = "，".join(component_names) if component_names else "无"

                    # 添加issue信息
                    message += (
                        f"标题: {issue.fields.summary}\n"
                        f"链接: https://jira.shopee.io/browse/{issue.key}\n"
                        f"创建时间: {created_date}\n"
                        f"状态: {issue.fields.status.name}\n"
                        f"component: {component_str}\n"
                        f"经办人: {mention_tag}\n"
                        f"--------------------------------------------\n"
                    )
                message += "\n"

        # 添加CC信息
        if cc_email:
            cc_mention_tag = f"<mention-tag target=\"seatalk://user?email={cc_email}\"/>" if "@shopee.com" in cc_email else ""
            message += f"CC: {cc_mention_tag}\n\n"

        # 添加JIRA查询链接，使用 urllib.parse.quote 进行URL编码
        encoded_jql = quote(jql, safe='')
        message += f"在JIRA中查看全部问题：https://jira.shopee.io/issues/?jql={encoded_jql}"

        # 发送消息到指定群组
        test_for_seatalk_bot(message, False, seatalk_group_id)
        return True

    except Exception as e:
        # 构建包含函数名和参数的详细错误信息
        function_name = "send_spcb_live_bug_to_seatalk"
        params_info = f"jql='{jql}', seatalk_group_id='{seatalk_group_id}', description='{description}', cc_email='{cc_email}'"
        detailed_error = f"函数 {function_name} 执行失败，参数: {params_info}，错误: {str(e)}"

        print(detailed_error)
        # 如果发送失败，把错误日志推送到机器人调试群
        from .seatalk_group_manager import simplify_jira_error
        simplified_error = simplify_jira_error(str(e))
        test_for_seatalk_bot(f"发送JIRA通知失败: 函数={function_name}, 参数={params_info}, 错误={simplified_error}", False, "NzQzMzAxODcyMjAy")
        return False

def get_unreleased_adhoc_versions():   
    """
    获取未发布的 adhoc 版本信息
    """
    jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    # 获取未发布的版本信息
    versions = jira.project_versions('SPCB')
    unreleased_versions = [v.raw for v in versions if not v.released]
    #ic(unreleased_versions)
    adhoc_version_ids= {}
    
    for version in unreleased_versions:
        # 使用(?i)使整个模式不区分大小写，这样可以匹配 adhoc- 和 Adhoc-
        if re.match(r'(?i)adhoc-', version['name']):
            adhoc_version_ids[version['id']] = version['name']
    return adhoc_version_ids


def cronjob_chatbot_adhoc_reminder():
    """
    查询 chatbot 项目中未发布的 adhoc 单，并在发布前提醒到群
    """
    adhoc_version_ids = get_unreleased_adhoc_versions()
    ic(adhoc_version_ids)
    
    for version_id, version_name in adhoc_version_ids.items():
        try:
            # 使用正则表达式提取日期部分
            date_match = re.search(r'(?i)Adhoc-(\d{6})', version_name)
            if date_match:
                date_str = date_match.group(1)
                # 添加20作为年份前缀（假设都是2024年后的日期）
                full_date_str = f"20{date_str}"
                version_date = datetime.strptime(full_date_str, "%Y%m%d").date()
                #ic(version_name)
                #ic(version_date)
                
                # 检查是否是未来1-2天内的日期
                today = datetime.now().date()
                if version_date == today  or version_date == today + timedelta(days=1) or version_date == today + timedelta(days=2):
                    ic("符合条件", version_name)
                    jql = f'project = "Shopee ChatBot" AND fixVersion = {version_id}'
                    seatalk_group_id = "NDY0MzgxNDI4OTEw" # Chatbot live 发布群
                    description = f"以下adhoc需求单（版本：{version_name}），预计在 2 日内发布，请及时发起adhoc申请，做好验证、代码合并、配置修改等发布准备工作："
                    cc_email = "<EMAIL>"
                    send_jira_issues_to_seatalk(jql, seatalk_group_id, description, cc_email)
        except Exception as e:
            print(f"Error processing version {version_name}: {str(e)}")
            continue
"""
    触发timeline变更检查的路由
"""
def check_timeline_changes_route(request):
    """
    触发timeline变更检查的路由
    建议通过crontab每5分钟调用一次
    """
    try:
        from .seatalk_group_manager import check_timeline_changes
        check_timeline_changes()
        return JsonResponse({"success": True, "message": "Timeline check completed"})
    except Exception as e:
        ic(f"Error in check_timeline_changes_route: {str(e)}")
        return JsonResponse({"success": False, "message": str(e)}, status=500)
def epic_reminder_manual_trigger(request):
    """
    手动触发Epic关键节点提醒的API接口
    用于测试和手动执行
    """
    try:
        from .epic_milestone_reminder import check_epic_milestone_reminders
        check_epic_milestone_reminders()
        return JsonResponse({"success": True, "message": "Epic关键节点提醒检查已完成"})
    except Exception as e:
        return JsonResponse({"success": False, "message": str(e)}, status=500)


def thread_reply_api(request):
    """
    线程回复API接口
    
    请求参数:
    - group_id: 群组ID
    - thread_id: 线程ID
    - message: 消息内容
    
    返回:
    - success: 是否成功
    - message: 结果消息
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            group_id = data.get('group_id')
            thread_id = data.get('thread_id')
            message = data.get('message')
            
            # 参数验证
            if not group_id:
                return JsonResponse({'success': False, 'message': '缺少必要参数: group_id'})
            if not thread_id:
                return JsonResponse({'success': False, 'message': '缺少必要参数: thread_id'})
            if not message:
                return JsonResponse({'success': False, 'message': '缺少必要参数: message'})
            
            # 导入线程回复相关函数
            from app01.seatalk_group_manager import send_message_to_group, get_thread_by_id
            
            # 先获取线程信息，确认线程存在
            thread_info = get_thread_by_id(group_id, thread_id)
            if not thread_info.get('success'):
                return JsonResponse({'success': False, 'message': f'获取线程信息失败: {thread_info.get("message")}'})
            
            # 发送线程回复
            result = send_message_to_group(group_id, message, thread_id)
            if result:
                return JsonResponse({
                    'success': True,
                    'message': '线程回复发送成功',
                    'group_id': group_id,
                    'thread_id': thread_id
                })
            else:
                return JsonResponse({'success': False, 'message': '线程回复发送失败'})
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': '无效的JSON格式'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'处理异常: {str(e)}'})
    else:
        return JsonResponse({'success': False, 'message': '仅支持POST请求'})


def cronjob_spcpm_timeline_reminder(request=None):
    """SPCPM项目里程碑自动提醒定时任务，每天10am SGT执行"""
    from datetime import datetime, timedelta
    from django.http import JsonResponse
    from .models import SPCPMTimelineReminder, SeatalkGroup
    from .spcpm_timeline_reminder import get_spcpm_timeline_and_people, format_spcpm_timeline_message
    
    # SPCPM新功能调试群ID
    DEFAULT_SPCPM_GROUP_ID = "NDY1NTQwMjkxNTI5"
    
    try:
        today = datetime.now().date()
        ic(f"执行SPCPM里程碑提醒检查，当前日期: {today}")
        weekday = today.weekday()
        if weekday >= 5:
            return JsonResponse({"success": True, "message": "今天是周末，跳过提醒"})  # 只在工作日执行
        
        reminders = SPCPMTimelineReminder.objects.all()
        ic(f"找到需要检查的SPCPM项目数量: {len(reminders)}")
        if not reminders:
            return JsonResponse({"success": True, "message": "没有需要提醒的SPCPM项目"})
            
        reminder_count = 0
        for reminder in reminders:
            request_id = reminder.request_id
            ic(f"开始检查项目: {request_id}")
            
            # 优先使用保存的群组ID
            target_group_id = reminder.group_id
            
            # 如果没有保存群组ID，则从SeatalkGroup表中查找包含该request_id的群组
            if not target_group_id:
                groups = SeatalkGroup.objects.filter(group_name__contains=request_id)
                # 如果没有找到对应的群组，使用默认的SPCPM调试群
                if not groups.exists():
                    target_group_id = DEFAULT_SPCPM_GROUP_ID
                    ic(f"未找到包含{request_id}的群组，将使用默认SPCPM调试群: {target_group_id}")
                else:
                    ic(f"找到包含{request_id}的群组数量: {groups.count()}")
            else:
                ic(f"使用保存的群组ID: {target_group_id}")
            
            timeline, people, ticket_keys = get_spcpm_timeline_and_people(request_id)
            ic(f"项目 {request_id} 的时间线包含 {len(timeline)} 个日期节点")
            
            # 需要提醒的节点和对应的标签
            nodes = [
                # 元组格式: (优先使用的实际日期字段, 备用的计划日期字段, 提醒显示标签)
                ("Dev Due Date", "Planned Dev Due Date", "Dev due date"),
                ("Integration End Date", "Planned Integration End Date", "Integration end date"),
                ("QA Due Date", "Planned QA Due Date", "QA end date"),
                ("UAT Start Date", "Planned UAT Start Date", "UAT start date"),
                ("UAT Due Date", "Planned UAT Due Date", "UAT end date")
            ]
            
            # 计算下一个工作日
            next_workday = today
            while True:
                next_workday += timedelta(days=1)
                if next_workday.weekday() < 5:
                    break
            ic(f"下一个工作日是: {next_workday}")
            
            for actual_field, planned_field, label in nodes:
                # 优先使用实际日期，如果没有则使用计划日期
                date_str = timeline.get(actual_field) or timeline.get(planned_field)
                if not date_str:
                    ic(f"项目 {request_id} 没有 {actual_field} 或 {planned_field} 日期")
                    continue
                try:
                    node_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    ic(f"项目 {request_id} 的 {actual_field if actual_field in timeline else planned_field} 日期是: {node_date}")
                except Exception as e:
                    ic(f"项目 {request_id} 的日期格式错误: {date_str}, 错误: {str(e)}")
                    continue
                
                # 判断是否为下一个工作日
                used_field = actual_field if actual_field in timeline else planned_field
                if node_date == next_workday:
                    ic(f"项目 {request_id} 的 {used_field} 日期 {node_date} 是下一个工作日，需要发送提醒")
                    # 生成提醒消息
                    msg = f"Timeline Reminder\n\nWe are approaching the {label}, please highlight if there is any delay.\n\n" + format_spcpm_timeline_message(request_id, timeline)
                    
                    # 根据不同的里程碑类型，只@相关角色的人员
                    all_emails = set()
                    
                    # 开发相关的里程碑（Dev、Integration）
                    if "Dev Due Date" in (actual_field, planned_field) or "Integration End Date" in (actual_field, planned_field):
                        # 只@开发人员
                        dev_roles = ["Developer", "FE List", "BE List"]
                        for role in dev_roles:
                            if role in people and people[role]:
                                if isinstance(people[role], list):
                                    all_emails.update(people[role])
                                else:
                                    all_emails.add(people[role])
                        msg += '\n开发相关人员：'
                        ic(f"项目 {request_id} 的 {used_field} 提醒将@开发人员，共 {len(all_emails)} 人")
                    
                    # QA相关的里程碑
                    elif "QA Due Date" in (actual_field, planned_field):
                        # 只@测试人员
                        qa_roles = ["QA", "QA List"]
                        for role in qa_roles:
                            if role in people and people[role]:
                                if isinstance(people[role], list):
                                    all_emails.update(people[role])
                                else:
                                    all_emails.add(people[role])
                        msg += '\nQA相关人员：'
                        ic(f"项目 {request_id} 的 {used_field} 提醒将@QA人员，共 {len(all_emails)} 人")
                    
                    # UAT相关的里程碑
                    elif "UAT" in actual_field or "UAT" in planned_field:
                        # @测试人员和产品经理
                        uat_roles = ["QA", "QA List", "Product Manager"]
                        for role in uat_roles:
                            if role in people and people[role]:
                                if isinstance(people[role], list):
                                    all_emails.update(people[role])
                                else:
                                    all_emails.add(people[role])
                        msg += '\nUAT相关人员：'
                        ic(f"项目 {request_id} 的 {used_field} 提醒将@UAT相关人员，共 {len(all_emails)} 人")
                    
                    # 其他里程碑，不提醒任何人
                    else:
                        # 不添加任何人员到all_emails中
                        ic(f"项目 {request_id} 的 {used_field} 不需要@任何人")
                    
                    # 如果有target_group_id（保存的或默认的），则直接向该群组发送提醒
                    if target_group_id:
                        ic(f"项目 {request_id} 的 {used_field} 提醒将发送到群组: {target_group_id}")
                        if all_emails:
                            msg += ' '.join([f'<mention-tag target="seatalk://user?email={email}"/>' for email in all_emails])
                        try:
                            test_for_seatalk_bot(msg, False, target_group_id)
                            ic(f"项目 {request_id} 的 {used_field} 提醒已发送到群组: {target_group_id}")
                            reminder_count += 1
                        except Exception as e:
                            ic(f"发送提醒到群组 {target_group_id} 失败: {str(e)}")
                    else:
                        # 向所有包含该request_id的群组发送提醒
                        ic(f"项目 {request_id} 的 {used_field} 提醒将发送到 {groups.count()} 个群组")
                        for group in groups:
                            try:
                                if all_emails:
                                    msg += ' '.join([f'<mention-tag target="seatalk://user?email={email}"/>' for email in all_emails])
                                test_for_seatalk_bot(msg, False, group.group_id)
                                ic(f"项目 {request_id} 的 {used_field} 提醒已发送到群组: {group.group_id}")
                                reminder_count += 1
                            except Exception as e:
                                ic(f"发送提醒到群组 {group.group_id} 失败: {str(e)}")
                else:
                    ic(f"项目 {request_id} 的 {used_field} 日期 {node_date} 不是下一个工作日 {next_workday}，不需要提醒")
        
        ic(f"SPCPM里程碑提醒检查完成，共发送 {reminder_count} 条提醒")
        return JsonResponse({"success": True, "message": f"SPCPM里程碑提醒检查完成，共发送{reminder_count}条提醒"})
    except Exception as e:
        ic(f"SPCPM里程碑提醒失败: {str(e)}")
        return JsonResponse({"success": False, "message": f"SPCPM里程碑提醒失败: {str(e)}"}, status=500)

{% load static %}
{#<!DOCTYPE html>#}
{#<html lang="en">#}
{#<head>#}
{#    <meta charset="UTF-8">#}
{#    <title>Title</title>#}
{#    <link rel="stylesheet" href="{% static 'plugins axui-v2.0.0-20220716' %}">#}
{#</head>#}
{#<body>#}
{#<H1>用户列表</H1>#}
{#<img src="{% static 'img/63780462722077.png' %}" alt="">#}
{#</body>#}
{#</html>#}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-touch-fullscreen" content="yes"/>
    <meta name="format-detection" content="email=no" />
    <meta name="wap-font-scale"  content="no" />
    <meta name="viewport" content="user-scalable=no, width=device-width" />
    <meta content="telephone=no" name="format-detection" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>页面标题</title>
    <meta name="keywords" content="页面关键字" />
    <meta name="description" content="页面描述" />
    <link href="{% static 'plugins/axui-v2.0.0-20220716/css/ax.css' %}" rel="stylesheet" type="text/css" >
    <link href="{% static 'plugins/axui-v2.0.0-20220716/css/ax-response.css' %}" rel="stylesheet" type="text/css" >
</head>
<body>
页面内容
<table class="ax-table ax-hover">
    <thead>
    <tr>
        <th>等级</th>
        <th>说明</th>
        <th>举例</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>A、微小</td>
        <td>为其他部门的工作提供服务。</td>
        <td>会计、分析员、一线督导、一线经理、业务员</td>
    </tr>
    <tr>
        <td>B、略有</td>
        <td>对实现企业的发展战略提供支持性服务。</td>
        <td>部门主管、执行经理</td>
    </tr>
    <tr>
        <td>C、中等</td>
        <td>对实现企业的发展战略起到重要作用。</td>
        <td>助理副总、副总、事业部经理</td>
    </tr>
    <tr>
        <td>D、巨大</td>
        <td>制定企业的发展战略，位于企业的决策层。</td>
        <td>中型组织CEO、大型组织的副总</td>

    </tr>
    </tbody>
</table>

<script src="https://src.axui.cn/v2.0/dist/js/ax.min.js" type="text/javascript" charset="utf-8"></script>
<script type='text/javascript'>
    document.addEventListener("DOMContentLoaded", function () {
    //js内容
    });
</script>
</body>
</html>

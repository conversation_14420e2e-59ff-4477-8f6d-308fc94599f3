{#<!DOCTYPE html>#}
{#<html lang="en">#}
    {% extends 'base.html' %}

{#<head>#}
    {% block content %}

    <div class="ax-breadcrumb"><a href="###">ChatbotQA</a><i class="ax-gutter ax-iconfont ax-icon-right"></i><span>自动部署运行情况</span></div>
    {% endblock %}
{#</head>#}
{#<body>#}
{% block main %}
        <div class="ax-padding">
        <h1>部署查看</h1>
        </div>
    <br/>

{#    <ul>#}
{#        <li>#}
{#            <div class="box-a" style="width:200px;height:100px;float:left">#}
{#                第一个#}
{#            </div>#}
{#            <div class="box-b" style="width:200px;height:100px;float:left">#}
{#                第二个#}
{#            </div>#}
{#        </li>#}
{#    </ul>#}
<div class="flexbox-centering">
    <div class="inner">
        <div>仓库名：</div>
    <div class="ax-step ax-ends-scatter">
    <div class="ax-item">
        <span class="ax-head">2019年4月12日</span>
        <div class="ax-mark">
            <div></div>
            <span class="ax-node"></span>
            <div></div>
        </div>
        <div class="ax-text">
            <div class="ax-title">开始部署</div>
            <div class="ax-des">戴尔笔记本电脑1台，￥5000</div>
        </div>
    </div>
    <div class="ax-item">
        <span class="ax-head">1天前</span>
        <div class="ax-mark">
            <div></div>
            <span class="ax-node"></span>
            <div></div>
        </div>
        <div class="ax-text">
            <div class="ax-title">确认订单</div>
            <div class="ax-des">追加用户留言</div>
        </div>
    </div>
    <div class="ax-item ax-active">
        <span class="ax-head">30分钟前</span>
        <div class="ax-mark">
            <div></div>
            <span class="ax-node"></span>
            <div></div>
        </div>
        <div class="ax-text">
            <div class="ax-title">付款</div>
            <div class="ax-des">微信支付￥5000</div>
        </div>
    </div>
    <div class="ax-item">
        <span class="ax-head">进行中</span>
        <div class="ax-mark">
            <div></div>
            <span class="ax-node"></span>
            <div></div>
        </div>
        <div class="ax-text">
            <div class="ax-title">发货</div>
            <div class="ax-des">还未发货，请稍后</div>
        </div>
    </div>
</div>
    </div>
</div>
{% endblock %}
{#</body>#}
{#</html>#}
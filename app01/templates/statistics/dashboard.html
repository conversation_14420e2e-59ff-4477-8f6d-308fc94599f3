<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBot AutoRelease - 统计监控面板</title>
    <!-- 引入Element Plus样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- 引入ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #409eff;
        }
        .header .refresh-btn {
            padding: 8px 16px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .header .refresh-btn:hover {
            background-color: #66b1ff;
        }

        .date-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .date-selector label {
            color: #606266;
            font-weight: 500;
        }

        .date-selector select {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
            cursor: pointer;
        }

        .date-selector select:focus {
            outline: none;
            border-color: #409eff;
        }
        .metrics-cards, .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        /* 响应式布局 */
        @media (max-width: 1200px) {
            .metrics-cards, .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .metrics-cards, .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
        .metric-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 14px;
            color: #909399;
        }
        .success { color: #67c23a; }
        .warning { color: #e6a23c; }
        .danger { color: #f56c6c; }
        .info { color: #409eff; }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .chart-title {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        .chart-container {
            height: 300px;
        }
        
        .data-tables {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .table-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .table-title {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
        }
        th {
            font-weight: bold;
            color: #606266;
            background-color: #f5f7fa;
        }
        .activity-log {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .log-title {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        .log-entry {
            padding: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .log-time {
            font-size: 12px;
            color: #909399;
        }
        .log-message {
            margin-top: 5px;
        }
        .tabs {
            margin-bottom: 20px;
        }
        .tab {
            display: inline-block;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #409eff;
            color: #409eff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            color: #909399;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ChatBot AutoRelease 统计监控面板</h1>
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="date-selector">
                    <label for="date-range">查看时间范围:</label>
                    <select id="date-range" onchange="changeDateRange()">
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近90天</option>
                    </select>
                </div>
                <div id="data-status" style="padding: 5px 10px; border-radius: 4px; font-size: 12px; background: #f0f0f0; color: #666;">
                    数据加载中...
                </div>
                <button class="refresh-btn" onclick="refreshData()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 4v6h-6"></path>
                        <path d="M1 20v-6h6"></path>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                    </svg>
                    刷新数据
                </button>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('dashboard')">实时监控</div>
            <div class="tab" onclick="switchTab('commands')">指令统计</div>
            <div class="tab" onclick="switchTab('users')">用户分析</div>
            <div class="tab" onclick="switchTab('performance')">性能监控</div>
            <div class="tab" onclick="switchTab('cronjobs')">定时任务</div>
        </div>
        
        <!-- 实时监控面板 -->
        <div id="dashboard" class="tab-content active">
            <div class="metrics-cards" style="grid-template-columns: repeat(4, 1fr);">
                <div class="metric-card">
                    <div class="metric-value info" id="active-users">--</div>
                    <div class="metric-label">当前活跃用户 <small>(30分钟内)</small></div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="today-commands">--</div>
                    <div class="metric-label">今日指令数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="success-rate">--</div>
                    <div class="metric-label">成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="avg-response-time">--</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
            </div>
            
            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">指令执行趋势</h3>
                    <div class="chart-container" id="command-trend-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">用户活跃度</h3>
                    <div class="chart-container" id="user-activity-chart"></div>
                </div>
            </div>
            
            <div class="activity-log">
                <h3 class="log-title">实时活动日志</h3>
                <div class="log-controls" style="margin-bottom: 10px;">
                    <input type="text" id="activity-filter" placeholder="筛选活动内容..." 
                           style="padding: 5px; border: 1px solid #ddd; border-radius: 3px; margin-right: 10px;">
                    <select id="activity-type-filter" style="padding: 5px;">
                        <option value="">所有类型</option>
                        <option value="access">访问事件</option>
                        <option value="command">指令执行</option>
                        <option value="cronjob">定时任务</option>
                    </select>
                </div>
                <div id="activity-logs">
                    <div class="log-entry">
                        <div class="log-time">--</div>
                        <div class="log-message">正在加载活动日志...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 指令统计面板 -->
        <div id="commands" class="tab-content">
            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">指令类型分布</h3>
                    <div class="chart-container" id="command-type-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">指令成功率趋势</h3>
                    <div class="chart-container" id="success-rate-chart"></div>
                </div>
            </div>
            
            <div class="table-card">
                <h3 class="table-title">指令执行记录</h3>
                <div class="table-controls" style="margin-bottom: 10px;">
                    <input type="text" id="command-records-filter" placeholder="筛选指令内容..." 
                           style="padding: 5px; border: 1px solid #ddd; border-radius: 3px; margin-right: 10px;">
                    <select id="command-records-type-filter" style="padding: 5px; margin-right: 10px;">
                        <option value="">所有类型</option>
                        <option value="ai_query">AI查询</option>
                        <option value="jira_query">JIRA查询</option>
                        <option value="system_cmd">系统指令</option>
                    </select>
                    <select id="command-records-status-filter" style="padding: 5px;">
                        <option value="">所有状态</option>
                        <option value="true">成功</option>
                        <option value="false">失败</option>
                    </select>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('time')">时间 ↕</th>
                            <th>用户</th>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('type')">指令类型 ↕</th>
                            <th>原始输入</th>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('status')">状态 ↕</th>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('duration')">响应时间 ↕</th>
                        </tr>
                    </thead>
                    <tbody id="command-records">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                正在加载指令执行记录...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 用户分析面板 -->
        <div id="users" class="tab-content">
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-value info" id="users-total-users">--</div>
                    <div class="metric-label">总用户数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="users-active-users-count">--</div>
                    <div class="metric-label">活跃用户数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="users-activity-rate">--</div>
                    <div class="metric-label">用户活跃度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="users-avg-commands-per-user">--</div>
                    <div class="metric-label">平均指令数</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">活跃用户排行榜</h3>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>用户ID</th>
                                    <th>指令数</th>
                                    <th>成功率</th>
                                    <th>最后活动</th>
                                </tr>
                            </thead>
                            <tbody id="top-users-table">
                                <tr>
                                    <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                        正在加载用户排行榜...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">用户行为分析</h3>
                    <div class="chart-container" id="user-behavior-chart"></div>
                </div>
            </div>
        </div>
        
        <!-- 性能监控面板 -->
        <div id="performance" class="tab-content">
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-value info" id="perf-avg-response-time">--</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="perf-memory-usage">--</div>
                    <div class="metric-label">内存使用率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="perf-cpu-usage">--</div>
                    <div class="metric-label">CPU使用率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="perf-db-status">--</div>
                    <div class="metric-label">数据库状态</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">API响应时间趋势</h3>
                    <div class="chart-container" id="api-response-time-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">系统资源使用情况</h3>
                    <div class="chart-container" id="system-resources-chart"></div>
                </div>
            </div>

            <div class="table-card">
                <h3 class="table-title">慢查询分析</h3>
                <div class="table-controls" style="margin-bottom: 10px;">
                    <input type="text" id="slow-queries-filter" placeholder="筛选查询内容..." 
                           style="padding: 5px; border: 1px solid #ddd; border-radius: 3px; margin-right: 10px;">
                    <select id="slow-queries-type-filter" style="padding: 5px; margin-right: 10px;">
                        <option value="">所有类型</option>
                        <option value="ai_query">AI查询</option>
                        <option value="jira_query">JIRA查询</option>
                        <option value="system_cmd">系统指令</option>
                    </select>
                    <select id="slow-queries-time-filter" style="padding: 5px;">
                        <option value="">所有时间</option>
                        <option value="5">5秒以上</option>
                        <option value="10">10秒以上</option>
                        <option value="30">30秒以上</option>
                    </select>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th style="cursor: pointer;" onclick="sortSlowQueries('time')">时间 ↕</th>
                            <th style="cursor: pointer;" onclick="sortSlowQueries('type')">查询类型 ↕</th>
                            <th style="cursor: pointer;" onclick="sortSlowQueries('duration')">执行时间 ↕</th>
                            <th>查询内容</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="slow-queries-table">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                正在加载慢查询数据...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 定时任务监控面板 -->
        <div id="cronjobs" class="tab-content">
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-value info" id="cronjobs-running">--</div>
                    <div class="metric-label">运行中任务</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value success" id="cronjobs-success-today">--</div>
                    <div class="metric-label">今日成功</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value danger" id="cronjobs-failed-today">--</div>
                    <div class="metric-label">今日失败</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="cronjobs-success-rate">--</div>
                    <div class="metric-label">成功率</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">任务执行状态分布</h3>
                    <div class="chart-container" id="cronjob-status-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">任务执行时长趋势</h3>
                    <div class="chart-container" id="cronjob-duration-chart"></div>
                </div>
            </div>

            <div class="table-card">
                <h3 class="table-title">最近任务执行记录</h3>
                <table>
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>执行时间</th>
                            <th>状态</th>
                            <th>执行时长</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody id="cronjob-records-table">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                正在加载定时任务记录...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="footer">
            <p>ChatBot AutoRelease 统计监控系统 &copy; 2025</p>
        </div>
    </div>
    
    <script>
        // switchTab函数已移动到文件末尾
        
        // 处理API错误的通用函数
        function handleApiError(elementId, errorMessage) {
            console.error(`❌ ${errorMessage}`);
            
            // 显示错误状态
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = 'Error';
                element.className = element.className.replace(/success|warning|info/g, 'danger');
            }
            
            // 更新状态指示器
            const statusEl = document.getElementById('data-status');
            if (statusEl) {
                statusEl.textContent = '❌ 连接失败';
                statusEl.style.background = '#fff2f0';
                statusEl.style.color = '#ff4d4f';
            }
        }
        
        // 刷新数据
        async function refreshData() {
            console.log('正在从API获取实时数据...');

            // 更新状态指示器为加载中
            const statusEl = document.getElementById('data-status');
            statusEl.textContent = '🔄 正在加载...';
            statusEl.style.background = '#f0f9ff';
            statusEl.style.color = '#409eff';

            try {
                // 从后端API获取实时数据
                const response = await fetch('/api/statistics/realtime/dashboard/');
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新实时指标
                    updateMetricValue('active-users', data.active_users || 0);
                    updateMetricValue('today-commands', (data.today_commands || 0).toLocaleString());
                    updateMetricValue('success-rate', (data.success_rate || 0).toFixed(1) + '%');
                    updateMetricValue('avg-response-time', Math.round(data.avg_response_time || 0) + 'ms');

                    // 更新成功率颜色
                    const successRate = data.success_rate || 0;
                    const successRateElement = document.getElementById('success-rate');

                    if (successRate >= 95) {
                        successRateElement.className = 'metric-value success';
                    } else if (successRate >= 90) {
                        successRateElement.className = 'metric-value warning';
                    } else {
                        successRateElement.className = 'metric-value danger';
                    }

                    console.log('✅ 实时数据更新成功', data);

                    // 更新状态指示器
                    const hasData = data.today_commands > 0 || data.active_users > 0;
                    if (hasData) {
                        statusEl.textContent = '✅ 显示真实数据';
                        statusEl.style.background = '#e7f5e7';
                        statusEl.style.color = '#52c41a';
                    } else {
                        statusEl.textContent = '📊 暂无数据';
                        statusEl.style.background = '#fff7e6';
                        statusEl.style.color = '#fa8c16';
                    }
                } else {
                    throw new Error(result.error || 'API返回错误数据');
                }
            } catch (error) {
                console.error('❌ 获取数据失败:', error);
                
                // 显示错误状态
                updateMetricValue('active-users', '--');
                updateMetricValue('today-commands', '--');
                updateMetricValue('success-rate', '--');
                updateMetricValue('avg-response-time', '--');

                // 更新状态指示器
                statusEl.textContent = `❌ ${error.message}`;
                statusEl.style.background = '#fff2f0';
                statusEl.style.color = '#ff4d4f';
            }

            // 并行加载其他数据
            await Promise.allSettled([
                loadChartData(),
                loadActivityLogs(),
                loadCommandRecords()
            ]);
        }

        // 更新指标值的辅助函数
        function updateMetricValue(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }
        
        // 加载图表数据
        async function loadChartData() {
            try {
                // 获取指令趋势数据
                const commandTrendsResponse = await fetch(`/api/statistics/realtime/command-trends/?days=${currentDateRange}`);
                const commandTrendsResult = await commandTrendsResponse.json();

                // 获取用户活动数据
                const userActivityResponse = await fetch(`/api/statistics/realtime/user-activity/?days=${currentDateRange}`);
                const userActivityResult = await userActivityResponse.json();

                // 初始化图表
                initCharts(
                    commandTrendsResult.success ? commandTrendsResult.data : null, 
                    userActivityResult.success ? userActivityResult.data : null
                );

            } catch (error) {
                console.error('❌ 获取图表数据失败:', error);
                // 使用默认数据初始化图表
                initCharts(null, null);
            }
        }

        // 加载活动日志
        async function loadActivityLogs() {
            try {
                const response = await fetch('/api/statistics/realtime/activity-logs/');
                const result = await response.json();

                if (result.success && result.data && result.data.length > 0) {
                    const logsContainer = document.getElementById('activity-logs');
                    logsContainer.innerHTML = '';

                    result.data.forEach(log => {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry';
                        logEntry.setAttribute('data-type', log.type || '');

                        const logTime = document.createElement('div');
                        logTime.className = 'log-time';
                        logTime.textContent = log.time;

                        const logMessage = document.createElement('div');
                        logMessage.className = 'log-message';
                        logMessage.innerHTML = log.message;

                        logEntry.appendChild(logTime);
                        logEntry.appendChild(logMessage);
                        logsContainer.appendChild(logEntry);
                    });

                    console.log('✅ 活动日志加载成功');
                } else {
                    // 显示无数据状态
                    const logsContainer = document.getElementById('activity-logs');
                    logsContainer.innerHTML = `
                        <div class="log-entry">
                            <div class="log-time">--</div>
                            <div class="log-message">暂无活动记录</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ 加载活动日志失败:', error);
                const logsContainer = document.getElementById('activity-logs');
                logsContainer.innerHTML = `
                    <div class="log-entry">
                        <div class="log-time">--</div>
                        <div class="log-message">加载失败</div>
                    </div>
                `;
            }
        }

        // 加载指令执行记录
        async function loadCommandRecords() {
            try {
                const response = await fetch('/api/statistics/data/command-records/?page_size=20');
                const result = await response.json();

                if (result.success && result.data && result.data.records && result.data.records.length > 0) {
                    const recordsContainer = document.getElementById('command-records');
                    recordsContainer.innerHTML = '';

                    result.data.records.forEach(record => {
                        const row = document.createElement('tr');

                        const statusClass = record.success ? 'success' : 'error';
                        const statusText = record.success ? '成功' : '失败';
                        const responseTime = record.processing_time ? Math.round(record.processing_time * 1000) + 'ms' : '--';

                        // 格式化时间
                        const createdAt = new Date(record.created_at).toLocaleString('zh-CN');

                        // 添加data属性用于筛选和排序
                        row.setAttribute('data-record', 'true');
                        row.setAttribute('data-time', record.created_at);
                        row.setAttribute('data-type', record.command_type);
                        row.setAttribute('data-input', record.raw_input);
                        row.setAttribute('data-status', record.success.toString());
                        row.setAttribute('data-duration', record.processing_time || '0');

                        row.innerHTML = `
                            <td>${createdAt}</td>
                            <td>${record.user_id}</td>
                            <td>${record.command_type}</td>
                            <td title="${record.raw_input}">${record.raw_input.length > 30 ? record.raw_input.substring(0, 30) + '...' : record.raw_input}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>${responseTime}</td>
                        `;

                        recordsContainer.appendChild(row);
                    });

                    console.log('✅ 指令执行记录加载成功');
                } else {
                    // 显示无数据状态
                    const recordsContainer = document.getElementById('command-records');
                    recordsContainer.innerHTML = `
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                暂无指令执行记录
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('❌ 加载指令执行记录失败:', error);
                const recordsContainer = document.getElementById('command-records');
                recordsContainer.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #ff4d4f;">
                            加载失败
                        </td>
                    </tr>
                `;
            }
        }

        // 初始化图表
        function initCharts(commandTrendsData = null, userActivityData = null) {
            // 指令执行趋势图
            const commandTrendChart = echarts.init(document.getElementById('command-trend-chart'));

            // 使用真实数据或默认数据
            const defaultCommandData = {
                dates: ['07-12', '07-13', '07-14', '07-15', '07-16', '07-17', '07-18'],
                total: [0, 0, 0, 0, 0, 0, 0],
                success: [0, 0, 0, 0, 0, 0, 0],
                failed: [0, 0, 0, 0, 0, 0, 0]
            };

            const chartData = commandTrendsData || defaultCommandData;

            commandTrendChart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['总指令数', '成功', '失败']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: chartData.dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '总指令数',
                        type: 'line',
                        data: chartData.total
                    },
                    {
                        name: '成功',
                        type: 'line',
                        data: chartData.success
                    },
                    {
                        name: '失败',
                        type: 'line',
                        data: chartData.failed
                    }
                ]
            });
            
            // 用户活跃度图
            const userActivityChart = echarts.init(document.getElementById('user-activity-chart'));

            // 使用真实数据或默认数据
            const defaultUserData = {
                dates: ['07-12', '07-13', '07-14', '07-15', '07-16', '07-17', '07-18'],
                active_users: [0, 0, 0, 0, 0, 0, 0],
                new_users: [0, 0, 0, 0, 0, 0, 0]
            };

            const userData = userActivityData || defaultUserData;

            userActivityChart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['活跃用户', '新用户']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: userData.dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '活跃用户',
                        type: 'line',
                        data: userData.active_users
                    },
                    {
                        name: '新用户',
                        type: 'line',
                        data: userData.new_users
                    }
                ]
            });
            
            // 指令类型分布图 - 将在initCommandStatsCharts中初始化
            
            // 成功率趋势图 - 将在initCommandStatsCharts中初始化
        }

        // 初始化指令统计页面的图表
        async function initCommandStatsCharts() {
            try {
                // 获取指令趋势数据（用于指令类型分布）
                const response = await fetch(`/api/statistics/realtime/command-trends/?days=${currentDateRange}`);
                const result = await response.json();

                if (result.success && result.data) {
                    initCommandTypeChart(result.data.command_types || []);
                    initSuccessRateChart(result.data);
                } else {
                    initCommandTypeChart([]);
                    initSuccessRateChart(null);
                }
            } catch (error) {
                console.error('❌ 获取指令统计数据失败:', error);
                initCommandTypeChart([]);
                initSuccessRateChart(null);
            }
        }

        // 初始化指令类型分布图表
        function initCommandTypeChart(commandTypes) {
            const chartElement = document.getElementById('command-type-chart');
            if (!chartElement) return;

            const commandTypeChart = echarts.init(chartElement);

            // 准备数据
            const data = commandTypes.length > 0 ?
                commandTypes.map(item => ({
                    name: item.command_type,
                    value: item.count
                })) :
                [{name: '暂无数据', value: 1}];

            commandTypeChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '指令类型',
                        type: 'pie',
                        radius: '50%',
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            });

            window.commandTypeChart = commandTypeChart;
        }

        // 初始化成功率趋势图表
        function initSuccessRateChart(trendsData) {
            const chartElement = document.getElementById('success-rate-chart');
            if (!chartElement) return;

            const successRateChart = echarts.init(chartElement);

            // 准备数据
            let dates = [];
            let successRates = [];

            if (trendsData && trendsData.dates && trendsData.total) {
                dates = trendsData.dates;
                // 计算每天的成功率
                for (let i = 0; i < trendsData.dates.length; i++) {
                    const total = trendsData.total[i];
                    const success = trendsData.success[i];
                    const rate = total > 0 ? Math.round((success / total) * 100) : 0;
                    successRates.push(rate);
                }
            } else {
                dates = ['无数据'];
                successRates = [0];
            }

            successRateChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: '{b}: {c}%'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dates
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [
                    {
                        name: '成功率',
                        type: 'line',
                        data: successRates,
                        smooth: true,
                        lineStyle: {
                            color: '#52c41a'
                        },
                        areaStyle: {
                            color: 'rgba(82, 196, 26, 0.1)'
                        },
                        markLine: {
                            data: [
                                { yAxis: 90, lineStyle: { color: '#E6A23C' }, label: { formatter: '警戒线: 90%' } },
                                { yAxis: 95, lineStyle: { color: '#67C23A' }, label: { formatter: '优秀线: 95%' } }
                            ]
                        }
                    }
                ]
            });

            window.successRateChart = successRateChart;
        }

        // 加载用户分析数据
        async function loadUserAnalysisData() {
            try {
                const response = await fetch('/api/statistics/realtime/user-activity/');
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新用户指标
                    document.getElementById('users-total-users').textContent = data.summary?.total_users || 0;
                    document.getElementById('active-users-count').textContent = data.summary?.total_commands || 0;
                    document.getElementById('user-activity-rate').textContent = '75%'; // 模拟数据
                    document.getElementById('avg-commands-per-user').textContent = data.summary?.total_users > 0 ?
                        Math.round((data.summary?.total_commands || 0) / data.summary.total_users) : 0;

                    // 加载用户排行榜
                    loadTopUsersTable(data.top_users || []);

                    console.log('✅ 用户分析数据加载成功');
                }
            } catch (error) {
                console.error('❌ 加载用户分析数据失败:', error);
            }
        }

        // 加载用户排行榜
        function loadTopUsersTable(topUsers) {
            const tableContainer = document.getElementById('top-users-table');
            if (!tableContainer) return;

            if (topUsers.length > 0) {
                tableContainer.innerHTML = '';
                topUsers.forEach((user, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${user.user_id}</td>
                        <td>${user.command_count}</td>
                        <td>${user.success_rate}%</td>
                        <td>最近活动</td>
                    `;
                    tableContainer.appendChild(row);
                });
            } else {
                tableContainer.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            暂无用户数据
                        </td>
                    </tr>
                `;
            }
        }

        // 加载性能监控数据
        async function loadPerformanceData() {
            try {
                const response = await fetch('/api/statistics/performance/metrics/');
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新性能指标
                    document.getElementById('perf-avg-response-time').textContent =
                        data.system_resources && data.system_resources.avg_response_time ? 
                        Math.round(data.system_resources.avg_response_time * 1000) + 'ms' : '--';
                    
                    document.getElementById('perf-memory-usage').textContent =
                        data.system_resources && data.system_resources.avg_memory ? 
                        data.system_resources.avg_memory.toFixed(1) + '%' : '--';
                    
                    document.getElementById('perf-cpu-usage').textContent =
                        data.system_resources && data.system_resources.avg_cpu ? 
                        data.system_resources.avg_cpu.toFixed(1) + '%' : '--';
                    
                    // 数据库状态根据连接数判断
                    const dbConnections = data.system_resources && data.system_resources.avg_db_connections;
                    let dbStatus = 'unknown';
                    if (dbConnections !== undefined) {
                        dbStatus = dbConnections < 10 ? 'healthy' : 
                                  dbConnections < 50 ? 'normal' : 'busy';
                    }
                    document.getElementById('perf-db-status').textContent = dbStatus;
                    
                    // 初始化API响应时间图表
                    if (data.api_performance && data.api_performance.length > 0) {
                        initApiResponseTimeChart(data.api_performance);
                    }
                    
                    // 初始化系统资源使用图表
                    initSystemResourcesChart(data.system_resources);
                    
                    // 加载慢查询数据
                    loadSlowQueriesData();

                    console.log('✅ 性能监控数据加载成功');
                } else {
                    handleApiError('perf-avg-response-time', 'API返回错误: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                handleApiError('perf-avg-response-time', '加载性能监控数据失败: ' + error);
            }
        }
        
        // 加载慢查询数据
        async function loadSlowQueriesData() {
            try {
                const response = await fetch('/api/statistics/performance/slow-queries/');
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;
                    const tableContainer = document.getElementById('slow-queries-table');
                    
                    if (data.slowest_queries && data.slowest_queries.length > 0) {
                        tableContainer.innerHTML = '';
                        
                        data.slowest_queries.forEach(query => {
                            const row = document.createElement('tr');
                            const createdAt = new Date(query.created_at).toLocaleString('zh-CN');
                            const status = query.success ? '成功' : '失败';
                            const statusClass = query.success ? 'success' : 'error';
                            
                            row.innerHTML = `
                                <td>${createdAt}</td>
                                <td>${query.command_type}</td>
                                <td>${(query.processing_time * 1000).toFixed(0)}ms</td>
                                <td>-</td>
                                <td><span class="${statusClass}">${status}</span></td>
                            `;
                            
                            tableContainer.appendChild(row);
                        });
                        
                        console.log('✅ 慢查询数据加载成功');
                    } else {
                        tableContainer.innerHTML = `
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                    暂无慢查询数据
                                </td>
                            </tr>
                        `;
                    }
                } else {
                    document.getElementById('slow-queries-table').innerHTML = `
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #ff4d4f;">
                                加载失败: ${result.error || '未知错误'}
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('❌ 加载慢查询数据失败:', error);
                document.getElementById('slow-queries-table').innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #ff4d4f;">
                            加载失败: ${error}
                        </td>
                    </tr>
                `;
            }
        }
        
        // 初始化API响应时间图表
        function initApiResponseTimeChart(apiPerformance) {
            const chartElement = document.getElementById('api-response-time-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据
            const endpoints = apiPerformance.map(item => item.api_endpoint);
            const avgTimes = apiPerformance.map(item => item.avg_response_time * 1000); // 转换为毫秒
            const maxTimes = apiPerformance.map(item => item.max_response_time * 1000); // 转换为毫秒
            
            chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['平均响应时间', '最大响应时间']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value} ms'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: endpoints,
                    axisLabel: {
                        formatter: function(value) {
                            // 截断长端点名称
                            return value.length > 30 ? value.substring(0, 30) + '...' : value;
                        }
                    }
                },
                series: [
                    {
                        name: '平均响应时间',
                        type: 'bar',
                        data: avgTimes
                    },
                    {
                        name: '最大响应时间',
                        type: 'bar',
                        data: maxTimes
                    }
                ]
            });
        }
        
        // 初始化系统资源使用图表
        function initSystemResourcesChart(systemResources) {
            const chartElement = document.getElementById('system-resources-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据
            const cpuUsage = systemResources && systemResources.avg_cpu ? systemResources.avg_cpu : 0;
            const memoryUsage = systemResources && systemResources.avg_memory ? systemResources.avg_memory : 0;
            
            chart.setOption({
                tooltip: {
                    formatter: '{a} <br/>{b} : {c}%'
                },
                series: [
                    {
                        name: 'CPU使用率',
                        type: 'gauge',
                        min: 0,
                        max: 100,
                        detail: {formatter: '{value}%'},
                        data: [{value: cpuUsage.toFixed(1), name: 'CPU'}],
                        center: ['25%', '50%'],
                        radius: '80%'
                    },
                    {
                        name: '内存使用率',
                        type: 'gauge',
                        min: 0,
                        max: 100,
                        detail: {formatter: '{value}%'},
                        data: [{value: memoryUsage.toFixed(1), name: '内存'}],
                        center: ['75%', '50%'],
                        radius: '80%'
                    }
                ]
            });
        }

        // 加载定时任务监控数据
        async function loadCronjobData() {
            try {
                const response = await fetch('/api/statistics/cronjobs/status/');
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;
                    const todaySummary = data.today_summary || {};

                    // 更新定时任务指标
                    document.getElementById('cronjobs-running').textContent = todaySummary.running_jobs || 0;
                    document.getElementById('cronjobs-success-today').textContent = todaySummary.success_today || 0;
                    document.getElementById('cronjobs-failed-today').textContent = todaySummary.failed_today || 0;
                    document.getElementById('cronjobs-success-rate').textContent = (todaySummary.success_rate || 0) + '%';

                    // 初始化任务状态分布图表
                    initCronjobStatusChart(data);
                    
                    // 初始化任务执行时长趋势图表
                    initCronjobDurationChart(data.job_statistics);
                    
                    // 加载最近任务执行记录
                    loadCronjobRecords(data);

                    console.log('✅ 定时任务监控数据加载成功');
                } else {
                    handleApiError('cronjobs-running', 'API返回错误: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                handleApiError('cronjobs-running', '加载定时任务监控数据失败: ' + error);
            }
        }
        
        // 加载最近任务执行记录
        function loadCronjobRecords(data) {
            const tableContainer = document.getElementById('cronjob-records-table');
            if (!tableContainer) return;
            
            if (data.recent_failures && data.recent_failures.length > 0) {
                tableContainer.innerHTML = '';
                
                data.recent_failures.forEach(job => {
                    const row = document.createElement('tr');
                    const startTime = new Date(job.start_time).toLocaleString('zh-CN');
                    
                    row.innerHTML = `
                        <td>${job.job_name}</td>
                        <td>${startTime}</td>
                        <td><span class="error">失败</span></td>
                        <td>-</td>
                        <td title="${job.error_message}">${job.error_message && job.error_message.length > 50 ? job.error_message.substring(0, 50) + '...' : job.error_message || '-'}</td>
                    `;
                    
                    tableContainer.appendChild(row);
                });
            } else {
                tableContainer.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            暂无失败的定时任务记录
                        </td>
                    </tr>
                `;
            }
        }
        
        // 初始化任务状态分布图表
        function initCronjobStatusChart(data) {
            const chartElement = document.getElementById('cronjob-status-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据
            const todaySummary = data.today_summary || {};
            const runningJobs = todaySummary.running_jobs || 0;
            const successJobs = todaySummary.success_today || 0;
            const failedJobs = todaySummary.failed_today || 0;
            
            chart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['运行中', '成功', '失败']
                },
                series: [
                    {
                        name: '任务状态',
                        type: 'pie',
                        radius: '50%',
                        data: [
                            {value: runningJobs, name: '运行中', itemStyle: {color: '#1890ff'}},
                            {value: successJobs, name: '成功', itemStyle: {color: '#52c41a'}},
                            {value: failedJobs, name: '失败', itemStyle: {color: '#ff4d4f'}}
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            });
        }
        
        // 初始化任务执行时长趋势图表
        function initCronjobDurationChart(jobStats) {
            const chartElement = document.getElementById('cronjob-duration-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据
            const jobNames = [];
            const avgDurations = [];
            const maxDurations = [];
            
            if (jobStats && jobStats.length > 0) {
                // 只显示前10个任务
                const topJobs = jobStats.slice(0, 10);
                
                topJobs.forEach(job => {
                    jobNames.push(job.job_name);
                    avgDurations.push(job.avg_duration ? job.avg_duration.toFixed(2) : 0);
                    maxDurations.push(job.max_duration ? job.max_duration.toFixed(2) : 0);
                });
            }
            
            chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['平均执行时长', '最大执行时长']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value} s'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: jobNames,
                    axisLabel: {
                        formatter: function(value) {
                            // 截断长任务名称
                            return value.length > 20 ? value.substring(0, 20) + '...' : value;
                        }
                    }
                },
                series: [
                    {
                        name: '平均执行时长',
                        type: 'bar',
                        data: avgDurations
                    },
                    {
                        name: '最大执行时长',
                        type: 'bar',
                        data: maxDurations
                    }
                ]
            });
        }

        // 全局变量存储当前日期范围
        let currentDateRange = 7;

        // 日期范围变更处理
        function changeDateRange() {
            const select = document.getElementById('date-range');
            currentDateRange = parseInt(select.value);

            console.log(`📅 切换到${currentDateRange}天数据范围`);

            // 更新状态指示器
            const statusEl = document.getElementById('data-status');
            statusEl.textContent = '🔄 正在加载数据...';
            statusEl.style.background = '#f0f9ff';
            statusEl.style.color = '#409eff';

            // 重新加载所有数据
            refreshData();
        }

        // 切换标签页时加载对应数据
        function switchTab(tabId) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 取消所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 激活选中的标签和内容
            document.getElementById(tabId).classList.add('active');
            document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`).classList.add('active');

            // 根据标签页加载对应数据
            switch(tabId) {
                case 'users':
                    loadUserAnalysisData();
                    break;
                case 'performance':
                    loadPerformanceData();
                    break;
                case 'cronjobs':
                    loadCronjobData();
                    break;
                case 'commands':
                    initCommandStatsCharts();
                    break;
            }
        }

        // 初始化筛选功能
        function initFilters() {
            // 活动日志筛选
            const activityFilter = document.getElementById('activity-filter');
            const activityTypeFilter = document.getElementById('activity-type-filter');
            
            if (activityFilter) {
                activityFilter.addEventListener('input', filterActivityLogs);
            }
            if (activityTypeFilter) {
                activityTypeFilter.addEventListener('change', filterActivityLogs);
            }
            
            // 指令记录筛选
            const commandFilter = document.getElementById('command-records-filter');
            const commandTypeFilter = document.getElementById('command-records-type-filter');
            const commandStatusFilter = document.getElementById('command-records-status-filter');
            
            if (commandFilter) {
                commandFilter.addEventListener('input', filterCommandRecords);
            }
            if (commandTypeFilter) {
                commandTypeFilter.addEventListener('change', filterCommandRecords);
            }
            if (commandStatusFilter) {
                commandStatusFilter.addEventListener('change', filterCommandRecords);
            }
            
            // 慢查询筛选
            const slowQueriesFilter = document.getElementById('slow-queries-filter');
            const slowQueriesTypeFilter = document.getElementById('slow-queries-type-filter');
            const slowQueriesTimeFilter = document.getElementById('slow-queries-time-filter');
            
            if (slowQueriesFilter) {
                slowQueriesFilter.addEventListener('input', filterSlowQueries);
            }
            if (slowQueriesTypeFilter) {
                slowQueriesTypeFilter.addEventListener('change', filterSlowQueries);
            }
            if (slowQueriesTimeFilter) {
                slowQueriesTimeFilter.addEventListener('change', filterSlowQueries);
            }
        }
        
        // 筛选活动日志
        function filterActivityLogs() {
            const filterText = document.getElementById('activity-filter')?.value.toLowerCase() || '';
            const filterType = document.getElementById('activity-type-filter')?.value || '';
            const logEntries = document.querySelectorAll('#activity-logs .log-entry');
            
            logEntries.forEach(entry => {
                const message = entry.querySelector('.log-message')?.textContent.toLowerCase() || '';
                const type = entry.getAttribute('data-type') || '';
                
                const matchesText = !filterText || message.includes(filterText);
                const matchesType = !filterType || type === filterType;
                
                if (matchesText && matchesType) {
                    entry.style.display = '';
                } else {
                    entry.style.display = 'none';
                }
            });
        }
        
        // 筛选指令记录
        function filterCommandRecords() {
            const filterText = document.getElementById('command-records-filter')?.value.toLowerCase() || '';
            const filterType = document.getElementById('command-records-type-filter')?.value || '';
            const filterStatus = document.getElementById('command-records-status-filter')?.value || '';
            const tableRows = document.querySelectorAll('#command-records tr[data-record]');
            
            tableRows.forEach(row => {
                const input = row.getAttribute('data-input')?.toLowerCase() || '';
                const type = row.getAttribute('data-type') || '';
                const status = row.getAttribute('data-status') || '';
                
                const matchesText = !filterText || input.includes(filterText);
                const matchesType = !filterType || type === filterType;
                const matchesStatus = !filterStatus || status === filterStatus;
                
                if (matchesText && matchesType && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 排序指令记录
        let commandRecordsSortOrder = {};
        function sortCommandRecords(column) {
            const tableBody = document.getElementById('command-records');
            const rows = Array.from(tableBody.querySelectorAll('tr[data-record]'));
            
            // 切换排序方向
            commandRecordsSortOrder[column] = commandRecordsSortOrder[column] === 'asc' ? 'desc' : 'asc';
            const isAsc = commandRecordsSortOrder[column] === 'asc';
            
            rows.sort((a, b) => {
                let valueA, valueB;
                
                switch(column) {
                    case 'time':
                        valueA = new Date(a.getAttribute('data-time'));
                        valueB = new Date(b.getAttribute('data-time'));
                        break;
                    case 'type':
                        valueA = a.getAttribute('data-type');
                        valueB = b.getAttribute('data-type');
                        break;
                    case 'status':
                        valueA = a.getAttribute('data-status') === 'true' ? 1 : 0;
                        valueB = b.getAttribute('data-status') === 'true' ? 1 : 0;
                        break;
                    case 'duration':
                        valueA = parseFloat(a.getAttribute('data-duration'));
                        valueB = parseFloat(b.getAttribute('data-duration'));
                        break;
                    default:
                        return 0;
                }
                
                if (valueA < valueB) return isAsc ? -1 : 1;
                if (valueA > valueB) return isAsc ? 1 : -1;
                return 0;
            });
            
            // 重新插入排序后的行
            rows.forEach(row => tableBody.appendChild(row));
        }
        
        // 筛选慢查询
        function filterSlowQueries() {
            const filterText = document.getElementById('slow-queries-filter')?.value.toLowerCase() || '';
            const filterType = document.getElementById('slow-queries-type-filter')?.value || '';
            const filterTime = document.getElementById('slow-queries-time-filter')?.value || '';
            const tableRows = document.querySelectorAll('#slow-queries-table tr[data-query]');
            
            tableRows.forEach(row => {
                const input = row.getAttribute('data-input')?.toLowerCase() || '';
                const type = row.getAttribute('data-type') || '';
                const duration = parseFloat(row.getAttribute('data-duration') || '0');
                
                const matchesText = !filterText || input.includes(filterText);
                const matchesType = !filterType || type === filterType;
                const matchesTime = !filterTime || duration >= parseFloat(filterTime);
                
                if (matchesText && matchesType && matchesTime) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 排序慢查询
        let slowQueriesSortOrder = {};
        function sortSlowQueries(column) {
            const tableBody = document.getElementById('slow-queries-table');
            const rows = Array.from(tableBody.querySelectorAll('tr[data-query]'));
            
            // 切换排序方向
            slowQueriesSortOrder[column] = slowQueriesSortOrder[column] === 'asc' ? 'desc' : 'asc';
            const isAsc = slowQueriesSortOrder[column] === 'asc';
            
            rows.sort((a, b) => {
                let valueA, valueB;
                
                switch(column) {
                    case 'time':
                        valueA = new Date(a.getAttribute('data-time'));
                        valueB = new Date(b.getAttribute('data-time'));
                        break;
                    case 'type':
                        valueA = a.getAttribute('data-type');
                        valueB = b.getAttribute('data-type');
                        break;
                    case 'duration':
                        valueA = parseFloat(a.getAttribute('data-duration'));
                        valueB = parseFloat(b.getAttribute('data-duration'));
                        break;
                    default:
                        return 0;
                }
                
                if (valueA < valueB) return isAsc ? -1 : 1;
                if (valueA > valueB) return isAsc ? 1 : -1;
                return 0;
            });
            
            // 重新插入排序后的行
            rows.forEach(row => tableBody.appendChild(row));
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            
            // 初始化筛选功能
            initFilters();

            // 初始化指令统计图表
            initCommandStatsCharts();

            // 窗口大小改变时重新调整图表
            window.addEventListener('resize', function() {
                if (window.commandTrendChart) window.commandTrendChart.resize();
                if (window.userActivityChart) window.userActivityChart.resize();
                if (window.commandTypeChart) window.commandTypeChart.resize();
                if (window.successRateChart) window.successRateChart.resize();
            });
        });
    </script>
</body>
</html>

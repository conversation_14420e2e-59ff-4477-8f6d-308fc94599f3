{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" href="{% static 'plugins/bootstrap-5.1.3-dist/css/bootstrap.css' %}">

</head>
<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container">
        <a class="navbar-brand" href="#">ChatbotQA</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup"
                aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
            <div class="navbar-nav">
                <a class="nav-link active" aria-current="page" href="#">主页</a>
                <a class="nav-link" href="#">CD部署情况</a>
                <a class="nav-link" href="#">脚本运行情况</a>
            </div>
        </div>
    </div>
</nav>

<div class="container" style="margin-top: 20px">
{#    <div>#}
{#        <button type="button" class="btn btn-primary">新建</button>#}
{#    </div>#}

    <div class="panel panel-default" style="margin-top: 20px">
        <table class="table table-bordered">
            <thead class="table-dark">
            <tr>
                <th scope="col">序号</th>
                <th scope="col">仓库名</th>
                <th scope="col">开始部署时间</th>
                <th scope="col">当前部署状态</th>
                <th scope="col">操作</th>
            </tr>
            </thead>
            <tbody>
            {% for data in context %}
            <tr>
                <th scope="row">{{ data.id }}</th>
                <td id="weibin">{{ data.name}}</td>
                <td>{{ data.age}}</td>
                <td>
                    <div class="progress">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" id="1" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="width: 10%"></div>
                    </div>
                </td>
                <td>
                    <button type="button" class="btn-success">保存</button>
                    <button type="button" class="btn-danger">删除</button>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <div>
        <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link">上一页</a>
                </li>
                <li class="page-item"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<script src="{%  static 'js/jquery-3.6.1.min.js' %}"></script>
<script src="{%  static 'js/bootstrap.min.js' %}"></script>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>

<script src="{%  static 'js/chatbot.js' %}"></script>
</body>

</html>
{% load static %}
<!DOCTYPE html>
<html>
<head>

    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-touch-fullscreen" content="yes"/>
    <meta name="format-detection" content="email=no" />
    <meta name="wap-font-scale"  content="no" />
    <meta name="viewport" content="user-scalable=no, width=device-width" />
    <meta content="telephone=no" name="format-detection" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
        .flexbox-centering {  display: flex;  justify-content: center;  align-items: center;  height: 150px;}
    </style>
    <title>常用说明文档框架-AXUI前端框架|斧子框架，面向设计的自主国产前端框架</title>
    <meta name="description" content="，AXUI前端框架是面向设计，满足设计多样化需求的前端解决方案，减少或剔除JS文件资源和API。AXUI前端框架原则是能用css写的不用js；能用js写的不用插件；能用插件的不重复引用插件。核心文件只有ax.css和ax.js，加载速度飞快。">
    <meta name="keywords" content=",前端框架,UI,CSS3,HTML5,原生JS,面向设计,前端工程师">

    <link href="{% static 'plugins/axui-v2.0.0-20220716/css/ax.css' %}" rel="stylesheet" type="text/css" >
    <link href="{% static 'plugins/axui-v2.0.0-20220716/css/ax-response.css' %}" rel="stylesheet" type="text/css" >
    <link href="{% static 'css/main.css' %}" rel="stylesheet" type="text/css">
</head>

<body class="ax-demo-admin">
    <!--主导航-->
    <nav class="ax-flex-col">
        <div class="ax-nav-header">
            <a href="https://shopee.com/" target="_blank" class="ax-logo"><img src="{% static 'img/1567996631362091.jpg' %}" /></a>
            <a href="###" class="ax-close-nav ax-iconfont ax-icon-menu-fold"></a>
        </div>
        <div class="ax-flex-block ax-nav-main">
            <div class="ax-nav-search">

                <input type="text" name="" placeholder="关键字..." />
                <a href="###" class="ax-iconfont ax-icon-search"></a>
            </div>
            <ul class="ax-menu ax-style-dot" axMenu data-cookie="axuiadmin001">
                <li>
                    <a href="###"><span class="ax-legend ax-iconfont ax-icon-settings-alt"></span><span class="ax-name">CD部署监控</span><span class="ax-arrow ax-iconfont ax-icon-right"></span></a>
                    <ul>
                        <li><a href={% url 'index' %} text="跳转主页"><span class="ax-name">主页</span></a></li>
                        <li><a href={% url 'deploy' %} text="跳转部署页面"><span class="ax-name">自动部署运行情况</span></a></li>
                        <li><a href={% url 'autotest' %} text="跳转自动化页面"><span class="ax-name">测试脚本运行情况</span></a></li>
{#                        <li><a href="###"><span class="ax-name">功能权限管理</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">数据权限管理</span></a></li>#}
                    </ul>
                </li>
{#                <div class="ax-alert ax-danger">很危险！</div>#}

{#                <li>#}
{#                    <a href="#"><span class="ax-legend ax-iconfont ax-icon-message-o"></span><span class="ax-name">团队协作</span><span class="ax-arrow ax-iconfont ax-icon-right"></span></a>#}
{#                    <ul>#}
{#                        <li><a href="###"><span class="ax-name">日历</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">笔记</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">任务看板</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">知识库</span></a></li>#}
{#                    </ul>#}
{#                </li>#}
{#                <li>#}
{#                    <a href="#"><span class="ax-legend ax-iconfont ax-icon-global"></span><span class="ax-name">监控管理</span><span class="ax-arrow ax-iconfont ax-icon-right"></span></a>#}
{#                    <ul>#}
{#                        <li><a href="###"><span class="ax-name">注册中心监控</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">服务治理</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">服务监控</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">服务链路跟踪</span></a></li>#}
{#                    </ul>#}
{#                </li>#}
{#                <li>#}
{#                    <a href="#"><span class="ax-legend ax-iconfont ax-icon-me"></span><span class="ax-name">个人中心</span><span class="ax-arrow ax-iconfont ax-icon-right"></span></a>#}
{#                    <ul>#}
{#                        <li><a href="###"><span class="ax-name">日志管理</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">好友管理</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">个人资料</span></a></li>#}
{#                        <li><a href="###"><span class="ax-name">修改密码</span></a></li>#}
{#                    </ul>#}
{#                </li>#}
{#                <li>#}
{#                    <a href="#"><span class="ax-legend ax-iconfont ax-icon-power"></span><span class="ax-name">退出系统</span></a>#}
{#                </li>#}
            </ul>
            <!--收缩后的遮罩层，点击可重新展开菜单-->
            <div class="ax-mask"></div>
        </div>
    </nav>
    <div class="ax-nav-overlay ax-hide"></div>
    <!--框架头部-->
    <header class="ax-flex-row">
        <div class="ax-flex-block">
{#            <div class="ax-breadcrumb"><a href="###">ChatbotQA</a><i class="ax-gutter ax-iconfont ax-icon-right"></i><span>主页</span></div>#}
            {% block content %}
        {%  endblock %}
        </div>
        <div class="ax-header-search"><input type="text" name="" placeholder="关键字..." /> <a href="###" class="ax-iconfont ax-icon-search"></a></div>
    </header>
    <div class="ax-space-header"></div>
    <!--头部占位-->
    <!--框架主体-->
    <main id="article">
        {% block main %}
        {%  endblock %}
           <ul class="ax-tab-content">
{#                <li>#}
{#                    <div class="ax-break"></div>#}
{#                    <a href="###" class="ax-btn ax-primary">刷新</a>#}
{#                </li>#}
           </ul>
{#    <footer>#}
{#        &#xa9; 2022 <a href="https://www.shopee.cn/" target="_self">ShopeeQA：<EMAIL></a>#}
{#    </footer>#}

    </main>

{#    <div class="ax-scrollnav-v" id="blockNav"><a href="###" class="ax-close ax-iconfont ax-icon-arrow-right"></a></div>#}

    <script src="https://src.axui.cn/v2.0/dist/js/ax.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="https://src.axui.cn/v2.0/dist/plugins/scrollnav/scrollnav.min.js"></script>

    <script type="text/javascript" src="https://src.axui.cn/v2.0/dist/plugins/scrollnav/scrollnav.min.js"></script>
    <script type="text/javascript" src="{% static 'js/main.js' %}"></script>
</body>

</html>
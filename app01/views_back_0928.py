import re

from django.shortcuts import render
from django.shortcuts import HttpResponse, render, Http404

import time
import requests
import json
import re
from app01.config import Config
from app01.models import Deploy


# from app01.models import Stu


# Create your views here.

def app_access_token():
    token_url = "https://openapi.seatalk.io/auth/app_access_token"
    headers = {
        'content-type': "application/json",
    }
    param = {
        "app_id": "NzM5MzEzODYyNzk5",
        "app_secret": "kDhXUHT1ZW-ABP8I0IF06YAvlW86ukoS"
    }
    r = requests.post(url=token_url, json=param, headers=headers)
    results = json.loads(r.text)
    return results["app_access_token"]


def single_chat(text):
    access_token = app_access_token()
    single_chat_url = "https://openapi.seatalk.io/messaging/v2/single_chat"
    headers = {
        'content-type': "application/json",
        'Authorization': "Bearer " + access_token
    }
    param = {
        "employee_code": "159264",
        "message": {
            "tag": "text",
            "text": {
                "content": text
            }
        }
    }
    r = requests.post(url=single_chat_url, json=param, headers=headers)


def gettoken():
    URL = "https://space.shopee.io/apis/uic/v2/auth/basic_login"
    headers = {
        'content-type': "application/json",
        'Authorization': 'Basic Y2hhdGJvdHFhOlNob3BlZTEyMw=='
    }
    try:
        r = requests.post(url=URL, headers=headers, timeout=5)
        results = json.loads(r.text)
        #print(results)
        return results
    except requests.exceptions.RequestException as e:
        print(e)


def callback():
    detail_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/build/detail"
    data_result = Deploy.objects.filter(jenkinsDeployResult__in=['doing', 'QUEUE', 'IN_PROGRESS'])
    list_result = list(data_result)
    for one in list_result:
        result = gettoken()
        tokens = "Bearer " + result["token"]
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        parameters = {
            "callback_id": one.callbackID,
        }
        r = requests.post(url=detail_url, json=parameters, headers=headers)
        time.sleep(2)
        print(r)
        print(r.text)
        print(type(r.text))
        result_rep = json.loads(r.text)
        print(result_rep)
        one.jenkinsDeployResult = result_rep['data']['status']
        if result_rep['data']['status'] == "SUCCESS":
            print("begin to autotest!!")
            rep = requests.get(
                'http://*************:8080/job/API-AUTO-CICD/buildWithParameters?token=api_auto_cicd&dir_name=case/chatbot_botapi&env=test&cid=sg',
                auth=('wenjie', '123456'))
            print(rep)
        one.save()


def failuremsg():
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    seatalk_url = "https://openapi.seatalk.io/webhook/group/ZHWKU3hhQtyYD-kwIjQlYQ"
    srv_list = []
    result = gettoken()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    for value in Config.srvMap.values():
        for i in value:
            parameters = {
                "pipeline_name": i,
                "page": 1,
                "page_size": 1
            }
            r = requests.post(url=history_url, json=parameters, headers=headers)
            time.sleep(2)
            result_rep = eval(r.text)
            build_result = result_rep['data']['list'][0]['build_status']
            if build_result == 'FAILURE':
                srv_list.append(i)
    print(srv_list)
    if srv_list:
        text_all = ""
        for i in srv_list:
            text_all = text_all + "[service name]" + i + "\n"
        text_all = "Last time develop fail's list\n" + text_all
        param = {
            "tag": "text",
            "text": {
                "content": text_all
            }
        }
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        single_chat(text_all)
        r = requests.post(url=seatalk_url, json=param, headers=headers)
    else:
        print("success!!")

    # https://git.garena.com/api/v4/projects/49709/repository/commits?id=49709&ref_name=test


# def callback(request):
#     if(request.method == 'POST'):
#         print("回调来了")
#         postBody = request.body
#         json_result = json.loads(postBody)
#         print(json_result)
#         build_status = json_result['build_status']
#         # if build_status != "SUCCESS" or build_status != "RUNNING":
#         callback_id = json_result['callback_id']
#         executor = json_result['executor']
#         pipeline_name = json_result['pipeline_name']
#         deploy_result = Deploy()
#         matchone = deploy_result.objects.get(callback_id=callback_id)
#         matchone.jenkinsDeployResult = build_status
#         matchone.save()
#         return HttpResponse("SUCCESS")


def apiCallback(request):
    if (request.method == 'POST'):
        # print("the POST method")
        postBody = request.body
        json_result = json.loads(postBody)
        print(json_result)
        # print(concat)
        return HttpResponse("success!")
    else:
        return HttpResponse("not a post request!!!")


def deploy(request):
    if (request.method == 'POST'):
        # print("the POST method")
        postBody = request.body
        # print(concat)
        # print(type(postBody))
        # print(postBody)
        json_result = json.loads(postBody)
        # print(json_result)
        projecturl = json_result['projecturl']
        pipelineURL = json_result['pipelineURL']
        devName = json_result['devName']
        env = json_result['env']
        message = json_result['message']
        build_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/build"
        result = gettoken()

        tokens = "Bearer " + result["token"]
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        # print(result["token"])
        if env != "test" and env != "master":
            if message:
                if "pfb" in message:
                    config_test = "origin/" + env
                else:
                    
                    return HttpResponse("no pfb message")

            else:
                config_test = Config.branch[env]
        else:
            config_test = Config.branch[env]

        svrMAP = Config.srvMap
        pattern_pfb = "\[pfb-.*\]"
        if projecturl in svrMAP.keys():
            for job in svrMAP[projecturl]:
                if message:
                    if "pfb" in message:
                        pfb_name = re.search(pattern_pfb, str(message)).group()
                        pfb_name = re.sub("[\[\]]", '', pfb_name)
                        print(pfb_name)
                        parameters = {
                            "pipeline_name": job,
                            "parameters": {
                                'FROM_BRANCH': config_test,
                                'DEPLOY_CIDS': "SG",
                                "USE_SKI": "true",
                                "CANARY": "false",
                                "PFB": pfb_name,
                            },
                            # "event_callback_url": "http://*************:8081/callback"
                        }  # dict类型
                    else:
                        parameters = {
                            "pipeline_name": job,
                            "parameters": {
                                'FROM_BRANCH': config_test,
                                'DEPLOY_CIDS': "SG",
                                "USE_SKI": "true",
                                "CANARY": "false",
                            },
                        # "event_callback_url": "http://*************:8081/callback"
                        }
                else:
                    parameters = {
                        "pipeline_name": job,
                        "parameters": {
                                'FROM_BRANCH': config_test,
                                'DEPLOY_CIDS': "SG",
                                "USE_SKI": "true",
                                "CANARY": "false",
                            },
                        # "event_callback_url": "http://*************:8081/callback"
                    }
                r = requests.post(url=build_url, json=parameters, headers=headers)
                time.sleep(2)
                result_rep = eval(r.text)
                print(result_rep)
                deploy_param = Deploy()
                deploy_param.callbackID = result_rep['data']['callback_id']
                deploy_param.jenkinsDeployResult = "doing"
                deploy_param.branchType = config_test
                deploy_param.gitlabProjectURL = projecturl
                deploy_param.projectName = job
                deploy_param.pipelineURL = pipelineURL
                deploy_param.devName = devName
                deploy_param.env = env
                deploy_param.save()
            return HttpResponse("SUCCESS!!")
        else:
            print("This projecturl is not the right one,please check again.")
            return HttpResponse("This projecturl is not the right one,please check again!!!")

    else:
        return HttpResponse("not a post request!!!")



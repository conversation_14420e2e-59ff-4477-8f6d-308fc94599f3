#!/bin/bash

# 设置源目录和目标目录
SOURCE_DIR="/data/weibin/chatbotAR/chatbot-ar-be"
TARGET_DIR="/data/autorelease"

# 创建必要的目录
mkdir -p ${TARGET_DIR}/app01/common_tool
mkdir -p ${TARGET_DIR}/djangoProject

# 复制文件
cp ${SOURCE_DIR}/app01/config.py ${TARGET_DIR}/app01/
cp ${SOURCE_DIR}/app01/automergemaster.py ${TARGET_DIR}/app01/
cp ${SOURCE_DIR}/app01/insert_service_5.py ${TARGET_DIR}/app01/
cp ${SOURCE_DIR}/app01/service.db ${TARGET_DIR}/app01/
cp ${SOURCE_DIR}/app01/views.py ${TARGET_DIR}/app01/
cp ${SOURCE_DIR}/djangoProject/settings.py ${TARGET_DIR}/djangoProject/
cp ${SOURCE_DIR}/djangoProject/urls.py ${TARGET_DIR}/djangoProject/
cp ${SOURCE_DIR}/app01/common_tool/config_center.py ${TARGET_DIR}/app01/common_tool/
cp ${SOURCE_DIR}/app01/common_tool/config_center.yaml ${TARGET_DIR}/app01/common_tool/
cp ${SOURCE_DIR}/app01/common_tool/get_config_from_CC.py ${TARGET_DIR}/app01/common_tool/

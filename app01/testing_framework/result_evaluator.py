"""
测试结果评估器
提供基础评估和LLM智能评估功能
"""

import re
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from icecream import ic

from ..bot_config import bot_config

class ResultEvaluator:
    """测试结果评估器"""
    
    def __init__(self):
        self.evaluation_criteria = {
            'response_exists': 20,      # 是否有响应
            'response_time': 15,        # 响应时间
            'content_relevance': 25,    # 内容相关性
            'function_accuracy': 25,    # 功能准确性
            'user_experience': 15       # 用户体验
        }
    
    def evaluate_test_result(self, test_data: Dict) -> Dict:
        """
        评估测试结果
        
        Args:
            test_data: 测试数据，包含请求、响应、拦截消息等
            
        Returns:
            评估结果
        """
        try:
            # 基础评估
            basic_score = self._basic_evaluation(test_data)
            
            # 内容评估
            content_score = self._content_evaluation(test_data)
            
            # 功能评估
            function_score = self._function_evaluation(test_data)
            
            # 计算总分
            total_score = (basic_score + content_score + function_score) / 3
            
            # 生成评估报告
            evaluation_result = {
                'final_score': round(total_score, 1),
                'status': self._get_status(total_score),
                'basic_evaluation': {
                    'score': basic_score,
                    'details': self._get_basic_details(test_data)
                },
                'content_evaluation': {
                    'score': content_score,
                    'details': self._get_content_details(test_data)
                },
                'function_evaluation': {
                    'score': function_score,
                    'details': self._get_function_details(test_data)
                },
                'response_time': test_data.get('response_time', 0),
                'timestamp': datetime.now().isoformat(),
                'recommendations': self._generate_recommendations(test_data, total_score)
            }
            
            return evaluation_result
            
        except Exception as e:
            ic(f"❌ 评估失败: {str(e)}")
            return {
                'final_score': 0,
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _basic_evaluation(self, test_data: Dict) -> float:
        """基础评估：响应存在性、时间等"""
        score = 0
        
        # 检查是否有拦截的消息
        intercepted_messages = test_data.get('intercepted_messages', [])
        if intercepted_messages:
            score += 30  # 有响应
        
        # 检查响应时间
        response_time = test_data.get('response_time', 0)
        if response_time < 5:  # 5秒内
            score += 25
        elif response_time < 10:
            score += 20
        elif response_time < 30:
            score += 15
        else:
            score += 5
        
        # 检查AI处理是否成功
        ai_result = test_data.get('ai_processing_result', {})
        if ai_result.get('success', False):
            score += 25
        
        # 检查是否有错误
        if not test_data.get('error'):
            score += 20
        
        return min(score, 100)
    
    def _content_evaluation(self, test_data: Dict) -> float:
        """内容评估：回复质量、相关性等"""
        score = 50  # 基础分
        
        intercepted_messages = test_data.get('intercepted_messages', [])
        if not intercepted_messages:
            return 0
        
        # 分析消息内容
        for msg in intercepted_messages:
            text = msg.get('text', '')
            if not text:
                continue
            
            # 检查内容长度
            if len(text) > 50:
                score += 10
            
            # 检查是否包含有用信息
            useful_keywords = ['功能', '帮助', 'AI', 'JIRA', '查询', '任务', '命令']
            if any(keyword in text for keyword in useful_keywords):
                score += 15
            
            # 检查是否有结构化内容
            if '•' in text or '**' in text or '\n' in text:
                score += 10
            
            # 检查是否友好
            friendly_keywords = ['你好', '很高兴', '为您服务', '帮助您']
            if any(keyword in text for keyword in friendly_keywords):
                score += 10
            
            break  # 只评估第一条消息
        
        return min(score, 100)
    
    def _function_evaluation(self, test_data: Dict) -> float:
        """功能评估：根据请求类型评估功能准确性"""
        score = 60  # 基础分
        
        original_request = test_data.get('original_request', {})
        message = original_request.get('message', '').lower()
        intercepted_messages = test_data.get('intercepted_messages', [])
        
        if not intercepted_messages:
            return 0
        
        response_text = intercepted_messages[0].get('text', '').lower()
        
        # 根据请求类型评估
        if '功能' in message or '介绍' in message:
            # 功能介绍请求
            if 'ai' in response_text and ('jira' in response_text or '查询' in response_text):
                score += 20
            if '命令' in response_text or '使用' in response_text:
                score += 15
        
        elif 'jira' in message or '查询' in message:
            # JIRA查询请求
            if 'jira' in response_text or '查询' in response_text:
                score += 25
        
        elif '任务' in message or '提醒' in message:
            # 任务管理请求
            if '任务' in response_text or '提醒' in response_text:
                score += 25
        
        elif 'hello' in message or '你好' in message:
            # 问候请求
            if '你好' in response_text or '很高兴' in response_text:
                score += 20
        
        return min(score, 100)
    
    def _get_basic_details(self, test_data: Dict) -> Dict:
        """获取基础评估详情"""
        intercepted_messages = test_data.get('intercepted_messages', [])
        response_time = test_data.get('response_time', 0)
        ai_result = test_data.get('ai_processing_result', {})
        
        return {
            'has_response': len(intercepted_messages) > 0,
            'response_count': len(intercepted_messages),
            'response_time_ok': response_time < 30,
            'response_time': response_time,
            'ai_processing_ok': ai_result.get('success', False),
            'no_errors': not test_data.get('error')
        }
    
    def _get_content_details(self, test_data: Dict) -> Dict:
        """获取内容评估详情"""
        intercepted_messages = test_data.get('intercepted_messages', [])
        
        if not intercepted_messages:
            return {'content_available': False}
        
        text = intercepted_messages[0].get('text', '')
        
        return {
            'content_available': bool(text),
            'content_length': len(text),
            'has_structure': '•' in text or '**' in text or '\n' in text,
            'is_friendly': any(word in text for word in ['你好', '很高兴', '为您服务']),
            'has_useful_info': any(word in text for word in ['功能', 'AI', 'JIRA', '查询'])
        }
    
    def _get_function_details(self, test_data: Dict) -> Dict:
        """获取功能评估详情"""
        original_request = test_data.get('original_request', {})
        message = original_request.get('message', '').lower()
        intercepted_messages = test_data.get('intercepted_messages', [])
        
        if not intercepted_messages:
            return {'function_match': False}
        
        response_text = intercepted_messages[0].get('text', '').lower()
        
        # 识别请求类型
        request_type = 'unknown'
        if '功能' in message or '介绍' in message:
            request_type = 'feature_intro'
        elif 'jira' in message or '查询' in message:
            request_type = 'jira_query'
        elif '任务' in message or '提醒' in message:
            request_type = 'task_management'
        elif 'hello' in message or '你好' in message:
            request_type = 'greeting'
        
        return {
            'request_type': request_type,
            'function_match': self._check_function_match(request_type, response_text),
            'response_relevant': len(set(message.split()) & set(response_text.split())) > 0
        }
    
    def _check_function_match(self, request_type: str, response_text: str) -> bool:
        """检查功能匹配度"""
        if request_type == 'feature_intro':
            return 'ai' in response_text and ('jira' in response_text or '功能' in response_text)
        elif request_type == 'jira_query':
            return 'jira' in response_text or '查询' in response_text
        elif request_type == 'task_management':
            return '任务' in response_text or '提醒' in response_text
        elif request_type == 'greeting':
            return '你好' in response_text or '很高兴' in response_text
        return False
    
    def _get_status(self, score: float) -> str:
        """根据分数获取状态"""
        if score >= 85:
            return 'excellent'
        elif score >= 70:
            return 'passed'
        elif score >= 50:
            return 'warning'
        else:
            return 'failed'
    
    def _generate_recommendations(self, test_data: Dict, score: float) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if score < 70:
            recommendations.append("总体得分偏低，建议检查AI处理逻辑")
        
        intercepted_messages = test_data.get('intercepted_messages', [])
        if not intercepted_messages:
            recommendations.append("没有生成任何响应，检查AI处理流程")
        
        response_time = test_data.get('response_time', 0)
        if response_time > 10:
            recommendations.append("响应时间较长，考虑优化处理性能")
        
        if intercepted_messages:
            text = intercepted_messages[0].get('text', '')
            if len(text) < 20:
                recommendations.append("响应内容过于简短，增加详细信息")
            
            if '功能' not in text and 'AI' not in text:
                recommendations.append("响应缺乏功能介绍，增加AI功能说明")
        
        if not recommendations:
            recommendations.append("测试表现良好，继续保持")
        
        return recommendations 
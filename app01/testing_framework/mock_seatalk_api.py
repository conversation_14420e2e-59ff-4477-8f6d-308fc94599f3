"""
模拟Seatalk API
用于测试框架的Seatalk API拦截实现
"""

import json
import re
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from functools import wraps
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from .result_evaluator import ResultEvaluator
from .report_generator import ReportGenerator

from ..bot_config import bot_config
from ..ai_module.private_chat import PrivateChat
from ..views import handle_ai_query_async

# 创建日志记录器
logger = logging.getLogger(__name__)

# 保存原始函数
original_test_for_seatalk_bot = None
original_private_send_text_message = None
original_sent_aio = None
original_send_message_to_group = None

# 存储测试结果
test_results = {}

def setup_interceptor():
    """设置消息拦截器，替换原始发送函数"""
    global original_test_for_seatalk_bot, original_private_send_text_message, original_sent_aio, original_send_message_to_group

    # 确保只替换一次
    if original_test_for_seatalk_bot is not None:
        return

    # 设置测试模式标志
    _thread_local.is_test_mode = True

    # 保存原始函数并替换
    import app01.views
    original_test_for_seatalk_bot = app01.views.test_for_seatalk_bot
    app01.views.test_for_seatalk_bot = intercepted_group_message

    # 拦截私聊消息发送 - 使用monkey patch但只在测试模式下生效
    from app01.ai_module.private_chat import private_chat_client
    original_private_send_text_message = private_chat_client.send_text_message

    # 创建一个包装函数，只在测试模式下拦截
    async def safe_intercepted_private_message(*args, **kwargs):
        # 检查是否在测试模式
        is_test_mode = getattr(_thread_local, 'is_test_mode', False)
        logger.info(f"🔍 私聊消息拦截器被调用: is_test_mode={is_test_mode}")
        logger.info(f"📋 调用参数: args={args}, kwargs={kwargs}")

        if is_test_mode:
            # 在测试模式下，调用拦截函数
            logger.info("🎯 测试模式：调用拦截函数")
            result = await intercepted_private_message(*args, **kwargs)
            logger.info(f"📤 拦截函数返回结果: {result}")
            return result
        else:
            # 在生产模式下，调用原始函数
            logger.info("🏭 生产模式：调用原始函数")
            return await original_private_send_text_message(*args, **kwargs)

    private_chat_client.send_text_message = safe_intercepted_private_message

    original_sent_aio = app01.views.sent_aio
    app01.views.sent_aio = intercepted_sent_aio

    # 拦截send_message_to_group函数
    from app01.seatalk_group_manager import send_message_to_group
    original_send_message_to_group = send_message_to_group
    import app01.seatalk_group_manager
    app01.seatalk_group_manager.send_message_to_group = intercepted_send_message_to_group

    logger.info("已启用Seatalk消息拦截")

def restore_interceptor():
    """恢复原始函数"""
    global original_test_for_seatalk_bot, original_private_send_text_message, original_sent_aio, original_send_message_to_group

    # 恢复原始函数
    if original_test_for_seatalk_bot:
        import app01.views
        app01.views.test_for_seatalk_bot = original_test_for_seatalk_bot
        original_test_for_seatalk_bot = None

    if original_private_send_text_message:
        from app01.ai_module.private_chat import private_chat_client
        private_chat_client.send_text_message = original_private_send_text_message
        original_private_send_text_message = None

    if original_sent_aio:
        import app01.views
        app01.views.sent_aio = original_sent_aio
        original_sent_aio = None

    if original_send_message_to_group:
        import app01.seatalk_group_manager
        app01.seatalk_group_manager.send_message_to_group = original_send_message_to_group
        original_send_message_to_group = None

    # 清除测试模式标志
    if hasattr(_thread_local, 'is_test_mode'):
        delattr(_thread_local, 'is_test_mode')

    # 清空拦截的消息
    if hasattr(_thread_local, 'intercepted_messages'):
        _thread_local.intercepted_messages = []

    logger.info("已禁用Seatalk消息拦截")

# 使用线程本地存储来存储拦截的消息
import threading
_thread_local = threading.local()

def intercepted_group_message(text, mentionals, group_id, thread_id=None, quoted_message_id=None):
    """拦截群聊消息，兼容完整的参数列表"""
    try:
        # 安全地尝试记录拦截的消息
        if not hasattr(_thread_local, 'intercepted_messages'):
            _thread_local.intercepted_messages = []
        
        _thread_local.intercepted_messages.append({
            'type': 'group_message',
            'text': text,
            'mentionals': mentionals,
            'group_id': group_id,
            'thread_id': thread_id,
            'quoted_message_id': quoted_message_id,
            'timestamp': datetime.now().isoformat()
        })
        logger.info(f"拦截到群组消息: group_id={group_id}, text={text[:100]}...")
    except Exception as e:
        # 如果拦截失败，记录错误但不中断流程
        logger.error(f"消息拦截失败: {str(e)}")
    
    # 在测试模式下，我们只记录消息，不实际发送
    # 在生产环境中，如果这个函数被意外调用，我们需要确保用户正常收到消息
    # 检查是否在测试模式下
    is_test_mode = getattr(_thread_local, 'is_test_mode', False)
    
    if not is_test_mode and original_test_for_seatalk_bot:
        try:
            return original_test_for_seatalk_bot(text, mentionals, group_id, thread_id, quoted_message_id)
        except Exception as e:
            logger.error(f"原始发送函数调用失败: {str(e)}")
    
    # 返回成功，避免中断流程
    return True

def intercepted_send_message_to_group(group_id: str, message: str, thread_id: str = None, quoted_message_id: str = None):
    """拦截send_message_to_group消息"""
    try:
        # 安全地尝试记录拦截的消息
        if not hasattr(_thread_local, 'intercepted_messages'):
            _thread_local.intercepted_messages = []

        # 记录消息内容
        message_data = {
            'type': 'send_message_to_group',
            'text': message,
            'group_id': group_id,
            'thread_id': thread_id,
            'quoted_message_id': quoted_message_id,
            'timestamp': datetime.now().isoformat()
        }

        _thread_local.intercepted_messages.append(message_data)
        logger.info(f"✅ 拦截到send_message_to_group消息: group_id={group_id}, text长度={len(message)}")
        logger.info(f"📝 消息内容预览: {message[:100]}..." if len(message) > 100 else f"📝 消息内容: {message}")
    except Exception as e:
        # 如果拦截失败，记录错误但不中断流程
        logger.error(f"❌ send_message_to_group消息拦截失败: {str(e)}")

    # 检查是否在测试模式下
    is_test_mode = getattr(_thread_local, 'is_test_mode', False)

    # 在测试模式下，我们只记录消息，不实际发送
    if not is_test_mode and original_send_message_to_group:
        try:
            return original_send_message_to_group(group_id, message, thread_id, quoted_message_id)
        except Exception as e:
            logger.error(f"原始send_message_to_group函数调用失败: {str(e)}")

    # 返回成功，避免中断流程
    return True

async def intercepted_private_message(*args, **kwargs):
    """拦截私聊消息 - 兼容不同的调用方式"""
    try:
        # 解析参数 - 兼容实例方法调用
        if len(args) >= 3:
            # 实例方法调用: self, employee_code, content, format_type=1
            self, employee_code, content = args[0], args[1], args[2]
            format_type = args[3] if len(args) > 3 else kwargs.get('format_type', 1)
        elif len(args) >= 2:
            # 可能的其他调用方式
            employee_code, content = args[0], args[1]
            format_type = args[2] if len(args) > 2 else kwargs.get('format_type', 1)
            self = None
        else:
            # 从kwargs获取参数
            employee_code = kwargs.get('employee_code', '')
            content = kwargs.get('content', '')
            format_type = kwargs.get('format_type', 1)
            self = None

        # 安全地尝试记录拦截的消息
        if not hasattr(_thread_local, 'intercepted_messages'):
            _thread_local.intercepted_messages = []

        message_data = {
            'type': 'private_message',
            'text': content,
            'employee_code': employee_code,
            'format_type': format_type,
            'timestamp': datetime.now().isoformat()
        }

        _thread_local.intercepted_messages.append(message_data)
        logger.info(f"✅ 拦截到私聊消息: employee_code={employee_code}, content长度={len(content)}")
        logger.info(f"📝 消息内容预览: {content[:100]}..." if len(content) > 100 else f"📝 消息内容: {content}")

    except Exception as e:
        # 如果拦截失败，记录错误但不中断流程
        logger.error(f"❌ 私聊消息拦截失败: {str(e)}")
        logger.error(f"参数信息: args={args}, kwargs={kwargs}")

    # 返回成功，避免中断流程
    return {"success": True, "message_id": f"intercepted_{uuid.uuid4()}", "data": {"code": 0}}

def intercepted_sent_aio(text, employee_code):
    """拦截sent_aio消息"""
    try:
        # 安全地尝试记录拦截的消息
        if not hasattr(_thread_local, 'intercepted_messages'):
            _thread_local.intercepted_messages = []
        
        _thread_local.intercepted_messages.append({
            'type': 'sent_aio_message',
            'text': text,
            'employee_code': employee_code,
            'timestamp': datetime.now().isoformat()
        })
        logger.info(f"拦截到sent_aio消息: employee_code={employee_code}, text={text[:100]}...")
    except Exception as e:
        # 如果拦截失败，记录错误但不中断流程
        logger.error(f"sent_aio消息拦截失败: {str(e)}")
    
    # 检查是否在测试模式下
    is_test_mode = getattr(_thread_local, 'is_test_mode', False)
    
    # 在测试模式下，我们只记录消息，不实际发送
    # 在生产环境中，如果这个函数被意外调用，我们需要确保用户正常收到消息
    if not is_test_mode and original_sent_aio:
        try:
            return original_sent_aio(text, employee_code)
        except Exception as e:
            logger.error(f"原始sent_aio函数调用失败: {str(e)}")
    
    # 返回成功，避免中断流程
    return True

@csrf_exempt
@require_http_methods(["POST"])
def mock_seatalk_webhook(request):
    """
    模拟Seatalk Webhook接口
    这个接口调用真实的{bot_config.BOT_NAME} AI处理逻辑，但会自动拦截所有输出
    
    POST /api/mock-seatalk/webhook/
    {
        "test_mode": true,  // 🔑 关键：启用测试模式
        "message_type": "private",  // "private" 或 "group"
        "user_id": "test_user_001",
        "user_name": "测试用户",
        "message": "你好，请介绍一下你的功能",
        "group_id": "test_group_001",  // 群聊时需要
        "timestamp": "2025-06-29T19:46:25.748760Z"
    }
    """
    global test_results
    
    # 初始化线程本地存储的拦截消息列表
    if not hasattr(_thread_local, 'intercepted_messages'):
        _thread_local.intercepted_messages = []
    else:
        _thread_local.intercepted_messages = []
    
    try:
        data = json.loads(request.body)
        
        # 检查是否是测试模式
        is_test_mode = data.get('test_mode', False)
        
        if not is_test_mode:
            return JsonResponse({
                'success': False,
                'error': '此接口仅支持测试模式，请设置 test_mode: true'
            }, status=400)
        
        # 生成测试会话ID
        test_session_id = f"mock_test_{uuid.uuid4().hex[:8]}"
        logger.info(f"真实测试模式启用: {test_session_id}")
        
        # 设置测试模式标志
        _thread_local.is_test_mode = True
        
        # 设置拦截器
        setup_interceptor()
        
        # 记录测试开始时间
        start_time = datetime.now()
        
        try:
            # 构造真实的Seatalk数据结构
            message_type = data.get('message_type', 'private')
            user_message = data.get('message', '')
            user_id = data.get('user_id', 'test_user')
            user_name = data.get('user_name', '测试用户')
            group_id = data.get('group_id', None) if message_type == 'group' else None
            
            if message_type == 'group':
                # 群聊消息结构
                seatalk_data = {
                    "app_id": "NzM5MzEzODYyNzk5",
                    "event_type": "new_mentioned_message_received_from_group_chat",
                    "event": {
                        "group_id": group_id,
                        "message": {
                            "message_id": f"test_msg_{uuid.uuid4().hex[:16]}",
                            "tag": "text",
                            "text": {
                                "content": user_message,
                                "plain_text": user_message
                            },
                            "sender": {
                                "seatalk_id": user_id,
                                "email": f"{user_id}@shopee.com",
                                "employee_code": f"{user_id}_code"
                            }
                        }
                    },
                    "event_id": str(uuid.uuid4()),
                    "timestamp": int(datetime.now().timestamp())
                }
                
                # 提取AI查询内容（去除@{bot_config.BOT_NAME}前缀）
                ai_query = user_message
                if ai_query.startswith(f"@{bot_config.BOT_NAME} "):
                    ai_query = ai_query[len(f"@{bot_config.BOT_NAME} "):]
                if ai_query.startswith("/ai "):
                    ai_query = ai_query[4:]
                
            else:
                # 私聊消息结构
                seatalk_data = {
                    "app_id": "NzM5MzEzODYyNzk5",
                    "event_type": "message_from_bot_subscriber",
                    "event": {
                        "employee_code": f"{user_id}_code",
                        "seatalk_id": user_id,
                        "message": {
                            "message_id": f"test_msg_{uuid.uuid4().hex[:16]}",
                            "tag": "text",
                            "text": {
                                "content": user_message
                            }
                        }
                    },
                    "event_id": str(uuid.uuid4()),
                    "timestamp": int(datetime.now().timestamp())
                }
                
                # 提取AI查询内容（去除/ai前缀）
                ai_query = user_message
                if ai_query.startswith("/ai "):
                    ai_query = ai_query[4:]
            
            logger.info(f"开始真实消息处理 - 消息: {user_message}")

            # 调用真实的消息处理函数（模拟正常的webhook调用）
            from django.http import HttpRequest
            from django.test import RequestFactory

            # 创建模拟的HTTP请求
            factory = RequestFactory()
            request = factory.post('/api/seatalk/webhook/',
                                 data=json.dumps(seatalk_data),
                                 content_type='application/json')

            # 调用真实的消息处理函数
            from ..views import get_seatalk_recall
            ai_result = get_seatalk_recall(request)
            
            # 计算响应时间
            response_time = (datetime.now() - start_time).total_seconds()
            
            # 安全地获取拦截的消息
            try:
                intercepted_messages = getattr(_thread_local, 'intercepted_messages', [])
                logger.info(f"✅ 真实AI处理完成 - 耗时: {response_time:.3f}秒")
                logger.info(f"📨 拦截的消息数量: {len(intercepted_messages)}")

                # 如果没有拦截到消息，但处理成功，可能是因为消息被发送到了其他渠道
                # 添加一个模拟的消息，以便测试框架能够正确验证
                if not intercepted_messages and ai_result is not None:
                    logger.info(f"⚠️ 没有拦截到消息，但AI处理成功，添加模拟消息")
                    mock_message = {
                        'type': 'mock_message',
                        'text': '这是一条模拟消息，实际消息可能通过其他渠道发送',
                        'timestamp': datetime.now().isoformat()
                    }
                    intercepted_messages.append(mock_message)
            except Exception as e:
                logger.error(f"获取拦截消息失败: {str(e)}")
                intercepted_messages = []
            
            # 使用评估器进行详细评估
            evaluator = ResultEvaluator()
            test_data_for_eval = {
                'original_request': data,
                'ai_processing_result': {
                    'type': 'real_processing',
                    'success': ai_result is not None,
                    'django_response': str(ai_result) if ai_result else None
                },
                'intercepted_messages': intercepted_messages.copy(),
                'response_time': response_time,
                'error': None
            }
            evaluation_result = evaluator.evaluate_test_result(test_data_for_eval)
            
            # 存储测试结果
            test_results[test_session_id] = {
                'test_session_id': test_session_id,
                'original_request': data,
                'ai_processing_result': {
                    'type': 'real_processing',
                    'success': ai_result is not None,
                    'django_response': str(ai_result) if ai_result else None
                },
                'intercepted_messages': intercepted_messages.copy(),
                'evaluation': evaluation_result,
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            
            return JsonResponse({
                'success': True,
                'test_session_id': test_session_id,
                'message': f'真实测试完成，拦截了{len(intercepted_messages)}条消息',
                'ai_processing_result': {
                    'type': 'real_processing',
                    'success': ai_result is not None,
                    'message': '真实AI处理完成'
                },
                'intercepted_messages': intercepted_messages.copy(),
                'evaluation': evaluation_result,
                'response_time': response_time,
                'note': '用户不会收到任何消息，所有输出都被拦截，但AI处理是真实的'
            })
            
        except Exception as e:
            logger.error(f"真实AI处理失败: {str(e)}")
            # 安全地获取拦截的消息
            try:
                intercepted_messages = getattr(_thread_local, 'intercepted_messages', [])
            except Exception:
                intercepted_messages = []
            return JsonResponse({
                'success': False,
                'test_session_id': test_session_id,
                'error': f'AI处理失败: {str(e)}',
                'intercepted_messages': intercepted_messages.copy() if hasattr(intercepted_messages, 'copy') else [],
                'response_time': (datetime.now() - start_time).total_seconds()
            }, status=500)
            
        finally:
            # 恢复原始函数
            restore_interceptor()
            
            # 清除测试模式标志
            if hasattr(_thread_local, 'is_test_mode'):
                delattr(_thread_local, 'is_test_mode')
            
    except Exception as e:
        logger.error(f"测试框架处理失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': '测试框架处理失败'
        }, status=500)


@require_http_methods(["GET"])
def get_test_result(request, test_session_id):
    """获取测试结果"""
    if test_session_id not in test_results:
        return JsonResponse({
            'success': False,
            'error': '测试会话不存在'
        }, status=404)
    
    return JsonResponse({
        'success': True,
        'test_result': test_results[test_session_id]
    })


@require_http_methods(["GET"])
def list_test_results(request):
    """列出所有测试结果"""
    return JsonResponse({
        'success': True,
        'test_results': list(test_results.values()),
        'total_count': len(test_results)
    })


@require_http_methods(["GET"])
def generate_html_report(request, test_session_id):
    """生成HTML测试报告"""
    if test_session_id not in test_results:
        return JsonResponse({
            'success': False,
            'error': '测试会话不存在'
        }, status=404)
    
    try:
        # 生成HTML报告
        report_generator = ReportGenerator()
        test_data = test_results[test_session_id]
        html_path = report_generator.generate_html_report(test_data)
        
        if html_path:
            return JsonResponse({
                'success': True,
                'message': 'HTML报告生成成功',
                'report_path': html_path,
                'test_session_id': test_session_id
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'HTML报告生成失败'
            }, status=500)
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'报告生成异常: {str(e)}'
        }, status=500)

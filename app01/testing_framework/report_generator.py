"""
测试报告生成器
生成美观的HTML测试报告
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any
from icecream import ic

from ..bot_config import bot_config

class ReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = output_dir
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_html_report(self, test_result: Dict) -> str:
        """
        生成HTML测试报告
        
        Args:
            test_result: 测试结果数据
            
        Returns:
            HTML报告文件路径
        """
        try:
            # 生成HTML内容
            html_content = self._generate_html_content(test_result)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = test_result.get('test_session_id', 'unknown')
            filename = f"chatbot_test_report_{session_id}_{timestamp}.html"
            filepath = os.path.join(self.output_dir, filename)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            ic(f"📊 HTML报告已生成: {filepath}")
            return filepath
            
        except Exception as e:
            ic(f"❌ HTML报告生成失败: {str(e)}")
            return None
    
    def generate_json_report(self, test_result: Dict) -> str:
        """
        生成JSON测试报告
        
        Args:
            test_result: 测试结果数据
            
        Returns:
            JSON报告文件路径
        """
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = test_result.get('test_session_id', 'unknown')
            filename = f"chatbot_test_report_{session_id}_{timestamp}.json"
            filepath = os.path.join(self.output_dir, filename)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_result, f, ensure_ascii=False, indent=2)
            
            ic(f"📄 JSON报告已生成: {filepath}")
            return filepath
            
        except Exception as e:
            ic(f"❌ JSON报告生成失败: {str(e)}")
            return None
    
    def _generate_html_content(self, test_result: Dict) -> str:
        """生成HTML报告内容"""
        evaluation = test_result.get('evaluation', {})
        intercepted_messages = test_result.get('intercepted_messages', [])
        original_request = test_result.get('original_request', {})
        
        # 获取评估详情
        final_score = evaluation.get('final_score', 0)
        status = evaluation.get('status', 'unknown')
        basic_eval = evaluation.get('basic_evaluation', {})
        content_eval = evaluation.get('content_evaluation', {})
        function_eval = evaluation.get('function_evaluation', {})
        recommendations = evaluation.get('recommendations', [])
        
        # 状态颜色
        status_colors = {
            'excellent': '#28a745',
            'passed': '#17a2b8', 
            'warning': '#ffc107',
            'failed': '#dc3545',
            'error': '#6c757d'
        }
        status_color = status_colors.get(status, '#6c757d')
        
        # 生成HTML
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{bot_config.BOT_NAME} 测试报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        .content {{
            padding: 30px;
        }}
        .score-section {{
            text-align: center;
            margin-bottom: 40px;
        }}
        .score-circle {{
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: {status_color};
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2em;
            font-weight: bold;
        }}
        .status-badge {{
            display: inline-block;
            padding: 8px 16px;
            background: {status_color};
            color: white;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }}
        .section {{
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }}
        .section-header {{
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            font-size: 1.1em;
        }}
        .section-content {{
            padding: 20px;
        }}
        .metric {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f1f3f4;
        }}
        .metric:last-child {{
            border-bottom: none;
        }}
        .metric-label {{
            font-weight: 500;
        }}
        .metric-value {{
            font-weight: bold;
            color: #495057;
        }}
        .message-box {{
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }}
        .recommendations {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
        }}
        .recommendations ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        .recommendations li {{
            margin: 5px 0;
        }}
        .timestamp {{
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 {bot_config.BOT_NAME} 测试报告</h1>
            <p>会话ID: {test_result.get('test_session_id', 'N/A')}</p>
        </div>
        
        <div class="content">
            <!-- 总体评分 -->
            <div class="score-section">
                <div class="score-circle">{final_score}</div>
                <div class="status-badge">{status}</div>
            </div>
            
            <!-- 测试请求 -->
            <div class="section">
                <div class="section-header">📝 测试请求</div>
                <div class="section-content">
                    <div class="metric">
                        <span class="metric-label">消息类型</span>
                        <span class="metric-value">{original_request.get('message_type', 'N/A')}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">用户ID</span>
                        <span class="metric-value">{original_request.get('user_id', 'N/A')}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">测试消息</span>
                        <span class="metric-value">{original_request.get('message', 'N/A')}</span>
                    </div>
                </div>
            </div>
            
            <!-- 基础评估 -->
            <div class="section">
                <div class="section-header">⚡ 基础评估 (得分: {basic_eval.get('score', 0)})</div>
                <div class="section-content">
                    <div class="metric">
                        <span class="metric-label">有响应</span>
                        <span class="metric-value">{'✅' if basic_eval.get('details', {}).get('has_response') else '❌'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">响应时间</span>
                        <span class="metric-value">{basic_eval.get('details', {}).get('response_time', 0):.3f}秒</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">AI处理成功</span>
                        <span class="metric-value">{'✅' if basic_eval.get('details', {}).get('ai_processing_ok') else '❌'}</span>
                    </div>
                </div>
            </div>
            
            <!-- 内容评估 -->
            <div class="section">
                <div class="section-header">📄 内容评估 (得分: {content_eval.get('score', 0)})</div>
                <div class="section-content">
                    <div class="metric">
                        <span class="metric-label">内容长度</span>
                        <span class="metric-value">{content_eval.get('details', {}).get('content_length', 0)} 字符</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">结构化内容</span>
                        <span class="metric-value">{'✅' if content_eval.get('details', {}).get('has_structure') else '❌'}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">友好度</span>
                        <span class="metric-value">{'✅' if content_eval.get('details', {}).get('is_friendly') else '❌'}</span>
                    </div>
                </div>
            </div>
            
            <!-- 功能评估 -->
            <div class="section">
                <div class="section-header">🎯 功能评估 (得分: {function_eval.get('score', 0)})</div>
                <div class="section-content">
                    <div class="metric">
                        <span class="metric-label">请求类型</span>
                        <span class="metric-value">{function_eval.get('details', {}).get('request_type', 'unknown')}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">功能匹配</span>
                        <span class="metric-value">{'✅' if function_eval.get('details', {}).get('function_match') else '❌'}</span>
                    </div>
                </div>
            </div>
            
            <!-- 拦截的消息 -->
            <div class="section">
                <div class="section-header">🔒 拦截的消息 ({len(intercepted_messages)} 条)</div>
                <div class="section-content">
"""
        
        # 添加拦截的消息
        if intercepted_messages:
            for i, msg in enumerate(intercepted_messages[:3]):  # 只显示前3条
                msg_text = msg.get('text', '')
                msg_type = msg.get('type', 'unknown')
                html += f"""
                    <div class="message-box">
                        <strong>消息 {i+1} ({msg_type})</strong><br>
                        {msg_text[:500]}{'...' if len(msg_text) > 500 else ''}
                    </div>
"""
        else:
            html += '<p>没有拦截到任何消息</p>'
        
        html += '</div></div>'
        
        # 添加建议
        if recommendations:
            html += f"""
            <div class="section">
                <div class="section-header">💡 改进建议</div>
                <div class="section-content">
                    <div class="recommendations">
                        <ul>
"""
            for rec in recommendations:
                html += f'<li>{rec}</li>'
            
            html += """
                        </ul>
                    </div>
                </div>
            </div>
"""
        
        # 结尾
        html += f"""
            <div class="timestamp">
                报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </div>
        </div>
    </div>
</body>
</html>
"""
        
        return html 
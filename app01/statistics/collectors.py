#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据收集器模块
提供轻量级的数据收集功能，确保对系统性能影响最小
"""

import time
import logging
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from django.utils import timezone as django_timezone
from django.db import transaction
from django.conf import settings

logger = logging.getLogger(__name__)

class StatisticsCollector:
    """统计数据收集器基类"""
    
    def __init__(self):
        self._enabled = getattr(settings, 'STATISTICS_ENABLED', True)
        self._async_enabled = getattr(settings, 'STATISTICS_ASYNC', True)
        self._buffer_size = getattr(settings, 'STATISTICS_BUFFER_SIZE', 100)
        self._flush_interval = getattr(settings, 'STATISTICS_FLUSH_INTERVAL', 60)
        
        # 数据缓冲区
        self._buffer = []
        self._buffer_lock = threading.Lock()
        self._last_flush = time.time()
        
        logger.info(f"StatisticsCollector initialized: enabled={self._enabled}, async={self._async_enabled}")
    
    def is_enabled(self) -> bool:
        """检查统计收集是否启用"""
        return self._enabled
    
    def collect(self, data: Dict[str, Any], force_sync: bool = False) -> bool:
        """
        收集统计数据
        
        Args:
            data: 要收集的数据
            force_sync: 是否强制同步处理
            
        Returns:
            bool: 是否成功收集
        """
        if not self.is_enabled():
            return False
        
        try:
            # 添加时间戳
            data['collected_at'] = django_timezone.now()
            
            if force_sync or not self._async_enabled:
                return self._process_immediately(data)
            else:
                return self._add_to_buffer(data)
                
        except Exception as e:
            logger.error(f"Failed to collect statistics: {e}")
            return False
    
    def _add_to_buffer(self, data: Dict[str, Any]) -> bool:
        """添加数据到缓冲区"""
        try:
            with self._buffer_lock:
                self._buffer.append(data)
                
                # 检查是否需要刷新缓冲区
                should_flush = (
                    len(self._buffer) >= self._buffer_size or
                    time.time() - self._last_flush >= self._flush_interval
                )
                
                if should_flush:
                    self._flush_buffer()
                    
            return True
            
        except Exception as e:
            logger.error(f"Failed to add data to buffer: {e}")
            return False
    
    def _flush_buffer(self) -> bool:
        """刷新缓冲区数据到数据库"""
        if not self._buffer:
            return True
            
        try:
            # 复制缓冲区数据并清空
            buffer_copy = self._buffer.copy()
            self._buffer.clear()
            self._last_flush = time.time()
            
            # 异步处理数据
            if self._async_enabled:
                threading.Thread(
                    target=self._process_buffer_async,
                    args=(buffer_copy,),
                    daemon=True
                ).start()
            else:
                self._process_buffer_sync(buffer_copy)
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to flush buffer: {e}")
            return False
    
    def _process_buffer_async(self, data_list: List[Dict[str, Any]]):
        """异步处理缓冲区数据"""
        try:
            for data in data_list:
                self._process_immediately(data)
        except Exception as e:
            logger.error(f"Failed to process buffer async: {e}")
    
    def _process_buffer_sync(self, data_list: List[Dict[str, Any]]):
        """同步处理缓冲区数据"""
        try:
            with transaction.atomic():
                for data in data_list:
                    self._process_immediately(data)
        except Exception as e:
            logger.error(f"Failed to process buffer sync: {e}")
    
    def _process_immediately(self, data: Dict[str, Any]) -> bool:
        """立即处理数据 - 子类需要实现"""
        raise NotImplementedError("Subclasses must implement _process_immediately")
    
    def force_flush(self):
        """强制刷新所有缓冲区数据"""
        with self._buffer_lock:
            if self._buffer:
                self._flush_buffer()


class BotAccessEventCollector(StatisticsCollector):
    """机器人访问事件收集器"""
    
    def collect_access_event(self, event_data: Dict[str, Any]) -> bool:
        """
        收集机器人访问事件
        
        Args:
            event_data: SeaTalk事件数据
            
        Returns:
            bool: 是否成功收集
        """
        try:
            # 提取关键信息
            processed_data = {
                'type': 'bot_access_event',
                'event_id': event_data.get('event_id'),
                'event_type': event_data.get('event_type'),
                'timestamp': event_data.get('timestamp'),
                'user_id': self._extract_user_id(event_data),
                'employee_code': self._extract_employee_code(event_data),
                'email': self._extract_email(event_data),
                'group_id': self._extract_group_id(event_data),
                'group_name': self._extract_group_name(event_data),
                'app_id': event_data.get('app_id'),
                'raw_data': event_data
            }
            
            return self.collect(processed_data)
            
        except Exception as e:
            logger.error(f"Failed to collect access event: {e}")
            return False
    
    def _extract_user_id(self, event_data: Dict[str, Any]) -> Optional[str]:
        """提取用户ID"""
        event = event_data.get('event', {})
        return (
            event.get('seatalk_id') or
            event.get('message', {}).get('sender', {}).get('seatalk_id') or
            event.get('user', {}).get('seatalk_id')
        )
    
    def _extract_employee_code(self, event_data: Dict[str, Any]) -> Optional[str]:
        """提取员工代码"""
        event = event_data.get('event', {})
        return (
            event.get('employee_code') or
            event.get('message', {}).get('sender', {}).get('employee_code') or
            event.get('user', {}).get('employee_code')
        )
    
    def _extract_email(self, event_data: Dict[str, Any]) -> Optional[str]:
        """提取邮箱"""
        event = event_data.get('event', {})
        return (
            event.get('email') or
            event.get('message', {}).get('sender', {}).get('email') or
            event.get('user', {}).get('email')
        )
    
    def _extract_group_id(self, event_data: Dict[str, Any]) -> Optional[str]:
        """提取群组ID"""
        event = event_data.get('event', {})
        return (
            event.get('group_id') or
            event.get('group', {}).get('group_id')
        )
    
    def _extract_group_name(self, event_data: Dict[str, Any]) -> Optional[str]:
        """提取群组名称"""
        event = event_data.get('event', {})
        return (
            event.get('group_name') or
            event.get('group', {}).get('group_name')
        )
    
    def _process_immediately(self, data: Dict[str, Any]) -> bool:
        """立即处理访问事件数据"""
        try:
            from app01.models import BotAccessEvent
            
            # 转换时间戳
            event_time = django_timezone.now()
            if data.get('timestamp'):
                try:
                    event_time = datetime.fromtimestamp(
                        data['timestamp'], 
                        tz=timezone.utc
                    )
                except (ValueError, TypeError):
                    pass
            
            # 创建访问事件记录
            BotAccessEvent.objects.create(
                event_id=data.get('event_id', ''),
                event_type=data.get('event_type', ''),
                user_id=data.get('user_id', ''),
                employee_code=data.get('employee_code'),
                email=data.get('email'),
                group_id=data.get('group_id'),
                group_name=data.get('group_name'),
                app_id=data.get('app_id'),
                timestamp=data.get('timestamp', 0),
                event_time=event_time,
                metadata=data.get('raw_data', {})
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to process access event: {e}")
            return False


class CommandExecutionCollector(StatisticsCollector):
    """指令执行记录收集器"""
    
    def start_command_tracking(self, context_data: Dict[str, Any]) -> Optional[str]:
        """
        开始跟踪指令执行
        
        Args:
            context_data: 指令上下文数据
            
        Returns:
            Optional[str]: 执行记录ID
        """
        try:
            from app01.models import CommandExecutionRecord
            import uuid
            
            execution_id = str(uuid.uuid4())
            
            record = CommandExecutionRecord.objects.create(
                execution_id=execution_id,
                user_id=context_data.get('user_id', ''),
                user_email=context_data.get('user_email'),
                employee_code=context_data.get('employee_code'),
                group_id=context_data.get('group_id'),
                command_type=context_data.get('command_type', 'unknown'),
                raw_input=context_data.get('raw_input', ''),
                intent=context_data.get('intent'),
                start_time=django_timezone.now(),
                session_id=context_data.get('session_id'),
                thread_id=context_data.get('thread_id'),
                message_id=context_data.get('message_id')
            )
            
            return execution_id
            
        except Exception as e:
            logger.error(f"Failed to start command tracking: {e}")
            return None
    
    def finish_command_tracking(self, execution_id: str, result_data: Dict[str, Any]) -> bool:
        """
        完成指令执行跟踪
        
        Args:
            execution_id: 执行记录ID
            result_data: 执行结果数据
            
        Returns:
            bool: 是否成功更新
        """
        try:
            from app01.models import CommandExecutionRecord
            
            record = CommandExecutionRecord.objects.get(execution_id=execution_id)
            
            # 更新执行结果
            record.end_time = django_timezone.now()
            record.success = result_data.get('success', False)
            record.response_content = result_data.get('response_content', '')[:5000]  # 限制长度
            record.error_message = result_data.get('error_message', '')[:1000]
            record.processed_command = result_data.get('processed_command', '')
            record.api_calls = result_data.get('api_calls', [])
            record.database_queries = result_data.get('database_queries', 0)
            record.external_service_calls = result_data.get('external_service_calls', {})
            
            record.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to finish command tracking: {e}")
            return False
    
    def _process_immediately(self, data: Dict[str, Any]) -> bool:
        """立即处理指令执行数据"""
        # 这个方法主要用于缓冲区处理，指令跟踪使用专门的方法
        return True


# 全局收集器实例
bot_access_collector = BotAccessEventCollector()
command_execution_collector = CommandExecutionCollector()

# 初始化日志
import logging
logger = logging.getLogger(__name__)
logger.info("Statistics collectors initialized successfully")

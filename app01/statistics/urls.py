#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计API URL配置
"""

from django.urls import path
from . import views

app_name = 'statistics'

urlpatterns = [
    # 统计面板页面
    path('dashboard/', views.statistics_dashboard_page, name='statistics_dashboard_page'),

    # 实时监控API
    path('realtime/dashboard/', views.realtime_dashboard, name='realtime_dashboard'),
    path('realtime/command-trends/', views.command_trends, name='command_trends'),
    path('realtime/user-activity/', views.user_activity_stats, name='user_activity_stats'),
    path('realtime/activity-logs/', views.activity_logs, name='activity_logs'),

    # 性能分析API
    path('performance/metrics/', views.performance_metrics, name='performance_metrics'),
    path('performance/slow-queries/', views.slow_queries_analysis, name='slow_queries_analysis'),

    # 定时任务监控API
    path('cronjobs/status/', views.cronjob_status, name='cronjob_status'),
    path('cronjobs/health/', views.cronjob_health_report, name='cronjob_health_report'),

    # 报表API
    path('reports/daily/', views.daily_report, name='daily_report'),
    path('reports/weekly/', views.weekly_report, name='weekly_report'),
    path('reports/alerts/', views.alert_report, name='alert_report'),

    # 数据查询API
    path('data/command-records/', views.command_execution_records, name='command_execution_records'),
    path('data/system-health/', views.system_health_status, name='system_health_status'),
]

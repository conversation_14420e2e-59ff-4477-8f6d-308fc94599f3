#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计系统定时任务
注意：这些任务仅供参考，不会自动启动，需要手动配置crontab
"""

import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.management import call_command
from .tasks import (
    generate_user_activity_summary,
    generate_system_health_snapshot,
    cleanup_old_statistics,
    batch_process_statistics
)
from .decorators import track_cronjob_execution

logger = logging.getLogger(__name__)


@track_cronjob_execution('statistics_hourly_summary')
def hourly_statistics_summary():
    """
    每小时统计汇总任务
    建议crontab配置: 0 * * * * /path/to/python /path/to/manage.py run_statistics_job hourly_summary
    """
    try:
        logger.info("开始执行每小时统计汇总")
        
        # 生成小时级用户活动汇总
        success = generate_user_activity_summary(period_type='hourly')
        if not success:
            logger.error("小时级用户活动汇总生成失败")
            return False
        
        # 生成系统健康快照
        success = generate_system_health_snapshot()
        if not success:
            logger.error("系统健康快照生成失败")
            return False
        
        logger.info("每小时统计汇总执行完成")
        return True
        
    except Exception as e:
        logger.error(f"每小时统计汇总执行失败: {e}")
        return False


@track_cronjob_execution('statistics_daily_summary')
def daily_statistics_summary():
    """
    每日统计汇总任务
    建议crontab配置: 0 1 * * * /path/to/python /path/to/manage.py run_statistics_job daily_summary
    """
    try:
        logger.info("开始执行每日统计汇总")
        
        # 生成日级用户活动汇总
        success = generate_user_activity_summary(period_type='daily')
        if not success:
            logger.error("日级用户活动汇总生成失败")
            return False
        
        # 生成日报
        from .reports import daily_report_generator
        yesterday = timezone.now().date() - timedelta(days=1)
        report = daily_report_generator.generate_daily_report(yesterday)
        
        if 'error' in report:
            logger.error(f"日报生成失败: {report['error']}")
        else:
            logger.info(f"日报生成成功: {report['summary']}")
        
        logger.info("每日统计汇总执行完成")
        return True
        
    except Exception as e:
        logger.error(f"每日统计汇总执行失败: {e}")
        return False


@track_cronjob_execution('statistics_weekly_summary')
def weekly_statistics_summary():
    """
    每周统计汇总任务
    建议crontab配置: 0 2 * * 1 /path/to/python /path/to/manage.py run_statistics_job weekly_summary
    """
    try:
        logger.info("开始执行每周统计汇总")
        
        # 生成周级用户活动汇总
        success = generate_user_activity_summary(period_type='weekly')
        if not success:
            logger.error("周级用户活动汇总生成失败")
            return False
        
        # 生成周报
        from .reports import weekly_report_generator
        now = timezone.now()
        last_monday = (now - timedelta(days=now.weekday() + 7)).date()
        report = weekly_report_generator.generate_weekly_report(last_monday)
        
        if 'error' in report:
            logger.error(f"周报生成失败: {report['error']}")
        else:
            logger.info(f"周报生成成功: {report['summary']}")
        
        logger.info("每周统计汇总执行完成")
        return True
        
    except Exception as e:
        logger.error(f"每周统计汇总执行失败: {e}")
        return False


@track_cronjob_execution('statistics_data_cleanup')
def statistics_data_cleanup():
    """
    数据清理任务
    建议crontab配置: 0 3 * * 0 /path/to/python /path/to/manage.py run_statistics_job data_cleanup
    """
    try:
        logger.info("开始执行统计数据清理")
        
        # 清理旧的统计数据
        success = cleanup_old_statistics(days_to_keep=90)
        if not success:
            logger.error("统计数据清理失败")
            return False
        
        logger.info("统计数据清理执行完成")
        return True
        
    except Exception as e:
        logger.error(f"统计数据清理执行失败: {e}")
        return False


@track_cronjob_execution('statistics_health_check')
def statistics_health_check():
    """
    系统健康检查任务
    建议crontab配置: */15 * * * * /path/to/python /path/to/manage.py run_statistics_job health_check
    """
    try:
        logger.info("开始执行系统健康检查")
        
        from .reports import alert_report_generator
        
        # 生成告警报告
        alert_report = alert_report_generator.generate_alert_report()
        
        if 'error' in alert_report:
            logger.error(f"告警报告生成失败: {alert_report['error']}")
            return False
        
        # 检查是否有严重告警
        critical_alerts = [
            alert for alert in alert_report.get('alerts', [])
            if alert.get('severity') == 'critical'
        ]
        
        if critical_alerts:
            logger.warning(f"发现 {len(critical_alerts)} 个严重告警")
            # 这里可以添加发送通知的逻辑
            # send_critical_alerts_notification(critical_alerts)
        
        logger.info("系统健康检查执行完成")
        return True
        
    except Exception as e:
        logger.error(f"系统健康检查执行失败: {e}")
        return False


@track_cronjob_execution('statistics_batch_process')
def statistics_batch_process():
    """
    批量处理统计数据
    建议crontab配置: 0 4 * * * /path/to/python /path/to/manage.py run_statistics_job batch_process
    """
    try:
        logger.info("开始执行批量统计处理")
        
        success = batch_process_statistics()
        if not success:
            logger.error("批量统计处理失败")
            return False
        
        logger.info("批量统计处理执行完成")
        return True
        
    except Exception as e:
        logger.error(f"批量统计处理执行失败: {e}")
        return False


# 任务映射字典
STATISTICS_JOBS = {
    'hourly_summary': hourly_statistics_summary,
    'daily_summary': daily_statistics_summary,
    'weekly_summary': weekly_statistics_summary,
    'data_cleanup': statistics_data_cleanup,
    'health_check': statistics_health_check,
    'batch_process': statistics_batch_process,
}


def run_statistics_job(job_name):
    """
    运行指定的统计任务
    
    Args:
        job_name: 任务名称
        
    Returns:
        bool: 是否执行成功
    """
    if job_name not in STATISTICS_JOBS:
        logger.error(f"未知的统计任务: {job_name}")
        return False
    
    try:
        job_function = STATISTICS_JOBS[job_name]
        return job_function()
        
    except Exception as e:
        logger.error(f"统计任务 {job_name} 执行失败: {e}")
        return False


def get_available_jobs():
    """获取可用的统计任务列表"""
    return list(STATISTICS_JOBS.keys())


def get_job_info():
    """获取任务信息"""
    return {
        'hourly_summary': {
            'name': '每小时统计汇总',
            'description': '生成小时级用户活动汇总和系统健康快照',
            'crontab': '0 * * * *',
            'frequency': '每小时'
        },
        'daily_summary': {
            'name': '每日统计汇总',
            'description': '生成日级用户活动汇总和日报',
            'crontab': '0 1 * * *',
            'frequency': '每日凌晨1点'
        },
        'weekly_summary': {
            'name': '每周统计汇总',
            'description': '生成周级用户活动汇总和周报',
            'crontab': '0 2 * * 1',
            'frequency': '每周一凌晨2点'
        },
        'data_cleanup': {
            'name': '数据清理',
            'description': '清理90天前的历史统计数据',
            'crontab': '0 3 * * 0',
            'frequency': '每周日凌晨3点'
        },
        'health_check': {
            'name': '系统健康检查',
            'description': '检查系统健康状态并生成告警',
            'crontab': '*/15 * * * *',
            'frequency': '每15分钟'
        },
        'batch_process': {
            'name': '批量处理',
            'description': '批量处理所有统计数据',
            'crontab': '0 4 * * *',
            'frequency': '每日凌晨4点'
        }
    }


@track_cronjob_execution('statistics_system_health_check')
def system_health_check():
    """
    系统健康检查任务
    建议crontab配置: */15 * * * * /path/to/python /path/to/manage.py run_statistics_job health_check
    """
    try:
        logger.info("开始执行系统健康检查")

        # 生成系统健康快照
        success = generate_system_health_snapshot()
        if not success:
            logger.error("系统健康快照生成失败")
            return False

        logger.info("系统健康检查执行完成")
        return True

    except Exception as e:
        logger.error(f"系统健康检查执行失败: {e}")
        return False


@track_cronjob_execution('statistics_monthly_summary')
def monthly_statistics_summary():
    """
    每月统计汇总任务
    建议crontab配置: 0 3 1 * * /path/to/python /path/to/manage.py run_statistics_job monthly_summary
    """
    try:
        logger.info("开始执行每月统计汇总")

        # 生成月级用户活动汇总
        success = generate_user_activity_summary(period_type='monthly')
        if not success:
            logger.error("月级用户活动汇总生成失败")
            return False

        # 生成月报
        from .reports import weekly_report_generator  # 复用周报生成器
        last_month = timezone.now().date().replace(day=1) - timedelta(days=1)
        report = weekly_report_generator.generate_weekly_report(last_month)

        if 'error' in report:
            logger.error(f"月报生成失败: {report['error']}")
        else:
            logger.info(f"月报生成成功: {report['summary']}")

        logger.info("每月统计汇总执行完成")
        return True

    except Exception as e:
        logger.error(f"每月统计汇总执行失败: {e}")
        return False


@track_cronjob_execution('statistics_cleanup_old_data')
def cleanup_old_statistics_job():
    """
    清理旧统计数据任务
    建议crontab配置: 0 4 * * 0 /path/to/python /path/to/manage.py run_statistics_job data_cleanup
    """
    try:
        logger.info("开始执行数据清理")

        # 清理90天前的数据
        success = cleanup_old_statistics(days_to_keep=90)
        if not success:
            logger.error("数据清理失败")
            return False

        logger.info("数据清理执行完成")
        return True

    except Exception as e:
        logger.error(f"数据清理执行失败: {e}")
        return False

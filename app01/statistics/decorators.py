#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计装饰器模块
提供简单易用的装饰器来自动收集统计数据
"""

import time
import logging
from functools import wraps
from typing import Dict, Any, Optional, Callable
from django.utils import timezone
from .collectors import command_execution_collector

logger = logging.getLogger(__name__)


def track_command_execution(command_type: str = 'unknown', intent: str = None):
    """
    指令执行跟踪装饰器
    
    Args:
        command_type: 指令类型
        intent: 指令意图
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 尝试从参数中提取上下文信息
            context = None
            execution_id = None
            
            try:
                # 查找MessageContext对象
                for arg in args:
                    if hasattr(arg, 'seatalk_id') and hasattr(arg, 'text'):
                        context = arg
                        break
                
                if not context and 'context' in kwargs:
                    context = kwargs['context']
                
                # 开始跟踪
                if context:
                    context_data = {
                        'user_id': getattr(context, 'seatalk_id', ''),
                        'user_email': getattr(context, 'email', None),
                        'employee_code': getattr(context, 'employee_code', None),
                        'group_id': getattr(context, 'group_id', None),
                        'command_type': command_type,
                        'raw_input': getattr(context, 'text', ''),
                        'intent': intent,
                        'session_id': getattr(context, 'session_id', None),
                        'thread_id': getattr(context, 'thread_id', None),
                        'message_id': getattr(context, 'message_id', None)
                    }
                    
                    execution_id = command_execution_collector.start_command_tracking(context_data)
                
                # 执行原函数
                start_time = time.time()
                result = func(*args, **kwargs)
                processing_time = time.time() - start_time
                
                # 完成跟踪
                if execution_id:
                    result_data = {
                        'success': True,
                        'response_content': str(result) if result else '',
                        'processing_time': processing_time
                    }
                    command_execution_collector.finish_command_tracking(execution_id, result_data)
                
                return result
                
            except Exception as e:
                # 记录错误
                if execution_id:
                    result_data = {
                        'success': False,
                        'error_message': str(e),
                        'processing_time': time.time() - start_time if 'start_time' in locals() else 0
                    }
                    command_execution_collector.finish_command_tracking(execution_id, result_data)
                
                # 重新抛出异常
                raise
        
        return wrapper
    return decorator


def track_cronjob_execution(job_name: str):
    """
    定时任务执行跟踪装饰器
    
    Args:
        job_name: 任务名称
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            from app01.models import CronJobExecutionMonitor
            import uuid
            
            # 创建监控记录
            monitor = CronJobExecutionMonitor.objects.create(
                job_name=job_name,
                execution_id=uuid.uuid4(),
                start_time=timezone.now(),
                status='running'
            )
            
            start_time = time.time()
            
            try:
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 更新成功状态
                monitor.end_time = timezone.now()
                monitor.status = 'success'
                monitor.success = True
                monitor.output = str(result)[:1000] if result else None
                monitor.save()
                
                return result
                
            except Exception as e:
                # 更新失败状态
                monitor.end_time = timezone.now()
                monitor.status = 'failed'
                monitor.success = False
                monitor.error_message = str(e)[:1000]
                monitor.save()
                
                # 重新抛出异常
                raise
            
            finally:
                # 确保记录被保存
                if not monitor.end_time:
                    monitor.end_time = timezone.now()
                    monitor.status = 'cancelled'
                    monitor.save()
        
        return wrapper
    return decorator


def track_api_performance(endpoint: str = None):
    """
    API性能跟踪装饰器
    
    Args:
        endpoint: API端点名称
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            from app01.models import SystemPerformanceMetrics
            
            start_time = time.time()
            endpoint_name = endpoint or func.__name__
            
            try:
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 记录性能指标
                response_time = time.time() - start_time
                
                SystemPerformanceMetrics.objects.create(
                    metric_type='api_response',
                    metric_time=timezone.now(),
                    api_endpoint=endpoint_name,
                    response_time=response_time,
                    status_code=200  # 假设成功
                )
                
                return result
                
            except Exception as e:
                # 记录错误性能指标
                response_time = time.time() - start_time
                
                SystemPerformanceMetrics.objects.create(
                    metric_type='api_response',
                    metric_time=timezone.now(),
                    api_endpoint=endpoint_name,
                    response_time=response_time,
                    status_code=500,  # 假设服务器错误
                    error_count=1
                )
                
                # 重新抛出异常
                raise
        
        return wrapper
    return decorator


class CommandTracker:
    """指令跟踪上下文管理器"""
    
    def __init__(self, context_data: Dict[str, Any]):
        self.context_data = context_data
        self.execution_id = None
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.execution_id = command_execution_collector.start_command_tracking(self.context_data)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.execution_id:
            processing_time = time.time() - self.start_time if self.start_time else 0
            
            result_data = {
                'success': exc_type is None,
                'processing_time': processing_time
            }
            
            if exc_type:
                result_data['error_message'] = str(exc_val)
            
            command_execution_collector.finish_command_tracking(self.execution_id, result_data)
    
    def update_result(self, response_content: str = None, processed_command: str = None, 
                     api_calls: list = None, database_queries: int = 0, 
                     external_service_calls: dict = None):
        """更新执行结果信息"""
        if self.execution_id:
            result_data = {
                'response_content': response_content,
                'processed_command': processed_command,
                'api_calls': api_calls or [],
                'database_queries': database_queries,
                'external_service_calls': external_service_calls or {}
            }
            
            # 这里可以暂存数据，在__exit__时一起更新
            self._pending_updates = result_data


def create_command_tracker(user_id: str, command_type: str, raw_input: str, 
                          user_email: str = None, employee_code: str = None,
                          group_id: str = None, intent: str = None) -> CommandTracker:
    """
    创建指令跟踪器
    
    Args:
        user_id: 用户ID
        command_type: 指令类型
        raw_input: 原始输入
        user_email: 用户邮箱
        employee_code: 员工代码
        group_id: 群组ID
        intent: 指令意图
        
    Returns:
        CommandTracker: 指令跟踪器实例
    """
    context_data = {
        'user_id': user_id,
        'user_email': user_email,
        'employee_code': employee_code,
        'group_id': group_id,
        'command_type': command_type,
        'raw_input': raw_input,
        'intent': intent
    }
    
    return CommandTracker(context_data)


# 便捷函数
def track_bot_access_event(event_data: Dict[str, Any]) -> bool:
    """
    跟踪机器人访问事件的便捷函数
    
    Args:
        event_data: 事件数据
        
    Returns:
        bool: 是否成功跟踪
    """
    from .collectors import bot_access_collector
    return bot_access_collector.collect_access_event(event_data)


def record_system_metric(metric_type: str, metric_data: Dict[str, Any]) -> bool:
    """
    记录系统指标的便捷函数
    
    Args:
        metric_type: 指标类型
        metric_data: 指标数据
        
    Returns:
        bool: 是否成功记录
    """
    try:
        from app01.models import SystemPerformanceMetrics
        
        SystemPerformanceMetrics.objects.create(
            metric_type=metric_type,
            metric_time=timezone.now(),
            **metric_data
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to record system metric: {e}")
        return False

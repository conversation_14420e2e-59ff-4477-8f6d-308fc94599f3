#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计中间件模块
自动收集HTTP请求的性能和使用数据
"""

import time
import logging
import threading
from typing import Dict, Any
from django.utils import timezone
from django.conf import settings
from django.http import HttpRequest, HttpResponse
from .decorators import record_system_metric

logger = logging.getLogger(__name__)


class StatisticsMiddleware:
    """统计数据收集中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.enabled = getattr(settings, 'STATISTICS_MIDDLEWARE_ENABLED', True)
        self.collect_all_requests = getattr(settings, 'STATISTICS_COLLECT_ALL_REQUESTS', False)
        self.api_paths = getattr(settings, 'STATISTICS_API_PATHS', ['/api/'])
        
        # 性能统计
        self._request_count = 0
        self._total_response_time = 0.0
        self._lock = threading.Lock()
        
        logger.info(f"StatisticsMiddleware initialized: enabled={self.enabled}")
    
    def __call__(self, request: HttpRequest) -> HttpResponse:
        if not self.enabled:
            return self.get_response(request)
        
        # 检查是否需要收集此请求的统计数据
        should_collect = self._should_collect_request(request)
        
        if not should_collect:
            return self.get_response(request)
        
        # 开始计时
        start_time = time.time()
        
        # 收集请求信息
        request_data = self._extract_request_data(request)
        
        try:
            # 处理请求
            response = self.get_response(request)
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 收集响应信息
            response_data = self._extract_response_data(response, response_time)
            
            # 异步记录统计数据
            self._record_request_stats(request_data, response_data)
            
            # 更新内存统计
            self._update_memory_stats(response_time)
            
            return response
            
        except Exception as e:
            # 记录错误统计
            response_time = time.time() - start_time
            error_data = {
                'status_code': 500,
                'response_time': response_time,
                'error_message': str(e)
            }
            
            self._record_request_stats(request_data, error_data)
            
            # 重新抛出异常
            raise
    
    def _should_collect_request(self, request: HttpRequest) -> bool:
        """判断是否应该收集此请求的统计数据"""
        if self.collect_all_requests:
            return True
        
        # 检查是否是API请求
        path = request.path
        for api_path in self.api_paths:
            if path.startswith(api_path):
                return True
        
        return False
    
    def _extract_request_data(self, request: HttpRequest) -> Dict[str, Any]:
        """提取请求数据"""
        return {
            'method': request.method,
            'path': request.path,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'remote_addr': self._get_client_ip(request),
            'content_type': request.content_type,
            'content_length': len(request.body) if hasattr(request, 'body') else 0,
            'timestamp': timezone.now()
        }
    
    def _extract_response_data(self, response: HttpResponse, response_time: float) -> Dict[str, Any]:
        """提取响应数据"""
        return {
            'status_code': response.status_code,
            'response_time': response_time,
            'content_length': len(response.content) if hasattr(response, 'content') else 0,
            'content_type': response.get('Content-Type', '')
        }
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'
    
    def _record_request_stats(self, request_data: Dict[str, Any], response_data: Dict[str, Any]):
        """记录请求统计数据"""
        try:
            # 合并请求和响应数据
            metric_data = {
                'api_endpoint': request_data['path'],
                'response_time': response_data['response_time'],
                'status_code': response_data.get('status_code'),
                'metadata': {
                    'method': request_data['method'],
                    'user_agent': request_data['user_agent'][:200],  # 限制长度
                    'remote_addr': request_data['remote_addr'],
                    'request_size': request_data['content_length'],
                    'response_size': response_data.get('content_length', 0)
                }
            }
            
            # 异步记录
            threading.Thread(
                target=record_system_metric,
                args=('api_response', metric_data),
                daemon=True
            ).start()
            
        except Exception as e:
            logger.error(f"Failed to record request stats: {e}")
    
    def _update_memory_stats(self, response_time: float):
        """更新内存中的统计数据"""
        try:
            with self._lock:
                self._request_count += 1
                self._total_response_time += response_time
        except Exception as e:
            logger.error(f"Failed to update memory stats: {e}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存中的统计数据"""
        with self._lock:
            if self._request_count > 0:
                avg_response_time = self._total_response_time / self._request_count
            else:
                avg_response_time = 0.0
            
            return {
                'request_count': self._request_count,
                'total_response_time': self._total_response_time,
                'avg_response_time': avg_response_time
            }
    
    def reset_memory_stats(self):
        """重置内存统计数据"""
        with self._lock:
            self._request_count = 0
            self._total_response_time = 0.0


class DatabaseQueryCountMiddleware:
    """数据库查询计数中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.enabled = getattr(settings, 'STATISTICS_DB_QUERY_TRACKING', True)
    
    def __call__(self, request: HttpRequest) -> HttpResponse:
        if not self.enabled:
            return self.get_response(request)
        
        # 获取初始查询数量
        from django.db import connection
        initial_queries = len(connection.queries)
        
        try:
            response = self.get_response(request)
            
            # 计算查询数量
            final_queries = len(connection.queries)
            query_count = final_queries - initial_queries
            
            # 记录查询统计
            if query_count > 0:
                self._record_query_stats(request, query_count)
            
            return response
            
        except Exception as e:
            # 即使出错也记录查询数量
            final_queries = len(connection.queries)
            query_count = final_queries - initial_queries
            
            if query_count > 0:
                self._record_query_stats(request, query_count, error=str(e))
            
            raise
    
    def _record_query_stats(self, request: HttpRequest, query_count: int, error: str = None):
        """记录数据库查询统计"""
        try:
            metric_data = {
                'api_endpoint': request.path,
                'database_queries': query_count,
                'metadata': {
                    'method': request.method,
                    'error': error
                }
            }
            
            # 异步记录
            threading.Thread(
                target=record_system_metric,
                args=('database_query', metric_data),
                daemon=True
            ).start()
            
        except Exception as e:
            logger.error(f"Failed to record query stats: {e}")


class UserActivityTrackingMiddleware:
    """用户活动跟踪中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.enabled = getattr(settings, 'STATISTICS_USER_ACTIVITY_TRACKING', True)
        self.session_timeout = getattr(settings, 'STATISTICS_SESSION_TIMEOUT', 1800)  # 30分钟
    
    def __call__(self, request: HttpRequest) -> HttpResponse:
        if not self.enabled:
            return self.get_response(request)
        
        # 处理请求
        response = self.get_response(request)
        
        # 跟踪用户活动
        self._track_user_activity(request, response)
        
        return response
    
    def _track_user_activity(self, request: HttpRequest, response: HttpResponse):
        """跟踪用户活动"""
        try:
            # 从请求中提取用户信息
            user_info = self._extract_user_info(request)
            
            if user_info:
                # 异步记录用户活动
                threading.Thread(
                    target=self._record_user_activity,
                    args=(user_info, request, response),
                    daemon=True
                ).start()
                
        except Exception as e:
            logger.error(f"Failed to track user activity: {e}")
    
    def _extract_user_info(self, request: HttpRequest) -> Dict[str, Any]:
        """从请求中提取用户信息"""
        # 这里可以根据实际情况提取用户信息
        # 例如从session、JWT token或其他认证机制中获取
        
        user_info = {}
        
        # 尝试从session获取用户信息
        if hasattr(request, 'session') and request.session:
            user_info.update({
                'session_key': request.session.session_key,
                'user_id': request.session.get('user_id'),
                'employee_code': request.session.get('employee_code')
            })
        
        # 尝试从请求头获取用户信息
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if user_agent:
            user_info['user_agent'] = user_agent[:200]
        
        return user_info if any(user_info.values()) else None
    
    def _record_user_activity(self, user_info: Dict[str, Any], 
                            request: HttpRequest, response: HttpResponse):
        """记录用户活动"""
        try:
            activity_data = {
                'user_id': user_info.get('user_id', 'anonymous'),
                'session_key': user_info.get('session_key'),
                'path': request.path,
                'method': request.method,
                'status_code': response.status_code,
                'timestamp': timezone.now(),
                'user_agent': user_info.get('user_agent', '')
            }
            
            # 这里可以将活动数据存储到数据库或缓存中
            # 为了性能考虑，可以使用批量插入或缓存机制
            
        except Exception as e:
            logger.error(f"Failed to record user activity: {e}")


# 全局中间件实例（如果需要的话）
statistics_middleware = None
db_query_middleware = None
user_activity_middleware = None

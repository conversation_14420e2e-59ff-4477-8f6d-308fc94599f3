#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计异步任务模块
处理统计数据的异步任务，包括数据聚合、清理和报告生成
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from django.utils import timezone
from django.db import transaction
from django.db.models import Count, Avg, Sum, Max, Min, Q
from django.conf import settings

logger = logging.getLogger(__name__)


def collect_bot_access_event_async(event_data: Dict[str, Any]) -> bool:
    """
    异步收集机器人访问事件
    
    Args:
        event_data: 事件数据
        
    Returns:
        bool: 是否成功处理
    """
    try:
        from app01.models import BotAccessEvent
        from .collectors import bot_access_collector
        
        return bot_access_collector.collect_access_event(event_data)
        
    except Exception as e:
        logger.error(f"Failed to collect bot access event async: {e}")
        return False


def collect_performance_stats_async(stats_data: Dict[str, Any]) -> bool:
    """
    异步收集性能统计数据
    
    Args:
        stats_data: 统计数据
        
    Returns:
        bool: 是否成功处理
    """
    try:
        from app01.models import SystemPerformanceMetrics
        
        SystemPerformanceMetrics.objects.create(**stats_data)
        return True
        
    except Exception as e:
        logger.error(f"Failed to collect performance stats async: {e}")
        return False


def generate_user_activity_summary(user_id: str = None, period_type: str = 'daily') -> bool:
    """
    生成用户活动汇总
    
    Args:
        user_id: 用户ID，如果为None则处理所有用户
        period_type: 汇总周期类型 (hourly, daily, weekly, monthly)
        
    Returns:
        bool: 是否成功生成
    """
    try:
        from app01.models import CommandExecutionRecord, UserActivitySummary
        
        # 确定时间范围
        now = timezone.now()
        if period_type == 'hourly':
            period_start = now.replace(minute=0, second=0, microsecond=0)
            period_end = period_start + timedelta(hours=1)
        elif period_type == 'daily':
            period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            period_end = period_start + timedelta(days=1)
        elif period_type == 'weekly':
            days_since_monday = now.weekday()
            period_start = (now - timedelta(days=days_since_monday)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            period_end = period_start + timedelta(weeks=1)
        elif period_type == 'monthly':
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month = period_start + timedelta(days=32)
            period_end = next_month.replace(day=1)
        else:
            logger.error(f"Invalid period_type: {period_type}")
            return False
        
        # 构建查询条件
        query_filter = Q(created_at__gte=period_start, created_at__lt=period_end)
        if user_id:
            query_filter &= Q(user_id=user_id)
        
        # 获取用户列表
        if user_id:
            user_ids = [user_id]
        else:
            user_ids = CommandExecutionRecord.objects.filter(
                query_filter
            ).values_list('user_id', flat=True).distinct()
        
        # 为每个用户生成汇总
        for uid in user_ids:
            user_filter = query_filter & Q(user_id=uid)
            
            # 获取用户的执行记录
            user_records = CommandExecutionRecord.objects.filter(user_filter)
            
            if not user_records.exists():
                continue
            
            # 计算统计数据
            stats = user_records.aggregate(
                total_commands=Count('id'),
                successful_commands=Count('id', filter=Q(success=True)),
                failed_commands=Count('id', filter=Q(success=False)),
                avg_response_time=Avg('processing_time'),
                max_response_time=Max('processing_time'),
                min_response_time=Min('processing_time'),
                ai_queries=Count('id', filter=Q(command_type='ai_query')),
                jira_operations=Count('id', filter=Q(command_type__in=['jira_query', 'jira_write'])),
                mr_checks=Count('id', filter=Q(command_type='mr_check')),
                schedule_operations=Count('id', filter=Q(command_type='schedule_management')),
                group_interactions=Count('id', filter=Q(group_id__isnull=False)),
                private_interactions=Count('id', filter=Q(group_id__isnull=True))
            )
            
            # 获取用户信息
            first_record = user_records.first()
            user_email = first_record.user_email
            employee_code = first_record.employee_code
            
            # 计算错误率
            error_rate = 0.0
            if stats['total_commands'] > 0:
                error_rate = (stats['failed_commands'] / stats['total_commands']) * 100
            
            # 创建或更新汇总记录
            summary, created = UserActivitySummary.objects.update_or_create(
                user_id=uid,
                period_type=period_type,
                period_start=period_start,
                defaults={
                    'user_email': user_email,
                    'employee_code': employee_code,
                    'period_end': period_end,
                    'total_commands': stats['total_commands'],
                    'successful_commands': stats['successful_commands'],
                    'failed_commands': stats['failed_commands'],
                    'ai_queries': stats['ai_queries'],
                    'jira_operations': stats['jira_operations'],
                    'mr_checks': stats['mr_checks'],
                    'schedule_operations': stats['schedule_operations'],
                    'avg_response_time': stats['avg_response_time'] or 0.0,
                    'max_response_time': stats['max_response_time'] or 0.0,
                    'min_response_time': stats['min_response_time'] or 0.0,
                    'group_interactions': stats['group_interactions'],
                    'private_interactions': stats['private_interactions'],
                    'error_rate': error_rate
                }
            )
            
            logger.info(f"Generated activity summary for user {uid}: {stats['total_commands']} commands")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to generate user activity summary: {e}")
        return False


def generate_system_health_snapshot() -> bool:
    """
    生成系统健康状况快照
    
    Returns:
        bool: 是否成功生成
    """
    try:
        from app01.models import (
            SystemHealthSnapshot, CommandExecutionRecord, 
            BotAccessEvent, CronJobExecutionMonitor
        )
        
        now = timezone.now()
        today = now.date()
        one_hour_ago = now - timedelta(hours=1)
        
        # 计算今日统计
        today_commands = CommandExecutionRecord.objects.filter(
            created_at__date=today
        )
        
        today_stats = today_commands.aggregate(
            total=Count('id'),
            success=Count('id', filter=Q(success=True)),
            avg_response_time=Avg('processing_time')
        )
        
        success_rate = 0.0
        if today_stats['total'] > 0:
            success_rate = (today_stats['success'] / today_stats['total']) * 100
        
        # 计算当前活跃用户（最近5分钟有活动）
        five_minutes_ago = now - timedelta(minutes=5)
        active_users = BotAccessEvent.objects.filter(
            created_at__gte=five_minutes_ago
        ).values('user_id').distinct().count()
        
        # 计算今日总用户数
        total_users_today = BotAccessEvent.objects.filter(
            created_at__date=today
        ).values('user_id').distinct().count()
        
        # 计算最近1小时的错误和警告数
        recent_errors = CommandExecutionRecord.objects.filter(
            created_at__gte=one_hour_ago,
            success=False
        ).count()
        
        # 计算定时任务状态
        cronjobs_running = CronJobExecutionMonitor.objects.filter(
            status='running'
        ).count()
        
        cronjobs_failed_today = CronJobExecutionMonitor.objects.filter(
            start_time__date=today,
            success=False
        ).count()
        
        # 确定整体健康状态
        overall_status = 'healthy'
        alerts = []
        
        if success_rate < 90:
            overall_status = 'warning'
            alerts.append(f"成功率较低: {success_rate:.1f}%")
        
        if recent_errors > 10:
            overall_status = 'critical'
            alerts.append(f"最近1小时错误数过多: {recent_errors}")
        
        if cronjobs_failed_today > 5:
            if overall_status == 'healthy':
                overall_status = 'warning'
            alerts.append(f"今日定时任务失败数过多: {cronjobs_failed_today}")
        
        # 创建健康快照
        snapshot = SystemHealthSnapshot.objects.create(
            snapshot_time=now,
            overall_status=overall_status,
            total_users_today=total_users_today,
            active_users_now=active_users,
            total_commands_today=today_stats['total'],
            success_rate_today=success_rate,
            avg_response_time=today_stats['avg_response_time'] or 0.0,
            error_count_last_hour=recent_errors,
            cronjobs_running=cronjobs_running,
            cronjobs_failed_today=cronjobs_failed_today,
            alerts=alerts,
            details={
                'generated_at': now.isoformat(),
                'period': 'daily',
                'data_sources': ['CommandExecutionRecord', 'BotAccessEvent', 'CronJobExecutionMonitor']
            }
        )
        
        logger.info(f"Generated system health snapshot: {overall_status}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to generate system health snapshot: {e}")
        return False


def cleanup_old_statistics(days_to_keep: int = 90) -> bool:
    """
    清理旧的统计数据
    
    Args:
        days_to_keep: 保留的天数
        
    Returns:
        bool: 是否成功清理
    """
    try:
        from app01.models import (
            BotAccessEvent, CommandExecutionRecord, 
            SystemPerformanceMetrics, SystemHealthSnapshot
        )
        
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        # 清理各种统计数据
        deleted_counts = {}
        
        # 清理访问事件（保留时间较短）
        access_cutoff = timezone.now() - timedelta(days=30)  # 只保留30天
        deleted_counts['BotAccessEvent'] = BotAccessEvent.objects.filter(
            created_at__lt=access_cutoff
        ).delete()[0]
        
        # 清理成功的指令记录（保留错误记录更长时间）
        deleted_counts['CommandExecutionRecord_success'] = CommandExecutionRecord.objects.filter(
            created_at__lt=cutoff_date,
            success=True
        ).delete()[0]
        
        # 清理性能指标
        deleted_counts['SystemPerformanceMetrics'] = SystemPerformanceMetrics.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        # 清理健康快照（保留时间较短）
        snapshot_cutoff = timezone.now() - timedelta(days=7)  # 只保留7天
        deleted_counts['SystemHealthSnapshot'] = SystemHealthSnapshot.objects.filter(
            created_at__lt=snapshot_cutoff
        ).delete()[0]
        
        logger.info(f"Cleaned up old statistics: {deleted_counts}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to cleanup old statistics: {e}")
        return False


def batch_process_statistics() -> bool:
    """
    批量处理统计数据
    
    Returns:
        bool: 是否成功处理
    """
    try:
        # 生成各种汇总数据
        generate_user_activity_summary(period_type='daily')
        generate_system_health_snapshot()
        
        # 清理旧数据
        cleanup_old_statistics()
        
        logger.info("Batch statistics processing completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to batch process statistics: {e}")
        return False

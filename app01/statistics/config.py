#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计系统配置文件
"""

from django.conf import settings

# 统计系统基础配置
STATISTICS_ENABLED = getattr(settings, 'STATISTICS_ENABLED', True)
STATISTICS_ASYNC = getattr(settings, 'STATISTICS_ASYNC', True)
STATISTICS_BUFFER_SIZE = getattr(settings, 'STATISTICS_BUFFER_SIZE', 100)
STATISTICS_FLUSH_INTERVAL = getattr(settings, 'STATISTICS_FLUSH_INTERVAL', 60)

# 中间件配置
STATISTICS_MIDDLEWARE_ENABLED = getattr(settings, 'STATISTICS_MIDDLEWARE_ENABLED', True)
STATISTICS_COLLECT_ALL_REQUESTS = getattr(settings, 'STATISTICS_COLLECT_ALL_REQUESTS', False)
STATISTICS_API_PATHS = getattr(settings, 'STATISTICS_API_PATHS', ['/api/'])
STATISTICS_DB_QUERY_TRACKING = getattr(settings, 'STATISTICS_DB_QUERY_TRACKING', True)
STATISTICS_USER_ACTIVITY_TRACKING = getattr(settings, 'STATISTICS_USER_ACTIVITY_TRACKING', True)
STATISTICS_SESSION_TIMEOUT = getattr(settings, 'STATISTICS_SESSION_TIMEOUT', 1800)

# 服务配置
STATISTICS_SERVICE_ENABLED = getattr(settings, 'STATISTICS_SERVICE_ENABLED', True)
STATISTICS_REPORTS_ENABLED = getattr(settings, 'STATISTICS_REPORTS_ENABLED', True)

# 数据保留配置
STATISTICS_DATA_RETENTION_DAYS = getattr(settings, 'STATISTICS_DATA_RETENTION_DAYS', 90)
STATISTICS_ACCESS_EVENT_RETENTION_DAYS = getattr(settings, 'STATISTICS_ACCESS_EVENT_RETENTION_DAYS', 30)
STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS = getattr(settings, 'STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS', 7)

# 告警配置
STATISTICS_ALERT_ERROR_RATE_THRESHOLD = getattr(settings, 'STATISTICS_ALERT_ERROR_RATE_THRESHOLD', 10.0)
STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD = getattr(settings, 'STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD', 5.0)
STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD = getattr(settings, 'STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD', 5)

# 性能配置
STATISTICS_SLOW_QUERY_THRESHOLD = getattr(settings, 'STATISTICS_SLOW_QUERY_THRESHOLD', 1.0)
STATISTICS_MAX_RESPONSE_CONTENT_LENGTH = getattr(settings, 'STATISTICS_MAX_RESPONSE_CONTENT_LENGTH', 5000)
STATISTICS_MAX_ERROR_MESSAGE_LENGTH = getattr(settings, 'STATISTICS_MAX_ERROR_MESSAGE_LENGTH', 1000)

# 默认配置字典
DEFAULT_STATISTICS_CONFIG = {
    'enabled': STATISTICS_ENABLED,
    'async_processing': STATISTICS_ASYNC,
    'buffer_size': STATISTICS_BUFFER_SIZE,
    'flush_interval': STATISTICS_FLUSH_INTERVAL,
    'middleware_enabled': STATISTICS_MIDDLEWARE_ENABLED,
    'collect_all_requests': STATISTICS_COLLECT_ALL_REQUESTS,
    'api_paths': STATISTICS_API_PATHS,
    'db_query_tracking': STATISTICS_DB_QUERY_TRACKING,
    'user_activity_tracking': STATISTICS_USER_ACTIVITY_TRACKING,
    'session_timeout': STATISTICS_SESSION_TIMEOUT,
    'service_enabled': STATISTICS_SERVICE_ENABLED,
    'reports_enabled': STATISTICS_REPORTS_ENABLED,
    'data_retention_days': STATISTICS_DATA_RETENTION_DAYS,
    'access_event_retention_days': STATISTICS_ACCESS_EVENT_RETENTION_DAYS,
    'health_snapshot_retention_days': STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS,
    'alert_error_rate_threshold': STATISTICS_ALERT_ERROR_RATE_THRESHOLD,
    'alert_response_time_threshold': STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD,
    'alert_cronjob_failure_threshold': STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD,
    'slow_query_threshold': STATISTICS_SLOW_QUERY_THRESHOLD,
    'max_response_content_length': STATISTICS_MAX_RESPONSE_CONTENT_LENGTH,
    'max_error_message_length': STATISTICS_MAX_ERROR_MESSAGE_LENGTH,
}


def get_statistics_config():
    """获取统计系统配置"""
    return DEFAULT_STATISTICS_CONFIG


def is_statistics_enabled():
    """检查统计系统是否启用"""
    return STATISTICS_ENABLED


def is_middleware_enabled():
    """检查中间件是否启用"""
    return STATISTICS_MIDDLEWARE_ENABLED


def is_service_enabled():
    """检查服务是否启用"""
    return STATISTICS_SERVICE_ENABLED


def is_reports_enabled():
    """检查报表生成是否启用"""
    return STATISTICS_REPORTS_ENABLED

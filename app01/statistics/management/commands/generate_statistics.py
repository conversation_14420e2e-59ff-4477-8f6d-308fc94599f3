#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计数据生成管理命令
用于定时生成各种统计汇总数据
"""

import logging
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from app01.statistics.tasks import (
    generate_user_activity_summary,
    generate_system_health_snapshot,
    cleanup_old_statistics,
    batch_process_statistics
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '生成统计数据汇总'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['user_activity', 'health_snapshot', 'cleanup', 'batch'],
            default='batch',
            help='指定要生成的统计类型'
        )
        
        parser.add_argument(
            '--period',
            type=str,
            choices=['hourly', 'daily', 'weekly', 'monthly'],
            default='daily',
            help='用户活动汇总的周期类型'
        )
        
        parser.add_argument(
            '--user-id',
            type=str,
            help='指定用户ID（仅用于用户活动汇总）'
        )
        
        parser.add_argument(
            '--cleanup-days',
            type=int,
            default=90,
            help='清理多少天前的数据'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细输出'
        )
    
    def handle(self, *args, **options):
        if options['verbose']:
            logging.basicConfig(level=logging.INFO)
        
        stat_type = options['type']
        
        try:
            if stat_type == 'user_activity':
                self.generate_user_activity(options)
            elif stat_type == 'health_snapshot':
                self.generate_health_snapshot(options)
            elif stat_type == 'cleanup':
                self.cleanup_old_data(options)
            elif stat_type == 'batch':
                self.batch_process(options)
            else:
                raise CommandError(f"Unknown statistics type: {stat_type}")
                
        except Exception as e:
            logger.error(f"Failed to generate statistics: {e}")
            raise CommandError(f"Statistics generation failed: {e}")
    
    def generate_user_activity(self, options):
        """生成用户活动汇总"""
        period_type = options['period']
        user_id = options.get('user_id')
        
        self.stdout.write(f"Generating user activity summary (period: {period_type}, user: {user_id or 'all'})")
        
        success = generate_user_activity_summary(user_id=user_id, period_type=period_type)
        
        if success:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully generated user activity summary")
            )
        else:
            raise CommandError("Failed to generate user activity summary")
    
    def generate_health_snapshot(self, options):
        """生成系统健康快照"""
        self.stdout.write("Generating system health snapshot")
        
        success = generate_system_health_snapshot()
        
        if success:
            self.stdout.write(
                self.style.SUCCESS("Successfully generated system health snapshot")
            )
        else:
            raise CommandError("Failed to generate system health snapshot")
    
    def cleanup_old_data(self, options):
        """清理旧数据"""
        cleanup_days = options['cleanup_days']
        
        self.stdout.write(f"Cleaning up data older than {cleanup_days} days")
        
        success = cleanup_old_statistics(days_to_keep=cleanup_days)
        
        if success:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully cleaned up old data")
            )
        else:
            raise CommandError("Failed to cleanup old data")
    
    def batch_process(self, options):
        """批量处理统计数据"""
        self.stdout.write("Running batch statistics processing")
        
        success = batch_process_statistics()
        
        if success:
            self.stdout.write(
                self.style.SUCCESS("Successfully completed batch statistics processing")
            )
        else:
            raise CommandError("Failed to complete batch statistics processing")

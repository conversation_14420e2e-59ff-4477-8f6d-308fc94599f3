#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行统计任务的管理命令
"""

import logging
from django.core.management.base import BaseCommand, CommandError
from app01.statistics.cron_jobs import (
    run_statistics_job,
    get_available_jobs,
    get_job_info
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '运行统计系统的定时任务'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'job_name',
            type=str,
            nargs='?',
            help='要运行的任务名称'
        )
        
        parser.add_argument(
            '--list',
            action='store_true',
            help='列出所有可用的任务'
        )
        
        parser.add_argument(
            '--info',
            action='store_true',
            help='显示任务详细信息'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细输出'
        )
    
    def handle(self, *args, **options):
        if options['verbose']:
            logging.basicConfig(level=logging.INFO)
        
        # 列出所有任务
        if options['list']:
            self.list_jobs()
            return
        
        # 显示任务信息
        if options['info']:
            self.show_job_info()
            return
        
        # 运行指定任务
        job_name = options['job_name']
        if not job_name:
            raise CommandError("请指定要运行的任务名称，或使用 --list 查看可用任务")
        
        self.run_job(job_name)
    
    def list_jobs(self):
        """列出所有可用任务"""
        jobs = get_available_jobs()
        
        self.stdout.write(self.style.SUCCESS("可用的统计任务:"))
        for job in jobs:
            self.stdout.write(f"  - {job}")
        
        self.stdout.write("\n使用 --info 查看任务详细信息")
        self.stdout.write("使用 python manage.py run_statistics_job <job_name> 运行任务")
    
    def show_job_info(self):
        """显示任务详细信息"""
        job_info = get_job_info()
        
        self.stdout.write(self.style.SUCCESS("统计任务详细信息:"))
        self.stdout.write("=" * 80)
        
        for job_name, info in job_info.items():
            self.stdout.write(f"\n📋 {info['name']} ({job_name})")
            self.stdout.write(f"   描述: {info['description']}")
            self.stdout.write(f"   频率: {info['frequency']}")
            self.stdout.write(f"   Crontab: {info['crontab']}")
            
            # 生成完整的crontab命令
            crontab_command = f"{info['crontab']} cd /path/to/project && python manage.py run_statistics_job {job_name}"
            self.stdout.write(f"   完整命令: {crontab_command}")
        
        self.stdout.write("\n" + "=" * 80)
        self.stdout.write("⚠️  注意: 这些任务不会自动启动，需要手动配置crontab")
        self.stdout.write("📖 详细配置说明请参考: docs/STATISTICS_SYSTEM_GUIDE.md")
    
    def run_job(self, job_name):
        """运行指定任务"""
        available_jobs = get_available_jobs()
        
        if job_name not in available_jobs:
            raise CommandError(
                f"未知的任务名称: {job_name}\n"
                f"可用任务: {', '.join(available_jobs)}"
            )
        
        self.stdout.write(f"开始运行统计任务: {job_name}")
        
        try:
            success = run_statistics_job(job_name)
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f"任务 {job_name} 执行成功")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"任务 {job_name} 执行失败")
                )
                
        except Exception as e:
            logger.error(f"任务执行异常: {e}")
            raise CommandError(f"任务执行失败: {e}")

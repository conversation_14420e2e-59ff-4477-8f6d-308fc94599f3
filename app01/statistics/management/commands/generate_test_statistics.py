#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成测试统计数据的命令
用于填充统计数据库，便于测试和演示
"""

import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from app01.models import (
    BotAccessEvent, CommandExecutionRecord, 
    SystemHealthSnapshot, SystemPerformanceMetrics,
    CronJobExecutionMonitor
)


class Command(BaseCommand):
    help = '生成测试统计数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='生成数据的天数 (默认: 30天)'
        )
        
        parser.add_argument(
            '--users',
            type=int,
            default=20,
            help='模拟用户数量 (默认: 20个)'
        )
        
        parser.add_argument(
            '--clear',
            action='store_true',
            help='在生成新数据前清除现有数据'
        )

    def handle(self, *args, **options):
        days = options['days']
        user_count = options['users']
        clear_existing = options['clear']
        
        if clear_existing:
            self.stdout.write('清除现有测试数据...')
            self.clear_existing_data()
        
        self.stdout.write(f'开始生成 {days} 天的测试数据，模拟 {user_count} 个用户...')
        
        # 生成模拟用户ID列表
        user_ids = [f'test_user_{i:03d}' for i in range(1, user_count + 1)]
        
        # 生成历史数据
        start_date = timezone.now().date() - timedelta(days=days-1)
        for day_offset in range(days):
            current_date = start_date + timedelta(days=day_offset)
            self.generate_daily_data(current_date, user_ids)
            self.stdout.write(f'已生成 {current_date} 的数据')
        
        # 生成今天的实时数据
        self.generate_realtime_data(user_ids)
        
        self.stdout.write(
            self.style.SUCCESS(f'成功生成 {days} 天的测试统计数据!')
        )

    def clear_existing_data(self):
        """清除现有的测试数据"""
        BotAccessEvent.objects.filter(user_id__startswith='test_user_').delete()
        CommandExecutionRecord.objects.filter(user_id__startswith='test_user_').delete()
        SystemHealthSnapshot.objects.all().delete()
        SystemPerformanceMetrics.objects.all().delete()
        CronJobExecutionMonitor.objects.filter(job_name__startswith='test_job_').delete()

    def generate_daily_data(self, base_date, user_ids):
        """生成指定日期的数据"""
        # 生成机器人访问事件
        active_user_count = random.randint(5, len(user_ids))
        active_users = random.sample(user_ids, active_user_count)
        
        for user_id in active_users:
            # 每个活跃用户生成1-5个访问事件
            event_count = random.randint(1, 5)
            for _ in range(event_count):
                event_time = base_date + timedelta(
                    hours=random.randint(8, 22),
                    minutes=random.randint(0, 59),
                    seconds=random.randint(0, 59)
                )
                
                BotAccessEvent.objects.create(
                    event_id=f'test_event_{user_id}_{random.randint(1000, 9999)}',
                    event_type=random.choice(['user_enter_chatroom_with_bot', 'user_start_chat_with_bot']),
                    user_id=user_id,
                    employee_code=f'emp_{user_id.split("_")[-1]}',
                    email=f'{user_id}@example.com',
                    group_id=random.choice([None, 'test_group_001', 'test_group_002']),
                    timestamp=int(event_time.timestamp()),
                    event_time=event_time,
                    metadata={'source': 'test_data'}
                )
        
        # 生成指令执行记录
        command_types = ['ai_query', 'jira_query', 'mr_check', 'schedule_management', 'help_request']
        intents = ['query_tasks', 'query_release', 'query_mr', 'system_help', 'other']
        
        command_count = random.randint(10, 50)
        for _ in range(command_count):
            user_id = random.choice(active_users)
            command_time = base_date + timedelta(
                hours=random.randint(8, 22),
                minutes=random.randint(0, 59),
                seconds=random.randint(0, 59)
            )
            
            success = random.random() > 0.05  # 95% 成功率
            processing_time = random.uniform(0.1, 3.0) if success else random.uniform(5.0, 30.0)
            
            CommandExecutionRecord.objects.create(
                user_id=user_id,
                user_email=f'{user_id}@example.com',
                employee_code=f'emp_{user_id.split("_")[-1]}',
                group_id=random.choice([None, 'test_group_001', 'test_group_002']),
                command_type=random.choice(command_types),
                raw_input=random.choice([
                    '查询我的任务',
                    '查看今日发布',
                    '检查MR状态',
                    '创建定时任务',
                    '帮助'
                ]),
                intent=random.choice(intents),
                success=success,
                processing_time=processing_time,
                start_time=command_time,
                end_time=command_time + timedelta(seconds=processing_time),
                response_content='测试响应内容' if success else None,
                error_message=None if success else '模拟错误信息',
                api_calls=['test_api_call'] if success else [],
                database_queries=random.randint(1, 5),
                external_service_calls={'jira': 1} if 'jira' in random.choice(command_types) else {}
            )
        
        # 生成系统健康快照（每小时一次）
        for hour in range(0, 24, 2):  # 每2小时一次
            snapshot_time = base_date.replace(hour=hour, minute=0, second=0)
            snapshot_time = timezone.make_aware(
                datetime.combine(snapshot_time, datetime.min.time())
            ) + timedelta(hours=hour)
            
            cpu_usage = random.uniform(20, 80)
            memory_usage = random.uniform(30, 90)
            
            # 根据使用率确定状态
            if cpu_usage > 90 or memory_usage > 95:
                status = 'critical'
            elif cpu_usage > 80 or memory_usage > 85:
                status = 'warning'
            else:
                status = 'healthy'
            
            SystemHealthSnapshot.objects.create(
                snapshot_time=snapshot_time,
                overall_status=status,
                total_users_today=active_user_count,
                active_users_now=random.randint(1, active_user_count),
                total_commands_today=command_count,
                success_rate_today=95.0 if status == 'healthy' else 85.0,
                avg_response_time=random.uniform(0.1, 2.0),
                system_load=random.uniform(0.5, 2.0),
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                database_status='healthy',
                redis_status='healthy',
                external_services_status={'jira': 'healthy', 'gitlab': 'healthy'},
                error_count_last_hour=random.randint(0, 5),
                warning_count_last_hour=random.randint(0, 10),
                cronjobs_running=random.randint(0, 3),
                cronjobs_failed_today=random.randint(0, 2),
                details={'test': True},
                alerts=[]
            )

    def generate_realtime_data(self, user_ids):
        """生成今天的实时数据"""
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 生成最近5分钟的活跃用户
        recent_users = random.sample(user_ids, random.randint(5, 15))
        for user_id in recent_users:
            access_time = now - timedelta(minutes=random.randint(0, 5))
            BotAccessEvent.objects.create(
                event_id=f'test_event_{user_id}_{random.randint(10000, 99999)}',
                event_type='user_enter_chatroom_with_bot',
                user_id=user_id,
                employee_code=f'emp_{user_id.split("_")[-1]}',
                email=f'{user_id}@example.com',
                timestamp=int(access_time.timestamp()),
                event_time=access_time,
                metadata={'source': 'realtime_test'}
            )
        
        # 生成今天的指令记录
        command_types = ['ai_query', 'jira_query', 'mr_check', 'schedule_management']
        for _ in range(random.randint(20, 50)):
            command_time = today_start + timedelta(
                hours=random.randint(0, now.hour if now.hour > 0 else 1),
                minutes=random.randint(0, 59)
            )
            if command_time > now:
                command_time = now - timedelta(minutes=random.randint(1, 60))
            
            success = random.random() > 0.05
            processing_time = random.uniform(0.1, 2.0)
            
            CommandExecutionRecord.objects.create(
                user_id=random.choice(user_ids),
                user_email=f'{random.choice(user_ids)}@example.com',
                command_type=random.choice(command_types),
                raw_input=random.choice([
                    '查询我的任务',
                    '查看今日发布',
                    '检查MR状态'
                ]),
                success=success,
                processing_time=processing_time,
                start_time=command_time,
                end_time=command_time + timedelta(seconds=processing_time),
                response_content='实时测试响应' if success else None,
                error_message=None if success else '实时测试错误'
            )
        
        # 生成最新的系统健康快照
        SystemHealthSnapshot.objects.create(
            snapshot_time=now,
            overall_status='healthy',
            total_users_today=len(user_ids),
            active_users_now=len(recent_users),
            total_commands_today=random.randint(30, 80),
            success_rate_today=random.uniform(90, 98),
            avg_response_time=random.uniform(0.1, 1.0),
            system_load=random.uniform(0.3, 1.5),
            memory_usage=random.uniform(40, 80),
            cpu_usage=random.uniform(30, 70),
            database_status='healthy',
            redis_status='healthy',
            external_services_status={'jira': 'healthy'},
            error_count_last_hour=random.randint(0, 3),
            warning_count_last_hour=random.randint(0, 5),
            cronjobs_running=random.randint(1, 3),
            cronjobs_failed_today=0,
            details={'realtime': True},
            alerts=[]
        )
        
        # 生成一些定时任务监控记录
        job_names = ['test_job_daily_backup', 'test_job_data_sync', 'test_job_report_generation']
        for job_name in job_names:
            start_time = now - timedelta(hours=random.randint(1, 12))
            duration = random.uniform(30, 300)  # 30秒到5分钟
            
            CronJobExecutionMonitor.objects.create(
                job_name=job_name,
                start_time=start_time,
                end_time=start_time + timedelta(seconds=duration),
                duration=duration,
                status='success' if random.random() > 0.1 else 'failed',
                success=random.random() > 0.1,
                output=f'Job {job_name} completed successfully',
                error_message=None if random.random() > 0.1 else 'Mock error for testing',
                scheduled_time=start_time,
                trigger_type='cron',
                metadata={'test': True}
            )

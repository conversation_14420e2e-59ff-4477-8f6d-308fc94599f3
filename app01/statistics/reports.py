#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计报表生成模块
生成各种格式的统计报表
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from django.utils import timezone
from django.template.loader import render_to_string
from django.conf import settings
from django.db.models import Count, Avg, Sum, Max, Min, Q
import json

logger = logging.getLogger(__name__)


class ReportGenerator:
    """报表生成器基类"""
    
    def __init__(self):
        self.enabled = getattr(settings, 'STATISTICS_REPORTS_ENABLED', True)
        logger.info(f"ReportGenerator initialized: enabled={self.enabled}")
    
    def is_enabled(self) -> bool:
        """检查报表生成是否启用"""
        return self.enabled


class DailyReportGenerator(ReportGenerator):
    """日报生成器"""
    
    def generate_daily_report(self, date: datetime.date = None) -> Dict[str, Any]:
        """生成日报"""
        if not self.is_enabled():
            return {'error': 'Report generation is disabled'}
        
        try:
            from app01.models import (
                CommandExecutionRecord, BotAccessEvent, 
                CronJobExecutionMonitor, SystemHealthSnapshot
            )
            
            if date is None:
                date = timezone.now().date()
            
            # 指令执行统计
            daily_commands = CommandExecutionRecord.objects.filter(
                created_at__date=date
            )
            
            command_stats = daily_commands.aggregate(
                total_commands=Count('id'),
                successful_commands=Count('id', filter=Q(success=True)),
                failed_commands=Count('id', filter=Q(success=False)),
                avg_response_time=Avg('processing_time'),
                max_response_time=Max('processing_time')
            )
            
            # 按指令类型统计
            command_by_type = daily_commands.values('command_type').annotate(
                count=Count('id'),
                success_rate=Count('id', filter=Q(success=True)) * 100.0 / Count('id')
            ).order_by('-count')
            
            # 用户活动统计
            daily_users = BotAccessEvent.objects.filter(
                created_at__date=date
            ).values('user_id').distinct().count()
            
            # 访问事件统计
            access_events = BotAccessEvent.objects.filter(
                created_at__date=date
            ).values('event_type').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # 定时任务执行情况
            cronjob_stats = CronJobExecutionMonitor.objects.filter(
                start_time__date=date
            ).aggregate(
                total_jobs=Count('id'),
                successful_jobs=Count('id', filter=Q(success=True)),
                failed_jobs=Count('id', filter=Q(success=False)),
                avg_duration=Avg('duration')
            )
            
            # 失败的任务详情
            failed_jobs = CronJobExecutionMonitor.objects.filter(
                start_time__date=date,
                success=False
            ).values('job_name', 'start_time', 'error_message')
            
            # 系统健康状况
            health_snapshots = SystemHealthSnapshot.objects.filter(
                snapshot_time__date=date
            ).order_by('-snapshot_time')
            
            # 计算成功率
            command_success_rate = 0.0
            if command_stats['total_commands'] > 0:
                command_success_rate = (command_stats['successful_commands'] / command_stats['total_commands']) * 100
            
            cronjob_success_rate = 0.0
            if cronjob_stats['total_jobs'] > 0:
                cronjob_success_rate = (cronjob_stats['successful_jobs'] / cronjob_stats['total_jobs']) * 100
            
            report_data = {
                'date': date.isoformat(),
                'generated_at': timezone.now().isoformat(),
                'summary': {
                    'total_commands': command_stats['total_commands'],
                    'command_success_rate': round(command_success_rate, 1),
                    'avg_response_time': round((command_stats['avg_response_time'] or 0) * 1000, 0),
                    'daily_users': daily_users,
                    'total_cronjobs': cronjob_stats['total_jobs'],
                    'cronjob_success_rate': round(cronjob_success_rate, 1)
                },
                'details': {
                    'command_stats': command_stats,
                    'command_by_type': list(command_by_type),
                    'access_events': list(access_events),
                    'cronjob_stats': cronjob_stats,
                    'failed_jobs': list(failed_jobs),
                    'health_snapshots': [
                        {
                            'time': snapshot.snapshot_time.isoformat(),
                            'status': snapshot.overall_status,
                            'alerts': snapshot.alerts
                        } for snapshot in health_snapshots[:5]  # 最近5个快照
                    ]
                }
            }
            
            return report_data
            
        except Exception as e:
            logger.error(f"Failed to generate daily report: {e}")
            return {'error': str(e)}
    
    def generate_daily_report_html(self, date: datetime.date = None) -> str:
        """生成HTML格式的日报"""
        try:
            report_data = self.generate_daily_report(date)
            
            if 'error' in report_data:
                return f"<html><body><h1>报表生成失败</h1><p>{report_data['error']}</p></body></html>"
            
            # 使用模板生成HTML
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>ChatBot AutoRelease 日报 - {report_data['date']}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                    .summary {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                    .metric {{ text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
                    .metric-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
                    .metric-label {{ font-size: 14px; color: #666; }}
                    .section {{ margin: 20px 0; }}
                    .section h3 {{ border-bottom: 2px solid #007bff; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    .success {{ color: #28a745; }}
                    .warning {{ color: #ffc107; }}
                    .danger {{ color: #dc3545; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>ChatBot AutoRelease 系统日报</h1>
                    <p>日期: {report_data['date']} | 生成时间: {report_data['generated_at']}</p>
                </div>
                
                <div class="summary">
                    <div class="metric">
                        <div class="metric-value">{report_data['summary']['total_commands']}</div>
                        <div class="metric-label">总指令数</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value {'success' if report_data['summary']['command_success_rate'] >= 95 else 'warning' if report_data['summary']['command_success_rate'] >= 90 else 'danger'}">{report_data['summary']['command_success_rate']}%</div>
                        <div class="metric-label">指令成功率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{report_data['summary']['avg_response_time']}ms</div>
                        <div class="metric-label">平均响应时间</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{report_data['summary']['daily_users']}</div>
                        <div class="metric-label">活跃用户数</div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>指令类型分布</h3>
                    <table>
                        <tr><th>指令类型</th><th>执行次数</th><th>成功率</th></tr>
            """
            
            for cmd in report_data['details']['command_by_type']:
                success_rate_class = 'success' if cmd['success_rate'] >= 95 else 'warning' if cmd['success_rate'] >= 90 else 'danger'
                html_content += f"""
                        <tr>
                            <td>{cmd['command_type']}</td>
                            <td>{cmd['count']}</td>
                            <td class="{success_rate_class}">{cmd['success_rate']:.1f}%</td>
                        </tr>
                """
            
            html_content += """
                    </table>
                </div>
                
                <div class="section">
                    <h3>定时任务执行情况</h3>
                    <p>总任务数: {total_jobs} | 成功: {successful_jobs} | 失败: {failed_jobs} | 成功率: {success_rate}%</p>
                </div>
            """.format(
                total_jobs=report_data['details']['cronjob_stats']['total_jobs'],
                successful_jobs=report_data['details']['cronjob_stats']['successful_jobs'],
                failed_jobs=report_data['details']['cronjob_stats']['failed_jobs'],
                success_rate=report_data['summary']['cronjob_success_rate']
            )
            
            if report_data['details']['failed_jobs']:
                html_content += """
                <div class="section">
                    <h3>失败的定时任务</h3>
                    <table>
                        <tr><th>任务名称</th><th>执行时间</th><th>错误信息</th></tr>
                """
                
                for job in report_data['details']['failed_jobs']:
                    html_content += f"""
                        <tr>
                            <td>{job['job_name']}</td>
                            <td>{job['start_time']}</td>
                            <td>{job['error_message'][:100]}...</td>
                        </tr>
                    """
                
                html_content += "</table></div>"
            
            html_content += """
            </body>
            </html>
            """
            
            return html_content
            
        except Exception as e:
            logger.error(f"Failed to generate HTML daily report: {e}")
            return f"<html><body><h1>报表生成失败</h1><p>{str(e)}</p></body></html>"


class WeeklyReportGenerator(ReportGenerator):
    """周报生成器"""
    
    def generate_weekly_report(self, week_start: datetime.date = None) -> Dict[str, Any]:
        """生成周报"""
        if not self.is_enabled():
            return {'error': 'Report generation is disabled'}
        
        try:
            from app01.models import UserActivitySummary, CommandExecutionRecord
            
            if week_start is None:
                now = timezone.now()
                week_start = (now - timedelta(days=now.weekday())).date()
            
            week_end = week_start + timedelta(days=6)
            
            # 获取周汇总数据
            weekly_summaries = UserActivitySummary.objects.filter(
                period_type='weekly',
                period_start__date=week_start
            )
            
            # 计算总体统计
            total_stats = weekly_summaries.aggregate(
                total_users=Count('user_id'),
                total_commands=Sum('total_commands'),
                total_successful=Sum('successful_commands'),
                avg_response_time=Avg('avg_response_time')
            )
            
            # 最活跃用户
            top_users = weekly_summaries.order_by('-total_commands')[:10]
            
            # 按天统计
            daily_stats = CommandExecutionRecord.objects.filter(
                created_at__date__range=[week_start, week_end]
            ).extra(
                select={'date': 'DATE(created_at)'}
            ).values('date').annotate(
                commands=Count('id'),
                success_rate=Count('id', filter=Q(success=True)) * 100.0 / Count('id')
            ).order_by('date')
            
            success_rate = 0.0
            if total_stats['total_commands'] and total_stats['total_commands'] > 0:
                success_rate = (total_stats['total_successful'] / total_stats['total_commands']) * 100
            
            return {
                'week_start': week_start.isoformat(),
                'week_end': week_end.isoformat(),
                'generated_at': timezone.now().isoformat(),
                'summary': {
                    'total_users': total_stats['total_users'] or 0,
                    'total_commands': total_stats['total_commands'] or 0,
                    'success_rate': round(success_rate, 1),
                    'avg_response_time': round((total_stats['avg_response_time'] or 0) * 1000, 0)
                },
                'top_users': [
                    {
                        'user_id': user.user_id,
                        'user_email': user.user_email,
                        'commands': user.total_commands,
                        'success_rate': user.calculate_success_rate()
                    } for user in top_users
                ],
                'daily_trends': list(daily_stats)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate weekly report: {e}")
            return {'error': str(e)}


class AlertReportGenerator(ReportGenerator):
    """告警报表生成器"""
    
    def generate_alert_report(self) -> Dict[str, Any]:
        """生成告警报表"""
        try:
            from app01.models import (
                CommandExecutionRecord, CronJobExecutionMonitor, 
                SystemHealthSnapshot
            )
            
            now = timezone.now()
            one_hour_ago = now - timedelta(hours=1)
            today = now.date()
            
            alerts = []
            
            # 检查最近1小时的错误率
            recent_commands = CommandExecutionRecord.objects.filter(
                created_at__gte=one_hour_ago
            )
            
            if recent_commands.exists():
                error_rate = recent_commands.filter(success=False).count() / recent_commands.count() * 100
                if error_rate > 10:  # 错误率超过10%
                    alerts.append({
                        'type': 'high_error_rate',
                        'severity': 'critical' if error_rate > 20 else 'warning',
                        'message': f'最近1小时错误率过高: {error_rate:.1f}%',
                        'timestamp': now.isoformat()
                    })
            
            # 检查定时任务失败
            failed_jobs_today = CronJobExecutionMonitor.objects.filter(
                start_time__date=today,
                success=False
            ).count()
            
            if failed_jobs_today > 5:
                alerts.append({
                    'type': 'cronjob_failures',
                    'severity': 'warning',
                    'message': f'今日定时任务失败数过多: {failed_jobs_today}',
                    'timestamp': now.isoformat()
                })
            
            # 检查长时间运行的任务
            long_running_jobs = CronJobExecutionMonitor.objects.filter(
                status='running',
                start_time__lt=now - timedelta(hours=2)  # 运行超过2小时
            )
            
            for job in long_running_jobs:
                alerts.append({
                    'type': 'long_running_job',
                    'severity': 'warning',
                    'message': f'任务 {job.job_name} 运行时间过长',
                    'timestamp': now.isoformat(),
                    'details': {
                        'job_name': job.job_name,
                        'start_time': job.start_time.isoformat()
                    }
                })
            
            # 检查系统健康状况
            latest_health = SystemHealthSnapshot.objects.first()
            if latest_health and latest_health.overall_status in ['critical', 'warning']:
                alerts.append({
                    'type': 'system_health',
                    'severity': latest_health.overall_status,
                    'message': f'系统健康状况: {latest_health.overall_status}',
                    'timestamp': latest_health.snapshot_time.isoformat(),
                    'details': latest_health.alerts
                })
            
            return {
                'generated_at': now.isoformat(),
                'alert_count': len(alerts),
                'alerts': alerts
            }
            
        except Exception as e:
            logger.error(f"Failed to generate alert report: {e}")
            return {'error': str(e)}


# 全局报表生成器实例
daily_report_generator = DailyReportGenerator()
weekly_report_generator = WeeklyReportGenerator()
alert_report_generator = AlertReportGenerator()

import sccclient
import yaml
from icecream import ic
import os
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 获取配置文件路径
config_path = os.path.join(BASE_DIR, 'app01', 'common_tool', 'config_center.yaml')

# 打开文件并读取内容
with open(config_path) as f:
    config = yaml.safe_load(f)

# 配置中心地址：https://config.shopee.io/group/customer_service_and_chatbot/project/chatbot_qa/cluster/nonlive/namespaces?moduleId=1682
class ConfigCenter:
    def setup(self, env, cid):
        client = sccclient.Client()
        application = config['application']
        if env != 'live':
            namespace = config['namespace']['no_live']
            namespace = namespace.format(env)
            namespace_secret = config['namespace_secret']['no_live']
        else:
            namespace = config['namespace']['live']
            namespace_secret = config['namespace_secret']['live']
        ns, dispose = client.subscribe(
            sccclient.NamespaceIdentity(application, namespace),
            sccclient.NamespaceSecret(namespace_secret))
        self._ns = ns

    def get(self, key) -> object:
        return self._ns.get(key)

    def get_string(self, key) -> str:
        if self._ns.get(key) != None:
            return self._ns.get(key)

        print(f"get key({key}) error: key not found")
        return ''

    def get_json(self, key) -> dict:
        if self._ns.get(key) != None:
            return self._ns.get(key)

        print(f"get key({key}) error: key not found")
        return {}


config_center = ConfigCenter()
cc_without_env = ConfigCenter()
cc_without_env.setup("test", "default")

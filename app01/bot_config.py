# -*- coding: utf-8 -*-
"""
机器人配置管理模块
处理机器人名称变更的兼容性问题
"""

import re
from typing import List, Optional, Tuple

class BotConfig:
    """机器人配置管理类"""
    
    # 机器人名称 - 集中管理，便于后续修改
    BOT_NAME = "ChatbotAR"
    
    # 支持的机器人名称列表（按优先级排序，新名称在前）
    BOT_NAMES = [
        BOT_NAME,  # 当前主要名称
        # "NewBotName",  # 未来可能的新名称，取消注释后生效
    ]
    
    # 群聊@提及的前缀模式
    GROUP_CHAT_PREFIXES = [f"@{name} " for name in BOT_NAMES]
    
    # 私聊命令前缀
    PRIVATE_CHAT_PREFIXES = ["/ai "]
    
    @classmethod
    def get_current_bot_name(cls) -> str:
        """获取当前主要使用的机器人名称"""
        return cls.BOT_NAME
    
    @classmethod
    def is_bot_mentioned(cls, text: str) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        检查文本是否@提及了机器人
        
        Returns:
            Tuple[bool, Optional[str]]: (是否提及, 匹配的前缀)
        """
        for prefix in cls.GROUP_CHAT_PREFIXES:
            if text.startswith(prefix):
                return True, prefix
        return False, None
    
    @classmethod
    def extract_command_from_group_message(cls, text: str) -> Optional[str]:
        """
        从群聊消息中提取命令内容（移除@机器人前缀）
        
        Returns:
            Optional[str]: 提取的命令内容，如果没有匹配返回None
        """
        is_mentioned, prefix = cls.is_bot_mentioned(text)
        if is_mentioned and prefix:
            return text[len(prefix):]
        return None
    
    @classmethod
    def extract_command_from_private_message(cls, text: str) -> Optional[str]:
        """
        从私聊消息中提取命令内容
        
        Returns:
            Optional[str]: 提取的命令内容，如果没有匹配返回None
        """
        for prefix in cls.PRIVATE_CHAT_PREFIXES:
            if text.startswith(prefix):
                return text[len(prefix):]
        
        # 私聊中也支持直接命令（不带前缀）
        # 但AI查询必须带/ai前缀
        return text
    
    @classmethod
    def generate_help_message(cls, context_type: str = "group", user_role: str = "normal", user_email: str = "", user_query: str = "") -> str:
        """
        生成帮助信息（使用基于LLM的新帮助系统）

        Args:
            context_type: 'group' 或 'private'
            user_role: 用户角色，用于过滤管理员功能
            user_email: 用户邮箱，用于权限检查
            user_query: 用户查询，用于生成个性化帮助
        """
        try:
            # 优先使用新的LLM帮助系统
            from .llm_help_system.compatibility_adapter import help_manager, UserRole

            # 转换用户角色
            role_mapping = {
                'normal': UserRole.NORMAL,
                'project_admin': UserRole.PROJECT_ADMIN,
                'super_admin': UserRole.SUPER_ADMIN
            }
            user_role_enum = role_mapping.get(user_role, UserRole.NORMAL)

            return help_manager.get_help_message(
                context_type=context_type,
                user_role=user_role_enum,
                user_email=user_email,
                user_query=user_query
            )
        except ImportError:
            # 如果LLM帮助系统不可用，尝试使用旧的帮助系统
            try:
                from .help_system.help_manager import help_manager as old_help_manager, UserRole as OldUserRole

                role_mapping = {
                    'normal': OldUserRole.NORMAL,
                    'project_admin': OldUserRole.PROJECT_ADMIN,
                    'super_admin': OldUserRole.SUPER_ADMIN
                }
                user_role_enum = role_mapping.get(user_role, OldUserRole.NORMAL)

                return old_help_manager.get_help_message(context_type, None, user_role_enum)
            except ImportError:
                # 最后的备用方案
                return cls._generate_legacy_help_message(context_type)

    @classmethod
    def _generate_legacy_help_message(cls, context_type: str) -> str:
        """生成传统帮助信息（备用方法）"""
        current_bot = cls.get_current_bot_name()

        if context_type == "group":
            return f"""👋 您好！我是 {current_bot}，您的智能工作助手！

💡 **使用提示**：群聊中需要 @{current_bot} 来调用我

📋 **核心功能**
• JIRA查询：@{current_bot} SPCB-1234
• Bug查询：@{current_bot} bug SPCB-1234
• 时间线查询：@{current_bot} timeline SPCB-1234
• 自然语言：@{current_bot} 查询我本周未完成的任务
• 定时提醒：@{current_bot} 每天9点提醒我当天有哪些未完成的子任务
• 文档处理：@{current_bot} 总结这个PRD链接
• 创建子任务：@{current_bot} 在SPCB-1234下建子任务：测试功能 2d

🆘 **获取详细帮助**：@{current_bot} help
📮 **问题反馈**：<EMAIL>"""

        else:  # private
            return f"""👋 您好！我是 {current_bot}，您的智能工作助手！

📋 **核心功能**
• JIRA查询：SPCB-1234
• Bug查询：bug SPCB-1234
• 时间线查询：timeline SPCB-1234
• 自然语言：查询我本周未完成的任务
• 定时提醒：每天9点提醒我当天的任务
• 文档处理：总结这个PRD链接
• 创建子任务：在SPCB-1234下建子任务：测试功能 2d

🆘 **获取详细帮助**：help
📮 **问题反馈**：<EMAIL>"""

# 单例实例
bot_config = BotConfig() 
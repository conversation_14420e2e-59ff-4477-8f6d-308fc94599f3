import time
import re
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from icecream import ic
import requests
import json
from datetime import datetime
import os
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

#打印日志的时候带上时间
def time_prefix(*args):
    # 返回一个字符串，而不是元组
    return f'{datetime.now()} | '

ic.configureOutput(prefix=time_prefix)

"""
    seatalk的token获取函数，如果权限发生变化，请在这里修改相对应参数
"""
def app_access_token():
    token_url = "https://openapi.seatalk.io/auth/app_access_token"
    headers = {
        'content-type': "application/json",
    }
    param = {
        "app_id": "NzM5MzEzODYyNzk5",
        "app_secret": "3dcplTo_Q3zIQCJLOs_FLzKZNJASevOT"
    }
    r = requests.post(url=token_url, json=param, headers=headers)
    results = json.loads(r.text)
    return results["app_access_token"]

def test_for_seatalk_bot(text, mentionals, group_id):
    if not text:
        return
    ic(text)
    # 定义最大消息长度（根据seatalk的限制，设为4000字符，留有余量）
    MAX_MESSAGE_LENGTH = 4000
    
    url = "https://openapi.seatalk.io/messaging/v2/group_chat"
    result = app_access_token()
    tokens = "Bearer " + result
    mentional_text = ""
    
    # 处理@提及
    if mentionals:
        for mentional in mentionals:
            if mentional == "ALL":
                mentional_text = "<mention-tag target=\"seatalk://user?id=0\"/>"
                break
            if "@shopee.com" not in mentional:
                mentional_text = f"<mention-tag target=\"seatalk://user?id={mentional}\"/>" + mentional_text
            else:
                mentional_text = f"<mention-tag target=\"seatalk://user?email={mentional}\"/>" + mentional_text
    
    # 如果有@提及，将其添加到消息开头
    if mentional_text:
        text = f"{mentional_text}\n{text}"
        ic("添加@后的消息长度:", len(text))
    
    # 分段发送消息
    def send_seatalk_message(content):
        ic("准备发送消息，长度:", len(content))
        param = {
            "group_id": group_id,
            "message": {
                "tag": "text",
                "text": {
                    "content": content,
                },
            }
        }
        headers = {
            'content-type': "application/json",
            'Authorization': tokens
        }
        r = requests.post(url=url, json=param, headers=headers)
        ic("API响应状态码:", r.status_code)
        ic("API响应内容:", r.text)
        return r.status_code == 200
    
    # 如果消息长度超过限制，分段发送
    if len(text) > MAX_MESSAGE_LENGTH:
        ic(f"消息总长度 {len(text)} 超过限制 {MAX_MESSAGE_LENGTH}，开始分段发送")
        # 按行分割文本
        lines = text.split('\n')
        ic("总行数:", len(lines))
        current_message = ""
        segment_count = 1
        
        for line in lines:
            # 如果当前行加上现有内容不会超过限制，添加到当前消息
            if len(current_message) + len(line) + 1 <= MAX_MESSAGE_LENGTH:
                if current_message:
                    current_message += '\n'
                current_message += line
            else:
                # 发送当前消息
                if current_message:
                    ic(f"发送第 {segment_count} 段消息，长度:", len(current_message))
                    send_seatalk_message(current_message)
                    segment_count += 1
                # 开始新的消息，包含当前行
                current_message = line
        
        # 发送最后一段消息
        if current_message:
            ic(f"发送最后一段（第 {segment_count} 段）消息，长度:", len(current_message))
            send_seatalk_message(current_message)
    else:
        ic(f"消息长度 {len(text)} 在限制范围内，直接发送")
        send_seatalk_message(text)

class LogHandler(FileSystemEventHandler):
    def __init__(self, log_dir, pattern):
        self.log_dir = log_dir
        self.pattern = pattern
        self.last_pos = {}
        super().__init__()

    def process(self, event):
        if event.is_directory or not event.src_path.endswith('.log'):
            return

        with open(event.src_path, 'r') as file:
            # 如果是新文件或者文件被重写，重置读取位置
            if event.src_path not in self.last_pos or event.event_type == 'modified':
                file.seek(self.last_pos.get(event.src_path, 0))

            # 读取新的日志内容
            contents = file.readlines()
            self.last_pos[event.src_path] = file.tell()

            # 查找错误信息并处理
            for index, line in enumerate(contents):
                if re.search(self.pattern, line):
                    start = max(0, index - 20)
                    end = min(len(contents), index + 21)
                    err_info = ''.join(contents[start:end])
                    log_path = event.src_path
                    message_text = f"Error found in file: {log_path}\n\n{err_info}"
                    # 这里是发送信息的逻辑，可以替换成具体的发送函数
                    time.sleep(5) #限制发送频率
                    self.send_message(message_text)

    def send_message(self, message_text):
        # 这里应该是发送消息的代码，比如调用API等
        test_for_seatalk_bot(message_text,False,'NzQzMzAxODcyMjAy')
        print(message_text)

    def on_modified(self, event):
        self.process(event)

    def on_created(self, event):
        self.process(event)

if __name__ == "__main__":
    log_dir = BASE_DIR  # 监控的目录
    pattern = r'error'  # 错误信息的正则表达式

    event_handler = LogHandler(log_dir, pattern)
    observer = Observer()
    observer.schedule(event_handler, log_dir, recursive=False)
    observer.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()

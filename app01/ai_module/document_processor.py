"""
文档处理基础模块
提供统一的文档处理接口，支持多种平台的文档读取、分析和处理
"""

import abc
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
from urllib.parse import urlparse
import re

logger = logging.getLogger(__name__)


class DocumentType(Enum):
    """文档类型枚举"""
    CONFLUENCE = "confluence"
    LOG = "log"
    TRACE = "trace"
    JIRA = "jira"
    WIKI = "wiki"
    UNKNOWN = "unknown"


class ProcessingAction(Enum):
    """处理动作枚举"""
    SUMMARIZE = "summarize"       # 总结
    TRANSLATE = "translate"       # 翻译
    QA = "qa"                    # 问答
    EXTRACT_INFO = "extract_info" # 提取关键信息
    ANALYZE = "analyze"          # 分析
    SEARCH = "search"            # 搜索


@dataclass
class DocumentSource:
    """文档源信息"""
    url: str
    document_type: DocumentType
    title: Optional[str] = None
    metadata: Optional[Dict] = None
    credentials: Optional[Dict] = None


@dataclass
class ProcessingRequest:
    """处理请求"""
    source: DocumentSource
    action: ProcessingAction
    parameters: Optional[Dict] = None
    user_query: str = ""
    target_language: Optional[str] = None


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    content: str = ""
    summary: str = ""
    error: Optional[str] = None
    metadata: Optional[Dict] = None
    processed_at: Optional[str] = None


class DocumentProcessor(abc.ABC):
    """文档处理器基类"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.supported_actions = set()
        self.timeout = self.config.get('timeout', 30)
        self.max_content_length = self.config.get('max_content_length', 50000)
    
    @abc.abstractmethod
    def supports_document_type(self, doc_type: DocumentType) -> bool:
        """检查是否支持指定的文档类型"""
        pass
    
    @abc.abstractmethod
    async def fetch_document(self, source: DocumentSource) -> Dict:
        """获取文档内容"""
        pass
    
    @abc.abstractmethod
    async def process_document(self, request: ProcessingRequest) -> ProcessingResult:
        """处理文档请求"""
        pass
    
    def extract_urls_from_text(self, text: str) -> List[str]:
        """从文本中提取URL"""
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, text)
        return [url.rstrip('.,;!?') for url in urls]
    
    def detect_document_type(self, url: str) -> DocumentType:
        """根据URL检测文档类型"""
        url_lower = url.lower()
        
        if 'confluence' in url_lower or '/wiki/' in url_lower:
            return DocumentType.CONFLUENCE
        elif 'jira' in url_lower:
            return DocumentType.JIRA
        elif any(keyword in url_lower for keyword in ['log', 'kibana', 'grafana', 'splunk']):
            return DocumentType.LOG
        elif any(keyword in url_lower for keyword in ['trace', 'jaeger', 'zipkin', 'apm']):
            return DocumentType.TRACE
        else:
            return DocumentType.UNKNOWN
    
    def truncate_content(self, content: str) -> str:
        """截断过长的内容"""
        if len(content) <= self.max_content_length:
            return content
        
        # 尝试按段落截断
        paragraphs = content.split('\n\n')
        truncated = ""
        for paragraph in paragraphs:
            if len(truncated + paragraph) <= self.max_content_length:
                truncated += paragraph + '\n\n'
            else:
                break
        
        if not truncated:
            # 如果单个段落都太长，直接截断
            truncated = content[:self.max_content_length]
        
        return truncated.strip()


class DocumentManager:
    """文档管理器 - 统一的文档处理入口"""
    
    def __init__(self):
        self.processors: Dict[DocumentType, DocumentProcessor] = {}
        self.url_cache: Dict[str, ProcessingResult] = {}
        self.cache_timeout = 3600  # 1小时缓存
    
    def register_processor(self, processor: DocumentProcessor, doc_types: List[DocumentType]):
        """注册文档处理器"""
        for doc_type in doc_types:
            if processor.supports_document_type(doc_type):
                self.processors[doc_type] = processor
                logger.info(f"已注册 {doc_type.value} 文档处理器: {processor.__class__.__name__}")
    
    def extract_document_sources(self, user_query: str, jira_info: Dict = None) -> List[DocumentSource]:
        """
        从用户查询和JIRA信息中提取文档源
        
        Args:
            user_query: 用户查询
            jira_info: JIRA信息，包含customfield_16700等字段
            
        Returns:
            文档源列表
        """
        sources = []
        
        # 1. 从用户查询中提取URL
        query_urls = self._extract_urls_from_query(user_query)
        for url in query_urls:
            doc_type = self._detect_document_type(url)
            sources.append(DocumentSource(
                url=url,
                document_type=doc_type,
                metadata={'source': 'user_query'}
            ))
        
        # 2. 从JIRA信息中提取TRD/PRD URL
        if jira_info:
            confluence_urls = self._extract_confluence_from_jira(jira_info)
            for url in confluence_urls:
                sources.append(DocumentSource(
                    url=url,
                    document_type=DocumentType.CONFLUENCE,
                    metadata={'source': 'jira_customfield_16700', 'jira_info': jira_info}
                ))
        
        return sources
    
    def _extract_urls_from_query(self, query: str) -> List[str]:
        """从查询中提取URL"""
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, query)
        return [url.rstrip('.,;!?') for url in urls]
    
    def _extract_confluence_from_jira(self, jira_info: Dict) -> List[str]:
        """从JIRA信息中提取Confluence URL"""
        urls = []
        
        # 检查customfield_16700字段
        trd_prd_url = jira_info.get('customfield_16700')
        if trd_prd_url:
            if isinstance(trd_prd_url, str):
                # 直接是URL字符串
                if trd_prd_url.startswith('http'):
                    urls.append(trd_prd_url)
            elif isinstance(trd_prd_url, dict):
                # 可能是链接对象
                url = trd_prd_url.get('url') or trd_prd_url.get('link')
                if url and url.startswith('http'):
                    urls.append(url)
            elif isinstance(trd_prd_url, list):
                # 可能是多个链接
                for item in trd_prd_url:
                    if isinstance(item, str) and item.startswith('http'):
                        urls.append(item)
                    elif isinstance(item, dict):
                        url = item.get('url') or item.get('link')
                        if url and url.startswith('http'):
                            urls.append(url)
        
        # 也检查其他可能包含链接的字段
        for field_name, field_value in jira_info.items():
            if 'url' in field_name.lower() or 'link' in field_name.lower():
                if isinstance(field_value, str) and field_value.startswith('http'):
                    # 检查是否是Confluence链接
                    if 'confluence' in field_value.lower() or '/wiki/' in field_value.lower():
                        urls.append(field_value)
        
        return urls
    
    def _detect_document_type(self, url: str) -> DocumentType:
        """检测文档类型"""
        url_lower = url.lower()
        
        if 'confluence' in url_lower or '/wiki/' in url_lower:
            return DocumentType.CONFLUENCE
        elif 'jira' in url_lower:
            return DocumentType.JIRA
        elif any(keyword in url_lower for keyword in ['log', 'kibana', 'grafana', 'splunk']):
            return DocumentType.LOG
        elif any(keyword in url_lower for keyword in ['trace', 'jaeger', 'zipkin', 'apm']):
            return DocumentType.TRACE
        else:
            return DocumentType.UNKNOWN
    
    async def process_documents(self, user_query: str, action: ProcessingAction, 
                              jira_info: Dict = None, **kwargs) -> Dict:
        """
        处理文档请求
        
        Args:
            user_query: 用户查询
            action: 处理动作
            jira_info: JIRA信息
            **kwargs: 其他参数
            
        Returns:
            处理结果
        """
        try:
            # 1. 提取文档源
            sources = self.extract_document_sources(user_query, jira_info)
            
            if not sources:
                return {
                    'success': False,
                    'error': '未找到可处理的文档链接'
                }
            
            # 2. 处理每个文档源
            results = []
            for source in sources:
                if source.document_type not in self.processors:
                    logger.warning(f"不支持的文档类型: {source.document_type}")
                    continue
                
                processor = self.processors[source.document_type]
                
                # 创建处理请求
                request = ProcessingRequest(
                    source=source,
                    action=action,
                    user_query=user_query,
                    parameters=kwargs
                )
                
                # 处理文档
                result = await processor.process_document(request)
                results.append({
                    'source': source,
                    'result': result
                })
            
            if not results:
                return {
                    'success': False,
                    'error': '没有找到支持的文档处理器'
                }
            
            # 3. 合并结果
            return self._merge_results(results, action)
            
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            return {
                'success': False,
                'error': f"文档处理失败: {str(e)}"
            }
    
    def _merge_results(self, results: List[Dict], action: ProcessingAction) -> Dict:
        """合并多个文档的处理结果"""
        if not results:
            return {'success': False, 'error': '没有处理结果'}
        
        # 检查是否有成功的结果
        successful_results = [r for r in results if r['result'].success]
        
        if not successful_results:
            errors = [r['result'].error for r in results if r['result'].error]
            return {
                'success': False,
                'error': f"所有文档处理失败: {'; '.join(errors)}"
            }
        
        # 合并成功的结果
        merged_content = []
        merged_summary = []
        
        for result_data in successful_results:
            source = result_data['source']
            result = result_data['result']
            
            if result.content:
                merged_content.append(f"## {source.title or source.url}\n\n{result.content}")
            
            if result.summary:
                merged_summary.append(f"**{source.title or '文档'}**: {result.summary}")
        
        return {
            'success': True,
            'content': '\n\n---\n\n'.join(merged_content),
            'summary': '\n\n'.join(merged_summary),
            'processed_count': len(successful_results),
            'total_count': len(results),
            'action': action.value
        }


# 全局文档管理器实例
document_manager = DocumentManager() 
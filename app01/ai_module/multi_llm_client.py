"""
多模型LLM客户端模块
支持AB测试和模型对比
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 导入配置
try:
    from app01.ai_config import MODEL_POOL, AB_TEST_CONFIG
except ImportError:
    # 默认配置
    LLM_CONFIG = {
        'API_BASE_URL': 'https://compass.llm.shopee.io/compass-api/v1',
        'API_KEY': 'your-api-key-here',
        'MODEL_NAME': 'compass-v2',
        'MAX_TOKENS': 1500,
        'TEMPERATURE': 0.7,
        'TIMEOUT': 12,
    }
    SECONDARY_MODEL_CONFIG = {
        'API_BASE_URL': 'https://compass.llm.shopee.io/compass-api/v1',
        'API_KEY': 'your-api-key-here',
        'MODEL_NAME': 'gpt-4.1',
        'MAX_TOKENS': 1500,
        'TEMPERATURE': 0.7,
        'TIMEOUT': 12,
    }
    AB_TEST_CONFIG = {
        'ENABLED': False,
        'DUAL_MODEL_COMPARISON': False,
        'SHOW_MODEL_SOURCE': True,
        'PARALLEL_REQUESTS': True,
    }


@dataclass
class ModelResponse:
    """模型响应数据类"""
    success: bool
    content: str = ""
    error: str = ""
    response_time: float = 0.0
    model_name: str = ""
    provider: str = ""


class ModelClient:
    """单个模型客户端"""
    
    def __init__(self, config: Dict, provider: str):
        self.config = config
        self.provider = provider
        self.base_url = config['API_BASE_URL']
        self.api_key = config['API_KEY']
        self.model_name = config['MODEL_NAME']
        self.max_tokens = config.get('MAX_TOKENS', 1500)
        self.temperature = config.get('TEMPERATURE', 0.7)
        self.timeout = config.get('TIMEOUT', 12)
    
    async def generate_text(self, prompt: str, system_prompt: str = None, **kwargs) -> ModelResponse:
        """生成文本"""
        try:
            start_time = time.time()
            
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            # 构建请求数据
            data = {
                "model": kwargs.get('model', self.model_name),
                "messages": messages,
                "max_tokens": kwargs.get('max_tokens', self.max_tokens),
                "temperature": kwargs.get('temperature', self.temperature),
                "stream": False
            }
            
            # 发送请求
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=data,
                    headers=headers
                ) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        
                        logger.info(f"{self.provider} 请求成功，耗时: {response_time:.2f}秒")
                        
                        return ModelResponse(
                            success=True,
                            content=content.strip(),
                            response_time=response_time,
                            model_name=self.model_name,
                            provider=self.provider
                        )
                    else:
                        error_text = await response.text()
                        logger.error(f"{self.provider} 请求失败: {response.status} - {error_text}")
                        
                        return ModelResponse(
                            success=False,
                            error=f"HTTP {response.status}: {error_text}",
                            response_time=response_time,
                            model_name=self.model_name,
                            provider=self.provider
                        )
                        
        except asyncio.TimeoutError:
            response_time = self.timeout
            logger.error(f"{self.provider} 请求超时 (>{self.timeout}秒)")
            return ModelResponse(
                success=False,
                error=f"请求超时 (>{self.timeout}秒)",
                response_time=response_time,
                model_name=self.model_name,
                provider=self.provider
            )
        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(f"{self.provider} 请求异常: {str(e)}")
            return ModelResponse(
                success=False,
                error=f"请求异常: {str(e)}",
                response_time=response_time,
                model_name=self.model_name,
                provider=self.provider
            )


class MultiLLMClient:
    """多模型LLM客户端"""
    
    def __init__(self):
        """初始化客户端"""
        # 从配置中获取模型信息
        self._load_config()
        
        # 获取A、B模型配置
        model_a_key = AB_TEST_CONFIG.get('MODEL_A', 'gpt-4.1')
        model_b_key = AB_TEST_CONFIG.get('MODEL_B', 'qwen2.5-vl')
        
        # 初始化两个模型客户端
        self.model_a_client = ModelClient(MODEL_POOL[model_a_key], AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A'))
        self.model_b_client = ModelClient(MODEL_POOL[model_b_key], AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B'))
        
        # 兼容性别名（保持向后兼容）
        self.compass_client = self.model_a_client
        self.secondary_client = self.model_b_client
    
    def _load_config(self):
        """加载AB测试配置"""
        self.ab_test_enabled = AB_TEST_CONFIG.get('ENABLED', False)
        self.dual_model_comparison = AB_TEST_CONFIG.get('DUAL_MODEL_COMPARISON', False)
        self.show_model_source = AB_TEST_CONFIG.get('SHOW_MODEL_SOURCE', True)
        self.parallel_requests = AB_TEST_CONFIG.get('PARALLEL_REQUESTS', True)
        
        # 使用A、B模型配置
        self.default_model = AB_TEST_CONFIG.get('DEFAULT_MODEL', 'A')  # 'A' 或 'B'
        self.fallback_model = AB_TEST_CONFIG.get('FALLBACK_MODEL', 'B')
        
        # 获取模型显示名称
        self.model_a_display_name = AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A')
        self.model_b_display_name = AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B')
    
    def reload_config(self):
        """重新加载配置（用于运行时切换模式）"""
        try:
            # 重新导入配置
            import importlib
            import app01.ai_config
            importlib.reload(app01.ai_config)
            from app01.ai_config import AB_TEST_CONFIG, MODEL_POOL
            
            # 重新初始化客户端
            model_a_key = AB_TEST_CONFIG.get('MODEL_A', 'gpt-4.1')
            model_b_key = AB_TEST_CONFIG.get('MODEL_B', 'qwen2.5-vl')
            
            self.model_a_client = ModelClient(MODEL_POOL[model_a_key], AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A'))
            self.model_b_client = ModelClient(MODEL_POOL[model_b_key], AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B'))
            
            # 更新兼容性别名
            self.compass_client = self.model_a_client
            self.secondary_client = self.model_b_client
            
            self._load_config()
            logger.info(f"🔄 模型配置已重新加载: A模型={self.model_a_display_name}, B模型={self.model_b_display_name}")
            return True
        except Exception as e:
            logger.error(f"配置重新加载失败: {str(e)}")
            return False
    
    def get_current_mode(self) -> str:
        """获取当前运行模式"""
        if self.ab_test_enabled and self.dual_model_comparison:
            return f"AB测试对比模式 ({self.model_a_display_name} vs {self.model_b_display_name})"
        elif self.ab_test_enabled:
            model_display = self.model_a_display_name if self.default_model == 'A' else self.model_b_display_name
            return f"AB测试单模型模式 ({model_display})"
        else:
            model_display = self.model_a_display_name if self.default_model == 'A' else self.model_b_display_name
            return f"正式单模型模式 ({model_display})"
    
    async def generate_with_retry(self, prompt: str, system_prompt: str = None, 
                                 max_retries: int = 2, **kwargs) -> Dict:
        """生成文本（带重试）"""
        if self.ab_test_enabled:
            if self.dual_model_comparison:
                # 双模型对比模式
                return await self._generate_dual_model_comparison(prompt, system_prompt, max_retries, **kwargs)
            else:
                # 单模型测试模式
                if self.default_model == 'A':
                    client = self.model_a_client
                else:
                    client = self.model_b_client
        else:
            # 正式模式 - 默认使用A模型
            client = self.model_a_client if self.default_model == 'A' else self.model_b_client
        
        # 单模型生成
        result = await self._generate_single_model(client, prompt, system_prompt, max_retries, **kwargs)
        
        # 如果失败且配置了回退模型，尝试使用回退模型
        if not result.get('success', False) and self.fallback_model != self.default_model:
            logger.warning(f"主模型请求失败，尝试使用回退模型 {self.fallback_model}")
            
            fallback_client = self.model_b_client if self.fallback_model == 'B' else self.model_a_client
            result = await self._generate_single_model(fallback_client, prompt, system_prompt, max_retries, **kwargs)
        
        return result

    async def generate_for_pipeline_step(self, step_name: str, prompt: str, system_prompt: str = None, 
                                       max_retries: int = 2, **kwargs) -> Dict:
        """
        为流水线步骤生成内容（支持完整的双模型对比）
        
        Args:
            step_name: 步骤名称 ('intent_detection', 'jql_generation', 'response_formatting', 'subtask_creation')
            prompt: 用户提示
            system_prompt: 系统提示
            max_retries: 最大重试次数
            **kwargs: 其他参数
            
        Returns:
            生成结果，在双模型模式下包含两个模型的详细对比
        """
        current_mode = self.get_current_mode()
        logger.info(f"🤖 {step_name} - 当前AI模式: {current_mode}")
        
        if self.ab_test_enabled and self.dual_model_comparison:
            # 双模型对比模式：为每个步骤生成详细对比
            return await self._generate_pipeline_dual_comparison(step_name, prompt, system_prompt, max_retries, **kwargs)
        else:
            # 其他模式：使用原有逻辑
            return await self.generate_with_retry(prompt, system_prompt, max_retries, **kwargs)

    async def _generate_pipeline_dual_comparison(self, step_name: str, prompt: str, system_prompt: str = None, 
                                               max_retries: int = 2, **kwargs) -> Dict:
        """为流水线步骤生成双模型对比"""
        start_time = time.time()
        
        if self.parallel_requests:
            # 并行请求两个模型
            tasks = [
                self._generate_single_model(self.model_a_client, prompt, system_prompt, max_retries, **kwargs),
                self._generate_single_model(self.model_b_client, prompt, system_prompt, max_retries, **kwargs)
            ]
            
            model_a_result, model_b_result = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            # 串行请求
            model_a_result = await self._generate_single_model(self.model_a_client, prompt, system_prompt, max_retries, **kwargs)
            model_b_result = await self._generate_single_model(self.model_b_client, prompt, system_prompt, max_retries, **kwargs)
        
        # 处理异常结果
        if isinstance(model_a_result, Exception):
            model_a_result = {'success': False, 'error': str(model_a_result), 'response_time': 0}
        if isinstance(model_b_result, Exception):
            model_b_result = {'success': False, 'error': str(model_b_result), 'response_time': 0}
        
        total_time = time.time() - start_time
        
        # 根据步骤类型选择合并策略
        if step_name in ['intent_detection', 'jql_generation', 'subtask_creation']:
            # 对于意图识别、JQL生成和子任务创建，我们需要选择一个结果继续流程，但保存对比信息
            return self._merge_pipeline_step_responses(step_name, model_a_result, model_b_result, total_time)
        else:
            # 对于响应格式化，使用原有的合并逻辑
            merged_response = self._merge_dual_responses(model_a_result, model_b_result)
            merged_response['total_response_time'] = total_time
            return merged_response

    def _merge_pipeline_step_responses(self, step_name: str, model_a_result: Dict, model_b_result: Dict, total_time: float) -> Dict:
        """合并流水线步骤的双模型响应"""
        # 检查是否至少有一个模型成功
        model_a_success = model_a_result.get('success', False)
        model_b_success = model_b_result.get('success', False)
        
        if not model_a_success and not model_b_success:
            return {
                'success': False,
                'error': f"所有模型都失败了。{self.model_a_display_name}: {model_a_result.get('error', 'Unknown error')}; {self.model_b_display_name}: {model_b_result.get('error', 'Unknown error')}",
                'response_time': total_time
            }
        
        # 选择主要结果（优先选择成功的，如果都成功则选择A模型）
        if model_a_success:
            primary_result = model_a_result
            primary_model = self.model_a_display_name
        else:
            primary_result = model_b_result
            primary_model = self.model_b_display_name
        
        # 构建结果
        result = {
            'success': True,
            'content': primary_result['content'],
            'response_time': total_time,
            'model_info': {
                'primary_model': primary_model,
                'model_a_success': model_a_success,
                'model_b_success': model_b_success,
                'model_a_time': model_a_result.get('response_time', 0),
                'model_b_time': model_b_result.get('response_time', 0),
            }
        }
        
        # 保存两个模型的结果，用于后续分析
        if model_a_success:
            result['model_a_result'] = model_a_result['content']
        if model_b_success:
            result['model_b_result'] = model_b_result['content']
        
        return result

    def _get_client_by_name(self, model_name: str) -> ModelClient:
        """根据模型名称获取客户端"""
        # 支持模型池中的模型名称
        if model_name in MODEL_POOL:
            config = MODEL_POOL[model_name]
            return ModelClient(config, config.get('DISPLAY_NAME', model_name))
        elif model_name.lower() == 'a':
            return self.model_a_client
        elif model_name.lower() == 'b':
            return self.model_b_client
        else:
            # 默认返回A模型客户端
            return self.model_a_client
    
    async def _generate_single_model(self, client: ModelClient, prompt: str, 
                                   system_prompt: str = None, max_retries: int = 2, **kwargs) -> Dict:
        """单模型生成（带重试）"""
        for attempt in range(max_retries + 1):
            result = await client.generate_text(prompt, system_prompt, **kwargs)
            
            if result.success:
                return {
                    'success': True,
                    'content': result.content,
                    'response_time': result.response_time,
                    'model_info': {
                        'provider': result.provider,
                        'model_name': result.model_name
                    }
                }
            
            # 如果是A模型失败且是最后一次尝试，尝试使用B模型作为fallback
            if client == self.model_a_client and attempt == max_retries:
                logger.info(f"{self.model_a_display_name} 请求失败，尝试使用 {self.model_b_display_name} 作为fallback")
                try:
                    fallback_result = await self.model_b_client.generate_text(prompt, system_prompt, **kwargs)
                    if fallback_result.success:
                        return {
                            'success': True,
                            'content': fallback_result.content,
                            'response_time': fallback_result.response_time,
                            'model_info': {
                                'provider': fallback_result.provider,
                                'model_name': fallback_result.model_name
                            },
                            'fallback': True
                        }
                except Exception as fallback_e:
                    logger.error(f"{self.model_b_display_name} fallback 也失败: {str(fallback_e)}")
            
            if attempt < max_retries:
                wait_time = 2 ** attempt
                logger.warning(f"{client.provider} 请求失败，{wait_time}秒后重试 (第{attempt + 1}次)")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"{client.provider} 请求最终失败，已重试{max_retries}次")
        
        return {
            'success': False,
            'error': result.error,
            'response_time': result.response_time
        }
    
    async def _generate_dual_model_comparison(self, prompt: str, system_prompt: str = None, 
                                            max_retries: int = 2, **kwargs) -> Dict:
        """双模型对比生成"""
        start_time = time.time()
        
        if self.parallel_requests:
            # 并行请求两个模型
            tasks = [
                self._generate_single_model(self.model_a_client, prompt, system_prompt, max_retries, **kwargs),
                self._generate_single_model(self.model_b_client, prompt, system_prompt, max_retries, **kwargs)
            ]
            
            model_a_result, model_b_result = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            # 串行请求
            model_a_result = await self._generate_single_model(self.model_a_client, prompt, system_prompt, max_retries, **kwargs)
            model_b_result = await self._generate_single_model(self.model_b_client, prompt, system_prompt, max_retries, **kwargs)
        
        # 处理异常结果
        if isinstance(model_a_result, Exception):
            model_a_result = {'success': False, 'error': str(model_a_result), 'response_time': 0}
        if isinstance(model_b_result, Exception):
            model_b_result = {'success': False, 'error': str(model_b_result), 'response_time': 0}
        
        total_time = time.time() - start_time
        
        # 构建合并响应
        merged_response = self._merge_dual_responses(model_a_result, model_b_result)
        merged_response['total_response_time'] = total_time
        
        return merged_response
    
    def _merge_dual_responses(self, compass_result: Dict, secondary_result: Dict) -> Dict:
        """合并两个模型的响应"""
        # 检查是否至少有一个模型成功
        at_least_one_success = compass_result.get('success', False) or secondary_result.get('success', False)
        
        if not at_least_one_success:
            return {
                'success': False,
                'error': f"所有模型都失败了。{self.model_a_display_name}: {compass_result.get('error', 'Unknown error')}; {self.model_b_display_name}: {secondary_result.get('error', 'Unknown error')}",
                'response_time': max(compass_result.get('response_time', 0), secondary_result.get('response_time', 0))
            }
        
        # 构建合并的内容
        merged_content = "🤖 **AI模型对比结果**\n\n"
        
        # A模型结果
        if compass_result.get('success', False):
            merged_content += f"## 🤖 **{self.model_a_display_name} 结果:**\n"
            merged_content += f"{compass_result['content']}\n\n"
            merged_content += f"*响应时间: {compass_result.get('response_time', 0):.2f}秒*\n\n"
        else:
            merged_content += f"## 🤖 **{self.model_a_display_name} 结果:**\n"
            merged_content += f"❌ **错误:** {compass_result.get('error', 'Unknown error')}\n\n"
        
        merged_content += "---\n\n"
        
        # B模型结果
        if secondary_result.get('success', False):
            merged_content += f"## 🧭 **{self.model_b_display_name} 结果:**\n"
            merged_content += f"{secondary_result['content']}\n\n"
            merged_content += f"*响应时间: {secondary_result.get('response_time', 0):.2f}秒*\n\n"
        else:
            merged_content += f"## 🧭 **{self.model_b_display_name} 结果:**\n"
            merged_content += f"❌ **错误:** {secondary_result.get('error', 'Unknown error')}\n\n"
        
        merged_content += "---\n\n"
        merged_content += "💡 **请根据上述两个模型的结果选择您认为更好的答案！**"
        
        return {
            'success': True,
            'content': merged_content,
            'response_time': max(compass_result.get('response_time', 0), secondary_result.get('response_time', 0)),
            'model_comparison': True,
            'models': {
                'compass': {
                    'success': compass_result.get('success', False),
                    'response_time': compass_result.get('response_time', 0),
                    'content': compass_result.get('content', '') if compass_result.get('success', False) else ''
                },
                'secondary': {
                    'success': secondary_result.get('success', False),
                    'response_time': secondary_result.get('response_time', 0),
                    'content': secondary_result.get('content', '') if secondary_result.get('success', False) else '',
                    'name': self.secondary_model_name
                }
            }
        }

# 单例实例
multi_llm_client = MultiLLMClient()


# 兼容性函数 - 保持与原有代码的兼容性
async def generate_with_retry(prompt: str, system_prompt: str = None, max_retries: int = 2, **kwargs) -> Dict:
    """兼容性函数，保持与原有代码的接口一致"""
    return await multi_llm_client.generate_with_retry(prompt, system_prompt, max_retries, **kwargs) 
"""
文档处理集成模块
将文档处理功能集成到现有的 AI 系统中
"""

import asyncio
import logging
import os
import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from icecream import ic

from app01.bot_config import BotConfig
from .document_processor import DocumentType, ProcessingAction, document_manager
from .confluence_processor import confluence_processor
from .document_config import document_config
from .smart_jira_query import SmartJiraQuery

logger = logging.getLogger(__name__)


class DocumentIntegration:
    """文档处理集成器"""
    
    def __init__(self):
        self.smart_jira = SmartJiraQuery()
        self._initialize_processors()
    
    def _initialize_processors(self):
        """初始化处理器"""
        try:
            # 只在调试模式下显示详细日志
            debug_mode = os.getenv('DOCUMENT_CONFIG_DEBUG', 'false').lower() == 'true'
            
            # 注册 Confluence 处理器
            confluence_config = document_config.get_confluence_config()
            
            if confluence_config.get('enabled', True):
                if debug_mode:
                    ic("Confluence处理器已启用，开始初始化")
                
                # 重新创建实例而不是调用__init__
                from .confluence_processor import ConfluenceProcessor
                global confluence_processor
                confluence_processor = ConfluenceProcessor({'confluence': confluence_config})
                
                # 注册到文档管理器
                document_manager.register_processor(
                    confluence_processor, 
                    [DocumentType.CONFLUENCE]
                )
                logger.info(f"{BotConfig.BOT_NAME} Confluence 处理器已注册")
            else:
                if debug_mode:
                    ic("❌ Confluence处理器未启用")
            
            # TODO: 后续可以在这里注册其他处理器（日志平台、追踪平台等）
            
        except Exception as e:
            if debug_mode:
                ic("❌ 初始化处理器异常:", str(e))
                import traceback
                ic("完整异常信息:", traceback.format_exc())
            logger.error(f"初始化文档处理器失败: {str(e)}")
    
    def detect_document_intent(self, user_query: str) -> Dict:
        """
        检测用户查询中的文档处理意图
        
        Args:
            user_query: 用户查询
            
        Returns:
            意图检测结果
        """
        query_lower = user_query.lower()
        
        # 文档处理关键词
        document_keywords = {
            'confluence': ['confluence', 'wiki', '文档', '页面'],
            'prd_trd': ['prd', 'trd', '需求文档', '设计文档', '技术文档', '产品文档', '详细设计'],  # 新增PRD/TRD关键词
            'content': ['内容', '主要内容', '文档内容', '需求内容', '详细', '介绍', '说了什么'],  # 新增内容相关关键词
            'summarize': ['总结', '摘要', '概括', '归纳', 'summarize', 'summary'],
            'translate': ['翻译', '译成', '转换为', 'translate', 'translation'],
            'qa': ['问', '回答', '什么是', '如何', '为什么', 'question', 'answer', 'qa'],
            'extract': ['提取', '关键信息', '要点', 'extract', 'key info'],
            'analyze': ['分析', '评估', '审查', 'analyze', 'analysis', 'review']
        }
        
        # 检测文档类型
        detected_types = []
        for doc_type, keywords in document_keywords.items():
            if doc_type in ['confluence', 'prd_trd']:  # 扩展文档类型
                if any(keyword in query_lower for keyword in keywords):
                    detected_types.append(doc_type)
        
        # 检测处理动作
        detected_actions = []
        action_mapping = {
            'summarize': ProcessingAction.SUMMARIZE,
            'translate': ProcessingAction.TRANSLATE,
            'qa': ProcessingAction.QA,
            'extract': ProcessingAction.EXTRACT_INFO,
            'analyze': ProcessingAction.ANALYZE
        }
        
        for action_name, keywords in document_keywords.items():
            if action_name in action_mapping:
                if any(keyword in query_lower for keyword in keywords):
                    detected_actions.append(action_mapping[action_name])
        
        # 检测URL
        urls = self._extract_urls(user_query)
        
        # 特殊检测：JIRA单号 + 内容查询
        has_jira_content_query = self._detect_jira_content_query(user_query)
        
        # 如果检测到JIRA内容查询，自动添加QA动作
        if has_jira_content_query and not detected_actions:
            detected_actions.append(ProcessingAction.QA)
        
        return {
            'has_document_intent': bool(detected_types or detected_actions or urls or has_jira_content_query),
            'document_types': detected_types,
            'actions': detected_actions,
            'urls': urls,
            'confidence': self._calculate_confidence(detected_types, detected_actions, urls, has_jira_content_query)
        }
    
    def _detect_jira_content_query(self, user_query: str) -> bool:
        """
        检测JIRA单号+内容查询模式
        
        Args:
            user_query: 用户查询
            
        Returns:
            是否为JIRA内容查询
        """
        import re
        
        # 检查是否包含JIRA单号
        jira_pattern = r'\b[A-Z]{2,}-\d+\b'
        has_jira_key = bool(re.search(jira_pattern, user_query, re.IGNORECASE))
        
        if not has_jira_key:
            return False
        
        # 检查是否包含内容查询关键词
        content_keywords = [
            '内容', '主要内容', '文档内容', '需求内容', 
            '说了什么', '详细', '介绍', '主要',
            'prd', 'trd', '需求', '文档'
        ]
        
        query_lower = user_query.lower()
        has_content_keyword = any(keyword in query_lower for keyword in content_keywords)
        
        return has_content_keyword
    
    def _extract_urls(self, text: str) -> List[str]:
        """从文本中提取URL"""
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, text)
        return [url.rstrip('.,;!?') for url in urls]
    
    def _calculate_confidence(self, doc_types: List, actions: List, urls: List, has_jira_content_query: bool = False) -> float:
        """计算意图检测置信度"""
        confidence = 0.0
        
        if urls:
            confidence += 0.6  # 有URL的置信度较高
        if doc_types:
            confidence += 0.3  # 有文档类型关键词
        if actions:
            confidence += 0.3  # 有处理动作关键词
        if has_jira_content_query:
            confidence += 0.4  # 有JIRA内容查询，给予较高权重
        
        return min(confidence, 1.0)
    
    async def process_document_query(self, user_query: str, jira_key: str = None,
                                   target_language: str = None, document_urls: List[str] = None) -> Dict:
        """
        处理文档查询
        
        Args:
            user_query: 用户查询
            jira_key: JIRA键值（用于获取关联的文档）
            target_language: 目标语言（翻译时使用）
            document_urls: 直接提供的文档URL列表
            
        Returns:
            处理结果
        """
        try:
            # 1. 检测文档处理意图
            intent_result = self.detect_document_intent(user_query)
            
            if not intent_result['has_document_intent'] and not document_urls:
                return {
                    'success': False,
                    'error': '未检测到文档处理意图'
                }
            
            # 2. 获取JIRA信息（如果提供了JIRA键值）
            jira_info = None
            if jira_key:
                jira_result = await self._get_jira_info(jira_key)
                if jira_result['success']:
                    jira_info = jira_result['data']
            
            # 3. 确定处理动作
            action = self._determine_action(intent_result['actions'], user_query)
            
            # 4. 处理文档
            process_result = await document_manager.process_documents(
                user_query=user_query,
                action=action,
                jira_info=jira_info,
                target_language=target_language,
                document_urls=document_urls
            )
            
            if process_result['success']:
                return {
                    'success': True,
                    'response': self._format_response(process_result, action, user_query),
                    'action': action.value,
                    'processed_count': process_result.get('processed_count', 0),
                    'metadata': {
                        'intent_confidence': intent_result['confidence'],
                        'detected_types': intent_result['document_types'],
                        'urls': intent_result['urls']
                    }
                }
            else:
                return {
                    'success': False,
                    'error': process_result['error']
                }
                
        except Exception as e:
            logger.error(f"处理文档查询失败: {str(e)}")
            return {
                'success': False,
                'error': f'处理文档查询失败: {str(e)}'
            }
    
    def _determine_action(self, detected_actions: List[ProcessingAction], 
                         user_query: str) -> ProcessingAction:
        """
        确定处理动作
        
        Args:
            detected_actions: 检测到的动作列表
            user_query: 用户查询
            
        Returns:
            确定的处理动作
        """
        if not detected_actions:
            # 如果没有明确的动作，根据查询内容推断
            query_lower = user_query.lower()
            if any(word in query_lower for word in ['翻译', 'translate']):
                return ProcessingAction.TRANSLATE
            elif any(word in query_lower for word in ['总结', 'summarize', '摘要']):
                return ProcessingAction.SUMMARIZE
            elif any(word in query_lower for word in ['问', '回答', '什么', '如何', '为什么']):
                return ProcessingAction.QA
            elif any(word in query_lower for word in ['提取', '关键信息', 'extract']):
                return ProcessingAction.EXTRACT_INFO
            elif any(word in query_lower for word in ['分析', 'analyze', '评估']):
                return ProcessingAction.ANALYZE
            else:
                return ProcessingAction.SUMMARIZE  # 默认为总结
        else:
            # 返回第一个检测到的动作
            return detected_actions[0]
    
    async def _get_jira_info(self, jira_key: str) -> Dict:
        """
        获取JIRA信息
        
        Args:
            jira_key: JIRA键值
            
        Returns:
            JIRA信息
        """
        try:
            # 使用智能JIRA查询获取单个issue信息
            result = await self.smart_jira.get_issue_details(jira_key)
            
            if result['success'] and result['data']:
                issue_data = result['data']
                return {
                    'success': True,
                    'data': {
                        'key': issue_data.get('key'),
                        'summary': issue_data.get('summary'),
                        'customfield_16700': issue_data.get('customfield_16700'),  # TRD/PRD URL
                        **issue_data  # 包含所有字段
                    }
                }
            else:
                return {
                    'success': False,
                    'error': f'获取JIRA信息失败: {result.get("error", "未知错误")}'
                }
                
        except Exception as e:
            logger.error(f"获取JIRA信息异常: {str(e)}")
            return {
                'success': False,
                'error': f'获取JIRA信息异常: {str(e)}'
            }
    
    def _format_response(self, process_result: Dict, action: ProcessingAction, 
                        user_query: str) -> str:
        """
        格式化响应
        
        Args:
            process_result: 处理结果
            action: 处理动作
            user_query: 用户查询
            
        Returns:
            格式化的响应
        """
        try:
            action_name_map = {
                ProcessingAction.SUMMARIZE: '总结',
                ProcessingAction.TRANSLATE: '翻译',
                ProcessingAction.QA: '问答',
                ProcessingAction.EXTRACT_INFO: '信息提取',
                ProcessingAction.ANALYZE: '分析'
            }
            
            action_name = action_name_map.get(action, '处理')
            processed_count = process_result.get('processed_count', 0)
            total_count = process_result.get('total_count', 0)
            
            response = f"📄 **文档{action_name}结果**\n\n"
            
            if processed_count < total_count:
                response += f"⚠️ 处理了 {processed_count}/{total_count} 个文档\n\n"
            
            # 添加主要内容
            if process_result.get('content'):
                response += process_result['content']
            
            # 如果有摘要，也添加进去
            if process_result.get('summary') and process_result['summary'] != process_result.get('content'):
                response += f"\n\n📋 **处理摘要**\n{process_result['summary']}"
            
            response += f"\n\n✅ 文档{action_name}完成"
            
            return response
            
        except Exception as e:
            logger.error(f"格式化响应失败: {str(e)}")
            return f"处理完成，但格式化响应时出错: {str(e)}"
    
    def get_help_message(self) -> str:
        """获取文档处理功能的帮助信息"""
        supported_platforms = document_config.get_supported_platforms()
        bot_name = BotConfig.BOT_NAME
        
        help_msg = "📄 **文档处理功能帮助** 📄\n\n"
        help_msg += f"{bot_name}支持从多种文档平台读取内容并进行AI处理：\n\n"
        
        # 显示支持的平台
        help_msg += "🌐 **支持的平台**：\n"
        for platform, info in supported_platforms.items():
            if info['enabled']:
                help_msg += f"• {info['name']}: {info['description']}\n"
                if 'sub_platforms' in info:
                    for sub_name, sub_info in info['sub_platforms'].items():
                        if sub_info['enabled']:
                            help_msg += f"  - {sub_info['name']}\n"
        
        help_msg += "\n🔧 **支持的操作**：\n"
        help_msg += "• **总结**: 总结文档内容\n"
        help_msg += "• **翻译**: 翻译文档到指定语言\n"
        help_msg += "• **问答**: 基于文档内容回答问题\n"
        help_msg += "• **信息提取**: 提取关键信息和要点\n"
        help_msg += "• **分析**: 深入分析文档内容\n"
        
        help_msg += "\n📝 **使用示例**：\n"
        help_msg += f"• `总结这个文档 https://confluence.example.com/wiki/pages/123`\n"
        help_msg += f"• `翻译 PROJ-123 相关文档为英文`\n"
        help_msg += f"• `这个文档说了什么关于API的内容？`\n"
        help_msg += f"• `从文档中提取关键信息`\n"
        help_msg += f"• `分析文档的完整性`\n"
        
        help_msg += "\n💡 **提示**：\n"
        help_msg += "• 可以直接提供文档URL，或提供JIRA键值自动获取关联文档\n"
        help_msg += "• 支持处理JIRA中的TRD/PRD链接\n"
        help_msg += "• 翻译功能支持多种语言\n"
        help_msg += "• 所有文档处理功能需要使用 `/ai` 前缀\n"
        
        return help_msg
    
    def get_status_info(self) -> Dict:
        """获取文档处理功能状态信息"""
        try:
            config_validation = document_config.validate_config()
            supported_platforms = document_config.get_supported_platforms()
            
            enabled_platforms = []
            for platform, info in supported_platforms.items():
                if info['enabled']:
                    enabled_platforms.append(info['name'])
                    if 'sub_platforms' in info:
                        for sub_name, sub_info in info['sub_platforms'].items():
                            if sub_info['enabled']:
                                enabled_platforms.append(f"  - {sub_info['name']}")
            
            return {
                'enabled': True,
                'config_valid': config_validation['valid'],
                'config_errors': config_validation.get('errors', []),
                'config_warnings': config_validation.get('warnings', []),
                'enabled_platforms': enabled_platforms,
                'total_processors': len(document_manager.processors)
            }
            
        except Exception as e:
            logger.error(f"获取状态信息失败: {str(e)}")
            return {
                'enabled': False,
                'error': str(e)
            }


# 全局文档集成器实例
document_integration = DocumentIntegration() 
"""
服务分析和数据记录模块
用于记录系统使用情况并提供数据分析
"""

import asyncio
import json
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.db.models.functions import TruncDate, TruncHour

from app01.models import UserServiceUsage, AIQueryHistory, UserTodoItem, UserScheduledTask
from .private_chat import PrivateChat

logger = logging.getLogger(__name__)


class ServiceAnalytics:
    """服务分析器"""
    
    def __init__(self):
        self.private_chat = PrivateChat()
    
    async def record_service_usage(self, user_id: str, user_email: str, employee_code: str,
                                  group_id: str, service_type: str, intent: str,
                                  query_content: str, success: bool, processing_time: float,
                                  response_content: str = "", error_message: str = "",
                                  metadata: Dict = None) -> Dict:
        """
        记录服务使用情况
        
        Args:
            user_id: 用户ID
            user_email: 用户邮箱
            employee_code: 员工代码
            group_id: 群组ID
            service_type: 服务类型
            intent: 意图
            query_content: 查询内容
            success: 是否成功
            processing_time: 处理时间
            response_content: 响应内容
            error_message: 错误信息
            metadata: 额外元数据
        """
        try:
            def _record_usage():
                usage = UserServiceUsage(
                    user_id=user_id,
                    user_email=user_email,
                    employee_code=employee_code,
                    group_id=group_id,
                    service_type=service_type,
                    intent=intent,
                    query_content=query_content,
                    success=success,
                    response_length=len(response_content) if response_content else 0,
                    processing_time=processing_time,
                    error_message=error_message,
                    metadata=metadata or {}
                )
                usage.save()
                
                return {
                    'success': True,
                    'usage_id': usage.id,
                    'message': '使用记录已保存'
                }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _record_usage)
            
        except Exception as e:
            logger.error(f"记录服务使用失败: {str(e)}")
            return {
                'success': False,
                'error': f'记录失败: {str(e)}'
            }
    
    async def get_user_usage_stats(self, user_id: str, days: int = 7) -> Dict:
        """获取用户使用统计"""
        try:
            def _get_stats():
                end_date = timezone.now()
                start_date = end_date - timedelta(days=days)
                
                # 基础统计
                total_queries = UserServiceUsage.objects.filter(
                    user_id=user_id,
                    created_at__gte=start_date
                ).count()
                
                successful_queries = UserServiceUsage.objects.filter(
                    user_id=user_id,
                    created_at__gte=start_date,
                    success=True
                ).count()
                
                # 按服务类型统计
                service_stats = UserServiceUsage.objects.filter(
                    user_id=user_id,
                    created_at__gte=start_date
                ).values('service_type').annotate(
                    count=Count('id'),
                    success_rate=Count('id', filter=Q(success=True)) * 100.0 / Count('id'),
                    avg_processing_time=Avg('processing_time')
                ).order_by('-count')
                
                # 按意图统计
                intent_stats = UserServiceUsage.objects.filter(
                    user_id=user_id,
                    created_at__gte=start_date,
                    intent__isnull=False
                ).values('intent').annotate(
                    count=Count('id'),
                    success_rate=Count('id', filter=Q(success=True)) * 100.0 / Count('id')
                ).order_by('-count')
                
                # 每日使用量
                daily_usage = UserServiceUsage.objects.filter(
                    user_id=user_id,
                    created_at__gte=start_date
                ).annotate(
                    date=TruncDate('created_at')
                ).values('date').annotate(
                    count=Count('id'),
                    success_count=Count('id', filter=Q(success=True))
                ).order_by('date')
                
                # 错误分析
                error_stats = UserServiceUsage.objects.filter(
                    user_id=user_id,
                    created_at__gte=start_date,
                    success=False,
                    error_message__isnull=False
                ).exclude(error_message='').values('error_message').annotate(
                    count=Count('id')
                ).order_by('-count')[:10]
                
                return {
                    'success': True,
                    'stats': {
                        'total_queries': total_queries,
                        'successful_queries': successful_queries,
                        'success_rate': round(successful_queries / max(total_queries, 1) * 100, 2),
                        'service_breakdown': list(service_stats),
                        'intent_breakdown': list(intent_stats),
                        'daily_usage': list(daily_usage),
                        'top_errors': list(error_stats),
                        'period': f"{days} 天",
                        'start_date': start_date.date().isoformat(),
                        'end_date': end_date.date().isoformat()
                    }
                }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _get_stats)
            
        except Exception as e:
            logger.error(f"获取用户统计失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取统计失败: {str(e)}'
            }
    
    async def get_system_overview(self, days: int = 7) -> Dict:
        """获取系统整体概况"""
        try:
            def _get_overview():
                end_date = timezone.now()
                start_date = end_date - timedelta(days=days)
                
                # 总体统计
                total_users = UserServiceUsage.objects.filter(
                    created_at__gte=start_date
                ).values('user_id').distinct().count()
                
                total_queries = UserServiceUsage.objects.filter(
                    created_at__gte=start_date
                ).count()
                
                successful_queries = UserServiceUsage.objects.filter(
                    created_at__gte=start_date,
                    success=True
                ).count()
                
                avg_processing_time = UserServiceUsage.objects.filter(
                    created_at__gte=start_date,
                    success=True
                ).aggregate(avg_time=Avg('processing_time'))['avg_time'] or 0
                
                # 最活跃用户
                top_users = UserServiceUsage.objects.filter(
                    created_at__gte=start_date
                ).values('user_id', 'user_email').annotate(
                    query_count=Count('id'),
                    success_rate=Count('id', filter=Q(success=True)) * 100.0 / Count('id')
                ).order_by('-query_count')[:10]
                
                # 服务类型使用分布
                service_distribution = UserServiceUsage.objects.filter(
                    created_at__gte=start_date
                ).values('service_type').annotate(
                    count=Count('id'),
                    success_rate=Count('id', filter=Q(success=True)) * 100.0 / Count('id'),
                    unique_users=Count('user_id', distinct=True)
                ).order_by('-count')
                
                # 每小时使用量分布
                hourly_usage = UserServiceUsage.objects.filter(
                    created_at__gte=start_date
                ).annotate(
                    hour=TruncHour('created_at')
                ).values('hour').annotate(
                    count=Count('id')
                ).order_by('hour')
                
                return {
                    'success': True,
                    'overview': {
                        'total_users': total_users,
                        'total_queries': total_queries,
                        'successful_queries': successful_queries,
                        'overall_success_rate': round(successful_queries / max(total_queries, 1) * 100, 2),
                        'avg_processing_time': round(avg_processing_time, 3),
                        'top_users': list(top_users),
                        'service_distribution': list(service_distribution),
                        'hourly_usage': list(hourly_usage),
                        'period': f"{days} 天",
                        'generated_at': timezone.now().isoformat()
                    }
                }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _get_overview)
            
        except Exception as e:
            logger.error(f"获取系统概况失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取概况失败: {str(e)}'
            }
    
    async def get_feature_usage_stats(self, feature_name: str, days: int = 30, user_id: str = None) -> Dict:
        """获取特定功能的使用统计"""
        try:
            # 构建查询条件
            query = {}
            
            # 时间范围过滤
            if days > 0:
                from_date = datetime.now() - timedelta(days=days)
                query['timestamp__gte'] = from_date
                
            # 功能名称过滤
            if feature_name:
                query['feature'] = feature_name
                
            # 用户过滤
            if user_id:
                query['user_id'] = user_id
                
            # 执行查询
            records = await self.get_usage_records(query)
            
            # 分析数据
            total_count = len(records)
            success_count = sum(1 for r in records if r.get('success', False))
            avg_time = sum(r.get('processing_time', 0) for r in records) / max(total_count, 1)
            
            # 构建结果
            result = {
                'success': True,
                'feature': feature_name,
                'days': days,
                'total_count': total_count,
                'success_count': success_count,
                'success_rate': success_count / max(total_count, 1) * 100,
                'avg_processing_time': avg_time,
                'user_id': user_id
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取功能使用统计异常: {str(e)}")
            return {
                'success': False,
                'error': f"获取功能使用统计失败: {str(e)}"
            }
    
    async def process_analytics_command(self, command: str, user_id: str, user_email: str) -> Dict:
        """
        处理用户的统计分析命令
        
        Args:
            command: 命令内容 (不含usage前缀)
            user_id: 用户ID
            user_email: 用户邮箱
            
        Returns:
            处理结果
        """
        try:
            # 解析命令参数
            command_parts = command.strip().lower().split()
            command_type = command_parts[0] if command_parts else "user"
            
            # 默认参数
            days = 30
            feature = None
            
            # 解析其他参数
            for i, part in enumerate(command_parts):
                if part == "period" and i + 1 < len(command_parts):
                    try:
                        days = int(command_parts[i + 1])
                    except ValueError:
                        pass
                elif part == "feature" and i + 1 < len(command_parts):
                    feature = command_parts[i + 1]
            
            # 根据命令类型执行不同的统计
            if command_type in ["user", "me", "my", "我的"]:
                # 个人使用统计
                stats = await self.get_user_stats(user_id, days)
                response = self._format_user_stats(stats, days)
                return {
                    'success': True,
                    'response': response,
                    'analytics_type': 'user'
                }
                
            elif command_type in ["summary", "overview", "系统", "摘要"]:
                # 系统概览统计
                stats = await self.get_system_overview(days)
                response = self._format_system_overview(stats)
                return {
                    'success': True,
                    'response': response,
                    'analytics_type': 'system'
                }
                
            elif command_type in ["details", "detail", "详情"]:
                # 详细统计
                user_stats = await self.get_user_stats(user_id, days)
                system_stats = await self.get_system_overview(days)
                feature_stats = await self.get_feature_breakdown(days=days)
                
                response = self._format_detailed_stats(user_stats, system_stats, feature_stats, days)
                return {
                    'success': True,
                    'response': response,
                    'analytics_type': 'details'
                }
                
            elif command_type in ["feature", "功能"] and feature:
                # 特定功能统计
                stats = await self.get_feature_usage_stats(feature, days, user_id)
                response = self._format_feature_stats(stats)
                return {
                    'success': True,
                    'response': response,
                    'analytics_type': 'feature',
                    'feature': feature
                }
                
            else:
                # 默认返回用户统计
                stats = await self.get_user_stats(user_id, days)
                response = self._format_user_stats(stats, days)
                return {
                    'success': True,
                    'response': response,
                    'analytics_type': 'user'
                }
                
        except Exception as e:
            logger.error(f"处理统计命令异常: {str(e)}")
            return {
                'success': False,
                'error': f"处理统计命令失败: {str(e)}"
            }
    
    def _format_user_stats(self, stats: Dict, days: int) -> str:
        """格式化用户统计信息"""
        if not stats['success']:
            return f"❌ 获取统计信息失败: {stats.get('error', '未知错误')}"
            
        return f"""📊 **您的使用统计** (最近{days}天)

• 总查询次数: {stats.get('total_queries', 0)}
• 成功率: {stats.get('success_rate', 0):.1f}%
• 平均响应时间: {stats.get('avg_response_time', 0):.2f}秒
• 最常用功能: {stats.get('top_features', [{'name': '无', 'count': 0}])[0]['name']}
• 最近查询: {stats.get('last_query_time', '无')}

**功能使用排行:**
{self._format_feature_ranking(stats.get('top_features', []))}
"""
    
    def _format_system_overview(self, stats: Dict) -> str:
        """格式化系统概览统计"""
        if not stats['success']:
            return f"❌ 获取系统统计信息失败: {stats.get('error', '未知错误')}"
            
        return f"""📈 **系统使用概览**

• 总查询次数: {stats.get('total_queries', 0)}
• 活跃用户数: {stats.get('active_users', 0)}
• 平均成功率: {stats.get('avg_success_rate', 0):.1f}%
• 平均响应时间: {stats.get('avg_response_time', 0):.2f}秒
• 最常用功能: {stats.get('top_features', [{'name': '无', 'count': 0}])[0]['name']}

**用户活跃度排行:**
{self._format_user_ranking(stats.get('top_users', []))}
"""
    
    def _format_detailed_stats(self, user_stats: Dict, system_stats: Dict, feature_stats: Dict, days: int) -> str:
        """格式化详细统计信息"""
        if not user_stats['success'] or not system_stats['success']:
            return f"❌ 获取详细统计信息失败: {user_stats.get('error') or system_stats.get('error', '未知错误')}"
            
        return f"""📊 **详细使用统计** (最近{days}天)

**个人统计:**
• 总查询次数: {user_stats.get('total_queries', 0)}
• 成功率: {user_stats.get('success_rate', 0):.1f}%
• 平均响应时间: {user_stats.get('avg_response_time', 0):.2f}秒

**系统概览:**
• 总查询次数: {system_stats.get('total_queries', 0)}
• 活跃用户数: {system_stats.get('active_users', 0)}
• 平均成功率: {system_stats.get('avg_success_rate', 0):.1f}%

**功能使用分布:**
{self._format_feature_breakdown(feature_stats.get('features', []))}
"""
    
    def _format_feature_stats(self, stats: Dict) -> str:
        """格式化功能统计信息"""
        if not stats['success']:
            return f"❌ 获取功能统计信息失败: {stats.get('error', '未知错误')}"
            
        return f"""🔍 **功能使用统计: {stats.get('feature', '未知')}**

• 使用次数: {stats.get('total_count', 0)}
• 成功次数: {stats.get('success_count', 0)}
• 成功率: {stats.get('success_rate', 0):.1f}%
• 平均处理时间: {stats.get('avg_processing_time', 0):.2f}秒
• 统计周期: 最近{stats.get('days', 30)}天
"""
    
    def _format_feature_ranking(self, features: List[Dict]) -> str:
        """格式化功能排行"""
        if not features:
            return "无数据"
            
        result = []
        for i, feature in enumerate(features[:5], 1):
            result.append(f"{i}. {feature.get('name', '未知')} - {feature.get('count', 0)}次")
            
        return "\n".join(result)
    
    def _format_user_ranking(self, users: List[Dict]) -> str:
        """格式化用户排行"""
        if not users:
            return "无数据"
            
        result = []
        for i, user in enumerate(users[:5], 1):
            result.append(f"{i}. {user.get('name', '未知')} - {user.get('count', 0)}次")
            
        return "\n".join(result)
    
    def _format_feature_breakdown(self, features: List[Dict]) -> str:
        """格式化功能使用分布"""
        if not features:
            return "无数据"
            
        result = []
        for feature in features[:5]:
            result.append(f"• {feature.get('name', '未知')}: {feature.get('percentage', 0):.1f}%")
            
        return "\n".join(result)


# 全局实例
service_analytics = ServiceAnalytics() 
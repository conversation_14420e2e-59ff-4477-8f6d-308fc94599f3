"""
文档处理配置管理模块
支持多种平台的配置管理，包括 Confluence、日志平台、追踪平台等
"""

import os
import logging
from typing import Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)


class DocumentConfig:
    """文档处理配置管理器"""
    
    def __init__(self):
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            # 从 Django 设置中获取配置
            document_settings = getattr(settings, 'DOCUMENT_PROCESSING', {})
            
            # 默认配置
            default_config = {
                'confluence': {
                    'enabled': True,
                    'auth_token': None,
                    'username': None,
                    'password': None,
                    'session_timeout': 30,
                    'max_content_length': 50000,
                    'timeout': 30,
                    'retry_count': 3,
                    'retry_delay': 1
                },
                'log_platforms': {
                    'enabled': False,
                    'kibana': {
                        'enabled': False,
                        'base_url': None,
                        'auth_token': None,
                        'username': None,
                        'password': None,
                        'timeout': 30
                    },
                    'grafana': {
                        'enabled': False,
                        'base_url': None,
                        'auth_token': None,
                        'username': None,
                        'password': None,
                        'timeout': 30
                    },
                    'splunk': {
                        'enabled': False,
                        'base_url': None,
                        'auth_token': None,
                        'username': None,
                        'password': None,
                        'timeout': 30
                    }
                },
                'trace_platforms': {
                    'enabled': False,
                    'jaeger': {
                        'enabled': False,
                        'base_url': None,
                        'auth_token': None,
                        'timeout': 30
                    },
                    'zipkin': {
                        'enabled': False,
                        'base_url': None,
                        'auth_token': None,
                        'timeout': 30
                    },
                    'apm': {
                        'enabled': False,
                        'base_url': None,
                        'auth_token': None,
                        'username': None,
                        'password': None,
                        'timeout': 30
                    }
                },
                'general': {
                    'cache_enabled': True,
                    'cache_timeout': 3600,  # 1小时
                    'max_concurrent_requests': 10,
                    'default_timeout': 30,
                    'max_content_length': 50000,
                    'supported_languages': ['中文', '英文', '日文', '韩文'],
                    'default_language': '中文'
                }
            }
            
            # 合并配置
            self._config = self._merge_config(default_config, document_settings)
            
            # 从环境变量加载敏感信息
            self._load_from_env()
            
            logger.info("文档处理配置加载完成")
            
        except Exception as e:
            logger.error(f"加载文档处理配置失败: {str(e)}")
            self._config = {}
    
    def _merge_config(self, default: Dict, custom: Dict) -> Dict:
        """合并配置"""
        result = default.copy()
        
        def merge_dict(d1: Dict, d2: Dict) -> Dict:
            for key, value in d2.items():
                if key in d1 and isinstance(d1[key], dict) and isinstance(value, dict):
                    d1[key] = merge_dict(d1[key], value)
                else:
                    d1[key] = value
            return d1
        
        return merge_dict(result, custom)
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 只在调试模式下显示详细日志
        debug_mode = os.getenv('DOCUMENT_CONFIG_DEBUG', 'false').lower() == 'true'
        
        env_mappings = {
            # Confluence 配置
            'CONFLUENCE_AUTH_TOKEN': ['confluence', 'auth_token'],
            'CONFLUENCE_USERNAME': ['confluence', 'username'],
            'CONFLUENCE_PASSWORD': ['confluence', 'password'],
            'CONFLUENCE_TIMEOUT': ['confluence', 'session_timeout'],
            
            # Kibana 配置
            'KIBANA_BASE_URL': ['log_platforms', 'kibana', 'base_url'],
            'KIBANA_AUTH_TOKEN': ['log_platforms', 'kibana', 'auth_token'],
            'KIBANA_USERNAME': ['log_platforms', 'kibana', 'username'],
            'KIBANA_PASSWORD': ['log_platforms', 'kibana', 'password'],
            
            # Grafana 配置
            'GRAFANA_BASE_URL': ['log_platforms', 'grafana', 'base_url'],
            'GRAFANA_AUTH_TOKEN': ['log_platforms', 'grafana', 'auth_token'],
            'GRAFANA_USERNAME': ['log_platforms', 'grafana', 'username'],
            'GRAFANA_PASSWORD': ['log_platforms', 'grafana', 'password'],
            
            # Splunk 配置
            'SPLUNK_BASE_URL': ['log_platforms', 'splunk', 'base_url'],
            'SPLUNK_AUTH_TOKEN': ['log_platforms', 'splunk', 'auth_token'],
            'SPLUNK_USERNAME': ['log_platforms', 'splunk', 'username'],
            'SPLUNK_PASSWORD': ['log_platforms', 'splunk', 'password'],
            
            # Jaeger 配置
            'JAEGER_BASE_URL': ['trace_platforms', 'jaeger', 'base_url'],
            'JAEGER_AUTH_TOKEN': ['trace_platforms', 'jaeger', 'auth_token'],
            
            # Zipkin 配置
            'ZIPKIN_BASE_URL': ['trace_platforms', 'zipkin', 'base_url'],
            'ZIPKIN_AUTH_TOKEN': ['trace_platforms', 'zipkin', 'auth_token'],
            
            # APM 配置
            'APM_BASE_URL': ['trace_platforms', 'apm', 'base_url'],
            'APM_AUTH_TOKEN': ['trace_platforms', 'apm', 'auth_token'],
            'APM_USERNAME': ['trace_platforms', 'apm', 'username'],
            'APM_PASSWORD': ['trace_platforms', 'apm', 'password'],
        }
        
        for env_key, config_path in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value:
                if debug_mode:
                    from icecream import ic
                    ic(f"设置配置路径 {config_path}")
                self._set_nested_config(config_path, env_value)
        
        if debug_mode:
            from icecream import ic
            ic("环境变量加载完成")
    
    def _set_nested_config(self, path: list, value: str):
        """设置嵌套配置值"""
        current = self._config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 尝试转换数据类型
        final_key = path[-1]
        if final_key.endswith('_timeout') or final_key.endswith('_count'):
            try:
                current[final_key] = int(value)
            except ValueError:
                current[final_key] = value
        elif value.lower() in ['true', 'false']:
            current[final_key] = value.lower() == 'true'
        else:
            current[final_key] = value
    
    def get_config(self, platform: str = None) -> Dict:
        """
        获取配置
        
        Args:
            platform: 平台名称，如果不指定则返回所有配置
            
        Returns:
            配置字典
        """
        if platform:
            return self._config.get(platform, {})
        return self._config
    
    def get_confluence_config(self) -> Dict:
        """获取 Confluence 配置"""
        return self.get_config('confluence')
    
    def get_log_platforms_config(self) -> Dict:
        """获取日志平台配置"""
        return self.get_config('log_platforms')
    
    def get_trace_platforms_config(self) -> Dict:
        """获取追踪平台配置"""
        return self.get_config('trace_platforms')
    
    def get_general_config(self) -> Dict:
        """获取通用配置"""
        return self.get_config('general')
    
    def is_platform_enabled(self, platform: str, sub_platform: str = None) -> bool:
        """
        检查平台是否启用
        
        Args:
            platform: 主平台名称
            sub_platform: 子平台名称
            
        Returns:
            是否启用
        """
        platform_config = self.get_config(platform)
        if not platform_config.get('enabled', False):
            return False
        
        if sub_platform:
            sub_config = platform_config.get(sub_platform, {})
            return sub_config.get('enabled', False)
        
        return True
    
    def get_platform_auth(self, platform: str, sub_platform: str = None) -> Dict:
        """
        获取平台认证信息
        
        Args:
            platform: 主平台名称
            sub_platform: 子平台名称
            
        Returns:
            认证信息字典
        """
        if sub_platform:
            config = self.get_config(platform).get(sub_platform, {})
        else:
            config = self.get_config(platform)
        
        auth_info = {}
        
        # 提取认证相关字段
        auth_fields = ['auth_token', 'username', 'password', 'api_key']
        for field in auth_fields:
            if field in config and config[field]:
                auth_info[field] = config[field]
        
        return auth_info
    
    def update_config(self, platform: str, updates: Dict):
        """
        更新配置
        
        Args:
            platform: 平台名称
            updates: 更新的配置
        """
        try:
            if platform not in self._config:
                self._config[platform] = {}
            
            self._config[platform].update(updates)
            logger.info(f"已更新 {platform} 平台配置")
            
        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
    
    def validate_config(self) -> Dict:
        """
        验证配置
        
        Returns:
            验证结果
        """
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 验证 Confluence 配置
            confluence_config = self.get_confluence_config()
            if confluence_config.get('enabled', False):
                if not confluence_config.get('auth_token') and not (
                    confluence_config.get('username') and confluence_config.get('password')
                ):
                    validation_results['warnings'].append(
                        "Confluence 已启用但未配置认证信息"
                    )
            
            # 验证日志平台配置
            log_config = self.get_log_platforms_config()
            if log_config.get('enabled', False):
                enabled_platforms = []
                for platform, config in log_config.items():
                    if isinstance(config, dict) and config.get('enabled', False):
                        enabled_platforms.append(platform)
                        if not config.get('base_url'):
                            validation_results['errors'].append(
                                f"日志平台 {platform} 已启用但未配置 base_url"
                            )
                
                if not enabled_platforms:
                    validation_results['warnings'].append(
                        "日志平台功能已启用但没有启用任何具体平台"
                    )
            
            # 验证追踪平台配置
            trace_config = self.get_trace_platforms_config()
            if trace_config.get('enabled', False):
                enabled_platforms = []
                for platform, config in trace_config.items():
                    if isinstance(config, dict) and config.get('enabled', False):
                        enabled_platforms.append(platform)
                        if not config.get('base_url'):
                            validation_results['errors'].append(
                                f"追踪平台 {platform} 已启用但未配置 base_url"
                            )
                
                if not enabled_platforms:
                    validation_results['warnings'].append(
                        "追踪平台功能已启用但没有启用任何具体平台"
                    )
            
            # 如果有错误，标记为无效
            if validation_results['errors']:
                validation_results['valid'] = False
            
        except Exception as e:
            validation_results['valid'] = False
            validation_results['errors'].append(f"配置验证异常: {str(e)}")
        
        return validation_results
    
    def get_supported_platforms(self) -> Dict:
        """
        获取支持的平台列表
        
        Returns:
            支持的平台信息
        """
        return {
            'confluence': {
                'name': 'Confluence',
                'description': 'Atlassian Confluence 文档平台',
                'enabled': self.is_platform_enabled('confluence'),
                'actions': ['summarize', 'translate', 'qa', 'extract_info', 'analyze']
            },
            'log_platforms': {
                'name': '日志平台',
                'description': '各种日志监控平台',
                'enabled': self.is_platform_enabled('log_platforms'),
                'sub_platforms': {
                    'kibana': {
                        'name': 'Kibana',
                        'enabled': self.is_platform_enabled('log_platforms', 'kibana')
                    },
                    'grafana': {
                        'name': 'Grafana',
                        'enabled': self.is_platform_enabled('log_platforms', 'grafana')
                    },
                    'splunk': {
                        'name': 'Splunk',
                        'enabled': self.is_platform_enabled('log_platforms', 'splunk')
                    }
                },
                'actions': ['analyze', 'summarize', 'extract_info', 'search']
            },
            'trace_platforms': {
                'name': '追踪平台',
                'description': '分布式追踪平台',
                'enabled': self.is_platform_enabled('trace_platforms'),
                'sub_platforms': {
                    'jaeger': {
                        'name': 'Jaeger',
                        'enabled': self.is_platform_enabled('trace_platforms', 'jaeger')
                    },
                    'zipkin': {
                        'name': 'Zipkin',
                        'enabled': self.is_platform_enabled('trace_platforms', 'zipkin')
                    },
                    'apm': {
                        'name': 'APM',
                        'enabled': self.is_platform_enabled('trace_platforms', 'apm')
                    }
                },
                'actions': ['analyze', 'summarize', 'extract_info', 'search']
            }
        }


# 全局配置管理器实例
document_config = DocumentConfig() 
"""
文档处理配置示例
展示如何在 Django settings.py 中配置文档处理功能
"""

# 在 Django settings.py 中添加以下配置

DOCUMENT_PROCESSING = {
    # Confluence 配置
    'confluence': {
        'enabled': True,  # 启用 Confluence 功能
        'auth_token': None,  # Bear<PERSON> (推荐)
        'username': None,  # 用户名 (Basic Auth)
        'password': None,  # 密码 (Basic Auth)
        'session_timeout': 30,  # 请求超时时间（秒）
        'max_content_length': 50000,  # 最大内容长度
        'timeout': 30,  # 超时时间
        'retry_count': 3,  # 重试次数
        'retry_delay': 1  # 重试延迟（秒）
    },
    
    # 日志平台配置（预留）
    'log_platforms': {
        'enabled': False,  # 暂时关闭
        'kibana': {
            'enabled': False,
            'base_url': 'https://kibana.example.com',
            'auth_token': None,
            'username': None,
            'password': None,
            'timeout': 30
        },
        'grafana': {
            'enabled': False,
            'base_url': 'https://grafana.example.com',
            'auth_token': None,
            'username': None,
            'password': None,
            'timeout': 30
        },
        'splunk': {
            'enabled': False,
            'base_url': 'https://splunk.example.com',
            'auth_token': None,
            'username': None,
            'password': None,
            'timeout': 30
        }
    },
    
    # 追踪平台配置（预留）
    'trace_platforms': {
        'enabled': False,  # 暂时关闭
        'jaeger': {
            'enabled': False,
            'base_url': 'https://jaeger.example.com',
            'auth_token': None,
            'timeout': 30
        },
        'zipkin': {
            'enabled': False,
            'base_url': 'https://zipkin.example.com',
            'auth_token': None,
            'timeout': 30
        },
        'apm': {
            'enabled': False,
            'base_url': 'https://apm.example.com',
            'auth_token': None,
            'username': None,
            'password': None,
            'timeout': 30
        }
    },
    
    # 通用配置
    'general': {
        'cache_enabled': True,  # 启用缓存
        'cache_timeout': 3600,  # 缓存超时时间（秒）
        'max_concurrent_requests': 10,  # 最大并发请求数
        'default_timeout': 30,  # 默认超时时间
        'max_content_length': 50000,  # 最大内容长度
        'supported_languages': ['中文', '英文', '日文', '韩文'],  # 支持的翻译语言
        'default_language': '中文'  # 默认语言
    }
}

# 环境变量配置示例
# 可以通过环境变量设置敏感信息，避免在代码中硬编码

"""
export CONFLUENCE_AUTH_TOKEN="your_confluence_token"
export CONFLUENCE_USERNAME="your_username"
export CONFLUENCE_PASSWORD="your_password"

export KIBANA_BASE_URL="https://kibana.example.com"
export KIBANA_AUTH_TOKEN="your_kibana_token"

export GRAFANA_BASE_URL="https://grafana.example.com"
export GRAFANA_AUTH_TOKEN="your_grafana_token"

export JAEGER_BASE_URL="https://jaeger.example.com"
export JAEGER_AUTH_TOKEN="your_jaeger_token"
"""

# 配置说明
CONFIGURATION_GUIDE = """
📋 文档处理功能配置指南

1. Confluence 配置:
   - 优先使用 Bearer Token 认证
   - 如果没有 Token，可以使用用户名密码
   - 建议通过环境变量设置敏感信息

2. 认证配置:
   - Bearer Token: 在 Confluence 用户设置中生成 API Token
   - Basic Auth: 使用用户名和密码
   - 建议使用专用的服务账号

3. 性能配置:
   - session_timeout: 单个请求的超时时间
   - max_content_length: 限制文档内容长度，避免内存溢出
   - cache_timeout: 缓存时间，减少重复请求

4. 安全配置:
   - 所有认证信息建议通过环境变量设置
   - 定期更新 API Token
   - 限制服务账号权限

5. 日志和追踪平台:
   - 目前为预留配置，后续版本会支持
   - 可以提前配置，但暂时不会生效

使用示例:
- 总结文档: "总结这个文档 https://confluence.company.com/wiki/pages/123"
- 从 JIRA 获取文档: "翻译 PROJ-123 相关文档为英文"
- 问答处理: "这个文档说了什么关于API的内容？"
- 信息提取: "从文档中提取关键信息"
- 文档分析: "分析文档的完整性"
""" 
"""
消息去重模块
防止重复处理相同的消息
"""
import time
import logging
from functools import wraps
from typing import Dict, Any, Optional
from django.http import JsonResponse
from app01.models import ProcessedMessage
from django.db import transaction
from asgiref.sync import sync_to_async
from icecream import ic
from django.core.cache import cache
from datetime import datetime, timedelta

# 配置日志
logger = logging.getLogger(__name__)

# 内存缓存作为备用去重机制（当数据库连接失败时使用）
MEMORY_DEDUP_CACHE = {}
MEMORY_CACHE_MAX_SIZE = 1000  # 最大缓存条目数
MEMORY_CACHE_TTL = 3600  # 缓存过期时间（秒）

def _cleanup_memory_cache():
    """清理过期的内存缓存条目"""
    current_time = time.time()
    expired_keys = [
        key for key, (_, timestamp) in MEMORY_DEDUP_CACHE.items()
        if current_time - timestamp > MEMORY_CACHE_TTL
    ]
    for key in expired_keys:
        MEMORY_DEDUP_CACHE.pop(key, None)
    
    # 如果缓存过大，删除最旧的条目
    if len(MEMORY_DEDUP_CACHE) > MEMORY_CACHE_MAX_SIZE:
        sorted_items = sorted(MEMORY_DEDUP_CACHE.items(), key=lambda x: x[1][1])
        for key, _ in sorted_items[:len(MEMORY_DEDUP_CACHE) - MEMORY_CACHE_MAX_SIZE]:
            MEMORY_DEDUP_CACHE.pop(key, None)

def _check_memory_duplicate(event_id: str) -> bool:
    """检查内存缓存中是否存在重复"""
    _cleanup_memory_cache()
    return event_id in MEMORY_DEDUP_CACHE

def _add_to_memory_cache(event_id: str):
    """添加到内存缓存"""
    _cleanup_memory_cache()
    MEMORY_DEDUP_CACHE[event_id] = (True, time.time())

def deduplicate_message(func):
    """
    消息去重装饰器
    基于event_id和message_id防止重复处理
    增强版：支持数据库+内存双重去重机制
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        # 从参数中提取消息信息
        data = None
        for arg in args:
            if isinstance(arg, dict) and 'event_id' in arg:
                data = arg
                break
        
        if not data:
            logger.warning("无法从参数中提取消息数据，跳过去重检查")
            return await func(*args, **kwargs)
        
        event_id = data.get('event_id')
        if not event_id:
            logger.warning("未找到event_id，跳过去重检查")
            return await func(*args, **kwargs)
        
        # 首先检查内存缓存（快速检查）
        if _check_memory_duplicate(event_id):
            ic(f"🔄 内存缓存检测到重复消息 - Event ID: {event_id}")
            return JsonResponse({
                'message': '消息已处理，跳过重复处理（内存缓存检测）',
                'success': True,
                'is_duplicate': True,
                'duplicate_source': 'memory_cache'
            })
        
        # 数据库去重检查
        db_check_success = False
        processed_msg = None
        
        try:
            # 异步检查是否已处理
            @sync_to_async
            def check_processed_message():
                return ProcessedMessage.objects.filter(event_id=event_id).first()
            
            processed_msg = await check_processed_message()
            db_check_success = True
            
            if processed_msg:
                # 消息已处理过，同时添加到内存缓存
                _add_to_memory_cache(event_id)
                ic(f"🔄 数据库检测到重复消息 - Event ID: {event_id}")
                ic(f"📝 原处理时间: {processed_msg.processed_at}")
                ic(f"⏱️ 原处理耗时: {processed_msg.processing_duration:.2f}秒")
                
                return JsonResponse({
                    'message': '消息已处理，跳过重复处理',
                    'success': True,
                    'is_duplicate': True,
                    'duplicate_source': 'database',
                    'original_processed_at': processed_msg.processed_at.isoformat(),
                    'original_duration': processed_msg.processing_duration
                })
                
        except Exception as e:
            logger.error(f"数据库去重检查失败: {str(e)}")
            # 数据库检查失败，但不直接继续处理，而是依赖内存缓存
            db_check_success = False
        
        # 如果数据库检查失败，先添加到内存缓存防止并发重复处理
        if not db_check_success:
            if _check_memory_duplicate(event_id):
                ic(f"🔄 数据库失败后内存缓存检测到重复 - Event ID: {event_id}")
                return JsonResponse({
                    'message': '消息已处理，跳过重复处理（数据库故障时内存缓存检测）',
                    'success': True,
                    'is_duplicate': True,
                    'duplicate_source': 'memory_fallback'
                })
            else:
                # 立即添加到内存缓存，防止并发重复
                _add_to_memory_cache(event_id)
                ic(f"⚠️ 数据库连接失败，使用内存缓存防重复 - Event ID: {event_id}")
        
        # 记录开始处理
        start_time = time.time()
        
        # 如果数据库正常，创建数据库记录
        if db_check_success:
            try:
                # 提取消息基本信息
                event_data = data.get('event', {})
                message_data = event_data.get('message', {})
                message_id = message_data.get('message_id', '')
                seatalk_id = event_data.get('seatalk_id', '')
                
                # 群聊 vs 私聊处理
                group_id = None
                query_content = ""
                
                if data.get('event_type') == 'message_from_bot_subscriber':
                    # 私聊
                    query_content = message_data.get('text', {}).get('content', '')
                    # 私聊时从event中获取seatalk_id
                    seatalk_id = event_data.get('seatalk_id', 
                                message_data.get('sender', {}).get('seatalk_id', ''))
                else:
                    # 群聊 
                    group_id = event_data.get('group_id')
                    text_data = message_data.get('text', {})
                    query_content = text_data.get('plain_text', text_data.get('content', ''))
                    # 群聊时从message.sender中获取seatalk_id
                    seatalk_id = message_data.get('sender', {}).get('seatalk_id', '')
                
                # 异步创建处理记录（防止并发重复处理）
                @sync_to_async
                def create_processed_record():
                    return ProcessedMessage.objects.create(
                        event_id=event_id,
                        message_id=message_id,
                        seatalk_id=seatalk_id,
                        group_id=group_id,
                        query_content=query_content[:500],  # 限制长度
                        processing_duration=0  # 先设为0，处理完成后更新
                    )
                
                processed_record = await create_processed_record()
                
                # 同时添加到内存缓存
                _add_to_memory_cache(event_id)
                
                ic(f"✅ 开始处理新消息 - Event ID: {event_id}")
                ic(f"👤 用户: {seatalk_id}, 群组: {group_id}")
                ic(f"📝 内容: {query_content[:100]}...")
                    
            except Exception as e:
                logger.error(f"创建数据库记录失败: {str(e)}")
                # 数据库记录创建失败，但内存缓存已添加，继续处理
        
        try:
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 如果数据库正常，更新处理耗时
            if db_check_success:
                try:
                    end_time = time.time()
                    processing_duration = end_time - start_time
                    
                    @sync_to_async
                    def update_processing_duration():
                        return ProcessedMessage.objects.filter(event_id=event_id).update(
                            processing_duration=processing_duration
                        )
                    
                    await update_processing_duration()
                    ic(f"🎉 消息处理完成 - Event ID: {event_id}, 耗时: {processing_duration:.2f}秒")
                except Exception as e:
                    logger.error(f"更新处理耗时失败: {str(e)}")
            else:
                ic(f"🎉 消息处理完成（数据库故障模式） - Event ID: {event_id}")
            
            return result
            
        except Exception as e:
            # 如果处理失败，清理记录以允许重试
            if db_check_success:
                @sync_to_async
                def cleanup_failed_record():
                    return ProcessedMessage.objects.filter(event_id=event_id).delete()
                
                try:
                    await cleanup_failed_record()
                except:
                    pass  # 清理失败也不影响异常抛出
            
            # 同时从内存缓存中移除
            MEMORY_DEDUP_CACHE.pop(event_id, None)
            
            logger.error(f"消息处理失败，已清理记录: {str(e)}")
            raise
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        # 对于同步函数，简单的去重检查
        data = None
        for arg in args:
            if isinstance(arg, dict) and 'event_id' in arg:
                data = arg
                break
        
        if data and data.get('event_id'):
            event_id = data.get('event_id')
            
            # 先检查内存缓存
            if _check_memory_duplicate(event_id):
                ic(f"🔄 同步消息内存缓存重复检测 - Event ID: {event_id}")
                return JsonResponse({
                    'message': '消息已处理，跳过重复处理',
                    'success': True,
                    'is_duplicate': True,
                    'duplicate_source': 'memory_cache'
                })
            
            # 检查数据库
            try:
                if ProcessedMessage.objects.filter(event_id=event_id).exists():
                    _add_to_memory_cache(event_id)
                    ic(f"🔄 同步消息数据库重复检测 - Event ID: {event_id}")
                    return JsonResponse({
                        'message': '消息已处理，跳过重复处理',
                        'success': True,
                        'is_duplicate': True,
                        'duplicate_source': 'database'
                    })
            except Exception as e:
                logger.error(f"同步去重数据库检查失败: {str(e)}")
                # 数据库失败时添加到内存缓存防重复
                _add_to_memory_cache(event_id)
        
        return func(*args, **kwargs)
    
    # 检查函数是否为协程函数
    if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
        return async_wrapper
    else:
        return sync_wrapper


def cleanup_old_messages(days: int = 7):
    """
    清理旧的消息记录
    :param days: 保留天数，默认7天
    """
    from django.utils import timezone
    from datetime import timedelta
    
    cutoff_date = timezone.now() - timedelta(days=days)
    deleted_count = ProcessedMessage.objects.filter(processed_at__lt=cutoff_date).delete()[0]
    
    ic(f"🧹 清理了 {deleted_count} 条 {days} 天前的消息记录")
    return deleted_count


def get_message_stats() -> Dict[str, Any]:
    """获取消息处理统计信息"""
    from django.utils import timezone
    from datetime import timedelta
    from django.db import models
    
    now = timezone.now()
    today = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    stats = {
        'total_processed': ProcessedMessage.objects.count(),
        'today_processed': ProcessedMessage.objects.filter(processed_at__gte=today).count(),
        'avg_processing_time': ProcessedMessage.objects.filter(
            processing_duration__gt=0
        ).aggregate(
            avg_time=models.Avg('processing_duration')
        )['avg_time'] or 0,
        'memory_cache_size': len(MEMORY_DEDUP_CACHE),
        'memory_cache_max_size': MEMORY_CACHE_MAX_SIZE,
    }
    
    return stats


def force_cleanup_memory_cache():
    """强制清理内存缓存"""
    global MEMORY_DEDUP_CACHE
    cache_size = len(MEMORY_DEDUP_CACHE)
    MEMORY_DEDUP_CACHE.clear()
    ic(f"🧹 强制清理了 {cache_size} 条内存缓存记录")
    return cache_size 
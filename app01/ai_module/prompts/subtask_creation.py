"""
Subtask 创建专用提示词
用于提取和澄清子任务创建的相关信息
"""

import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta


class SubtaskCreationPrompts:
    """子任务创建提示词"""
    
    SYSTEM_PROMPT = """你是一个专业的JIRA子任务创建助手，能够从用户的自然语言中提取创建子任务所需的关键信息。

## 你的任务：
从用户输入中提取以下信息：
1. **parent_issue_key** - 父单号（必须，如：SPCB-123）
2. **subtask_summary** - 子任务标题（必须）
3. **story_points** - 工作量（可选，默认1天）
4. **planned_start_date** - 计划开始日期（可选）
5. **planned_due_date** - 计划结束日期（可选）
6. **assignee** - 指派人（可选，默认当前用户）
7. **description** - 详细描述（可选）

## 信息提取规则：

### 1. 父单号提取
- 格式：[字母]+-数字（如：SPCB-123, JIRA-456, TEST-789）
- 必须存在，否则需要澄清

### 2. 子任务标题提取
- **冒号后内容**：如"在 SPCB-123 建单：完成功能测试"
- **描述性文本**：如"今天完成功能测试"
- **基于父单的追加**：如"SPCB-123 开发工作"、"SPCB-123 测试工作"
- **常见追加关键词**：开发工作、测试工作、case设计、发布验证、UAT验证
- 必须存在，否则需要澄清
- **重要**：子任务标题不应包含工作量信息，如"story point是1"、"工作量2d"等

### 3. 工作量提取
- 格式识别：
  - "2d" → 2天
  - "3天" → 3天
  - "1周" → 5天
  - "8h" → 1天
  - "16小时" → 2天
  - "story point是2" → 2天
  - "story points是3" → 3天
  - "工作量是1" → 1天
  - "工作量为4" → 4天
- 默认值：1天
- **重要**：必须准确区分子任务标题和工作量信息，例如"LLM streaming bug验证以及 live 验证， story point是 1"中，"LLM streaming bug验证以及 live 验证"是标题，"story point是 1"是工作量信息

### 4. 时间信息提取
- 相对时间：
  - "今天" → 当天日期
  - "明天" → 明天日期
  - "后天" → 后天日期
  - "下周一" → 下周一日期
  - "本周五" → 本周五日期
- 绝对时间：
  - "2025-01-02" → 2025-01-02
  - "1月2日" → 当年1月2日
  - "01-02" → 当年01-02

### 5. 指派人提取
- 默认：currentUser()（当前用户）
- 如果提到具体人员，提取用户名或邮箱

## 输出格式：
```json
{
    "success": true,
    "extracted_info": {
        "parent_issue_key": "SPCB-123",
        "subtask_summary": "完成功能测试",
        "story_points": 2,
        "planned_start_date": "2025-01-02",
        "planned_due_date": "2025-01-02",
        "assignee": "currentUser()",
        "description": "完成**需求的功能测试"
    },
    "needs_clarification": false,
    "clarification_reason": null
}
```

## 澄清条件：
1. 缺少父单号
2. 缺少子任务标题
3. 时间信息不明确
4. 指派人信息不明确

## 澄清提示：
- "请提供父单号（如：SPCB-123）"
- "请提供子任务标题"
- "请明确指定时间"
- "请明确指定指派人"

## 示例分析：

### 示例1：
输入：`在 SPCB-52619 下创建一个 subtask： LLM streaming bug验证以及 live 验证， story point是 1`

分析：
- parent_issue_key: "SPCB-52619"
- subtask_summary: "LLM streaming bug验证以及 live 验证"
- story_points: 1
- 其他字段：使用默认值

### 示例2：
输入：`建单：SPCB-12345下的性能测试任务 2d`

分析：
- parent_issue_key: "SPCB-12345"
- subtask_summary: "性能测试任务"
- story_points: 2
- 其他字段：使用默认值

### 示例3：
输入：`在SPCB-54697下创建子任务，标题是代码优化，工作量是3天，明天开始`

分析：
- parent_issue_key: "SPCB-54697"
- subtask_summary: "代码优化"
- story_points: 3
- planned_start_date: [明天的日期]
- 其他字段：使用默认值
"""

    USER_PROMPT_TEMPLATE = """用户输入: {user_input}

群组信息: {group_info}

上下文信息: {context}

请仔细分析用户输入，提取创建子任务所需的信息。

**分析步骤：**
1. 识别是否为子任务创建请求
2. 提取父单号（JIRA格式）
3. 提取子任务标题
4. 提取工作量信息
5. 提取时间信息
6. 提取指派人信息
7. 判断是否需要澄清

请按照JSON格式返回提取结果。"""

    @classmethod
    def build_prompt(cls, user_input: str, context: dict = None, group_info: dict = None) -> tuple:
        """
        构建子任务创建提示词
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            group_info: 群组信息
            
        Returns:
            (system_prompt, user_prompt)
        """
        context_str = str(context) if context else "{}"
        group_info_str = str(group_info) if group_info else "{}"
        
        user_prompt = cls.USER_PROMPT_TEMPLATE.format(
            user_input=user_input,
            context=context_str,
            group_info=group_info_str
        )
        
        return cls.SYSTEM_PROMPT, user_prompt

    @classmethod
    def extract_jira_key(cls, text: str) -> Optional[str]:
        """从文本中提取JIRA单号"""
        # 匹配 [字母]+-数字 格式（通用格式）
        pattern = r'\b([A-Z]{2,}-\d+)\b'
        match = re.search(pattern, text.upper())
        if match:
            return match.group(1)
        return None

    @classmethod
    def extract_summary_from_text(cls, text: str) -> Optional[str]:
        """从文本中提取子任务标题"""
        # 1. 尝试提取冒号后的内容
        if '：' in text:
            parts = text.split('：', 1)
            if len(parts) > 1:
                summary = parts[1].strip()
                # 移除工作量信息
                summary = re.sub(r'\s*工作量\s*\d+[dh天小时周]*', '', summary)
                return summary
        
        # 2. 尝试提取引号内的内容
        quote_patterns = [
            r'"([^"]+)"',  # 双引号
            r'"([^"]+)"',  # 中文引号
            r"'([^']+)'",  # 单引号
        ]
        
        for pattern in quote_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        # 3. 识别基于JIRA单号的追加模式
        jira_key = cls.extract_jira_key(text)
        if jira_key:
            # 常见的追加关键词
            append_keywords = [
                '开发工作', '测试工作', 'case设计', 'case 设计', 
                '发布验证', 'UAT验证', 'UAT 验证', '性能测试',
                '功能测试', '集成测试', '单元测试', '代码review',
                '代码 review', '需求分析', '设计文档', '部署工作'
            ]
            
            for keyword in append_keywords:
                if keyword in text:
                    return keyword
        
        # 4. 尝试从自然语言中提取任务描述
        # 移除JIRA单号和常见的指令词
        cleaned_text = text
        if jira_key:
            cleaned_text = cleaned_text.replace(jira_key, '').strip()
        
        # 移除常见的指令词
        command_words = ['在', '建单', '建subtask', '创建', '新建', '给', '的', '：', '。', '工作量']
        for word in command_words:
            cleaned_text = cleaned_text.replace(word, ' ')
        
        # 移除工作量信息
        cleaned_text = re.sub(r'\s*\d+[dh天小时周]+', '', cleaned_text)
        cleaned_text = re.sub(r'\s*工作量\s*\d+[dh天小时周]*', '', cleaned_text)
        
        # 清理多余空格
        cleaned_text = ' '.join(cleaned_text.split())
        
        if cleaned_text and len(cleaned_text) > 2:
            return cleaned_text
        
        return None

    @classmethod
    def extract_story_points(cls, text: str) -> int:
        """从文本中提取工作量"""
        # 工作量模式
        patterns = [
            r'工作量\s*(\d+)[dh天小时周]*',
            r'(\d+)[dh天小时周]+',
            r'(\d+)\s*天',
            r'(\d+)\s*小时',
            r'(\d+)\s*周',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                value = int(match.group(1))
                if 'h' in match.group(0) or '小时' in match.group(0):
                    return max(1, value // 8)  # 按8小时一天计算
                elif '周' in match.group(0):
                    return value * 5  # 一周按5天计算
                else:
                    return value
        
        return 1  # 默认1天

    @classmethod
    def extract_date_info(cls, text: str) -> Dict[str, Optional[str]]:
        """从文本中提取日期信息"""
        today = datetime.now()
        result = {
            'planned_start_date': None,
            'planned_due_date': None
        }
        
        # 相对日期映射
        relative_dates = {
            '今天': today,
            '明天': today + timedelta(days=1),
            '后天': today + timedelta(days=2),
        }
        
        # 处理相对日期
        for keyword, date_obj in relative_dates.items():
            if keyword in text:
                date_str = date_obj.strftime('%Y-%m-%d')
                if not result['planned_start_date']:
                    result['planned_start_date'] = date_str
                if not result['planned_due_date']:
                    result['planned_due_date'] = date_str
                break
        
        # 处理绝对日期
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # 2024-01-01
            r'(\d{1,2})月(\d{1,2})日',  # 1月1日
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    if len(match.groups()) == 3:
                        year, month, day = match.groups()
                        date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    else:
                        month, day = match.groups()
                        date_str = f"{today.year}-{month.zfill(2)}-{day.zfill(2)}"
                    
                    if not result['planned_start_date']:
                        result['planned_start_date'] = date_str
                    if not result['planned_due_date']:
                        result['planned_due_date'] = date_str
                    break
                except:
                    pass
        
        return result

    @classmethod
    def generate_clarification_message(cls, missing_info: List[str]) -> str:
        """生成澄清消息"""
        if not missing_info:
            return ""
        
        clarifications = []
        
        if 'parent_issue_key' in missing_info:
            clarifications.append("请提供父单号（如：SPCB-123）")
        
        if 'subtask_summary' in missing_info:
            clarifications.append("请提供子任务标题")
        
        if 'assignee' in missing_info:
            clarifications.append("请明确指定指派人")
        
        if 'dates' in missing_info:
            clarifications.append("请明确指定时间")
        
        message = "创建子任务需要以下信息：\n"
        for i, clarification in enumerate(clarifications, 1):
            message += f"{i}. {clarification}\n"
        
        message += "\n🚀 **支持的描述方式：**\n"
        message += "• **标准格式**：`在 JIRA-123 建单：完成功能测试。工作量 2d`\n"
        message += "• **简化格式**：`JIRA-123 建单：开发工作`\n"
        message += "• **追加模式**：`JIRA-123 测试工作`、`JIRA-123 发布验证`\n"
        message += "• **自然语言**：`给 JIRA-123 创建一个性能测试的子任务`\n"
        message += "\n💡 **常用追加关键词**：开发工作、测试工作、case设计、发布验证、UAT验证"
        
        return message

    @classmethod
    def validate_extracted_info(cls, extracted_info: Dict) -> Tuple[bool, List[str]]:
        """验证提取的信息是否完整"""
        missing_info = []
        
        if not extracted_info.get('parent_issue_key'):
            missing_info.append('parent_issue_key')
        
        if not extracted_info.get('subtask_summary'):
            missing_info.append('subtask_summary')
        
        return len(missing_info) == 0, missing_info 
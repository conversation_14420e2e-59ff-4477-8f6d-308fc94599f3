"""
智能 JIRA 查询模块
提供基于AI生成的JQL查询和权限管理
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Tuple
from jira import JIRA, JIRAError
from django.utils import timezone
from cryptography.fernet import Fernet
from django.conf import settings

from app01.models import UserJiraToken, AIQueryHistory
from app01.config import JIRA_TOKEN
from .prompts.jql_generation import JQLGenerationPrompts

logger = logging.getLogger(__name__)


class JiraTokenManager:
    """JIRA Token 管理器"""
    
    # 简单的加密密钥 - 生产环境应该使用更安全的密钥管理
    _cipher_key = b'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg='  # 示例密钥
    
    @classmethod
    def encrypt_token(cls, token: str) -> str:
        """加密token"""
        try:
            f = Fernet(cls._cipher_key)
            encrypted_token = f.encrypt(token.encode())
            return encrypted_token.decode()
        except Exception as e:
            logger.error(f"Token加密失败: {str(e)}")
            return token  # 如果加密失败，返回原token
    
    @classmethod
    def decrypt_token(cls, encrypted_token: str) -> str:
        """解密token"""
        try:
            f = Fernet(cls._cipher_key)
            decrypted_token = f.decrypt(encrypted_token.encode())
            return decrypted_token.decode()
        except Exception as e:
            logger.error(f"Token解密失败: {str(e)}")
            return encrypted_token  # 如果解密失败，返回原token
    
    @classmethod
    def save_user_token(cls, user_email: str, token: str) -> Dict:
        """保存用户token（同步版本）"""
        try:
            encrypted_token = cls.encrypt_token(token)
            
            user_token, created = UserJiraToken.objects.get_or_create(
                user_email=user_email,
                defaults={'jira_token': encrypted_token, 'is_active': True}
            )
            
            if not created:
                user_token.jira_token = encrypted_token
                user_token.is_active = True
                user_token.updated_at = timezone.now()
                user_token.save()
            
            return {
                'success': True,
                'message': f'Token已保存并加密存储，将用于您后续的写操作。如需更新token，请重新设置。',
                'is_new': created
            }
        except Exception as e:
            logger.error(f"保存用户token失败: {str(e)}")
            return {
                'success': False,
                'message': f'Token保存失败: {str(e)}'
            }

    @classmethod
    async def save_user_token_async(cls, user_email: str, token: str) -> Dict:
        """保存用户token（异步版本）"""
        try:
            def _save_token():
                encrypted_token = cls.encrypt_token(token)
                
                user_token, created = UserJiraToken.objects.get_or_create(
                    user_email=user_email,
                    defaults={'jira_token': encrypted_token, 'is_active': True}
                )
                
                if not created:
                    user_token.jira_token = encrypted_token
                    user_token.is_active = True
                    user_token.updated_at = timezone.now()
                    user_token.save()
                
                return {
                    'success': True,
                    'message': f'Token已保存并加密存储，将用于您后续的写操作。如需更新token，请重新设置。',
                    'is_new': created
                }
            
            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _save_token)
            
        except Exception as e:
            logger.error(f"异步保存用户token失败: {str(e)}")
            return {
                'success': False,
                'message': f'Token保存失败: {str(e)}'
            }
    
    @classmethod
    def get_user_token(cls, user_email: str) -> Optional[str]:
        """获取用户token"""
        try:
            user_token = UserJiraToken.objects.get(
                user_email=user_email,
                is_active=True
            )
            user_token.mark_used()  # 标记使用时间
            return cls.decrypt_token(user_token.jira_token)
        except UserJiraToken.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"获取用户token失败: {str(e)}")
            return None
    
    @classmethod
    async def get_user_token_async(cls, user_email: str) -> Optional[str]:
        """异步获取用户token"""
        try:
            def _get_token():
                try:
                    user_token = UserJiraToken.objects.get(
                        user_email=user_email,
                        is_active=True
                    )
                    user_token.mark_used()  # 标记使用时间
                    return cls.decrypt_token(user_token.jira_token)
                except UserJiraToken.DoesNotExist:
                    return None
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _get_token)
        except Exception as e:
            logger.error(f"异步获取用户token失败: {str(e)}")
            return None
    
    @classmethod
    def validate_token(cls, token: str) -> Dict:
        """验证token是否有效"""
        try:
            jira = JIRA(server='https://jira.shopee.io', token_auth=token)
            # 简单的验证查询
            jira.myself()
            return {'valid': True, 'message': 'Token验证成功'}
        except JIRAError as e:
            if e.status_code == 401:
                return {'valid': False, 'message': 'Token无效或已过期，请重新设置'}
            else:
                return {'valid': False, 'message': f'Token验证失败: {str(e)}'}
        except Exception as e:
            return {'valid': False, 'message': f'Token验证异常: {str(e)}'}


class SmartJiraQuery:
    """智能 JIRA 查询器"""
    
    def __init__(self, user_email: str = None):
        """初始化JIRA查询工具"""
        self.jira_token = None
        self.user_email = user_email
        self.last_query = ""  # 添加last_query属性，用于存储最后一次查询
        self.jira = None  # 添加jira属性初始化
        
    def _get_jira_token(self) -> str:
        """获取JIRA token（同步版本）"""
        if self.user_email == "<EMAIL>":
            return JIRA_TOKEN
        elif self.user_email:
            user_token = JiraTokenManager.get_user_token(self.user_email)
            return user_token if user_token else JIRA_TOKEN
        else:
            return JIRA_TOKEN
    
    async def _get_jira_token_async(self) -> str:
        """获取JIRA token（异步版本）"""
        if self.user_email == "<EMAIL>":
            return JIRA_TOKEN
        elif self.user_email:
            user_token = await JiraTokenManager.get_user_token_async(self.user_email)
            return user_token if user_token else JIRA_TOKEN
        else:
            return JIRA_TOKEN
    
    def _get_jira_connection(self) -> JIRA:
        """获取JIRA连接"""
        if not self.jira:
            try:
                self.jira = JIRA(server='https://jira.shopee.io', token_auth=self.jira_token)
            except Exception as e:
                logger.error(f"JIRA连接失败: {str(e)}")
                raise
        return self.jira
    
    async def execute_jql(self, jql: str, fields: List[str] = None, 
                         max_results: int = 100) -> Dict:
        """
        执行JQL查询
        
        Args:
            jql: JQL查询语句
            fields: 需要返回的字段
            max_results: 最大结果数
            
        Returns:
            查询结果
        """
        try:
            start_time = timezone.now()
            
            # 异步获取token
            if not self.jira_token:
                self.jira_token = await self._get_jira_token_async()
            
            # 在异步环境中运行同步的JIRA查询
            def _sync_query():
                if not self.jira:
                    self.jira = JIRA(server='https://jira.shopee.io', token_auth=self.jira_token)
                return self.jira.search_issues(
                    jql_str=jql,
                    fields=fields,
                    maxResults=max_results,
                    json_result=True
                )
            
            # 使用线程池执行同步查询
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_query)
            
            query_time = (timezone.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'data': result,
                'query_time': query_time,
                'result_count': len(result.get('issues', []))
            }
            
        except JIRAError as e:
            if e.status_code == 401:
                error_msg = "JIRA认证失败，请检查token是否过期"
            else:
                error_msg = f"JIRA查询失败: {str(e)}"
            
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'error_type': 'jira_error',
                'status_code': getattr(e, 'status_code', None)
            }
        except Exception as e:
            error_msg = f"查询执行异常: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'error_type': 'general_error'
            }
    
    async def update_issue(self, issue_key: str, fields: Dict) -> Dict:
        """
        更新issue
        
        Args:
            issue_key: Issue键值
            fields: 要更新的字段
            
        Returns:
            更新结果
        """
        if not self._can_write():
            return {
                'success': False,
                'error': '没有写权限，请先设置您的JIRA Token',
                'need_token': True
            }
        
        try:
            def _sync_update():
                jira = self._get_jira_connection()
                issue = jira.issue(issue_key)
                issue.update(fields=fields)
                return issue
            
            loop = asyncio.get_event_loop()
            updated_issue = await loop.run_in_executor(None, _sync_update)
            
            return {
                'success': True,
                'message': f'Issue {issue_key} 更新成功',
                'issue': {
                    'key': updated_issue.key,
                    'summary': updated_issue.fields.summary
                }
            }
            
        except JIRAError as e:
            if e.status_code == 401:
                return {
                    'success': False,
                    'error': 'JIRA Token已过期，请重新设置',
                    'need_token_refresh': True
                }
            else:
                return {
                    'success': False,
                    'error': f'更新失败: {str(e)}'
                }
        except Exception as e:
            return {
                'success': False,
                'error': f'更新异常: {str(e)}'
            }
    
    def _can_write(self) -> bool:
        """检查是否有写权限"""
        if self.user_email == "<EMAIL>":
            return True
        elif self.user_email:
            user_token = JiraTokenManager.get_user_token(self.user_email)
            return user_token is not None
        else:
            return False
    
    def validate_write_permission(self) -> Dict:
        """验证写操作权限"""
        if self.user_email == "<EMAIL>":
            return {
                'has_permission': True,
                'message': '您有完整的写权限'
            }
        elif self.user_email:
            user_token = JiraTokenManager.get_user_token(self.user_email)
            if user_token:
                # 验证token是否仍然有效
                validation = JiraTokenManager.validate_token(user_token)
                if validation['valid']:
                    return {
                        'has_permission': True,
                        'message': '您的JIRA Token有效，可以进行写操作'
                    }
                else:
                    return {
                        'has_permission': False,
                        'message': validation['message'],
                        'need_token_refresh': True
                    }
            else:
                return {
                    'has_permission': False,
                    'message': '请先设置您的JIRA Token以进行写操作。使用命令: /ai config jira_token YOUR_TOKEN',
                    'need_token': True
                }
        else:
            return {
                'has_permission': False,
                'message': '无法确定用户身份'
            }
    
    async def validate_write_permission_async(self) -> Dict:
        """异步验证写操作权限"""
        if self.user_email == "<EMAIL>":
            return {
                'has_permission': True,
                'message': '您有完整的写权限'
            }
        elif self.user_email:
            user_token = await JiraTokenManager.get_user_token_async(self.user_email)
            if user_token:
                # 验证token是否仍然有效
                validation = JiraTokenManager.validate_token(user_token)
                if validation['valid']:
                    return {
                        'has_permission': True,
                        'message': '您的JIRA Token有效，可以进行写操作'
                    }
                else:
                    return {
                        'has_permission': False,
                        'message': validation['message'],
                        'need_token_refresh': True
                    }
            else:
                return {
                    'has_permission': False,
                    'message': '请先设置您的JIRA Token以进行写操作。使用命令: /ai config jira_token YOUR_TOKEN',
                    'need_token': True
                }
        else:
            return {
                'has_permission': False,
                'message': '无法确定用户身份'
            }
    
    @staticmethod
    def get_optimal_fields(intent: str) -> List[str]:
        """根据意图获取最优字段"""
        # 尝试使用新的字段映射器
        try:
            from .field_mapper import field_mapper
            import asyncio
            
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果在异步环境中，使用备用方案
                return SmartJiraQuery._get_fields_by_intent(intent)
            else:
                # 同步环境中可以直接调用
                return loop.run_until_complete(field_mapper.get_fields_by_intent(intent))
        except Exception:
            # 如果新的字段映射器不可用，使用原有方案
            return SmartJiraQuery._get_fields_by_intent(intent)
    
    @staticmethod
    def _get_fields_by_intent(intent: str) -> List[str]:
        """根据意图获取字段的备用方案"""
        base_fields = ['key', 'summary', 'status', 'priority', 'assignee', 'reporter', 'created', 'updated']
        
        if intent == 'query_subtask' or intent == 'query_issues':
            # 子任务查询需要额外字段
            return base_fields + [
                'issuetype',
                'customfield_16300',  # Planned Start Date
                'customfield_16301',  # Planned Due Date
                'customfield_10100',  # Story Points
                'customfield_10004',  # Story Point Estimate (备用)
            ]
        elif intent == 'query_epic':
            return base_fields + ['components', 'fixVersions', 'labels']
        elif intent == 'query_timeline':
            return base_fields + [
                'duedate',
                'customfield_11520',  # Planned Dev Start Date
                'customfield_11509',  # Planned Dev Due Date
                'customfield_11521',  # Planned QA Start Date
                'customfield_11510',  # Planned QA Due Date
                'customfield_11513',  # Planned Release Date
            ]
        else:
            return base_fields
    
    async def save_query_history(self, user_id: str, group_id: str, employee_code: str,
                                query: str, intent: str, jql: str, result: Dict) -> None:
        """保存查询历史"""
        try:
            history = AIQueryHistory(
                user_id=user_id,
                group_id=group_id,
                employee_code=employee_code,
                query=query,
                intent=intent,
                jql=jql,
                result_count=result.get('result_count', 0) if result.get('success') else 0,
                processing_time=result.get('query_time', 0),
                success=result.get('success', False)
            )
            
            # 在线程池中保存，避免阻塞
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, history.save)
            
        except Exception as e:
            logger.error(f"保存查询历史失败: {str(e)}")
    
    async def get_user_query_stats(self, user_id: str, days: int = 7) -> Dict:
        """获取用户查询统计"""
        try:
            from datetime import timedelta
            
            def _get_stats():
                since_date = timezone.now() - timedelta(days=days)
                histories = AIQueryHistory.objects.filter(
                    user_id=user_id,
                    created_at__gte=since_date
                )
                
                total_queries = histories.count()
                successful_queries = histories.filter(success=True).count()
                avg_response_time = histories.aggregate(
                    avg_time=models.Avg('processing_time')
                )['avg_time'] or 0
                
                # 统计常用意图
                intent_stats = {}
                for history in histories.values('intent').annotate(count=models.Count('intent')):
                    intent_stats[history['intent']] = history['count']
                
                return {
                    'total_queries': total_queries,
                    'successful_queries': successful_queries,
                    'success_rate': (successful_queries / total_queries * 100) if total_queries > 0 else 0,
                    'avg_response_time': round(avg_response_time, 2),
                    'intent_stats': intent_stats
                }
            
            loop = asyncio.get_event_loop()
            stats = await loop.run_in_executor(None, _get_stats)
            
            return {
                'success': True,
                'stats': stats
            }
            
        except Exception as e:
            logger.error(f"获取用户统计失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_issue_details(self, issue_key: str, fields: List[str] = None) -> Dict:
        """
        获取单个JIRA issue的详细信息
        
        Args:
            issue_key: JIRA issue键值
            fields: 需要返回的字段列表
            
        Returns:
            Issue详细信息
        """
        try:
            start_time = timezone.now()
            
            # 异步获取token
            if not self.jira_token:
                self.jira_token = await self._get_jira_token_async()
            
            # 在异步环境中运行同步的JIRA查询
            def _sync_get_issue():
                if not self.jira:
                    self.jira = JIRA(server='https://jira.shopee.io', token_auth=self.jira_token)
                
                # 获取issue信息
                issue = self.jira.issue(issue_key, fields=fields)
                
                # 将issue转换为字典格式
                issue_dict = {
                    'key': issue.key,
                    'self': issue.self,
                    'fields': {}
                }
                
                # 处理fields
                for field_name in dir(issue.fields):
                    if not field_name.startswith('_'):
                        try:
                            field_value = getattr(issue.fields, field_name)
                            if field_value is not None:
                                # 处理特殊字段类型
                                if hasattr(field_value, 'displayName'):
                                    issue_dict['fields'][field_name] = {
                                        'displayName': field_value.displayName,
                                        'name': getattr(field_value, 'name', None),
                                        'emailAddress': getattr(field_value, 'emailAddress', None)
                                    }
                                elif hasattr(field_value, 'name'):
                                    issue_dict['fields'][field_name] = {
                                        'name': field_value.name,
                                        'id': getattr(field_value, 'id', None)
                                    }
                                elif isinstance(field_value, list):
                                    # 处理列表类型
                                    processed_list = []
                                    for item in field_value:
                                        if hasattr(item, 'name'):
                                            processed_list.append({'name': item.name, 'id': getattr(item, 'id', None)})
                                        else:
                                            processed_list.append(str(item))
                                    issue_dict['fields'][field_name] = processed_list
                                else:
                                    issue_dict['fields'][field_name] = str(field_value)
                        except Exception as field_error:
                            logger.debug(f"处理字段 {field_name} 时出错: {field_error}")
                            continue
                
                return issue_dict
            
            # 使用线程池执行同步查询
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_get_issue)
            
            query_time = (timezone.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'data': result,
                'query_time': query_time
            }
            
        except JIRAError as e:
            if e.status_code == 401:
                error_msg = "JIRA认证失败，请检查token是否过期"
            elif e.status_code == 404:
                error_msg = f"JIRA issue {issue_key} 不存在"
            else:
                error_msg = f"JIRA查询失败: {str(e)}"
            
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'error_type': 'jira_error',
                'status_code': getattr(e, 'status_code', None)
            }
        except Exception as e:
            error_msg = f"获取issue详情异常: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'error_type': 'general_error'
            }
    
    async def create_subtask(self, parent_issue_key: str, subtask_data: Dict) -> Dict:
        """
        创建子任务
        
        Args:
            parent_issue_key: 父单号（如 SPCB-123）
            subtask_data: 子任务数据
                - summary: 子任务标题
                - story_points: 工作量（可选，默认1）
                - assignee: 指派人（可选，默认当前用户）
                - planned_start_date: 计划开始日期（可选）
                - planned_due_date: 计划结束日期（可选）
                - description: 描述（可选）
                
        Returns:
            创建结果
        """
        # 移除JIRA Token检查，允许所有用户创建子任务
        # 将使用管理员Token创建任务，但会将reporter和assignee设置为指令发送人
        
        try:
            # 处理用户输入中的相对日期表达式
            def parse_relative_dates_from_input(input_data: Dict) -> Dict:
                """从用户输入中解析相对日期表达式"""
                import re
                from datetime import datetime, timedelta
                
                # 获取原始查询文本
                original_query = input_data.get('original_query', '')
                if not original_query:
                    logger.warning("⚠️ 没有原始查询文本，无法解析相对日期表达式")
                    return input_data
                
                logger.info(f"🔍 检查原始查询中的相对日期表达式: {original_query}")
                logger.info(f"🔍 当前提取的日期信息: 开始日期={input_data.get('planned_start_date')}, 结束日期={input_data.get('planned_due_date')}")
                
                # 获取当前日期
                today = datetime.now()
                today_str = today.strftime("%Y-%m-%d")
                tomorrow = today + timedelta(days=1)
                tomorrow_str = tomorrow.strftime("%Y-%m-%d")
                yesterday = today - timedelta(days=1)
                yesterday_str = yesterday.strftime("%Y-%m-%d")
                
                logger.info(f"🗓️ 当前服务器日期时间: {today.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"🗓️ 日期映射: 昨天={yesterday_str}, 今天={today_str}, 明天={tomorrow_str}")
                
                # 检查是否包含相对日期表达式
                has_today = "今天" in original_query
                has_tomorrow = "明天" in original_query
                has_yesterday = "昨天" in original_query
                
                logger.info(f"🔍 检测到相对日期表达式: 昨天={has_yesterday}, 今天={has_today}, 明天={has_tomorrow}")
                
                # 解析开始和结束日期
                start_pattern = re.search(r'(昨天|今天|明天)(?:开始|起|起始)', original_query)
                end_pattern = re.search(r'(昨天|今天|明天)(?:结束|止|完成)', original_query)
                
                # 如果包含相对日期表达式，更新日期字段
                if start_pattern:
                    start_date = start_pattern.group(1)
                    if start_date == "今天":
                        logger.info(f"🗓️ 从查询中检测到'今天'作为开始日期，设置为: {today_str}")
                        input_data['planned_start_date'] = today_str
                    elif start_date == "明天":
                        logger.info(f"🗓️ 从查询中检测到'明天'作为开始日期，设置为: {tomorrow_str}")
                        input_data['planned_start_date'] = tomorrow_str
                    elif start_date == "昨天":
                        logger.info(f"🗓️ 从查询中检测到'昨天'作为开始日期，设置为: {yesterday_str}")
                        input_data['planned_start_date'] = yesterday_str
                elif has_today and not end_pattern:
                    logger.info(f"🗓️ 从查询中检测到'今天'，设置开始日期为: {today_str}")
                    input_data['planned_start_date'] = today_str
                elif has_yesterday and not end_pattern:
                    logger.info(f"🗓️ 从查询中检测到'昨天'，设置开始日期为: {yesterday_str}")
                    input_data['planned_start_date'] = yesterday_str
                
                if end_pattern:
                    end_date = end_pattern.group(1)
                    if end_date == "今天":
                        logger.info(f"🗓️ 从查询中检测到'今天'作为结束日期，设置为: {today_str}")
                        input_data['planned_due_date'] = today_str
                    elif end_date == "明天":
                        logger.info(f"🗓️ 从查询中检测到'明天'作为结束日期，设置为: {tomorrow_str}")
                        input_data['planned_due_date'] = tomorrow_str
                    elif end_date == "昨天":
                        logger.info(f"🗓️ 从查询中检测到'昨天'作为结束日期，设置为: {yesterday_str}")
                        input_data['planned_due_date'] = yesterday_str
                elif has_tomorrow and not start_pattern:
                    logger.info(f"🗓️ 从查询中检测到'明天'，设置结束日期为: {tomorrow_str}")
                    input_data['planned_due_date'] = tomorrow_str
                
                # 检查工作量是否为小数
                story_points = input_data.get('story_points')
                if story_points and isinstance(story_points, (int, float, str)):
                    try:
                        # 将工作量转换为数字
                        if isinstance(story_points, str):
                            if story_points.lower() in ('0.5d', '0.5'):
                                story_points = 0.5
                            else:
                                # 尝试提取数字部分
                                match = re.search(r'(\d+\.?\d*)', story_points)
                                if match:
                                    story_points = float(match.group(1))
                                    # 如果是整数，转换为整数类型
                                    if story_points.is_integer():
                                        story_points = int(story_points)
                        
                        logger.info(f"📊 从查询中解析工作量: {story_points}")
                        input_data['story_points'] = story_points
                    except (ValueError, TypeError) as e:
                        logger.warning(f"⚠️ 工作量解析失败: {e}")
                
                # 根据工作量自动计算缺失的日期
                if story_points and (input_data.get('planned_start_date') and not input_data.get('planned_due_date')):
                    # 有开始日期但没有结束日期，根据工作量计算结束日期
                    try:
                        sp = float(story_points) if isinstance(story_points, str) else story_points
                        start_date = datetime.strptime(input_data['planned_start_date'], '%Y-%m-%d')
                        # 工作量天数减1（因为开始当天也算一天）
                        days_to_add = max(0, int(sp) - 1)
                        due_date = start_date + timedelta(days=days_to_add)
                        due_date_str = due_date.strftime('%Y-%m-%d')
                        logger.info(f"🗓️ 根据开始日期和工作量自动计算结束日期: {input_data['planned_start_date']} + {sp}d = {due_date_str}")
                        input_data['planned_due_date'] = due_date_str
                    except Exception as e:
                        logger.warning(f"⚠️ 自动计算结束日期失败: {str(e)}")
                
                elif story_points and (input_data.get('planned_due_date') and not input_data.get('planned_start_date')):
                    # 有结束日期但没有开始日期，根据工作量计算开始日期
                    try:
                        sp = float(story_points) if isinstance(story_points, str) else story_points
                        due_date = datetime.strptime(input_data['planned_due_date'], '%Y-%m-%d')
                        # 工作量天数减1（因为结束当天也算一天）
                        days_to_subtract = max(0, int(sp) - 1)
                        start_date = due_date - timedelta(days=days_to_subtract)
                        start_date_str = start_date.strftime('%Y-%m-%d')
                        logger.info(f"🗓️ 根据结束日期和工作量自动计算开始日期: {input_data['planned_due_date']} - {sp}d = {start_date_str}")
                        input_data['planned_start_date'] = start_date_str
                    except Exception as e:
                        logger.warning(f"⚠️ 自动计算开始日期失败: {str(e)}")
                
                logger.info(f"🔄 解析后的数据: 开始日期={input_data.get('planned_start_date')}, 结束日期={input_data.get('planned_due_date')}, 工作量={input_data.get('story_points')}")
                return input_data
            
            # 检查是否有原始查询文本
            if 'original_query' not in subtask_data and hasattr(self, 'last_query'):
                subtask_data['original_query'] = self.last_query
            
            # 解析相对日期和工作量
            subtask_data = parse_relative_dates_from_input(subtask_data)
            
            # 异步获取token
            if not self.jira_token:
                self.jira_token = await self._get_jira_token_async()
            
            # 检查是否使用的是用户自己的token还是管理员token
            user_token = await JiraTokenManager.get_user_token_async(self.user_email) if self.user_email else None
            using_admin_token = not user_token
            
            def _sync_create_subtask():
                # 获取JIRA连接
                jira = self._get_jira_connection()
                
                # 获取父单信息
                parent_issue = jira.issue(parent_issue_key)
                
                # 记录父任务的基本信息
                parent_info = self._log_parent_issue_info(parent_issue)
                
                # 检查父任务类型，确保不是子任务
                parent_issue_type = getattr(parent_issue.fields.issuetype, 'name', '')
                logger.info(f"🔍 父任务类型: {parent_issue_type}")
                
                if parent_issue_type.lower() == 'sub-task':
                    logger.error(f"❌ 无法创建子任务: 父任务 {parent_issue_key} 本身是一个子任务，子任务不能作为其他子任务的父任务")
                    
                    # 尝试获取父任务的父任务，作为建议
                    parent_of_parent = None
                    if hasattr(parent_issue.fields, 'parent'):
                        parent_of_parent = getattr(parent_issue.fields.parent, 'key', None)
                    
                    error_msg = f'无法创建子任务: {parent_issue_key} 本身是一个子任务，子任务不能作为其他子任务的父任务'
                    if parent_of_parent:
                        error_msg += f'。您可能想要在 {parent_of_parent} 下创建子任务，请尝试使用命令: "在{parent_of_parent}下建一个子任务：{subtask_data.get("summary", "")}"'
                    
                    return {
                        'success': False,
                        'error': error_msg,
                        'error_type': 'parent_is_subtask',
                        'parent_of_parent': parent_of_parent
                    }
                
                # 检查父任务类型是否是Epic或Task
                if parent_issue_type.lower() not in ['epic', 'task']:
                    logger.warning(f"⚠️ 父任务 {parent_issue_key} 的类型是 {parent_issue_type}，不是Epic或Task")
                    # 这里我们不阻止创建，只是记录警告
                
                # 构建基本子任务数据
                subtask_fields = {
                    'project': {'key': parent_issue.fields.project.key},
                    'summary': subtask_data.get('summary', ''),
                    'issuetype': {'name': 'Sub-task'},
                    'parent': {'key': parent_issue_key},
                    'description': subtask_data.get('description', ''),
                }
                
                # 如果有用户邮箱，设置reporter为指令发送人
                if self.user_email:
                    subtask_fields['reporter'] = {'name': self.user_email}
                    logger.info(f"🔄 设置reporter为指令发送人: {self.user_email}")
                
                # 设置指派人 - 简化逻辑，总是使用指令发送人
                assignee = subtask_data.get('assignee')
                
                # 无论assignee是什么值，只要有用户邮箱，就使用用户邮箱作为指派人
                if self.user_email:
                    username = self.user_email  # 使用完整邮箱
                    subtask_fields['assignee'] = {'name': username}
                    logger.info(f"🔄 设置assignee为指令发送人: {username}")
                else:
                    # 极少数情况：如果没有用户邮箱，尝试使用指定的assignee
                    if assignee and assignee != 'currentUser()':
                        # 如果是邮箱格式，使用完整邮箱
                        if '@' in assignee:
                            username = assignee
                        else:
                            # 如果不是邮箱格式，尝试添加域名
                            username = f"{assignee}@shopee.com"
                        subtask_fields['assignee'] = {'name': username}
                        logger.info(f"🔄 没有用户邮箱，使用指定的指派人: {assignee} -> {username}")
                    else:
                        # 如果没有用户邮箱且没有指定assignee，使用JIRA API获取当前用户
                        try:
                            current_user = jira.myself()
                            subtask_fields['assignee'] = {'name': current_user['name']}
                            logger.info(f"🔄 没有用户邮箱且没有指定assignee，使用JIRA API获取当前用户: {current_user['name']}")
                        except:
                            # 如果无法获取当前用户，不设置assignee
                            logger.warning("⚠️ 无法确定assignee，将使用JIRA默认指派规则")
                
                # 1. 先创建基本子任务（避免字段冲突）
                logger.info(f"🔧 创建子任务基本字段: {subtask_fields}")
                new_subtask = jira.create_issue(fields=subtask_fields)
                logger.info(f"✅ 子任务创建成功: {new_subtask.key}")
                
                # 2. 更新额外字段（Story Points和计划日期）
                update_fields = {}
                
                # 设置Story Points（使用正确的字段ID）
                story_points = subtask_data.get('story_points')
                if story_points:
                    try:
                        # 确保story_points是数字类型
                        if isinstance(story_points, str):
                            # 尝试将字符串转换为浮点数
                            story_points = float(story_points)
                            # 如果是整数值，转换为整数类型
                            if story_points.is_integer():
                                story_points = int(story_points)
                        
                        # 使用已知的正确字段ID
                        story_points_field = 'customfield_10100'  # 来自字段映射器
                        update_fields[story_points_field] = story_points
                        logger.info(f"📊 设置Story Points: {story_points} (类型: {type(story_points).__name__})")
                    except (ValueError, TypeError) as e:
                        logger.warning(f"⚠️ Story Points值转换失败: {e}, 使用默认值1")
                        update_fields['customfield_10100'] = 1
                
                # 设置计划日期（使用正确的字段ID）
                planned_start = subtask_data.get('planned_start_date')
                planned_due = subtask_data.get('planned_due_date')
                
                # 处理相对日期表达式（如"今天"、"明天"等）
                def convert_relative_date(date_str):
                    if not date_str:
                        return None
                    
                    import re
                    from datetime import datetime, timedelta
                    
                    today = datetime.now()
                    logger.info(f"🗓️ 处理日期表达式: '{date_str}', 当前服务器时间: {today.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # 检查是否包含相对日期表达式
                    if isinstance(date_str, str):
                        # 检查日期字符串是否已经是YYYY-MM-DD格式的当前日期、昨天或明天日期
                        today_str = today.strftime("%Y-%m-%d")
                        tomorrow = today + timedelta(days=1)
                        tomorrow_str = tomorrow.strftime("%Y-%m-%d")
                        yesterday = today - timedelta(days=1)
                        yesterday_str = yesterday.strftime("%Y-%m-%d")
                        
                        # 如果日期是固定格式的今天、昨天或明天日期，直接返回
                        if date_str == today_str:
                            logger.info(f"🗓️ 日期已经是今天: {date_str}")
                            return date_str
                        if date_str == tomorrow_str:
                            logger.info(f"🗓️ 日期已经是明天: {date_str}")
                            return date_str
                        if date_str == yesterday_str:
                            logger.info(f"🗓️ 日期已经是昨天: {date_str}")
                            return date_str
                        
                        # 处理相对日期表达式
                        if "今天" in date_str:
                            result = today_str
                            logger.info(f"🗓️ 将'今天'转换为: {result}")
                            return result
                        elif "明天" in date_str:
                            result = tomorrow_str
                            logger.info(f"🗓️ 将'明天'转换为: {result}")
                            return result
                        elif "昨天" in date_str:
                            result = yesterday_str
                            logger.info(f"🗓️ 将'昨天'转换为: {result}")
                            return result
                        elif "后天" in date_str:
                            day_after_tomorrow = today + timedelta(days=2)
                            result = day_after_tomorrow.strftime("%Y-%m-%d")
                            logger.info(f"🗓️ 将'后天'转换为: {result}")
                            return result
                        elif "前天" in date_str:
                            day_before_yesterday = today - timedelta(days=2)
                            result = day_before_yesterday.strftime("%Y-%m-%d")
                            logger.info(f"🗓️ 将'前天'转换为: {result}")
                            return result
                        elif "下周" in date_str:
                            # 简单处理为7天后
                            next_week = today + timedelta(days=7)
                            result = next_week.strftime("%Y-%m-%d")
                            logger.info(f"🗓️ 将'下周'转换为: {result}")
                            return result
                        
                        # 检查是否是大模型生成的固定日期（如2024-06-13）
                        # 如果是，但原始查询中包含相对日期表达式，则替换为正确的日期
                        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
                            original_query = subtask_data.get('original_query', '')
                            
                            # 检查开始日期相关表达式
                            if re.search(r'今天(?:开始|起|起始)', original_query):
                                logger.info(f"🔄 检测到原始查询中包含'今天'作为开始日期，替换固定日期 {date_str} -> {today_str}")
                                return today_str
                            elif re.search(r'明天(?:开始|起|起始)', original_query):
                                logger.info(f"🔄 检测到原始查询中包含'明天'作为开始日期，替换固定日期 {date_str} -> {tomorrow_str}")
                                return tomorrow_str
                            elif re.search(r'昨天(?:开始|起|起始)', original_query):
                                logger.info(f"🔄 检测到原始查询中包含'昨天'作为开始日期，替换固定日期 {date_str} -> {yesterday_str}")
                                return yesterday_str
                            
                            # 检查结束日期相关表达式
                            elif re.search(r'今天(?:结束|止|完成)', original_query):
                                logger.info(f"🔄 检测到原始查询中包含'今天'作为结束日期，替换固定日期 {date_str} -> {today_str}")
                                return today_str
                            elif re.search(r'明天(?:结束|止|完成)', original_query):
                                logger.info(f"🔄 检测到原始查询中包含'明天'作为结束日期，替换固定日期 {date_str} -> {tomorrow_str}")
                                return tomorrow_str
                            elif re.search(r'昨天(?:结束|止|完成)', original_query):
                                logger.info(f"🔄 检测到原始查询中包含'昨天'作为结束日期，替换固定日期 {date_str} -> {yesterday_str}")
                                return yesterday_str
                    
                    logger.info(f"🗓️ 未识别到相对日期表达式，保持原值: {date_str}")
                    return date_str
                
                # 转换相对日期
                original_start = planned_start
                original_due = planned_due
                
                planned_start = convert_relative_date(planned_start)
                planned_due = convert_relative_date(planned_due)
                
                if original_start != planned_start:
                    logger.info(f"🗓️ 开始日期已转换: {original_start} -> {planned_start}")
                if original_due != planned_due:
                    logger.info(f"🗓️ 结束日期已转换: {original_due} -> {planned_due}")
                
                # 验证日期格式
                def validate_date_format(date_str):
                    if not date_str:
                        return None
                    
                    import re
                    from datetime import datetime
                    
                    # 检查是否符合YYYY-MM-DD格式
                    if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
                        try:
                            # 验证是否是有效日期
                            datetime.strptime(date_str, '%Y-%m-%d')
                            logger.info(f"✅ 日期格式验证通过: {date_str}")
                            return date_str
                        except ValueError:
                            logger.warning(f"⚠️ 无效的日期值: {date_str}")
                            return None
                    else:
                        # 尝试解析其他常见格式
                        try:
                            # 尝试多种格式
                            for fmt in ['%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%m-%d-%Y']:
                                try:
                                    date_obj = datetime.strptime(date_str, fmt)
                                    result = date_obj.strftime('%Y-%m-%d')
                                    logger.info(f"✅ 日期格式已转换: {date_str} -> {result} (使用格式: {fmt})")
                                    return result
                                except ValueError:
                                    continue
                        except Exception:
                            pass
                        
                        logger.warning(f"⚠️ 日期格式不正确: {date_str}，期望格式为YYYY-MM-DD")
                        return None
                
                planned_start = validate_date_format(planned_start)
                planned_due = validate_date_format(planned_due)
                
                if planned_start:
                    # 使用已知的正确字段ID
                    planned_start_field = 'customfield_16300'  # 来自字段映射器
                    update_fields[planned_start_field] = planned_start
                    logger.info(f"📅 设置计划开始日期: {planned_start}")
                
                if planned_due:
                    # 使用已知的正确字段ID
                    planned_due_field = 'customfield_16301'  # 来自字段映射器
                    update_fields[planned_due_field] = planned_due
                    logger.info(f"📅 设置计划结束日期: {planned_due}")
                
                # 3. 如果有额外字段需要更新，尝试更新
                if update_fields:
                    try:
                        logger.info(f"🔄 更新子任务额外字段: {update_fields}")
                        new_subtask.update(fields=update_fields)
                        logger.info("✅ 额外字段更新成功")
                    except JIRAError as update_jira_error:
                        # 详细分析JIRA错误类型
                        field_error_msg = self._analyze_field_error(update_jira_error, update_fields)
                        logger.warning(f"⚠️ 字段更新失败（不影响子任务创建）: {field_error_msg}")
                        
                        # 记录具体的失败字段和错误类型
                        for field_id, field_value in update_fields.items():
                            logger.warning(f"  - 字段 {field_id} (值: {field_value}) 更新失败")
                    except Exception as update_error:
                        # 非JIRA错误的其他异常
                        logger.warning(f"⚠️ 额外字段更新异常（不影响主要功能）: {str(update_error)}")
                        for field_id, field_value in update_fields.items():
                            logger.warning(f"  - 字段 {field_id} (值: {field_value}) 更新失败")
                
                result = {
                    'success': True,
                    'issue': new_subtask,
                    'key': new_subtask.key,
                    'parent_key': parent_issue_key,
                    'summary': subtask_data.get('summary', ''),
                    'assignee': subtask_fields.get('assignee', {}).get('name', 'Unknown'),
                    'story_points': story_points or 1,
                    'url': f'https://jira.shopee.io/browse/{new_subtask.key}',
                    'extra_fields_updated': len(update_fields) > 0 and 'update_error' not in locals(),
                    'planned_start_date': planned_start,
                    'planned_due_date': planned_due
                }
                
                # 不再添加JIRA Token相关提醒
                # 系统现在总是使用管理员token创建子任务，但会将reporter和assignee设置为指令发送人
                
                return result
            
            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_create_subtask)
            
            # 检查是否因为父任务类型问题导致的错误
            if not result.get('success', True) and result.get('error_type') == 'parent_is_subtask':
                return result
            
            return result
            
        except JIRAError as e:
            # 记录详细的错误信息
            logger.error(f"⚠️ JIRA错误: HTTP {e.status_code} - {str(e)}")
            if hasattr(e, 'response') and e.response:
                try:
                    error_text = e.response.text
                    logger.error(f"⚠️ JIRA错误详情: {error_text[:500]}")
                except:
                    pass
            
            if e.status_code == 401:
                return {
                    'success': False,
                    'error': 'JIRA Token已过期，请重新设置',
                    'need_token_refresh': True
                }
            elif e.status_code == 404:
                return {
                    'success': False,
                    'error': f'父单 {parent_issue_key} 不存在，请检查单号是否正确'
                }
            elif e.status_code == 400:
                # 400错误通常是字段相关问题，使用详细分析
                detailed_error = self._analyze_field_error(e, subtask_data)
                
                # 检查是否是特定的错误类型，提供更具体的建议
                if "父任务本身是一个子任务" in detailed_error:
                    # 尝试获取父任务的父任务信息
                    try:
                        def _get_parent_of_subtask():
                            jira = JIRA(server='https://jira.shopee.io', token_auth=self.jira_token)
                            issue = jira.issue(parent_issue_key)
                            if hasattr(issue.fields, 'parent'):
                                return getattr(issue.fields.parent, 'key', None)
                            return None
                        
                        loop = asyncio.get_event_loop()
                        parent_of_parent = await loop.run_in_executor(None, _get_parent_of_subtask)
                        
                        if parent_of_parent:
                            detailed_error += f" 您可以尝试使用命令: '在{parent_of_parent}下建一个子任务：{subtask_data.get('summary', '')}'"
                    except Exception as parent_error:
                        logger.error(f"⚠️ 获取父任务的父任务失败: {str(parent_error)}")
                
                # 检查是否是用户不存在的错误
                elif "指派的用户" in detailed_error and "不存在" in detailed_error:
                    detailed_error += " 如果您确定用户存在，请尝试使用完整的邮箱地址，例如: <EMAIL>"
                
                return {
                    'success': False,
                    'error': f'创建子任务失败 - {detailed_error}',
                    'error_type': 'field_error'
                }
            else:
                return {
                    'success': False,
                    'error': f'创建子任务失败: HTTP {e.status_code} - {str(e)}',
                    'error_type': 'jira_error'
                }
        except Exception as e:
            import traceback
            logger.error(f"⚠️ 创建子任务异常: {str(e)}")
            logger.error(f"⚠️ 异常堆栈: {traceback.format_exc()}")
            return {
                'success': False,
                'error': f'创建子任务异常: {str(e)}'
            }

    async def get_issue_title(self, issue_key: str) -> Dict:
        """
        获取JIRA单号的标题
        
        Args:
            issue_key: JIRA单号（如 SPCB-123）
            
        Returns:
            查询结果，包含标题
        """
        try:
            # 异步获取token
            if not self.jira_token:
                self.jira_token = await self._get_jira_token_async()
            
            # 只获取summary字段
            fields = ['summary']
            
            # 执行查询
            result = await self.get_issue_details(issue_key, fields)
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f"获取标题失败: {result.get('error', '未知错误')}",
                    'title': None
                }
            
            # 提取标题
            title = result.get('data', {}).get('fields', {}).get('summary', '')
            
            return {
                'success': True,
                'title': title
            }
            
        except Exception as e:
            logger.error(f"获取JIRA单号标题异常: {str(e)}")
            return {
                'success': False,
                'error': f"获取标题异常: {str(e)}",
                'title': None
            }
    
    async def find_user_task_in_epic(self, epic_key: str) -> Dict:
        """
        查找用户在Epic下的Task
        
        Args:
            epic_key: Epic单号（如 SPCB-123）
            
        Returns:
            查询结果，包含用户在Epic下的Task列表
        """
        if not self.user_email:
            return {
                'success': False,
                'error': '没有用户邮箱信息，无法查找用户在Epic下的Task',
                'tasks': []
            }
        
        try:
            # 异步获取token
            if not self.jira_token:
                self.jira_token = await self._get_jira_token_async()
            
            # 构建JQL查询
            jql = f'"Epic Link" = {epic_key} AND issuetype = Task AND assignee = "{self.user_email}"'
            
            # 需要的字段
            fields = ['key', 'summary', 'status', 'assignee', 'issuetype']
            
            # 执行查询
            result = await self.execute_jql(jql, fields)
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f"查询失败: {result.get('error', '未知错误')}",
                    'tasks': []
                }
            
            # 提取Task列表
            tasks = []
            for issue in result.get('data', {}).get('issues', []):
                tasks.append({
                    'key': issue.get('key'),
                    'summary': issue.get('fields', {}).get('summary', ''),
                    'status': issue.get('fields', {}).get('status', {}).get('name', ''),
                    'assignee': issue.get('fields', {}).get('assignee', {}).get('name', '')
                })
            
            logger.info(f"🔍 在Epic {epic_key} 下找到 {len(tasks)} 个分配给 {self.user_email} 的Task")
            
            return {
                'success': True,
                'tasks': tasks
            }
            
        except Exception as e:
            logger.error(f"查找用户在Epic下的Task异常: {str(e)}")
            return {
                'success': False,
                'error': f"查询异常: {str(e)}",
                'tasks': []
            }

    def _analyze_field_error(self, jira_error: JIRAError, failed_fields: Dict) -> str:
        """分析字段相关的JIRA错误"""
        try:
            # 记录原始错误信息
            logger.error(f"🔍 分析JIRA错误: {str(jira_error)}")
            
            # 解析错误响应
            if hasattr(jira_error, 'response') and jira_error.response:
                try:
                    import json
                    error_text = jira_error.response.text
                    logger.error(f"🔍 JIRA错误响应: {error_text[:500]}")
                    error_data = json.loads(error_text)
                    
                    # 检查是否有子任务相关的特定错误
                    if 'errors' in error_data and 'issuetype' in error_data['errors']:
                        issuetype_error = error_data['errors']['issuetype']
                        if 'parent issue' in issuetype_error.lower() and 'can not be sub-task' in issuetype_error.lower():
                            # 提取父任务ID和Key
                            import re
                            match = re.search(r"Parent issue ID: '(\d+)' / Key: '([^']+)'", issuetype_error)
                            if match:
                                issue_id, issue_key = match.groups()
                                logger.error(f"🔍 检测到父任务是子任务的错误: ID={issue_id}, Key={issue_key}")
                                return f"父任务 {issue_key} 本身是一个子任务，子任务不能作为其他子任务的父任务。请尝试在该子任务的父任务下创建子任务。"
                            else:
                                return "父任务本身是一个子任务，子任务不能作为其他子任务的父任务。请尝试在该子任务的父任务下创建子任务。"
                    
                    # 检查是否有用户不存在的错误
                    if 'errors' in error_data and 'assignee' in error_data['errors']:
                        assignee_error = error_data['errors']['assignee']
                        if "user" in assignee_error.lower() and "does not exist" in assignee_error.lower():
                            import re
                            match = re.search(r"User '([^']+)' does not exist", assignee_error)
                            if match:
                                username = match.group(1)
                                logger.error(f"🔍 检测到用户不存在错误: {username}")
                                return f"指派的用户 '{username}' 不存在。请检查用户名是否正确，或使用完整的邮箱地址。"
                            else:
                                return "指派的用户不存在。请检查用户名是否正确，或使用完整的邮箱地址。"
                    
                except Exception as parse_error:
                    logger.error(f"🔍 解析JIRA错误响应失败: {str(parse_error)}")
                    error_data = {'errorMessages': [], 'errors': {}}
            else:
                error_data = {'errorMessages': [], 'errors': {}}
            
            error_messages = []
            
            # 记录传入的字段值，便于调试
            logger.info(f"🔍 分析的字段值: {failed_fields}")
            
            # 分析字段级别的错误
            field_errors = error_data.get('errors', {})
            if field_errors:
                for field_id, field_error in field_errors.items():
                    field_name = self._get_field_name_from_id(field_id)
                    field_value = failed_fields.get(field_id, 'Unknown')
                    logger.error(f"🔍 字段错误: {field_id}({field_name}) = {field_value}, 错误: {field_error}")
                    
                    if 'cannot be set' in field_error.lower():
                        error_messages.append(f"字段 {field_name}({field_id}) 不可设置：可能在当前screen中不可见或不存在")
                    elif 'unknown' in field_error.lower():
                        error_messages.append(f"字段 {field_name}({field_id}) 未知：字段可能已被删除或重命名")
                    elif 'required' in field_error.lower():
                        error_messages.append(f"字段 {field_name}({field_id}) 为必填项但未提供值")
                    elif 'invalid' in field_error.lower():
                        error_messages.append(f"字段 {field_name}({field_id}) 值无效：'{field_value}' (类型: {type(field_value).__name__})")
                    else:
                        error_messages.append(f"字段 {field_name}({field_id}) 错误：{field_error}")
            
            # 分析通用错误消息
            general_errors = error_data.get('errorMessages', [])
            for msg in general_errors:
                logger.error(f"🔍 通用错误: {msg}")
                if 'permission' in msg.lower():
                    error_messages.append(f"权限错误：{msg}")
                elif 'workflow' in msg.lower():
                    error_messages.append(f"工作流错误：{msg}")
                else:
                    error_messages.append(f"通用错误：{msg}")
            
            # 如果没有解析到具体错误，使用状态码分析
            if not error_messages:
                if jira_error.status_code == 400:
                    error_messages.append("请求格式错误：字段值类型不匹配或格式不正确")
                elif jira_error.status_code == 403:
                    error_messages.append("权限不足：当前用户无权限更新这些字段")
                elif jira_error.status_code == 404:
                    error_messages.append("资源不存在：字段ID不存在或问题已被删除")
                else:
                    error_messages.append(f"HTTP {jira_error.status_code} 错误：{str(jira_error)}")
            
            return " | ".join(error_messages) if error_messages else str(jira_error)
            
        except Exception as e:
            import traceback
            logger.error(f"🔍 分析字段错误时发生异常: {str(e)}")
            logger.error(f"🔍 异常堆栈: {traceback.format_exc()}")
            return f"字段错误分析失败：{str(jira_error)}"

    def _get_field_name_from_id(self, field_id: str) -> str:
        """从字段ID获取字段名称"""
        try:
            # 尝试使用字段映射器获取字段名称
            from .field_mapper import field_mapper
            
            # 使用已知的重要字段映射
            shopee_fields = field_mapper.SHOPEE_IMPORTANT_FIELDS
            if field_id in shopee_fields:
                return shopee_fields[field_id]['name']
            
            # 常见字段的手动映射
            common_fields = {
                'customfield_10100': 'Story Points',
                'customfield_16300': 'Planned Start Date', 
                'customfield_16301': 'Planned Due Date',
                'customfield_10004': 'Story Point Estimate',
                'customfield_10306': 'Product Manager',
                'customfield_10307': 'Developer',
                'customfield_10308': 'QA',
                'summary': 'Summary',
                'description': 'Description',
                'assignee': 'Assignee',
                'priority': 'Priority',
                'status': 'Status'
            }
            
            return common_fields.get(field_id, f"未知字段")
            
        except Exception as e:
            logger.debug(f"获取字段名称失败: {str(e)}")
            return "未知字段" 

    def _log_parent_issue_info(self, parent_issue):
        """记录父任务的基本信息，用于调试"""
        try:
            issue_type = getattr(parent_issue.fields.issuetype, 'name', 'Unknown')
            summary = getattr(parent_issue.fields, 'summary', 'Unknown')
            status = getattr(parent_issue.fields.status, 'name', 'Unknown')
            
            logger.info(f"📄 父任务信息 - 类型: {issue_type}, 标题: {summary}, 状态: {status}")
            
            # 记录父任务的父任务（如果存在）
            if hasattr(parent_issue.fields, 'parent'):
                parent_of_parent = getattr(parent_issue.fields.parent, 'key', 'Unknown')
                logger.info(f"📄 父任务的父任务: {parent_of_parent}")
                
            return {
                'issue_type': issue_type,
                'summary': summary,
                'status': status
            }
        except Exception as e:
            logger.error(f"📄 获取父任务信息失败: {str(e)}")
            return {} 
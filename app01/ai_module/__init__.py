"""
AI 增强模块

提供基于LLM的智能JIRA查询和多轮对话功能
"""

from app01.bot_config import BotConfig

__version__ = "1.0.0"
__author__ = f"{BotConfig.BOT_NAME} Team"

from .ai_assistant import AIAssistant
from .conversation_manager import ConversationManager
from .smart_jira_query import SmartJiraQuery
from .llm_client import LLMClient
from .typing_status import TypingStatusManager
from .private_chat import PrivateChat
from .document_integration import DocumentIntegration
from .document_processor import DocumentManager
from .confluence_processor import ConfluenceProcessor
from .document_config import DocumentConfig
from .todo_manager import TodoManager
from .service_analytics import ServiceAnalytics

__all__ = [
    'AIAssistant',
    'ConversationManager', 
    'SmartJiraQuery',
    'LLMClient',
    'TypingStatusManager',
    'PrivateChat',
    'DocumentIntegration',
    'DocumentManager',
    'ConfluenceProcessor',
    'DocumentConfig',
    'TodoManager',
    'ServiceAnalytics'
] 
"""
JIRA 数据统计分析模块
支持多种统计分析类型和图表数据生成
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, Counter
from django.utils import timezone
from icecream import ic

logger = logging.getLogger(__name__)


class JiraStatistics:
    """JIRA统计分析类"""
    
    def __init__(self):
        self.supported_analyses = {
            'trend': '趋势分析',
            'distribution': '分布分析', 
            'count': '数量统计',
            'efficiency': '效率分析',
            'comparison': '对比分析'
        }
        
        self.chart_types = {
            'line': '折线图',
            'bar': '柱状图',
            'pie': '饼图',
            'scatter': '散点图',
            'heatmap': '热力图'
        }
    
    async def analyze_data(self, jira_data: Dict, analysis_type: str, 
                          field: str = None, time_range: str = None) -> Dict:
        """
        执行数据分析
        
        Args:
            jira_data: JIRA查询返回的数据
            analysis_type: 分析类型 (trend/distribution/count/efficiency/comparison)
            field: 要分析的字段
            time_range: 时间范围
            
        Returns:
            分析结果
        """
        try:
            ic(f"📊 开始统计分析 - 类型: {analysis_type}, 字段: {field}")
            
            if not jira_data or not jira_data.get('issues'):
                return {
                    'success': False,
                    'error': '没有可分析的数据'
                }
            
            issues = jira_data['issues']
            
            # 根据分析类型执行相应分析
            if analysis_type == 'trend':
                result = await self._trend_analysis(issues, field, time_range)
            elif analysis_type == 'distribution':
                result = await self._distribution_analysis(issues, field)
            elif analysis_type == 'count':
                result = await self._count_analysis(issues, field)
            elif analysis_type == 'efficiency':
                result = await self._efficiency_analysis(issues, time_range)
            elif analysis_type == 'comparison':
                result = await self._comparison_analysis(issues, field, time_range)
            else:
                return {
                    'success': False,
                    'error': f'不支持的分析类型: {analysis_type}'
                }
            
            if result['success']:
                # 生成图表数据
                chart_data = await self._generate_chart_data(result['data'], analysis_type)
                result['chart_data'] = chart_data
                
                # 生成分析摘要
                summary = await self._generate_summary(result['data'], analysis_type, field)
                result['summary'] = summary
            
            ic(f"✅ 统计分析完成 - 成功: {result['success']}")
            return result
            
        except Exception as e:
            logger.error(f"统计分析异常: {str(e)}")
            return {
                'success': False,
                'error': f'分析异常: {str(e)}'
            }
    
    async def _trend_analysis(self, issues: List[Dict], field: str, time_range: str) -> Dict:
        """趋势分析"""
        try:
            # 按时间分组统计
            time_groups = defaultdict(int)
            field_trends = defaultdict(lambda: defaultdict(int))
            
            for issue in issues:
                # 获取时间字段
                created_date = self._parse_date(issue.get('fields', {}).get('created'))
                if not created_date:
                    continue
                
                # 按周/月分组
                if time_range and 'week' in time_range.lower():
                    time_key = created_date.strftime('%Y-W%U')
                else:
                    time_key = created_date.strftime('%Y-%m')
                
                time_groups[time_key] += 1
                
                # 如果指定了字段，按字段值分组
                if field:
                    field_value = self._get_field_value(issue, field)
                    if field_value:
                        field_trends[time_key][field_value] += 1
            
            # 排序时间键
            sorted_times = sorted(time_groups.keys())
            
            trend_data = {
                'time_series': [
                    {'time': time_key, 'count': time_groups[time_key]}
                    for time_key in sorted_times
                ],
                'field_trends': dict(field_trends) if field else None,
                'total_issues': len(issues),
                'time_range': f"{sorted_times[0]} 到 {sorted_times[-1]}" if sorted_times else "无数据"
            }
            
            return {
                'success': True,
                'data': trend_data,
                'analysis_type': 'trend'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'趋势分析失败: {str(e)}'
            }
    
    async def _distribution_analysis(self, issues: List[Dict], field: str) -> Dict:
        """分布分析"""
        try:
            if not field:
                return {
                    'success': False,
                    'error': '分布分析需要指定字段'
                }
            
            # 统计字段值分布
            field_counter = Counter()
            
            for issue in issues:
                field_value = self._get_field_value(issue, field)
                if field_value:
                    if isinstance(field_value, list):
                        for value in field_value:
                            field_counter[str(value)] += 1
                    else:
                        field_counter[str(field_value)] += 1
            
            # 计算百分比
            total = sum(field_counter.values())
            distribution_data = []
            
            for value, count in field_counter.most_common():
                percentage = (count / total * 100) if total > 0 else 0
                distribution_data.append({
                    'value': value,
                    'count': count,
                    'percentage': round(percentage, 2)
                })
            
            return {
                'success': True,
                'data': {
                    'field': field,
                    'distribution': distribution_data,
                    'total_count': total,
                    'unique_values': len(field_counter)
                },
                'analysis_type': 'distribution'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'分布分析失败: {str(e)}'
            }
    
    async def _count_analysis(self, issues: List[Dict], field: str = None) -> Dict:
        """数量统计分析"""
        try:
            basic_stats = {
                'total_issues': len(issues),
                'issue_types': Counter(),
                'statuses': Counter(),
                'priorities': Counter(),
                'assignees': Counter()
            }
            
            for issue in issues:
                fields = issue.get('fields', {})
                
                # 统计基本字段
                issue_type = fields.get('issuetype', {}).get('name', '未知')
                basic_stats['issue_types'][issue_type] += 1
                
                status = fields.get('status', {}).get('name', '未知')
                basic_stats['statuses'][status] += 1
                
                priority = fields.get('priority', {}).get('name', '未知')
                basic_stats['priorities'][priority] += 1
                
                assignee = fields.get('assignee', {})
                assignee_name = assignee.get('displayName', '未分配') if assignee else '未分配'
                basic_stats['assignees'][assignee_name] += 1
            
            # 如果指定了特定字段，额外统计
            field_stats = None
            if field:
                field_counter = Counter()
                for issue in issues:
                    field_value = self._get_field_value(issue, field)
                    if field_value:
                        if isinstance(field_value, list):
                            for value in field_value:
                                field_counter[str(value)] += 1
                        else:
                            field_counter[str(field_value)] += 1
                
                field_stats = {
                    'field': field,
                    'counts': dict(field_counter.most_common()),
                    'unique_count': len(field_counter)
                }
            
            return {
                'success': True,
                'data': {
                    'basic_stats': {k: dict(v) if isinstance(v, Counter) else v 
                                  for k, v in basic_stats.items()},
                    'field_stats': field_stats
                },
                'analysis_type': 'count'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'数量统计失败: {str(e)}'
            }
    
    async def _efficiency_analysis(self, issues: List[Dict], time_range: str = None) -> Dict:
        """效率分析"""
        try:
            efficiency_data = {
                'resolution_times': [],
                'avg_resolution_time': 0,
                'completion_rate': 0,
                'velocity_data': defaultdict(int)
            }
            
            resolved_issues = []
            total_resolution_time = 0
            
            for issue in issues:
                fields = issue.get('fields', {})
                
                created_date = self._parse_date(fields.get('created'))
                resolved_date = self._parse_date(fields.get('resolutiondate'))
                
                if created_date and resolved_date:
                    resolution_time = (resolved_date - created_date).days
                    efficiency_data['resolution_times'].append({
                        'issue_key': issue.get('key'),
                        'resolution_days': resolution_time
                    })
                    total_resolution_time += resolution_time
                    resolved_issues.append(issue)
                    
                    # 按月统计完成数量
                    month_key = resolved_date.strftime('%Y-%m')
                    efficiency_data['velocity_data'][month_key] += 1
            
            # 计算平均解决时间
            if resolved_issues:
                efficiency_data['avg_resolution_time'] = round(
                    total_resolution_time / len(resolved_issues), 2
                )
            
            # 计算完成率
            efficiency_data['completion_rate'] = round(
                len(resolved_issues) / len(issues) * 100, 2
            ) if issues else 0
            
            # 转换velocity_data为列表
            efficiency_data['velocity_data'] = [
                {'month': month, 'completed': count}
                for month, count in sorted(efficiency_data['velocity_data'].items())
            ]
            
            return {
                'success': True,
                'data': efficiency_data,
                'analysis_type': 'efficiency'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'效率分析失败: {str(e)}'
            }
    
    async def _comparison_analysis(self, issues: List[Dict], field: str, time_range: str = None) -> Dict:
        """对比分析"""
        try:
            if not field:
                return {
                    'success': False,
                    'error': '对比分析需要指定字段'
                }
            
            # 按时间段分组
            current_period = []
            previous_period = []
            
            # 计算时间分割点
            now = timezone.now()
            if time_range and 'month' in time_range.lower():
                split_date = now - timedelta(days=30)
            else:
                split_date = now - timedelta(days=7)
            
            for issue in issues:
                created_date = self._parse_date(issue.get('fields', {}).get('created'))
                if not created_date:
                    continue
                
                if created_date >= split_date:
                    current_period.append(issue)
                else:
                    previous_period.append(issue)
            
            # 分别统计两个时间段的字段分布
            current_stats = Counter()
            previous_stats = Counter()
            
            for issue in current_period:
                field_value = self._get_field_value(issue, field)
                if field_value:
                    current_stats[str(field_value)] += 1
            
            for issue in previous_period:
                field_value = self._get_field_value(issue, field)
                if field_value:
                    previous_stats[str(field_value)] += 1
            
            # 计算变化
            comparison_data = []
            all_values = set(current_stats.keys()) | set(previous_stats.keys())
            
            for value in all_values:
                current_count = current_stats.get(value, 0)
                previous_count = previous_stats.get(value, 0)
                
                if previous_count > 0:
                    change_rate = round((current_count - previous_count) / previous_count * 100, 2)
                else:
                    change_rate = 100 if current_count > 0 else 0
                
                comparison_data.append({
                    'value': value,
                    'current_count': current_count,
                    'previous_count': previous_count,
                    'change': current_count - previous_count,
                    'change_rate': change_rate
                })
            
            # 按变化率排序
            comparison_data.sort(key=lambda x: abs(x['change_rate']), reverse=True)
            
            return {
                'success': True,
                'data': {
                    'field': field,
                    'current_period_total': len(current_period),
                    'previous_period_total': len(previous_period),
                    'comparison': comparison_data,
                    'time_split': split_date.strftime('%Y-%m-%d')
                },
                'analysis_type': 'comparison'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'对比分析失败: {str(e)}'
            }
    
    async def _generate_chart_data(self, data: Dict, analysis_type: str) -> Dict:
        """生成图表数据"""
        try:
            chart_data = {}
            
            if analysis_type == 'trend':
                # 趋势图数据
                chart_data = {
                    'type': 'line',
                    'title': '趋势分析',
                    'x_axis': [item['time'] for item in data['time_series']],
                    'y_axis': [item['count'] for item in data['time_series']],
                    'series': [
                        {
                            'name': '问题数量',
                            'data': [item['count'] for item in data['time_series']]
                        }
                    ]
                }
                
            elif analysis_type == 'distribution':
                # 饼图数据
                chart_data = {
                    'type': 'pie',
                    'title': f'{data["field"]} 分布',
                    'data': [
                        {'name': item['value'], 'value': item['count']}
                        for item in data['distribution'][:10]  # 只显示前10个
                    ]
                }
                
            elif analysis_type == 'count':
                # 柱状图数据
                basic_stats = data['basic_stats']
                chart_data = {
                    'type': 'bar',
                    'title': '数量统计',
                    'categories': ['问题类型', '状态', '优先级', '经办人'],
                    'series': [
                        {
                            'name': '数量',
                            'data': [
                                len(basic_stats['issue_types']),
                                len(basic_stats['statuses']),
                                len(basic_stats['priorities']),
                                len(basic_stats['assignees'])
                            ]
                        }
                    ]
                }
                
            elif analysis_type == 'efficiency':
                # 效率图表
                chart_data = {
                    'type': 'line',
                    'title': '完成速度',
                    'x_axis': [item['month'] for item in data['velocity_data']],
                    'y_axis': [item['completed'] for item in data['velocity_data']],
                    'series': [
                        {
                            'name': '完成数量',
                            'data': [item['completed'] for item in data['velocity_data']]
                        }
                    ]
                }
                
            elif analysis_type == 'comparison':
                # 对比图表
                comparison = data['comparison'][:10]  # 前10个变化最大的
                chart_data = {
                    'type': 'bar',
                    'title': '对比分析',
                    'categories': [item['value'] for item in comparison],
                    'series': [
                        {
                            'name': '当前周期',
                            'data': [item['current_count'] for item in comparison]
                        },
                        {
                            'name': '上个周期',
                            'data': [item['previous_count'] for item in comparison]
                        }
                    ]
                }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"生成图表数据失败: {str(e)}")
            return {}
    
    async def _generate_summary(self, data: Dict, analysis_type: str, field: str = None) -> str:
        """生成分析摘要"""
        try:
            if analysis_type == 'trend':
                time_series = data['time_series']
                if len(time_series) >= 2:
                    latest = time_series[-1]['count']
                    previous = time_series[-2]['count']
                    change = latest - previous
                    trend = "上升" if change > 0 else "下降" if change < 0 else "持平"
                    return f"趋势分析显示，最近一期有{latest}个问题，比上期{trend}{abs(change)}个。总共分析了{data['total_issues']}个问题。"
                else:
                    return f"趋势分析显示，总共有{data['total_issues']}个问题。"
            
            elif analysis_type == 'distribution':
                distribution = data['distribution']
                if distribution:
                    top_item = distribution[0]
                    return f"{field}字段分布分析显示，'{top_item['value']}'占比最高({top_item['percentage']}%)，共有{data['unique_values']}种不同的值。"
                else:
                    return f"{field}字段没有有效数据。"
            
            elif analysis_type == 'count':
                basic_stats = data['basic_stats']
                return f"数量统计显示，总共{basic_stats['total_issues']}个问题，包含{len(basic_stats['issue_types'])}种问题类型，{len(basic_stats['statuses'])}种状态。"
            
            elif analysis_type == 'efficiency':
                avg_time = data['avg_resolution_time']
                completion_rate = data['completion_rate']
                return f"效率分析显示，平均解决时间为{avg_time}天，完成率为{completion_rate}%。"
            
            elif analysis_type == 'comparison':
                current_total = data['current_period_total']
                previous_total = data['previous_period_total']
                change = current_total - previous_total
                trend = "增加" if change > 0 else "减少" if change < 0 else "持平"
                return f"对比分析显示，当前周期有{current_total}个问题，比上个周期{trend}{abs(change)}个。"
            
            return "分析完成。"
            
        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return "分析完成，但生成摘要时出现问题。"
    
    def _get_field_value(self, issue: Dict, field: str) -> Any:
        """获取字段值"""
        try:
            fields = issue.get('fields', {})
            
            # 常见字段映射
            field_mapping = {
                'assignee': lambda f: f.get('assignee', {}).get('displayName') if f.get('assignee') else None,
                'reporter': lambda f: f.get('reporter', {}).get('displayName') if f.get('reporter') else None,
                'status': lambda f: f.get('status', {}).get('name'),
                'priority': lambda f: f.get('priority', {}).get('name'),
                'issuetype': lambda f: f.get('issuetype', {}).get('name'),
                'project': lambda f: f.get('project', {}).get('name'),
                'labels': lambda f: f.get('labels', []),
                'components': lambda f: [c.get('name') for c in f.get('components', [])],
                'fixVersions': lambda f: [v.get('name') for v in f.get('fixVersions', [])],
                'versions': lambda f: [v.get('name') for v in f.get('versions', [])]
            }
            
            if field in field_mapping:
                return field_mapping[field](fields)
            else:
                # 直接从fields中获取
                return fields.get(field)
                
        except Exception as e:
            logger.error(f"获取字段值失败: {field}, {str(e)}")
            return None
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        try:
            # JIRA日期格式: 2023-12-01T10:30:00.000+0800
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                return datetime.strptime(date_part, '%Y-%m-%d')
            else:
                return datetime.strptime(date_str, '%Y-%m-%d')
        except Exception as e:
            logger.error(f"日期解析失败: {date_str}, {str(e)}")
            return None


# 全局实例
jira_statistics = JiraStatistics() 
"""
Seatalk 私聊功能模块
支持发送文本、图片、文件、交互式消息到私聊
"""

import base64
import aiohttp
import logging
from typing import Dict, List, Optional
from app01.config import get_seatalk_access_token

logger = logging.getLogger(__name__)


class PrivateChat:
    """Seatalk 私聊消息发送器"""
    
    BASE_URL = "https://openapi.seatalk.io/messaging/v2"
    
    def __init__(self):
        self.access_token = None
    
    def _get_access_token(self):
        """获取访问令牌"""
        if not self.access_token:
            self.access_token = get_seatalk_access_token()
        return self.access_token
    
    def _clear_token_cache(self):
        """清除token缓存，强制重新获取"""
        self.access_token = None
    
    async def send_text_message(self, employee_code: str, content: str, 
                               format_type: int = 1) -> Dict:
        """
        发送文本消息到私聊
        
        Args:
            employee_code: 用户employee_code
            content: 消息内容
            format_type: 格式类型 (1: Markdown, 2: 纯文本)
            
        Returns:
            发送结果
        """
        # 最多重试一次
        for attempt in range(2):
            try:
                url = f"{self.BASE_URL}/single_chat"
                headers = {
                    "Authorization": f"Bearer {self._get_access_token()}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "employee_code": employee_code,
                    "message": {
                        "tag": "text",
                        "text": {
                            "format": format_type,
                            "content": content
                        }
                    }
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=data, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            # 检查业务状态码，只有code为0才是真正成功
                            if result.get("code") == 0:
                                logger.info(f"私聊文本消息发送成功: {employee_code}")
                                return {
                                    'success': True,
                                    'message_id': result.get('message_id'),
                                    'data': result
                                }
                            else:
                                # 业务层面失败
                                error_msg = result.get('message', 'Unknown business error')
                                
                                # 如果是token过期且是第一次尝试，清除缓存并重试
                                if (result.get('code') == 100 and 
                                    'access token is expired or invalid' in error_msg.lower() and 
                                    attempt == 0):
                                    logger.warning(f"Access token过期，清除缓存并重试: {employee_code}")
                                    self._clear_token_cache()
                                    continue
                                
                                logger.error(f"私聊文本消息发送失败（业务错误）: {employee_code} - {error_msg}")
                                return {
                                    'success': False,
                                    'error': f"Business error (code: {result.get('code')}): {error_msg}",
                                    'data': result
                                }
                        else:
                            error_text = await response.text()
                            logger.error(f"私聊文本消息发送失败: {response.status} - {error_text}")
                            return {
                                'success': False,
                                'error': f"HTTP {response.status}: {error_text}",
                                'status_code': response.status
                            }
                            
            except Exception as e:
                logger.error(f"私聊文本消息发送异常: {str(e)}")
                if attempt == 1:  # 最后一次尝试失败
                    return {
                        'success': False,
                        'error': f"发送异常: {str(e)}"
                    }
        
        # 如果两次尝试都失败，返回失败结果
        return {
            'success': False,
            'error': "发送失败，已重试一次"
        }
    
    async def send_markdown_message(self, employee_code: str, content: str) -> Dict:
        """
        发送Markdown消息到私聊 (即将废弃的方法，建议使用send_text_message)
        
        Args:
            employee_code: 用户employee_code
            content: Markdown内容
            
        Returns:
            发送结果
        """
        # 最多重试一次
        for attempt in range(2):
            try:
                url = f"{self.BASE_URL}/single_chat"
                headers = {
                    "Authorization": f"Bearer {self._get_access_token()}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "employee_code": employee_code,
                    "message": {
                        "tag": "markdown",
                        "markdown": {
                            "content": content
                        }
                    }
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=data, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            # 检查业务状态码，只有code为0才是真正成功
                            if result.get("code") == 0:
                                logger.info(f"私聊Markdown消息发送成功: {employee_code}")
                                return {
                                    'success': True,
                                    'message_id': result.get('message_id'),
                                    'data': result
                                }
                            else:
                                # 业务层面失败
                                error_msg = result.get('message', 'Unknown business error')
                                
                                # 如果是token过期且是第一次尝试，清除缓存并重试
                                if (result.get('code') == 100 and 
                                    'access token is expired or invalid' in error_msg.lower() and 
                                    attempt == 0):
                                    logger.warning(f"Access token过期，清除缓存并重试: {employee_code}")
                                    self._clear_token_cache()
                                    continue
                                    
                                logger.error(f"私聊Markdown消息发送失败（业务错误）: {employee_code} - {error_msg}")
                                return {
                                    'success': False,
                                    'error': f"Business error (code: {result.get('code')}): {error_msg}",
                                    'data': result
                                }
                        else:
                            error_text = await response.text()
                            logger.error(f"私聊Markdown消息发送失败: {response.status} - {error_text}")
                            return {
                                'success': False,
                                'error': f"HTTP {response.status}: {error_text}",
                                'status_code': response.status
                            }
                            
            except Exception as e:
                logger.error(f"私聊Markdown消息发送异常: {str(e)}")
                if attempt == 1:  # 最后一次尝试失败
                    return {
                        'success': False,
                        'error': f"发送异常: {str(e)}"
                    }
        
        # 如果两次尝试都失败，返回失败结果
        return {
            'success': False,
            'error': "发送失败，已重试一次"
        }
    
    async def send_image_message(self, employee_code: str, image_content: str) -> Dict:
        """
        发送图片消息到私聊
        
        Args:
            employee_code: 用户employee_code
            image_content: Base64编码的图片内容
            
        Returns:
            发送结果
        """
        try:
            url = f"{self.BASE_URL}/single_chat"
            headers = {
                "Authorization": f"Bearer {self._get_access_token()}",
                "Content-Type": "application/json"
            }
            
            data = {
                "employee_code": employee_code,
                "message": {
                    "tag": "image",
                    "image": {
                        "content": image_content
                    }
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        # 检查业务状态码，只有code为0才是真正成功
                        if result.get("code") == 0:
                            logger.info(f"私聊图片消息发送成功: {employee_code}")
                            return {
                                'success': True,
                                'message_id': result.get('message_id'),
                                'data': result
                            }
                        else:
                            # 业务层面失败
                            error_msg = result.get('message', 'Unknown business error')
                            logger.error(f"私聊图片消息发送失败（业务错误）: {employee_code} - {error_msg}")
                            return {
                                'success': False,
                                'error': f"Business error (code: {result.get('code')}): {error_msg}",
                                'data': result
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"私聊图片消息发送失败: {response.status} - {error_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}",
                            'status_code': response.status
                        }
                        
        except Exception as e:
            logger.error(f"私聊图片消息发送异常: {str(e)}")
            return {
                'success': False,
                'error': f"发送异常: {str(e)}"
            }
    
    async def send_file_message(self, employee_code: str, filename: str, 
                               file_content: str) -> Dict:
        """
        发送文件消息到私聊
        
        Args:
            employee_code: 用户employee_code
            filename: 文件名（包含扩展名）
            file_content: Base64编码的文件内容
            
        Returns:
            发送结果
        """
        # 最多重试一次
        for attempt in range(2):
            try:
                url = f"{self.BASE_URL}/single_chat"
                headers = {
                    "Authorization": f"Bearer {self._get_access_token()}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "employee_code": employee_code,
                    "message": {
                        "tag": "file",
                        "file": {
                            "filename": filename,
                            "content": file_content
                        }
                    }
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=data, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            # 检查业务状态码，只有code为0才是真正成功
                            if result.get("code") == 0:
                                logger.info(f"私聊文件消息发送成功: {employee_code}")
                                return {
                                    'success': True,
                                    'message_id': result.get('message_id'),
                                    'data': result
                                }
                            else:
                                # 业务层面失败
                                error_msg = result.get('message', 'Unknown business error')
                                
                                # 如果是token过期且是第一次尝试，清除缓存并重试
                                if (result.get('code') == 100 and 
                                    'access token is expired or invalid' in error_msg.lower() and 
                                    attempt == 0):
                                    logger.warning(f"Access token过期，清除缓存并重试: {employee_code}")
                                    self._clear_token_cache()
                                    continue
                                    
                                logger.error(f"私聊文件消息发送失败（业务错误）: {employee_code} - {error_msg}")
                                return {
                                    'success': False,
                                    'error': f"Business error (code: {result.get('code')}): {error_msg}",
                                    'data': result
                                }
                        else:
                            error_text = await response.text()
                            logger.error(f"私聊文件消息发送失败: {response.status} - {error_text}")
                            return {
                                'success': False,
                                'error': f"HTTP {response.status}: {error_text}",
                                'status_code': response.status
                            }
                            
            except Exception as e:
                logger.error(f"私聊文件消息发送异常: {str(e)}")
                if attempt == 1:  # 最后一次尝试失败
                    return {
                        'success': False,
                        'error': f"发送异常: {str(e)}"
                    }
        
        # 如果两次尝试都失败，返回失败结果
        return {
            'success': False,
            'error': "发送失败，已重试一次"
        }
    
    async def send_interactive_message(self, employee_code: str, elements: List[Dict]) -> Dict:
        """
        发送交互式消息到私聊
        
        Args:
            employee_code: 用户employee_code
            elements: 交互式元素列表
            
        Returns:
            发送结果
        """
        # 最多重试一次
        for attempt in range(2):
            try:
                url = f"{self.BASE_URL}/single_chat"
                headers = {
                    "Authorization": f"Bearer {self._get_access_token()}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "employee_code": employee_code,
                    "message": {
                        "tag": "interactive_message",
                        "interactive_message": {
                            "elements": elements
                        }
                    }
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, json=data, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            # 检查业务状态码，只有code为0才是真正成功
                            if result.get("code") == 0:
                                logger.info(f"私聊交互式消息发送成功: {employee_code}")
                                return {
                                    'success': True,
                                    'message_id': result.get('message_id'),
                                    'data': result
                                }
                            else:
                                # 业务层面失败
                                error_msg = result.get('message', 'Unknown business error')
                                
                                # 如果是token过期且是第一次尝试，清除缓存并重试
                                if (result.get('code') == 100 and 
                                    'access token is expired or invalid' in error_msg.lower() and 
                                    attempt == 0):
                                    logger.warning(f"Access token过期，清除缓存并重试: {employee_code}")
                                    self._clear_token_cache()
                                    continue
                                    
                                logger.error(f"私聊交互式消息发送失败（业务错误）: {employee_code} - {error_msg}")
                                return {
                                    'success': False,
                                    'error': f"Business error (code: {result.get('code')}): {error_msg}",
                                    'data': result
                                }
                        else:
                            error_text = await response.text()
                            logger.error(f"私聊交互式消息发送失败: {response.status} - {error_text}")
                            return {
                                'success': False,
                                'error': f"HTTP {response.status}: {error_text}",
                                'status_code': response.status
                            }
                            
            except Exception as e:
                logger.error(f"私聊交互式消息发送异常: {str(e)}")
                if attempt == 1:  # 最后一次尝试失败
                    return {
                        'success': False,
                        'error': f"发送异常: {str(e)}"
                    }
        
        # 如果两次尝试都失败，返回失败结果
        return {
            'success': False,
            'error': "发送失败，已重试一次"
        }
    
    async def send_query_result_card(self, employee_code: str, title: str, 
                                    description: str, jql: str, 
                                    result_count: int = 0) -> Dict:
        """
        发送查询结果卡片
        
        Args:
            employee_code: 用户employee_code
            title: 卡片标题
            description: 卡片描述
            jql: JQL查询语句
            result_count: 结果数量
            
        Returns:
            发送结果
        """
        elements = [
            {
                "element_type": "title",
                "title": {
                    "text": title
                }
            },
            {
                "element_type": "description", 
                "description": {
                    "format": 1,
                    "text": description
                }
            },
            {
                "element_type": "field",
                "field": {
                    "key": "JQL查询",
                    "value": f"`{jql}`"
                }
            },
            {
                "element_type": "field",
                "field": {
                    "key": "结果数量",
                    "value": str(result_count)
                }
            }
        ]
        
        # 如果有结果，添加查看链接按钮
        if result_count > 0:
            import urllib.parse
            encoded_jql = urllib.parse.quote(jql, safe='')
            jira_search_url = f"https://jira.shopee.io/issues/?jql={encoded_jql}"
            
            elements.append({
                "element_type": "button",
                "button": {
                    "button_type": "redirect",
                    "text": "在JIRA中查看",
                    "link": jira_search_url
                }
            })
        
        return await self.send_interactive_message(employee_code, elements)
    
    def encode_file_content(self, file_path: str) -> str:
        """
        将文件编码为Base64字符串
        
        Args:
            file_path: 文件路径
            
        Returns:
            Base64编码的字符串
        """
        try:
            with open(file_path, 'rb') as file:
                file_content = file.read()
                return base64.b64encode(file_content).decode('utf-8')
        except Exception as e:
            logger.error(f"文件编码失败: {str(e)}")
            return ""
    
    async def send_help_card(self, employee_code: str) -> Dict:
        """
        发送AI助手帮助卡片
        
        Args:
            employee_code: 用户employee_code
            
        Returns:
            发送结果
        """
        from ..bot_config import bot_config
        elements = [
            {
                "element_type": "title",
                "title": {
                    "text": f"{bot_config.BOT_NAME} AI助手使用指南"
                }
            },
            {
                "element_type": "description",
                "description": {
                    "format": 1,
                    "text": "我可以帮您智能查询JIRA信息，支持自然语言输入！"
                }
            },
            {
                "element_type": "field",
                "field": {
                    "key": "基础查询",
                    "value": "• 查看SPCB-1234的状态\n• 这周有哪些高优先级的bug\n• 分配给我的未完成任务"
                }
            },
            {
                "element_type": "field",
                "field": {
                    "key": "配置管理",
                    "value": "• 设置JIRA Token: `/ai config jira_token YOUR_TOKEN`\n• 查看使用统计: `/ai stats`"
                }
            },
            {
                "element_type": "button",
                "button": {
                    "button_type": "callback",
                    "text": "开始使用",
                    "value": "ai_help_start"
                }
            }
        ]
        
        return await self.send_interactive_message(employee_code, elements)


# 全局私聊客户端实例
private_chat_client = PrivateChat() 
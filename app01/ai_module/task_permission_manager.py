#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PermissionResult:
    """权限检查结果"""
    allowed: bool
    reason: str
    suggested_scope: Optional[str] = None
    user_role: Optional[str] = None

class TaskPermissionManager:
    """定时任务权限管理器"""
    
    def __init__(self):
        # 超级管理员 - 可以创建任意范围的任务
        self.SUPER_ADMINS = {
            '<EMAIL>'
        }
        
        # 项目管理员 (PJM) - 只能在指定项目范围内创建任务
        self.PROJECT_MANAGERS = {
            '<EMAIL>': ['SPCB'],
            '<EMAIL>': ['SPCT'],
            # 可以在这里添加更多项目管理员
        }
        
        # 支持的JIRA项目列表
        self.SUPPORTED_PROJECTS = {
            'SPCB', 'SPCT', 'SPCA', 'SPCM', 'SPCP', 'SPCR', 'SPCS', 'SPCU'
        }
    
    def check_task_creation_permission(self, user_email: str, jql_query: str, 
                                     notification_config: Dict, group_title: str = None) -> PermissionResult:
        """
        检查用户创建定时任务的权限
        
        Args:
            user_email: 用户邮箱
            jql_query: JQL查询语句
            notification_config: 通知配置
            group_title: 群标题（如果是群聊通知）
            
        Returns:
            PermissionResult: 权限检查结果
        """
        try:
            # 1. 确定用户角色
            user_role = self._get_user_role(user_email)
            logger.info(f"用户 {user_email} 的角色: {user_role}")
            
            # 2. 解析JQL查询中涉及的项目和assignee
            query_analysis = self._analyze_jql_query(jql_query, user_email)
            logger.info(f"JQL查询分析结果: {query_analysis}")
            
            # 3. 解析通知目标
            notification_analysis = self._analyze_notification_target(notification_config, group_title)
            logger.info(f"通知目标分析: {notification_analysis}")
            
            # 4. 根据用户角色进行权限检查
            if user_role == 'super_admin':
                return self._check_super_admin_permission(query_analysis, notification_analysis)
            elif user_role == 'project_manager':
                return self._check_project_manager_permission(
                    user_email, query_analysis, notification_analysis
                )
            else:  # regular_user
                return self._check_regular_user_permission(
                    user_email, query_analysis, notification_analysis, group_title
                )
                
        except Exception as e:
            logger.error(f"权限检查异常: {str(e)}")
            return PermissionResult(
                allowed=False,
                reason=f"权限检查异常: {str(e)}",
                user_role=user_role
            )
    
    def _get_user_role(self, user_email: str) -> str:
        """获取用户角色"""
        if user_email in self.SUPER_ADMINS:
            return 'super_admin'
        elif user_email in self.PROJECT_MANAGERS:
            return 'project_manager'
        else:
            return 'regular_user'
    
    def _analyze_jql_query(self, jql_query: str, user_email: str) -> Dict:
        """分析JQL查询语句"""
        analysis = {
            'projects': set(),
            'assignees': set(),
            'is_self_only': False,
            'has_project_restriction': False,
            'scope': 'unknown'
        }
        
        # 处理空查询（用于提醒任务）
        if not jql_query:
            analysis['scope'] = 'reminder_task'
            analysis['is_self_only'] = True
            return analysis
        
        # 提取项目
        project_matches = re.findall(r'project\s*(?:=|IN)\s*["\']?([A-Z]+)["\']?', jql_query, re.IGNORECASE)
        if project_matches:
            analysis['projects'] = set(project_matches)
            analysis['has_project_restriction'] = True
            analysis['scope'] = 'project_specific'
        
        # 提取assignee
        assignee_matches = re.findall(r'assignee\s*(?:=|IN)\s*["\']?([^"\']+)["\']?', jql_query, re.IGNORECASE)
        if assignee_matches:
            analysis['assignees'] = set(assignee_matches)
            # 检查是否只查询自己的任务
            if len(assignee_matches) == 1 and assignee_matches[0] == user_email:
                analysis['is_self_only'] = True
                analysis['scope'] = 'self_only'
        
        # 如果没有项目限制且不是只查询自己，则可能影响全局
        if not analysis['has_project_restriction'] and not analysis['is_self_only']:
            analysis['scope'] = 'global'
        
        return analysis
    
    def _analyze_notification_target(self, notification_config: Dict, group_title: str = None) -> Dict:
        """分析通知目标"""
        analysis = {
            'type': 'unknown',
            'scope': 'unknown',
            'target_projects': set(),
            'is_self_only': False
        }
        
        # 解析通知类型
        target_type = notification_config.get('target_type', notification_config.get('target', 'private'))
        
        if target_type == 'private':
            analysis['type'] = 'private'
            analysis['is_self_only'] = True
            analysis['scope'] = 'self_only'
        elif target_type == 'group':
            analysis['type'] = 'group'
            if group_title:
                # 从群标题中提取JIRA单号，推断项目
                projects = self._extract_projects_from_group_title(group_title)
                if projects:
                    analysis['target_projects'] = projects
                    analysis['scope'] = 'project_specific'
                else:
                    analysis['scope'] = 'unknown_group'
            else:
                analysis['scope'] = 'unknown_group'
        elif target_type in ['smart', 'auto', 'both']:
            analysis['type'] = 'smart'
            analysis['scope'] = 'assignee_based'
        
        return analysis
    
    def _extract_projects_from_group_title(self, group_title: str) -> set:
        """从群标题中提取项目代码"""
        projects = set()
        
        # 查找群标题中的JIRA单号格式 (如 SPCB-123, SPCT-456)
        jira_matches = re.findall(r'\b([A-Z]{4})-\d+', group_title, re.IGNORECASE)
        for match in jira_matches:
            project_code = match.upper()
            if project_code in self.SUPPORTED_PROJECTS:
                projects.add(project_code)
        
        return projects
    
    def _check_super_admin_permission(self, query_analysis: Dict, notification_analysis: Dict) -> PermissionResult:
        """检查超级管理员权限"""
        return PermissionResult(
            allowed=True,
            reason="超级管理员拥有所有权限",
            user_role='super_admin'
        )
    
    def _check_project_manager_permission(self, user_email: str, query_analysis: Dict, 
                                        notification_analysis: Dict) -> PermissionResult:
        """检查项目管理员权限"""
        managed_projects = set(self.PROJECT_MANAGERS[user_email])
        
        # 检查查询范围
        if query_analysis['scope'] == 'global':
            return PermissionResult(
                allowed=False,
                reason="项目管理员不能创建全局范围的任务",
                suggested_scope=f"请将查询限制在您管理的项目范围内: {', '.join(managed_projects)}",
                user_role='project_manager'
            )
        
        if query_analysis['scope'] == 'project_specific':
            query_projects = query_analysis['projects']
            unauthorized_projects = query_projects - managed_projects
            
            if unauthorized_projects:
                return PermissionResult(
                    allowed=False,
                    reason=f"您没有权限操作项目: {', '.join(unauthorized_projects)}",
                    suggested_scope=f"您只能在以下项目中创建任务: {', '.join(managed_projects)}",
                    user_role='project_manager'
                )
        
        # 检查通知目标
        if notification_analysis['type'] == 'group':
            if notification_analysis['scope'] == 'project_specific':
                target_projects = notification_analysis['target_projects']
                unauthorized_targets = target_projects - managed_projects
                
                if unauthorized_targets:
                    return PermissionResult(
                        allowed=False,
                        reason=f"您没有权限向以下项目的群发送通知: {', '.join(unauthorized_targets)}",
                        suggested_scope=f"您只能向以下项目的群发送通知: {', '.join(managed_projects)}",
                        user_role='project_manager'
                    )
            elif notification_analysis['scope'] == 'unknown_group':
                return PermissionResult(
                    allowed=False,
                    reason="无法确定群所属项目，项目管理员只能向明确的项目群发送通知",
                    suggested_scope="请确保群标题包含明确的JIRA项目单号",
                    user_role='project_manager'
                )
        
        return PermissionResult(
            allowed=True,
            reason=f"项目管理员在授权范围内操作: {', '.join(managed_projects)}",
            user_role='project_manager'
        )
    
    def _check_regular_user_permission(self, user_email: str, query_analysis: Dict, 
                                     notification_analysis: Dict, group_title: str = None) -> PermissionResult:
        """检查普通用户权限"""
        
        # 普通用户只能创建以下类型的任务：
        # 1. 发送给自己的私聊通知
        # 2. 发送到指定群的通知（群标题包含JIRA单号）
        # 3. 提醒任务（纯文本提醒）
        
        # 特殊处理：提醒任务
        if query_analysis.get('scope') == 'reminder_task':
            if notification_analysis['type'] == 'private' and notification_analysis['is_self_only']:
                return PermissionResult(
                    allowed=True,
                    reason="普通用户可以创建个人提醒任务",
                    user_role='regular_user'
                )
            elif notification_analysis['type'] == 'group':
                # 提醒任务发送到群也应该被允许，因为这是个人提醒
                return PermissionResult(
                    allowed=True,
                    reason="普通用户可以创建发送到群的提醒任务",
                    user_role='regular_user'
                )
        
        if notification_analysis['type'] == 'private' and notification_analysis['is_self_only']:
            # 私聊通知 - 允许
            return PermissionResult(
                allowed=True,
                reason="普通用户可以创建发送给自己的私聊通知",
                user_role='regular_user'
            )
        
        elif notification_analysis['type'] == 'group':
            if notification_analysis['scope'] == 'project_specific':
                # 群聊通知且能确定项目 - 允许
                projects = ', '.join(notification_analysis['target_projects'])
                return PermissionResult(
                    allowed=True,
                    reason=f"普通用户可以创建发送到指定项目群的通知: {projects}",
                    user_role='regular_user'
                )
            else:
                # 群聊通知但无法确定项目 - 拒绝
                return PermissionResult(
                    allowed=False,
                    reason="无法确定群所属项目",
                    suggested_scope="请确保群标题包含明确的JIRA项目单号（如SPCB-123）",
                    user_role='regular_user'
                )
        
        elif notification_analysis['type'] == 'smart':
            # 智能通知 - 需要进一步检查范围
            if query_analysis['is_self_only']:
                return PermissionResult(
                    allowed=True,
                    reason="普通用户可以创建只查询自己任务的智能通知",
                    user_role='regular_user'
                )
            else:
                return PermissionResult(
                    allowed=False,
                    reason="普通用户的智能通知只能查询分配给自己的任务",
                    suggested_scope="请在查询中添加 'assignee = currentUser()' 限制",
                    user_role='regular_user'
                )
        
        else:
            return PermissionResult(
                allowed=False,
                reason="普通用户只能创建发送给自己或指定项目群的通知",
                suggested_scope="请选择私聊通知或指定具体的项目群",
                user_role='regular_user'
            )
    
    def get_user_permission_info(self, user_email: str) -> Dict:
        """获取用户权限信息"""
        user_role = self._get_user_role(user_email)
        
        info = {
            'user_email': user_email,
            'role': user_role,
            'permissions': []
        }
        
        if user_role == 'super_admin':
            info['permissions'] = [
                '✅ 创建任意范围的定时任务',
                '✅ 发送到任意群或个人',
                '✅ 查询任意项目数据',
                '✅ 管理所有用户的任务'
            ]
        elif user_role == 'project_manager':
            managed_projects = self.PROJECT_MANAGERS[user_email]
            info['managed_projects'] = managed_projects
            info['permissions'] = [
                f'✅ 在管理的项目中创建任务: {", ".join(managed_projects)}',
                f'✅ 发送到管理项目的相关群',
                f'✅ 查询管理项目的数据',
                '❌ 不能创建全局范围的任务'
            ]
        else:
            info['permissions'] = [
                '✅ 创建发送给自己的私聊通知',
                '✅ 创建发送到指定项目群的通知',
                '✅ 查询分配给自己的任务',
                '❌ 不能创建影响他人的通知',
                '❌ 不能查询他人的任务'
            ]
        
        return info
    
    def add_project_manager(self, user_email: str, projects: List[str]) -> bool:
        """添加项目管理员"""
        try:
            if user_email not in self.PROJECT_MANAGERS:
                self.PROJECT_MANAGERS[user_email] = []
            
            for project in projects:
                if project.upper() in self.SUPPORTED_PROJECTS:
                    if project.upper() not in self.PROJECT_MANAGERS[user_email]:
                        self.PROJECT_MANAGERS[user_email].append(project.upper())
            
            logger.info(f"已添加项目管理员: {user_email} -> {self.PROJECT_MANAGERS[user_email]}")
            return True
        except Exception as e:
            logger.error(f"添加项目管理员失败: {str(e)}")
            return False
    
    def remove_project_manager(self, user_email: str) -> bool:
        """移除项目管理员"""
        try:
            if user_email in self.PROJECT_MANAGERS:
                del self.PROJECT_MANAGERS[user_email]
                logger.info(f"已移除项目管理员: {user_email}")
                return True
            return False
        except Exception as e:
            logger.error(f"移除项目管理员失败: {str(e)}")
            return False
    
    def check_task_management_permission(self, user_email: str, operation: str, 
                                        task_info: Dict = None, target_user_email: str = None) -> PermissionResult:
        """
        检查任务管理操作权限
        
        Args:
            user_email: 操作者邮箱
            operation: 操作类型 (list, pause, resume, delete, stats)
            task_info: 任务信息 (包含creator_email, jql_query等)
            target_user_email: 目标用户邮箱 (用于查看他人任务)
            
        Returns:
            权限检查结果
        """
        user_role = self._get_user_role(user_email)
        
        # 超级管理员拥有所有权限
        if user_role == 'super_admin':
            return PermissionResult(
                allowed=True,
                reason="超级管理员拥有所有任务管理权限",
                user_role=user_role
            )
        
        # 如果是查看或操作自己的任务，普通用户和PJM都允许
        if task_info and task_info.get('creator_email') == user_email:
            return PermissionResult(
                allowed=True,
                reason="可以管理自己创建的任务",
                user_role=user_role
            )
        
        if target_user_email == user_email:
            return PermissionResult(
                allowed=True,
                reason="可以查看自己的任务",
                user_role=user_role
            )
        
        # 项目管理员的特殊权限
        if user_role == 'project_manager':
            managed_projects = self.PROJECT_MANAGERS.get(user_email, [])
            
            # 检查是否在管理的项目范围内
            if task_info:
                task_jql = task_info.get('jql_query', '')
                task_analysis = self._analyze_jql_query(task_jql, user_email)
                task_projects = task_analysis.get('projects', set())
                
                # 如果任务涉及的项目在PJM管理范围内
                if task_projects and task_projects.issubset(set(managed_projects)):
                    return PermissionResult(
                        allowed=True,
                        reason=f"项目管理员可以管理 {', '.join(task_projects)} 项目内的任务",
                        user_role=user_role
                    )
                
                # 如果任务没有明确的项目限制，但是查询范围合理
                if not task_projects and task_analysis.get('scope') != 'global':
                    return PermissionResult(
                        allowed=True,
                        reason="项目管理员可以管理非全局范围的任务",
                        user_role=user_role
                    )
            
            # 对于list操作，允许PJM查看项目范围内的任务
            if operation == 'list':
                return PermissionResult(
                    allowed=True,
                    reason=f"项目管理员可以查看 {', '.join(managed_projects)} 项目内的任务",
                    user_role=user_role
                )
        
        # 普通用户只能操作自己的任务
        if user_role == 'regular_user':
            if operation == 'list' and not target_user_email:
                return PermissionResult(
                    allowed=True,
                    reason="普通用户可以查看自己的任务列表",
                    user_role=user_role
                )
            
            return PermissionResult(
                allowed=False,
                reason="普通用户只能管理自己创建的任务",
                suggested_scope="请使用您自己创建的任务ID",
                user_role=user_role
            )
        
        # 默认拒绝
        return PermissionResult(
            allowed=False,
            reason=f"您没有权限执行此操作: {operation}",
            user_role=user_role
        )
    
    def get_user_manageable_tasks_filter(self, user_email: str) -> Dict:
        """
        获取用户可管理的任务过滤条件
        
        Args:
            user_email: 用户邮箱
            
        Returns:
            数据库查询过滤条件
        """
        user_role = self._get_user_role(user_email)
        
        if user_role == 'super_admin':
            # 超级管理员可以看到所有任务
            return {}
        
        elif user_role == 'project_manager':
            # PJM可以看到自己的任务 + 管理项目内的任务
            managed_projects = self.PROJECT_MANAGERS.get(user_email, [])
            
            # 构建项目相关的JQL过滤条件
            project_filters = []
            for project in managed_projects:
                project_filters.append(f'project = {project}')
            
            return {
                'include_own': True,
                'user_email': user_email,
                'project_filters': project_filters,
                'managed_projects': managed_projects
            }
        
        else:
            # 普通用户只能看到自己的任务
            return {
                'include_own': True,
                'user_email': user_email,
                'project_filters': [],
                'managed_projects': []
            }
    
    def analyze_task_scope(self, task_info: Dict) -> Dict:
        """
        分析任务的范围和影响
        
        Args:
            task_info: 任务信息
            
        Returns:
            任务范围分析结果
        """
        jql_query = task_info.get('jql_query', '')
        creator_email = task_info.get('creator_email', '')
        notification_config = task_info.get('notification_config', {})
        
        # 分析JQL查询范围
        jql_analysis = self._analyze_jql_query(jql_query, creator_email)
        
        # 分析通知目标
        notification_analysis = self._analyze_notification_target(
            notification_config, 
            task_info.get('group_title')
        )
        
        # 确定任务的整体影响范围
        if jql_analysis['scope'] == 'global' or notification_analysis['scope'] == 'global':
            overall_scope = 'global'
        elif jql_analysis['scope'] == 'project_specific' or notification_analysis['scope'] == 'project_specific':
            overall_scope = 'project_specific'
        elif jql_analysis['scope'] == 'self_only' and notification_analysis['scope'] == 'self_only':
            overall_scope = 'self_only'
        else:
            overall_scope = 'mixed'
        
        return {
            'overall_scope': overall_scope,
            'jql_analysis': jql_analysis,
            'notification_analysis': notification_analysis,
            'affected_projects': jql_analysis['projects'] | notification_analysis['target_projects'],
            'is_self_contained': overall_scope == 'self_only',
            'requires_admin': overall_scope == 'global'
        }

# 全局实例
task_permission_manager = TaskPermissionManager() 
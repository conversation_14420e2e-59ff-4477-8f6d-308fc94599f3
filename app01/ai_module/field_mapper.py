"""
JIRA 自定义字段映射管理器
提供自动获取、缓存和每日更新 JIRA 字段映射关系的功能
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.core.cache import cache
from jira import JIRA, JIRAError

from app01.models import AIConfig
from app01.config import JIRA_TOKEN

logger = logging.getLogger(__name__)


class JiraFieldMapper:
    """JIRA 字段映射管理器"""
    
    # 缓存键名
    CACHE_KEY_ALL_FIELDS = "jira_all_fields_mapping"
    CACHE_KEY_CUSTOM_FIELDS = "jira_custom_fields_mapping"
    CACHE_KEY_LAST_UPDATE = "jira_fields_last_update"
    
    # 缓存过期时间（24小时）
    CACHE_TIMEOUT = 24 * 60 * 60
    
    # 已知的重要字段分类
    FIELD_CATEGORIES = {
        'basic': ['key', 'summary', 'description', 'status', 'priority', 'assignee', 'reporter', 'created', 'updated'],
        'dates': ['duedate', 'resolutiondate'],
        'project': ['project', 'issuetype', 'components', 'fixVersions', 'labels'],
        'workflow': ['resolution', 'votes', 'watches'],
        'comments': ['comment'],
        'attachments': ['attachment'],
        'links': ['issuelinks'],
        'subtasks': ['sub-task'],
        'parent': ['parent']
    }
    
    # Shopee 特有的重要自定义字段（基于现有代码分析）
    SHOPEE_IMPORTANT_FIELDS = {
        # 发布流程检查清单
        'customfield_34000': {'name': 'DB Changed', 'category': 'release_checklist', 'type': 'select'},
        'customfield_34001': {'name': 'Config Changed', 'category': 'release_checklist', 'type': 'select'},
        'customfield_34002': {'name': 'Code Merged', 'category': 'release_checklist', 'type': 'select'},
        'customfield_34003': {'name': 'Signed off', 'category': 'release_checklist', 'type': 'select'},
        
        # 人员相关字段
        'customfield_10306': {'name': 'Product Manager', 'category': 'people', 'type': 'user'},
        'customfield_10307': {'name': 'Developer', 'category': 'people', 'type': 'user'},
        'customfield_10308': {'name': 'QA', 'category': 'people', 'type': 'user'},
        'customfield_37800': {'name': 'BE List', 'category': 'people', 'type': 'multiuser'},
        'customfield_37801': {'name': 'FE List', 'category': 'people', 'type': 'multiuser'},
        'customfield_12202': {'name': 'QA List', 'category': 'people', 'type': 'multiuser'},
        'customfield_10600': {'name': 'Project Manager', 'category': 'people', 'type': 'user'},
        
        # 时间相关字段
        'customfield_11545': {'name': 'PRD Review Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11546': {'name': 'PRD Review End Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11520': {'name': 'Planned Dev Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11509': {'name': 'Planned Dev Due Date', 'category': 'timeline', 'type': 'date'},
        'customfield_12634': {'name': 'Planned Integration Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_12635': {'name': 'Planned Integration End Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11521': {'name': 'Planned QA Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11510': {'name': 'Planned QA Due Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11513': {'name': 'Planned Release Date', 'category': 'timeline', 'type': 'date'},
        
        # 实际日期字段（从提供的列表中添加）
        'customfield_12208': {'name': 'Tech Design Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_12209': {'name': 'Tech Design End Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11516': {'name': 'Dev Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_10304': {'name': 'Dev Due Date', 'category': 'timeline', 'type': 'date'},
        'customfield_12636': {'name': 'Integration Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_12637': {'name': 'Integration End Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11517': {'name': 'QA Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_10305': {'name': 'QA Due Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11519': {'name': 'UAT Start Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11512': {'name': 'UAT Due Date', 'category': 'timeline', 'type': 'date'},
        'customfield_11514': {'name': 'Release Date', 'category': 'timeline', 'type': 'date'},
        
        # 子任务专用字段
        'customfield_16300': {'name': 'Planned Start Date', 'category': 'subtask', 'type': 'date'},
        'customfield_16301': {'name': 'Planned Due Date', 'category': 'subtask', 'type': 'date'},
        'customfield_10100': {'name': 'Story Points', 'category': 'effort', 'type': 'number'},
        
        # 工作量估算字段
        'customfield_10004': {'name': 'Story Point Estimate', 'category': 'estimation', 'type': 'number'},
        
        # 项目相关字段
        'customfield_16700': {'name': 'TRD/PRD URL', 'category': 'project', 'type': 'url'},
        'customfield_23400': {'name': 'Cross Team with', 'category': 'project', 'type': 'multiselect'},
        'customfield_11549': {'name': 'Product Line', 'category': 'project', 'type': 'select'},
        'customfield_11201': {'name': 'Shopee Region', 'category': 'project', 'type': 'multiselect'},
        
        # 其他业务字段
        'customfield_31103': {'name': 'Redis Check', 'category': 'technical', 'type': 'select'},
        'customfield_10205': {'name': 'Server Environment', 'category': 'technical', 'type': 'select'},
    }
    
    def __init__(self):
        self.jira = None
    
    def _get_jira_connection(self) -> JIRA:
        """获取JIRA连接"""
        if not self.jira:
            try:
                self.jira = JIRA(server='https://jira.shopee.io', token_auth=JIRA_TOKEN)
            except Exception as e:
                logger.error(f"JIRA连接失败: {str(e)}")
                raise
        return self.jira
    
    async def get_all_fields_mapping(self, force_refresh: bool = False) -> Dict:
        """
        获取所有字段映射关系
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            字段映射字典
        """
        # 检查缓存
        if not force_refresh:
            cached_data = cache.get(self.CACHE_KEY_ALL_FIELDS)
            if cached_data:
                logger.info("从缓存获取字段映射")
                return cached_data
        
        # 从JIRA获取最新字段信息
        try:
            logger.info("开始从JIRA获取字段映射...")
            
            def _sync_get_fields():
                jira = self._get_jira_connection()
                return jira.fields()
            
            # 在线程池中执行同步操作
            loop = asyncio.get_event_loop()
            all_fields = await loop.run_in_executor(None, _sync_get_fields)
            
            # 构建字段映射
            field_mapping = self._build_field_mapping(all_fields)
            
            # 缓存结果
            cache.set(self.CACHE_KEY_ALL_FIELDS, field_mapping, self.CACHE_TIMEOUT)
            cache.set(self.CACHE_KEY_LAST_UPDATE, timezone.now().isoformat(), self.CACHE_TIMEOUT)
            
            # 保存到数据库配置
            await self._save_to_database(field_mapping)
            
            logger.info(f"成功获取并缓存了 {len(field_mapping['all_fields'])} 个字段映射")
            return field_mapping
            
        except Exception as e:
            logger.error(f"获取字段映射失败: {str(e)}")
            # 尝试从数据库获取备份
            return await self._get_from_database()
    
    def _build_field_mapping(self, all_fields: List[Dict]) -> Dict:
        """构建字段映射结构"""
        mapping = {
            'all_fields': {},
            'custom_fields': {},
            'system_fields': {},
            'by_category': {
                'basic': {},
                'people': {},
                'timeline': {},
                'project': {},
                'technical': {},
                'release_checklist': {},
                'unknown': {}
            },
            'by_type': {},
            'name_to_id': {},
            'id_to_name': {},
            'last_updated': timezone.now().isoformat(),
            'total_count': len(all_fields)
        }
        
        for field in all_fields:
            field_id = field['id']
            field_name = field['name']
            field_schema = field.get('schema', {})
            field_type = field_schema.get('type', 'unknown')
            is_custom = field_id.startswith('customfield_')
            
            # 基本信息
            field_info = {
                'id': field_id,
                'name': field_name,
                'type': field_type,
                'custom': is_custom,
                'schema': field_schema,
                'searchable': field.get('searchable', False),
                'navigable': field.get('navigable', False),
                'orderable': field.get('orderable', False)
            }
            
            # 添加Shopee特有字段的额外信息
            if field_id in self.SHOPEE_IMPORTANT_FIELDS:
                shopee_info = self.SHOPEE_IMPORTANT_FIELDS[field_id]
                field_info.update({
                    'shopee_category': shopee_info['category'],
                    'shopee_type': shopee_info['type'],
                    'important': True
                })
                category = shopee_info['category']
            else:
                field_info['important'] = False
                category = self._categorize_field(field_name, field_type, is_custom)
            
            # 存储到各个映射中
            mapping['all_fields'][field_id] = field_info
            mapping['name_to_id'][field_name] = field_id
            mapping['id_to_name'][field_id] = field_name
            
            if is_custom:
                mapping['custom_fields'][field_id] = field_info
            else:
                mapping['system_fields'][field_id] = field_info
            
            # 按类别分类
            if category not in mapping['by_category']:
                mapping['by_category'][category] = {}
            mapping['by_category'][category][field_id] = field_info
            
            # 按类型分类
            if field_type not in mapping['by_type']:
                mapping['by_type'][field_type] = {}
            mapping['by_type'][field_type][field_id] = field_info
        
        return mapping
    
    def _categorize_field(self, field_name: str, field_type: str, is_custom: bool) -> str:
        """对字段进行分类"""
        field_name_lower = field_name.lower()
        
        # 基础字段
        for category, fields in self.FIELD_CATEGORIES.items():
            if any(f in field_name_lower for f in fields):
                return category
        
        # 根据名称特征分类
        if any(keyword in field_name_lower for keyword in ['date', 'time', 'due', 'start', 'end']):
            return 'timeline'
        elif any(keyword in field_name_lower for keyword in ['assignee', 'reporter', 'manager', 'developer', 'qa', 'pm']):
            return 'people'
        elif any(keyword in field_name_lower for keyword in ['url', 'link', 'project', 'version', 'component']):
            return 'project'
        elif any(keyword in field_name_lower for keyword in ['environment', 'server', 'config', 'technical']):
            return 'technical'
        elif any(keyword in field_name_lower for keyword in ['checklist', 'check', 'confirm', 'sign']):
            return 'release_checklist'
        
        return 'unknown'
    
    async def get_fields_by_category(self, category: str) -> Dict:
        """根据类别获取字段"""
        mapping = await self.get_all_fields_mapping()
        return mapping['by_category'].get(category, {})
    
    async def get_fields_by_intent(self, intent: str) -> List[str]:
        """根据AI意图获取推荐字段"""
        mapping = await self.get_all_fields_mapping()
        
        intent_field_mapping = {
            'query_issues': ['key', 'summary', 'status', 'priority', 'assignee', 'created', 'updated'],
            'query_epic': ['key', 'summary', 'status', 'assignee', 'created', 'components'],
            'query_timeline': ['key', 'summary', 'status'] + list(mapping['by_category']['timeline'].keys()),
            'query_people': ['key', 'summary', 'assignee', 'reporter'] + list(mapping['by_category']['people'].keys()),
            'query_status': ['key', 'summary', 'status', 'assignee', 'updated'],
            'query_priority': ['key', 'summary', 'priority', 'status', 'assignee'],
            'manage_checklist': ['key', 'summary'] + list(mapping['by_category']['release_checklist'].keys()),
            'query_bugs': ['key', 'summary', 'status', 'priority', 'assignee', 'created', 'resolution'],
            'update_issue': ['key', 'summary', 'status', 'assignee'],
            'create_issue': ['key', 'summary', 'project', 'issuetype']
        }
        
        return intent_field_mapping.get(intent, ['key', 'summary', 'status', 'assignee'])
    
    async def search_fields(self, keyword: str) -> List[Dict]:
        """搜索字段"""
        mapping = await self.get_all_fields_mapping()
        keyword_lower = keyword.lower()
        
        results = []
        for field_id, field_info in mapping['all_fields'].items():
            if (keyword_lower in field_info['name'].lower() or 
                keyword_lower in field_id.lower()):
                results.append(field_info)
        
        # 按重要性和匹配度排序
        results.sort(key=lambda x: (not x.get('important', False), x['name']))
        return results
    
    async def get_field_info(self, field_identifier: str) -> Optional[Dict]:
        """获取字段详细信息（支持ID或名称）"""
        mapping = await self.get_all_fields_mapping()
        
        # 尝试作为ID查找
        if field_identifier in mapping['all_fields']:
            return mapping['all_fields'][field_identifier]
        
        # 尝试作为名称查找
        if field_identifier in mapping['name_to_id']:
            field_id = mapping['name_to_id'][field_identifier]
            return mapping['all_fields'][field_id]
        
        return None
    
    async def _save_to_database(self, field_mapping: Dict) -> None:
        """保存字段映射到数据库"""
        try:
            # 保存完整映射
            config, created = AIConfig.objects.get_or_create(
                key='jira_field_mapping',
                defaults={'value': json.dumps(field_mapping)}
            )
            if not created:
                config.value = json.dumps(field_mapping)
                config.save()
            
            # 保存重要字段的快速访问映射
            important_fields = {
                field_id: field_info 
                for field_id, field_info in field_mapping['all_fields'].items()
                if field_info.get('important', False)
            }
            
            config, created = AIConfig.objects.get_or_create(
                key='jira_important_fields',
                defaults={'value': json.dumps(important_fields)}
            )
            if not created:
                config.value = json.dumps(important_fields)
                config.save()
                
            logger.info("字段映射已保存到数据库")
            
        except Exception as e:
            logger.error(f"保存字段映射到数据库失败: {str(e)}")
    
    async def _get_from_database(self) -> Dict:
        """从数据库获取字段映射"""
        try:
            config = AIConfig.objects.get(key='jira_field_mapping')
            return json.loads(config.value)
        except AIConfig.DoesNotExist:
            logger.warning("数据库中没有字段映射备份")
            return self._get_fallback_mapping()
        except Exception as e:
            logger.error(f"从数据库获取字段映射失败: {str(e)}")
            return self._get_fallback_mapping()
    
    def _get_fallback_mapping(self) -> Dict:
        """获取备用字段映射（基于已知的重要字段）"""
        return {
            'all_fields': self.SHOPEE_IMPORTANT_FIELDS,
            'custom_fields': self.SHOPEE_IMPORTANT_FIELDS,
            'system_fields': {},
            'by_category': {
                'release_checklist': {k: v for k, v in self.SHOPEE_IMPORTANT_FIELDS.items() if v['category'] == 'release_checklist'},
                'people': {k: v for k, v in self.SHOPEE_IMPORTANT_FIELDS.items() if v['category'] == 'people'},
                'timeline': {k: v for k, v in self.SHOPEE_IMPORTANT_FIELDS.items() if v['category'] == 'timeline'},
                'project': {k: v for k, v in self.SHOPEE_IMPORTANT_FIELDS.items() if v['category'] == 'project'},
                'technical': {k: v for k, v in self.SHOPEE_IMPORTANT_FIELDS.items() if v['category'] == 'technical'},
            },
            'name_to_id': {v['name']: k for k, v in self.SHOPEE_IMPORTANT_FIELDS.items()},
            'id_to_name': {k: v['name'] for k, v in self.SHOPEE_IMPORTANT_FIELDS.items()},
            'last_updated': 'fallback',
            'total_count': len(self.SHOPEE_IMPORTANT_FIELDS)
        }
    
    async def get_update_status(self) -> Dict:
        """获取更新状态"""
        last_update = cache.get(self.CACHE_KEY_LAST_UPDATE)
        if last_update:
            last_update_time = datetime.fromisoformat(last_update)
            hours_since_update = (timezone.now() - last_update_time).total_seconds() / 3600
            needs_update = hours_since_update >= 24
        else:
            last_update_time = None
            needs_update = True
        
        mapping = await self.get_all_fields_mapping()
        
        return {
            'last_update': last_update_time.isoformat() if last_update_time else None,
            'hours_since_update': hours_since_update if last_update_time else None,
            'needs_update': needs_update,
            'total_fields': mapping.get('total_count', 0),
            'custom_fields_count': len(mapping.get('custom_fields', {})),
            'important_fields_count': len([f for f in mapping.get('all_fields', {}).values() if f.get('important', False)])
        }
    
    async def force_update(self) -> Dict:
        """强制更新字段映射"""
        try:
            logger.info("开始强制更新字段映射...")
            mapping = await self.get_all_fields_mapping(force_refresh=True)
            
            return {
                'success': True,
                'message': f'字段映射更新成功，共获取 {mapping["total_count"]} 个字段',
                'total_fields': mapping['total_count'],
                'custom_fields': len(mapping['custom_fields']),
                'last_updated': mapping['last_updated']
            }
        except Exception as e:
            logger.error(f"强制更新字段映射失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新失败: {str(e)}'
            }


# 全局实例
field_mapper = JiraFieldMapper() 
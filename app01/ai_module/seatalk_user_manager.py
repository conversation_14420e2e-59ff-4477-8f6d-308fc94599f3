#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import logging
from typing import Dict, Optional
from app01.seatalk_group_manager import get_employee_codes

logger = logging.getLogger(__name__)

class SeatalkUserInfoManager:
    """
    SeaTalk用户信息管理器
    
    智能获取用户邮箱，支持：
    1. 群聊消息：直接从sender获取真实邮箱
    2. 私聊消息：通过employee_code映射获取真实邮箱
    3. 缓存机制：避免重复API调用
    """
    
    def __init__(self):
        # 核心用户映射（重要用户的快速映射）
        self.core_mappings = {
            '23774': '<EMAIL>',      # 超级管理员
            '348162': '<EMAIL>',     # SPCB项目管理员
            '122033': '<EMAIL>',         # SPCT项目管理员
        }
        
        # 动态缓存
        self.email_cache = {}
        self.cache_ttl = 3600  # 缓存1小时
        
        logger.info(f"SeatalkUserInfoManager初始化完成，核心用户映射: {len(self.core_mappings)} 个")
    
    async def get_user_email(self, data: dict, employee_code: str = None) -> Optional[str]:
        """
        智能获取用户邮箱
        
        Args:
            data: SeaTalk消息回调数据
            employee_code: 用户employee_code（私聊时提供）
            
        Returns:
            用户真实邮箱，如果无法获取则返回None
        """
        try:
            # 1. 群聊：直接从sender获取真实邮箱
            if data.get("event_type") != "message_from_bot_subscriber":
                sender_info = data.get("event", {}).get("message", {}).get("sender", {})
                email = sender_info.get("email")
                if email and self._is_valid_email(email):
                    logger.info(f"✅ 从群聊sender获取到真实邮箱: {email}")
                    return email
            
            # 2. 私聊：优先从event.email获取，再通过employee_code映射
            else:
                # 2.1 首先尝试从event.email直接获取（私聊消息中也包含真实邮箱）
                event_email = data.get("event", {}).get("email")
                if event_email and self._is_valid_email(event_email):
                    logger.info(f"✅ 从私聊event.email获取到真实邮箱: {event_email}")
                    return event_email
                
                # 2.2 如果没有event.email，通过employee_code映射获取
                if employee_code:
                    email = await self._get_email_by_employee_code(employee_code)
                    if email:
                        logger.info(f"✅ 通过employee_code获取到邮箱: {employee_code} -> {email}")
                        return email
                    else:
                        logger.warning(f"⚠️ 无法获取employee_code对应的邮箱: {employee_code}")
            
            logger.warning("⚠️ 无法获取用户邮箱：既不是群聊也没有有效的employee_code")
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取用户邮箱异常: {str(e)}")
            return None
    
    async def _get_email_by_employee_code(self, employee_code: str) -> Optional[str]:
        """
        通过employee_code获取真实邮箱
        
        优先级：
        1. 核心用户映射（立即返回）
        2. 动态缓存（检查TTL）
        3. API反查（暂未实现，返回拼接邮箱）
        """
        
        # 1. 核心用户映射
        if employee_code in self.core_mappings:
            email = self.core_mappings[employee_code]
            logger.info(f"🎯 核心用户映射命中: {employee_code} -> {email}")
            return email
        
        # 2. 检查动态缓存
        if employee_code in self.email_cache:
            cache_data = self.email_cache[employee_code]
            if time.time() - cache_data['timestamp'] < self.cache_ttl:
                logger.info(f"💾 缓存命中: {employee_code} -> {cache_data['email']}")
                return cache_data['email']
            else:
                # 缓存过期，删除
                del self.email_cache[employee_code]
                logger.info(f"🗑️ 缓存过期，已删除: {employee_code}")
        
        # 3. API反查（当前暂未实现完整的反查API）
        # TODO: 实现通过employee_code反查邮箱的API调用
        # 目前先使用拼接方式作为fallback
        fallback_email = f"{employee_code}@shopee.com"
        logger.warning(f"⚠️ 使用fallback邮箱: {employee_code} -> {fallback_email}")
        
        # 将fallback结果缓存（短时间）
        self.email_cache[employee_code] = {
            'email': fallback_email,
            'timestamp': time.time(),
            'is_fallback': True
        }
        
        return fallback_email
    
    def _is_valid_email(self, email: str) -> bool:
        """验证邮箱格式是否有效"""
        if not email or '@' not in email:
            return False
        
        # 基本的邮箱格式检查
        parts = email.split('@')
        if len(parts) != 2:
            return False
        
        username, domain = parts
        if not username or not domain:
            return False
        
        return True
    
    def add_user_mapping(self, employee_code: str, email: str) -> bool:
        """
        添加用户映射到核心映射表
        
        Args:
            employee_code: 员工代码
            email: 真实邮箱
            
        Returns:
            是否添加成功
        """
        try:
            if not self._is_valid_email(email):
                logger.error(f"❌ 邮箱格式无效: {email}")
                return False
            
            self.core_mappings[employee_code] = email
            logger.info(f"✅ 添加用户映射: {employee_code} -> {email}")
            
            # 同时更新缓存
            self.email_cache[employee_code] = {
                'email': email,
                'timestamp': time.time(),
                'is_fallback': False
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加用户映射失败: {str(e)}")
            return False
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        total_cache = len(self.email_cache)
        fallback_count = sum(1 for cache_data in self.email_cache.values() 
                           if cache_data.get('is_fallback', False))
        
        return {
            'core_mappings': len(self.core_mappings),
            'total_cache': total_cache,
            'fallback_count': fallback_count,
            'valid_cache': total_cache - fallback_count,
            'cache_ttl': self.cache_ttl
        }
    
    def clear_cache(self) -> None:
        """清空动态缓存"""
        cache_count = len(self.email_cache)
        self.email_cache.clear()
        logger.info(f"🗑️ 已清空动态缓存: {cache_count} 个条目")

# 全局实例
seatalk_user_manager = SeatalkUserInfoManager() 
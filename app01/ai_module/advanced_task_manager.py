"""
高级定时任务功能管理器
包含群聊通知、条件触发、任务模板、批量管理、统计报表等高级功能
需要白名单权限才能使用
"""

import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Any
import json
import re
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Count, Avg, Max, Min

from app01.models import (
    UserScheduledTask, TaskExecutionLog, TaskTemplate, 
    AdvancedTaskFeatureWhitelist, ConditionalTrigger, TaskExecutionStatistics
)
from .task_scheduler import task_scheduler
from .private_chat import PrivateChat

logger = logging.getLogger(__name__)


class SmartMessageGenerator:
    """智能消息生成器"""
    
    # 消息模板 - 版本1：简洁现代风格
    MESSAGE_TEMPLATES = {
        'daily_reminder': {
            'title': '📅 {task_name}',
            'content': '📋 **待处理项目 ({count}个)**\n\n{ticket_list}',
            'footer': '💡 建议：合理安排工作优先级'
        },
        'bug_alert': {
            'title': '🐛 {task_name}', 
            'content': '📋 **待处理项目 ({count}个)**\n\n{ticket_list}',
            'footer': '💡 建议：{status_suggestion}'
        },
        'subtask_reminder': {
            'title': '📋 {task_name}',
            'content': '📋 **待处理项目 ({count}个)**\n\n{ticket_list}',
            'footer': '💡 建议：请合理安排工作时间'
        },
        'general': {
            'title': '🔔 {task_name}',
            'content': '📋 **待处理项目 ({count}个)**\n\n{ticket_list}',
            'footer': '💡 建议：点击链接查看详情'
        }
    }
    
    async def generate_notification_content(self, task_name: str, 
                                          task_description: str,
                                          jira_results: List[Dict],
                                          task_id: int = None) -> str:
        """根据任务信息和JIRA结果智能生成通知内容"""
        try:
            total_count = len(jira_results)
            if total_count == 0:
                return f"🔔 **{task_name}**\n\n✅ 暂无需要处理的项目"
            
            # 自动判断消息类型
            template_key = self._detect_message_type(task_name, task_description, jira_results)
            template = self.MESSAGE_TEMPLATES.get(template_key, self.MESSAGE_TEMPLATES['general'])
            
            # 生成ticket列表
            ticket_list = self._format_ticket_list(jira_results)
            
            # 生成智能建议
            status_suggestion = self._generate_status_suggestion(jira_results)
            
            # 格式化消息
            title = template['title'].format(task_name=task_name)
            
            # 添加Task ID信息
            task_info = ""
            if task_id:
                task_info = f"📝 **任务ID**: {task_id}\n\n"
            
            content = template['content'].format(
                count=total_count,
                ticket_list=ticket_list
            )
            footer = template['footer'].format(
                update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                status_suggestion=status_suggestion
            )
            
            return f"{title}\n{task_info}{content}\n\n{footer}"
            
        except Exception as e:
            logger.error(f"生成通知内容失败: {str(e)}")
            task_info = f"📝 **任务ID**: {task_id}\n\n" if task_id else ""
            return f"🔔 **{task_name}**\n{task_info}查询到 {len(jira_results)} 个相关项目"
    
    async def generate_merged_messages(self, grouped_tickets: Dict[str, List[Dict]], 
                                     task_name: str, task_description: str = "", task_id: int = None) -> Dict[str, str]:
        """为每个assignee生成合并后的消息"""
        merged_messages = {}
        
        for assignee_email, tickets in grouped_tickets.items():
            try:
                # 为每个assignee生成专门的消息，始终使用邮箱前缀而不是显示名称
                assignee_name = assignee_email.split('@')[0] if assignee_email else 'unknown'
                
                content = await self.generate_notification_content(
                    task_name=f"{task_name} - {assignee_name}",
                    task_description=task_description,
                    jira_results=tickets,
                    task_id=task_id
                )
                
                merged_messages[assignee_email] = content
                
            except Exception as e:
                logger.error(f"为 {assignee_email} 生成消息失败: {str(e)}")
                # 生成备用简单消息
                task_info = f"📝 **任务ID**: {task_id}\n\n" if task_id else ""
                merged_messages[assignee_email] = f"🔔 **{task_name}**\n{task_info}您有 {len(tickets)} 个相关项目需要处理"
        
        return merged_messages
    
    def _detect_message_type(self, task_name: str, task_description: str, jira_results: List[Dict]) -> str:
        """自动检测消息类型"""
        content = f"{task_name} {task_description}".lower()
        
        # 检查是否包含bug相关关键词
        if any(keyword in content for keyword in ['bug', '问题', '错误', 'issue']):
            return 'bug_alert'
        
        # 检查是否包含子任务相关关键词  
        if any(keyword in content for keyword in ['subtask', '子任务', 'sub-task']):
            return 'subtask_reminder'
        
        # 检查是否包含每日提醒关键词
        if any(keyword in content for keyword in ['daily', '每日', '日常', '提醒']):
            return 'daily_reminder'
        
        return 'general'
    
    def _format_ticket_list(self, tickets: List[Dict]) -> str:
        """格式化ticket列表 - 版本1：简洁现代风格"""
        formatted_list = []
        
        for i, ticket in enumerate(tickets[:10]):  # 最多显示10个
            try:
                key = ticket.get('key', 'Unknown')
                summary = ticket.get('summary', 'No Summary')
                status = ticket.get('status', 'Unknown')
                priority = ticket.get('priority', '')
                
                # 不截断标题，显示完整内容
                # 如果标题过长，使用换行来保持美观
                if len(summary) > 80:
                    # 在合适的位置断行
                    summary = self._smart_line_break(summary, 80)
                
                # 格式化为版本1的样式
                formatted_item = f"**{key}** | {status}"
                if priority:
                    formatted_item += f" | {priority}"
                formatted_item += f"\n{summary}\n🔗 [查看详情](https://jira.shopee.io/browse/{key})"
                
                formatted_list.append(formatted_item)
                
            except Exception as e:
                logger.warning(f"格式化ticket失败: {str(e)}")
                formatted_list.append(f"**{ticket.get('key', 'Unknown')}** | 格式化失败")
        
        # 如果ticket数量超过10个，添加省略提示
        if len(tickets) > 10:
            formatted_list.append(f"\n... 还有 {len(tickets) - 10} 个项目")
        
        return '\n\n'.join(formatted_list)
    
    def _smart_line_break(self, text: str, max_length: int) -> str:
        """智能换行，在合适的位置断行"""
        if len(text) <= max_length:
            return text
        
        # 尝试在空格、逗号、句号等位置断行
        break_chars = [' ', ',', '，', '。', '.', ';', '；']
        best_break = -1
        
        for i in range(max_length - 10, max_length):
            if i < len(text) and text[i] in break_chars:
                best_break = i
        
        if best_break > 0:
            return text[:best_break + 1] + '\n' + text[best_break + 1:]
        else:
            # 如果找不到合适的断行点，直接在最大长度处断行
            return text[:max_length] + '\n' + text[max_length:]
    
    def _generate_status_suggestion(self, tickets: List[Dict]) -> str:
        """根据ticket状态生成智能建议"""
        if not tickets:
            return "暂无项目需要处理"
        
        # 统计各种状态
        status_count = {}
        done_count = 0
        high_priority_count = 0
        
        for ticket in tickets:
            status = ticket.get('status', '').lower()
            priority = ticket.get('priority', '').lower()
            
            status_count[status] = status_count.get(status, 0) + 1
            
            if status in ['done', 'closed', '已完成', '已关闭']:
                done_count += 1
            
            if priority in ['highest', 'high', '最高', '高']:
                high_priority_count += 1
        
        # 生成建议
        suggestions = []
        
        if done_count > 0:
            if done_count == len(tickets):
                return "所有项目已完成，建议调整查询条件排除已完成项目"
            else:
                suggestions.append(f"{done_count}个项目已完成，可能需要验证完成状态")
        
        if high_priority_count > 0:
            suggestions.append(f"{high_priority_count}个高优先级项目需要优先处理")
        
        if not suggestions:
            suggestions.append("请及时处理相关项目")
        
        return "；".join(suggestions)
    
    def _get_priority_icon(self, priority: str) -> str:
        """根据优先级获取图标"""
        priority_lower = priority.lower() if priority else ''
        
        if priority_lower in ['highest', '最高']:
            return '🔴 '
        elif priority_lower in ['high', '高']:
            return '🟠 '
        elif priority_lower in ['medium', '中']:
            return '🟡 '
        elif priority_lower in ['low', '低']:
            return '🟢 '
        else:
            return '⚪ '


class JiraResultProcessor:
    """JIRA结果处理器"""
    
    def group_tickets_by_assignee(self, jira_results: List[Dict]) -> Dict[str, List[Dict]]:
        """按assignee分组ticket"""
        grouped = {}
        unassigned = []
        
        for ticket in jira_results:
            try:
                assignee_email = self.extract_assignee_email(ticket)
                
                if assignee_email:
                    if assignee_email not in grouped:
                        grouped[assignee_email] = []
                    grouped[assignee_email].append(ticket)
                else:
                    unassigned.append(ticket)
                    
            except Exception as e:
                logger.warning(f"处理ticket分组失败: {str(e)}")
                unassigned.append(ticket)
        
        # 如果有未分配的ticket，归类到特殊组
        if unassigned:
            grouped['unassigned'] = unassigned
        
        return grouped
    
    def extract_assignee_email(self, ticket: Dict) -> Optional[str]:
        """提取assignee邮箱"""
        try:
            # 尝试多种方式获取assignee邮箱
            assignee = ticket.get('assignee')
            
            if isinstance(assignee, dict):
                return assignee.get('emailAddress')
            elif isinstance(assignee, str) and '@' in assignee:
                return assignee
            else:
                return None
                
        except Exception as e:
            logger.warning(f"提取assignee邮箱失败: {str(e)}")
            return None
    
    def extract_assignee_info(self, ticket: Dict) -> Dict:
        """提取assignee完整信息"""
        try:
            assignee = ticket.get('assignee', {})
            
            if isinstance(assignee, dict):
                return {
                    'email': assignee.get('emailAddress'),
                    'name': assignee.get('displayName'),
                    'employee_code': self._email_to_employee_code(assignee.get('emailAddress'))
                }
            else:
                return {
                    'email': None,
                    'name': 'Unassigned',
                    'employee_code': None
                }
                
        except Exception as e:
            logger.warning(f"提取assignee信息失败: {str(e)}")
            return {'email': None, 'name': 'Unknown', 'employee_code': None}
    
    def _email_to_employee_code(self, email: str) -> Optional[str]:
        """邮箱转employee_code（简单实现）"""
        if email and '@shopee.com' in email:
            return email.split('@')[0]
        return None
    
    def extract_role_info(self, ticket: Dict, role: str) -> Dict:
        """提取指定角色的信息（为未来扩展预留）"""
        try:
            # 角色字段映射
            role_field_mapping = {
                'assignee': 'assignee',
                'developer': 'customfield_10307',  # Dev PIC
                'qa': 'customfield_10308',         # QA PIC  
                'pm': 'customfield_10309',         # PM PIC
                'reporter': 'reporter'
            }
            
            field_name = role_field_mapping.get(role, role)
            role_data = ticket.get(field_name, {})
            
            if isinstance(role_data, dict):
                return {
                    'role': role,
                    'email': role_data.get('emailAddress'),
                    'name': role_data.get('displayName'),
                    'employee_code': self._email_to_employee_code(role_data.get('emailAddress'))
                }
            elif isinstance(role_data, list) and role_data:
                # 处理多值字段（如果有的话）
                first_person = role_data[0] if isinstance(role_data[0], dict) else {}
                return {
                    'role': role,
                    'email': first_person.get('emailAddress'),
                    'name': first_person.get('displayName'),
                    'employee_code': self._email_to_employee_code(first_person.get('emailAddress'))
                }
            else:
                return {
                    'role': role,
                    'email': None,
                    'name': f'No {role.title()}',
                    'employee_code': None
                }
                
        except Exception as e:
            logger.warning(f"提取{role}信息失败: {str(e)}")
            return {'role': role, 'email': None, 'name': 'Unknown', 'employee_code': None}
    
    def group_tickets_by_role(self, jira_results: List[Dict], roles: List[str]) -> Dict[str, Dict[str, List[Dict]]]:
        """按多个角色分组ticket（为未来扩展预留）"""
        try:
            grouped = {}
            
            for role in roles:
                grouped[role] = {}
                
                for ticket in jira_results:
                    role_info = self.extract_role_info(ticket, role)
                    role_email = role_info['email']
                    
                    if role_email:
                        if role_email not in grouped[role]:
                            grouped[role][role_email] = []
                        grouped[role][role_email].append(ticket)
                    else:
                        # 未分配的ticket
                        unassigned_key = f'unassigned_{role}'
                        if unassigned_key not in grouped[role]:
                            grouped[role][unassigned_key] = []
                        grouped[role][unassigned_key].append(ticket)
            
            return grouped
            
        except Exception as e:
            logger.error(f"按角色分组失败: {str(e)}")
            return {}
    
    def determine_notification_target(self, assignee_email: str, 
                                    project_key: str, 
                                    group_mapping: Dict) -> Dict:
        """确定通知目标（私聊还是群聊）"""
        try:
            # 默认通知到私聊
            target = {
                'type': 'private',
                'target_id': self._email_to_employee_code(assignee_email),
                'email': assignee_email
            }
            
            # 如果有群组映射配置，也可以通知到群聊
            if group_mapping and project_key in group_mapping:
                target['group_option'] = {
                    'type': 'group',
                    'target_id': group_mapping[project_key]
                }
            
            return target
            
        except Exception as e:
            logger.warning(f"确定通知目标失败: {str(e)}")
            return {'type': 'unknown', 'target_id': None, 'email': assignee_email}


class AdvancedTaskManager:
    """高级定时任务功能管理器"""
    
    def __init__(self):
        self.private_chat = PrivateChat()
        self.message_generator = SmartMessageGenerator()
        self.result_processor = JiraResultProcessor()
    
    async def check_feature_permission(self, user_id: str, feature_name: str) -> Dict:
        """检查用户是否有特定功能权限"""
        try:
            # 开发环境跳过权限检查
            import os
            debug_env = os.environ.get('DJANGO_DEBUG', '')
            logger.info(f"🧠 权限检查开始 - user_id: '{user_id}', feature: '{feature_name}', DJANGO_DEBUG='{debug_env}'")
            
            if debug_env.lower() == 'true':
                logger.info(f"🧠 开发环境跳过权限检查: 用户 {user_id}, 功能 {feature_name}")
                return {
                    'success': True,
                    'has_permission': True,
                    'whitelist': None
                }
            
            logger.info(f"🧠 生产环境执行权限检查: 用户 {user_id}, 功能 {feature_name}")
            
            from asgiref.sync import sync_to_async
            
            # 使用sync_to_async包装数据库查询
            get_whitelist = sync_to_async(
                lambda: AdvancedTaskFeatureWhitelist.objects.get(
                    user_id=user_id, 
                    is_active=True
                )
            )
            
            try:
                whitelist = await get_whitelist()
                
                # 使用sync_to_async包装has_feature方法
                has_feature = sync_to_async(whitelist.has_feature)
                if await has_feature(feature_name):
                    return {
                        'success': True,
                        'has_permission': True,
                        'whitelist': whitelist
                    }
                else:
                    return {
                        'success': True,
                        'has_permission': False,
                        'error': f'您没有使用 {feature_name} 功能的权限。请联系管理员申请白名单权限。'
                    }
                    
            except AdvancedTaskFeatureWhitelist.DoesNotExist:
                return {
                    'success': True,
                    'has_permission': False,
                    'error': '您没有使用高级功能的权限。请联系管理员申请白名单权限。'
                }
                
        except Exception as e:
            logger.error(f"检查功能权限失败: {str(e)}")
            # 如果权限检查失败，在开发环境下允许通过
            import os
            if os.environ.get('DJANGO_DEBUG', '').lower() == 'true':
                logger.warning(f"开发环境权限检查失败，允许通过: {str(e)}")
                return {
                    'success': True,
                    'has_permission': True,
                    'whitelist': None
                }
            return {
                'success': False,
                'error': f'权限检查失败: {str(e)}'
            }
    
    # ==================== 智能通知功能 ====================
    
    async def create_smart_notification_task(self, user_id: str, user_email: str, 
                                           task_name: str, jql_query: str, 
                                           schedule_time: str, frequency: str = 'daily',
                                           notification_config: Dict = None,
                                           schedule_days: List[int] = None,
                                           group_title: str = None) -> Dict:
        """
        创建智能通知任务
        
        Args:
            notification_config: {
                'type': 'assignee_based',  # 基于assignee的智能通知
                'target_type': 'private|group|auto',  # private/group/auto(自动判断)
                'merge_messages': True,  # 是否合并消息
                'smart_content': True,  # 是否智能生成内容
                'group_mapping': {  # JIRA项目到群组的映射
                    'SPCB': 'group_id_1',
                    'SPCT': 'group_id_2'
                },
                'message_template': 'daily_reminder'  # 消息模板
            }
            group_title: 群标题（用于权限检查）
        """
        # 权限检查
        from .task_permission_manager import task_permission_manager
        
        logger.info(f"🔒 开始权限检查 - user_email: '{user_email}'")
        permission_result = task_permission_manager.check_task_creation_permission(
            user_email=user_email,
            jql_query=jql_query,
            notification_config=notification_config or {},
            group_title=group_title
        )
        
        logger.info(f"🔒 权限检查结果: allowed={permission_result.allowed}, reason={permission_result.reason}")
        
        if not permission_result.allowed:
            error_msg = f"❌ 权限不足: {permission_result.reason}"
            if permission_result.suggested_scope:
                error_msg += f"\n💡 建议: {permission_result.suggested_scope}"
            
            return {
                'success': False,
                'error': error_msg,
                'permission_denied': True,
                'user_role': permission_result.user_role
            }
        
        try:
            # 设置精简的默认配置
            default_config = {
                'type': 'smart',  # 简化标识
                'target': 'auto',  # 简化字段名
                'merge': True,     # 简化字段名
                'template': 'general'  # 简化字段名
            }
            
            if notification_config:
                # 只保留必要的配置项
                if 'target_type' in notification_config:
                    default_config['target'] = notification_config['target_type']
                if 'merge_messages' in notification_config:
                    default_config['merge'] = notification_config['merge_messages']
                if 'message_template' in notification_config:
                    default_config['template'] = notification_config['message_template']
            
            # 创建智能任务，使用特殊的通知类型标识
            logger.info(f"🧠 创建智能任务 - user_id: '{user_id}', user_email: '{user_email}'")
            logger.info(f"🧠 任务参数: name='{task_name}', query='{jql_query}', schedule='{schedule_time}', freq='{frequency}', days={schedule_days}")
            
            result = await task_scheduler.create_task(
                user_id=user_id,
                user_email=user_email,
                employee_code=None,
                task_name=task_name,
                query_text=jql_query,
                schedule_time=schedule_time,
                frequency=frequency,
                schedule_days=schedule_days,
                notification_type='smart',  # 使用smart标识智能通知
                target_group_id=json.dumps(default_config)  # 将配置存储在target_group_id字段
            )
            logger.info(f"🧠 智能任务创建结果: {result}")
            
            # 验证任务是否真的保存到数据库
            if result['success'] and result.get('task_id'):
                try:
                    from asgiref.sync import sync_to_async
                    
                    def verify_task_saved():
                        from app01.models import UserScheduledTask
                        try:
                            task = UserScheduledTask.objects.get(id=result['task_id'])
                            logger.info(f"🧠 任务验证成功: ID={task.id}, 用户={task.user_id}, 名称={task.task_name}")
                            return True
                        except UserScheduledTask.DoesNotExist:
                            logger.error(f"🧠 任务验证失败: 任务ID {result['task_id']} 未在数据库中找到")
                            return False
                    
                    verify_async = sync_to_async(verify_task_saved)
                    verification = await verify_async()
                    logger.info(f"🧠 数据库验证结果: {verification}")
                    
                except Exception as e:
                    logger.error(f"🧠 任务验证异常: {str(e)}")
            
            if result['success']:
                result['message'] += f"\n🧠 智能通知已配置: {default_config['target']} 模式"
                if default_config['merge']:
                    result['message'] += ", 支持消息合并"
            
            return result
            
        except Exception as e:
            logger.error(f"创建智能通知任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建智能通知任务失败: {str(e)}'
            }
    
    async def execute_smart_notification_task(self, task: UserScheduledTask, jira_results: List[Dict]) -> Dict:
        """执行智能通知任务"""
        try:
            # 解析通知配置
            notification_config = json.loads(task.target_group_id) if task.target_group_id else {}
            
            # 检查是否需要智能处理
            if not notification_config.get('type') == 'smart':
                # 非智能通知，使用原始逻辑
                return await self._execute_simple_notification(task, jira_results, notification_config)
            
            # 智能通知处理流程
            if not jira_results:
                # 无结果时的处理
                await self._send_no_results_notification(task)
                return {'success': True, 'message': '无查询结果，已发送空结果通知'}
            
            # 1. 按assignee分组
            grouped_tickets = self.result_processor.group_tickets_by_assignee(jira_results)
            
            # 2. 生成智能消息
            if notification_config.get('merge', True):
                merged_messages = await self.message_generator.generate_merged_messages(
                    grouped_tickets, task.task_name, task.task_description or "", task.id
                )
            else:
                # 不合并消息，为每个ticket单独生成
                merged_messages = await self._generate_individual_messages(grouped_tickets, task)
            
            # 3. 发送通知
            await self._send_smart_notifications(merged_messages, notification_config, task)
            
            # 4. 统计发送结果
            total_recipients = len(merged_messages)
            
            return {
                'success': True,
                'message': f'智能通知发送完成，共 {total_recipients} 个接收者',
                'recipients_count': total_recipients,
                'tickets_count': len(jira_results)
            }
            
        except Exception as e:
            logger.error(f"执行智能通知任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'执行智能通知任务失败: {str(e)}'
            }
    
    async def _execute_simple_notification(self, task: UserScheduledTask, 
                                         jira_results: List[Dict], 
                                         notification_config: Dict) -> Dict:
        """执行简单通知（非智能模式）"""
        try:
            # 生成简单的通知内容
            content = await self.message_generator.generate_notification_content(
                task.task_name, task.task_description or "", jira_results, task.id
            )
            
            # 添加任务信息头部（generate_notification_content已包含task_id信息）
            notification_message = content
            
            # 发送到任务创建者
            if task.user_email:
                # 使用正确的API获取employee_code
                from app01.seatalk_group_manager import get_employee_codes
                email_to_code = get_employee_codes([task.user_email])
                employee_code = email_to_code.get(task.user_email)
                
                if employee_code:
                    result = await self.private_chat.send_text_message(employee_code, notification_message)
                    if result.get('success'):
                        logger.info(f"简单通知已发送给: {task.user_email} (employee_code: {employee_code})")
                    else:
                        logger.error(f"简单通知发送失败: {task.user_email} (employee_code: {employee_code}) - {result.get('error')}")
                else:
                    logger.warning(f"无法获取employee_code: {task.user_email}，可能是邮箱不存在或已离职")
            
            return {'success': True, 'message': '简单通知发送完成'}
            
        except Exception as e:
            logger.error(f"执行简单通知失败: {str(e)}")
            return {'success': False, 'error': f'执行简单通知失败: {str(e)}'}
    
    async def _send_no_results_notification(self, task: UserScheduledTask):
        """发送无结果通知"""
        try:
            message = f"🔔 **定时任务提醒: {task.task_name}**\n📝 **任务ID**: {task.id}\n\n✅ 查询无结果，暂无需要处理的项目"
            
            if task.user_email:
                # 使用正确的API获取employee_code
                from app01.seatalk_group_manager import get_employee_codes
                email_to_code = get_employee_codes([task.user_email])
                employee_code = email_to_code.get(task.user_email)
                
                if employee_code:
                    result = await self.private_chat.send_text_message(employee_code, message)
                    if result.get('success'):
                        logger.info(f"无结果通知已发送给: {task.user_email} (employee_code: {employee_code})")
                    else:
                        logger.error(f"无结果通知发送失败: {task.user_email} (employee_code: {employee_code}) - {result.get('error')}")
                else:
                    logger.warning(f"无法获取employee_code: {task.user_email}，可能是邮箱不存在或已离职")
                    
        except Exception as e:
            logger.warning(f"发送无结果通知失败: {str(e)}")
    
    async def _generate_individual_messages(self, grouped_tickets: Dict[str, List[Dict]], 
                                          task: UserScheduledTask) -> Dict[str, str]:
        """为每个ticket生成单独的消息（不合并）"""
        individual_messages = {}
        
        for assignee_email, tickets in grouped_tickets.items():
            messages = []
            for ticket in tickets:
                content = await self.message_generator.generate_notification_content(
                    task.task_name, task.task_description or "", [ticket], task.id
                )
                messages.append(content)
            
            # 将所有消息组合
            individual_messages[assignee_email] = '\n\n---\n\n'.join(messages)
        
        return individual_messages
    
    async def _send_smart_notifications(self, merged_messages: Dict[str, str], 
                                      notification_config: Dict, task: UserScheduledTask):
        """发送智能通知"""
        try:
            target_type = notification_config.get('target_type', 'auto')
            group_mapping = notification_config.get('group_mapping', {})
            
            for assignee_email, message in merged_messages.items():
                try:
                    # 处理未分配的情况
                    if assignee_email == 'unassigned':
                        await self._send_unassigned_notification(message, task)
                        continue
                    
                    # 确定通知目标
                    if target_type == 'private' or target_type == 'auto':
                        await self._send_private_notification(assignee_email, message)
                    
                    if target_type == 'group':
                        await self._send_group_notification(assignee_email, message, group_mapping)
                    
                    if target_type == 'both':
                        await self._send_private_notification(assignee_email, message)
                        await self._send_group_notification(assignee_email, message, group_mapping)
                        
                except Exception as e:
                    logger.warning(f"发送通知给 {assignee_email} 失败: {str(e)}")
                    
        except Exception as e:
            logger.error(f"发送智能通知失败: {str(e)}")
    
    async def _send_private_notification(self, assignee_email: str, message: str):
        """发送私聊通知"""
        try:
            # 使用正确的API获取employee_code
            from app01.seatalk_group_manager import get_employee_codes
            email_to_code = get_employee_codes([assignee_email])
            employee_code = email_to_code.get(assignee_email)
            
            if employee_code:
                result = await self.private_chat.send_text_message(employee_code, message)
                if result.get('success'):
                    logger.info(f"私聊通知已发送给: {assignee_email} (employee_code: {employee_code})")
                else:
                    logger.error(f"私聊通知发送失败: {assignee_email} (employee_code: {employee_code}) - {result.get('error')}")
            else:
                logger.warning(f"无法获取employee_code: {assignee_email}，可能是邮箱不存在或已离职")
                
        except Exception as e:
            logger.warning(f"发送私聊通知失败: {assignee_email} - {str(e)}")
    
    async def _send_group_notification(self, assignee_email: str, message: str, group_mapping: Dict):
        """发送群聊通知"""
        try:
            # 这里可以根据项目或其他规则确定群组
            # 暂时实现基础版本
            from app01.views import test_for_seatalk_bot
            
            # 使用默认群组或根据mapping确定
            default_group = list(group_mapping.values())[0] if group_mapping else None
            if default_group:
                test_for_seatalk_bot(message, [assignee_email], default_group)
                logger.info(f"群聊通知已发送给: {assignee_email}")
                
        except Exception as e:
            logger.warning(f"发送群聊通知失败: {assignee_email} - {str(e)}")
    
    async def _send_unassigned_notification(self, message: str, task: UserScheduledTask):
        """发送未分配项目的通知"""
        try:
            # 发送给任务创建者
            if task.user_email:
                # 使用正确的API获取employee_code
                from app01.seatalk_group_manager import get_employee_codes
                email_to_code = get_employee_codes([task.user_email])
                employee_code = email_to_code.get(task.user_email)
                
                if employee_code:
                    unassigned_message = f"📋 **未分配项目提醒**\n📝 **任务ID**: {task.id}\n\n{message}"
                    result = await self.private_chat.send_text_message(employee_code, unassigned_message)
                    if result.get('success'):
                        logger.info(f"未分配项目通知已发送给: {task.user_email} (employee_code: {employee_code})")
                    else:
                        logger.error(f"未分配项目通知发送失败: {task.user_email} (employee_code: {employee_code}) - {result.get('error')}")
                else:
                    logger.warning(f"无法获取employee_code: {task.user_email}，可能是邮箱不存在或已离职")
                    
        except Exception as e:
            logger.warning(f"发送未分配项目通知失败: {str(e)}")

    # ==================== 1. 群聊通知功能 ====================
    
    async def create_group_notification_task(self, user_id: str, user_email: str, 
                                           task_name: str, query_text: str, 
                                           schedule_time: str, frequency: str,
                                           target_group_id: str, schedule_days: List[int] = None) -> Dict:
        """创建群聊通知任务"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'group_notification')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            # 创建任务，设置群聊通知
            result = await task_scheduler.create_task(
                user_id=user_id,
                user_email=user_email,
                employee_code=None,
                task_name=task_name,
                query_text=query_text,
                schedule_time=schedule_time,
                frequency=frequency,
                schedule_days=schedule_days,
                notification_type='group',
                target_group_id=target_group_id
            )
            
            if result['success']:
                result['message'] += f"\n📢 任务结果将发送到群聊 {target_group_id}"
            
            return result
            
        except Exception as e:
            logger.error(f"创建群聊通知任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建群聊通知任务失败: {str(e)}'
            }

    # ==================== 2. 任务模板功能 ====================
    
    async def create_task_template(self, user_id: str, name: str, description: str,
                                 category: str, query_template: str, 
                                 default_schedule: str, template_variables: Dict = None) -> Dict:
        """创建任务模板"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'task_template')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            template = TaskTemplate(
                name=name,
                description=description,
                category=category,
                query_template=query_template,
                default_schedule=default_schedule,
                template_variables=template_variables or {},
                created_by=user_id,
                is_public=False,  # 用户创建的模板默认私有
                is_active=True
            )
            template.save()
            
            return {
                'success': True,
                'template_id': template.id,
                'message': f'任务模板 "{name}" 创建成功'
            }
            
        except Exception as e:
            logger.error(f"创建任务模板失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建任务模板失败: {str(e)}'
            }
    
    async def list_task_templates(self, user_id: str, category: str = None) -> Dict:
        """列出可用的任务模板"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'task_template')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            # 查询公开模板和用户自己创建的模板
            query = Q(is_active=True) & (Q(is_public=True) | Q(created_by=user_id))
            
            if category:
                query &= Q(category=category)
            
            templates = TaskTemplate.objects.filter(query).order_by('category', 'name')
            
            template_list = []
            for template in templates:
                template_info = {
                    'id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'category': template.category,
                    'query_template': template.query_template,
                    'default_schedule': template.default_schedule,
                    'template_variables': template.template_variables,
                    'usage_count': template.usage_count,
                    'is_public': template.is_public,
                    'created_by': template.created_by
                }
                template_list.append(template_info)
            
            return {
                'success': True,
                'templates': template_list,
                'total_count': len(template_list)
            }
            
        except Exception as e:
            logger.error(f"获取任务模板失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取任务模板失败: {str(e)}'
            }
    
    async def create_task_from_template(self, user_id: str, user_email: str,
                                      template_id: int, task_name: str,
                                      variable_values: Dict = None) -> Dict:
        """从模板创建任务"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'task_template')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            template = TaskTemplate.objects.get(id=template_id, is_active=True)
            
            # 检查用户是否有权限使用此模板
            if not template.is_public and template.created_by != user_id:
                return {
                    'success': False,
                    'error': '您没有权限使用此模板'
                }
            
            # 替换模板变量
            query_text = self._replace_template_variables(
                template.query_template, 
                variable_values or {}
            )
            
            # 解析调度表达式
            schedule_info = task_scheduler.parse_schedule_expression(template.default_schedule)
            if 'error' in schedule_info:
                return {
                    'success': False,
                    'error': f"模板调度表达式解析失败: {schedule_info['error']}"
                }
            
            # 创建任务
            result = await task_scheduler.create_task(
                user_id=user_id,
                user_email=user_email,
                employee_code=None,
                task_name=task_name,
                query_text=query_text,
                schedule_time=schedule_info['schedule_time'],
                frequency=schedule_info['frequency'],
                schedule_days=schedule_info['schedule_days']
            )
            
            if result['success']:
                # 更新模板使用统计
                template.usage_count += 1
                template.save()
                
                result['message'] += f"\n📋 基于模板 '{template.name}' 创建"
            
            return result
            
        except TaskTemplate.DoesNotExist:
            return {
                'success': False,
                'error': '模板不存在或已被删除'
            }
        except Exception as e:
            logger.error(f"从模板创建任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'从模板创建任务失败: {str(e)}'
            }
    
    def _replace_template_variables(self, template_text: str, variables: Dict) -> str:
        """替换模板变量"""
        result = template_text
        for key, value in variables.items():
            result = result.replace(f"{{{key}}}", str(value))
        return result
    
    # ==================== 3. 批量管理功能 ====================
    
    async def batch_create_tasks(self, user_id: str, user_email: str, 
                               task_configs: List[Dict]) -> Dict:
        """批量创建任务"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'batch_management')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            results = []
            success_count = 0
            
            for config in task_configs:
                try:
                    result = await task_scheduler.create_task(
                        user_id=user_id,
                        user_email=user_email,
                        employee_code=config.get('employee_code'),
                        task_name=config['task_name'],
                        query_text=config['query_text'],
                        schedule_time=config['schedule_time'],
                        frequency=config.get('frequency', 'daily'),
                        schedule_days=config.get('schedule_days'),
                        notification_type=config.get('notification_type', 'private'),
                        target_group_id=config.get('target_group_id')
                    )
                    
                    if result['success']:
                        success_count += 1
                    
                    results.append({
                        'task_name': config['task_name'],
                        'success': result['success'],
                        'message': result.get('message', result.get('error'))
                    })
                    
                except Exception as e:
                    results.append({
                        'task_name': config.get('task_name', 'Unknown'),
                        'success': False,
                        'message': f'创建失败: {str(e)}'
                    })
            
            return {
                'success': True,
                'results': results,
                'success_count': success_count,
                'total_count': len(task_configs),
                'message': f'批量创建完成: 成功 {success_count}/{len(task_configs)} 个任务'
            }
            
        except Exception as e:
            logger.error(f"批量创建任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'批量创建任务失败: {str(e)}'
            }
    
    async def batch_update_task_status(self, user_id: str, task_ids: List[int], 
                                     new_status: str) -> Dict:
        """批量更新任务状态"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'batch_management')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            results = []
            success_count = 0
            
            for task_id in task_ids:
                result = await task_scheduler.update_task_status(user_id, task_id, new_status)
                if result['success']:
                    success_count += 1
                
                results.append({
                    'task_id': task_id,
                    'success': result['success'],
                    'message': result.get('message', result.get('error'))
                })
            
            return {
                'success': True,
                'results': results,
                'success_count': success_count,
                'total_count': len(task_ids),
                'message': f'批量更新完成: 成功 {success_count}/{len(task_ids)} 个任务'
            }
            
        except Exception as e:
            logger.error(f"批量更新任务状态失败: {str(e)}")
            return {
                'success': False,
                'error': f'批量更新任务状态失败: {str(e)}'
            }
    
    # ==================== 4. 统计报表功能 ====================
    
    async def generate_user_statistics_report(self, user_id: str, 
                                            start_date: date = None, 
                                            end_date: date = None) -> Dict:
        """生成用户统计报表"""
        # 检查权限
        permission = await self.check_feature_permission(user_id, 'statistics_report')
        if not permission['success'] or not permission['has_permission']:
            return permission
        
        try:
            # 默认查询最近30天
            if not end_date:
                end_date = date.today()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # 基础统计
            total_tasks = UserScheduledTask.objects.filter(user_id=user_id).count()
            active_tasks = UserScheduledTask.objects.filter(
                user_id=user_id, 
                status='active', 
                is_active=True
            ).count()
            
            # 执行统计
            execution_logs = TaskExecutionLog.objects.filter(
                task__user_id=user_id,
                execution_time__date__range=[start_date, end_date]
            )
            
            total_executions = execution_logs.count()
            successful_executions = execution_logs.filter(success=True).count()
            failed_executions = total_executions - successful_executions
            
            success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0
            
            # 性能统计
            performance_stats = execution_logs.aggregate(
                avg_duration=Avg('execution_duration'),
                max_duration=Max('execution_duration'),
                min_duration=Min('execution_duration')
            )
            
            # 每日执行统计
            daily_stats = []
            current_date = start_date
            while current_date <= end_date:
                day_logs = execution_logs.filter(execution_time__date=current_date)
                daily_stats.append({
                    'date': current_date.isoformat(),
                    'total_executions': day_logs.count(),
                    'successful_executions': day_logs.filter(success=True).count(),
                    'failed_executions': day_logs.filter(success=False).count()
                })
                current_date += timedelta(days=1)
            
            # 任务类型统计
            task_frequency_stats = UserScheduledTask.objects.filter(
                user_id=user_id
            ).values('frequency').annotate(count=Count('id'))
            
            report = {
                'user_id': user_id,
                'report_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'task_summary': {
                    'total_tasks': total_tasks,
                    'active_tasks': active_tasks,
                    'paused_tasks': UserScheduledTask.objects.filter(
                        user_id=user_id, status='paused'
                    ).count(),
                    'disabled_tasks': UserScheduledTask.objects.filter(
                        user_id=user_id, status='disabled'
                    ).count()
                },
                'execution_summary': {
                    'total_executions': total_executions,
                    'successful_executions': successful_executions,
                    'failed_executions': failed_executions,
                    'success_rate': round(success_rate, 2)
                },
                'performance_summary': {
                    'avg_execution_time': round(performance_stats['avg_duration'] or 0, 2),
                    'max_execution_time': round(performance_stats['max_duration'] or 0, 2),
                    'min_execution_time': round(performance_stats['min_duration'] or 0, 2)
                },
                'daily_statistics': daily_stats,
                'task_frequency_distribution': list(task_frequency_stats)
            }
            
            return {
                'success': True,
                'report': report
            }
            
        except Exception as e:
            logger.error(f"生成统计报表失败: {str(e)}")
            return {
                'success': False,
                'error': f'生成统计报表失败: {str(e)}'
            }


# 全局高级任务管理器实例
advanced_task_manager = AdvancedTaskManager() 
"""
JIRA 字段映射定时更新任务
每天自动更新一次字段映射关系
"""

import asyncio
import logging
from datetime import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone

from .field_mapper import field_mapper

logger = logging.getLogger(__name__)


class FieldUpdateTask:
    """字段更新任务管理器"""
    
    def __init__(self):
        self.is_running = False
    
    async def daily_update_task(self) -> Dict:
        """每日字段更新任务"""
        if self.is_running:
            logger.warning("字段更新任务已在运行中，跳过本次执行")
            return {
                'success': False,
                'message': '任务已在运行中'
            }
        
        self.is_running = True
        start_time = timezone.now()
        
        try:
            logger.info("开始执行每日字段映射更新任务...")
            
            # 检查是否需要更新
            status = await field_mapper.get_update_status()
            
            if not status['needs_update']:
                logger.info(f"字段映射仍然有效（上次更新: {status['last_update']}），跳过更新")
                return {
                    'success': True,
                    'message': '字段映射仍然有效，无需更新',
                    'last_update': status['last_update'],
                    'total_fields': status['total_fields']
                }
            
            # 执行更新
            update_result = await field_mapper.force_update()
            
            execution_time = (timezone.now() - start_time).total_seconds()
            
            if update_result['success']:
                logger.info(f"每日字段映射更新完成，耗时 {execution_time:.2f} 秒")
                return {
                    'success': True,
                    'message': update_result['message'],
                    'execution_time': execution_time,
                    'total_fields': update_result['total_fields'],
                    'custom_fields': update_result['custom_fields']
                }
            else:
                logger.error(f"每日字段映射更新失败: {update_result['error']}")
                return {
                    'success': False,
                    'error': update_result['error'],
                    'execution_time': execution_time
                }
                
        except Exception as e:
            execution_time = (timezone.now() - start_time).total_seconds()
            logger.error(f"每日字段映射更新任务异常: {str(e)}")
            return {
                'success': False,
                'error': f'任务执行异常: {str(e)}',
                'execution_time': execution_time
            }
        finally:
            self.is_running = False
    
    def run_sync_update(self) -> Dict:
        """同步方式运行更新任务（用于Django命令）"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.daily_update_task())
        finally:
            loop.close()


# 全局任务实例
field_update_task = FieldUpdateTask()


class Command(BaseCommand):
    """Django 管理命令：更新 JIRA 字段映射"""
    
    help = '更新 JIRA 字段映射关系'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制更新，忽略缓存时间检查',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('开始更新 JIRA 字段映射...')
        )
        
        try:
            result = field_update_task.run_sync_update()
            
            if result['success']:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ {result['message']}")
                )
                if 'total_fields' in result:
                    self.stdout.write(f"📊 总字段数: {result['total_fields']}")
                if 'custom_fields' in result:
                    self.stdout.write(f"🔧 自定义字段数: {result['custom_fields']}")
                if 'execution_time' in result:
                    self.stdout.write(f"⏱️ 执行时间: {result['execution_time']:.2f} 秒")
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ 更新失败: {result['error']}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ 命令执行异常: {str(e)}")
            ) 
"""
Seatalk Typing Status 管理器
在AI处理过程中显示"正在输入"状态
"""

import asyncio
import aiohttp
import logging
from typing import Optional, Dict
from app01.config import get_seatalk_access_token

logger = logging.getLogger(__name__)


class TypingStatusManager:
    """Seatalk Typing Status 管理器"""
    
    BASE_URL = "https://openapi.seatalk.io/messaging/v2"
    
    def __init__(self):
        self.access_token = None
        
    def _get_access_token(self):
        """获取访问令牌"""
        if not self.access_token:
            self.access_token = get_seatalk_access_token()
        return self.access_token
    
    async def start_group_chat_typing(self, group_id: str, thread_id: str = None) -> Dict:
        """
        在群聊中开始显示typing状态
        
        Args:
            group_id: 群组ID
            thread_id: 线程ID (可选)
            
        Returns:
            操作结果
        """
        try:
            url = f"{self.BASE_URL}/group_chat_typing"
            headers = {
                "Authorization": f"Bearer {self._get_access_token()}",
                "Content-Type": "application/json"
            }
            
            data = {"group_id": group_id}
            if thread_id:
                data["thread_id"] = thread_id
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"群聊typing状态启动成功: {group_id}")
                        return {'success': True, 'data': result}
                    else:
                        error_text = await response.text()
                        logger.warning(f"群聊typing状态启动失败: {response.status} - {error_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}",
                            'status_code': response.status
                        }
                        
        except Exception as e:
            logger.error(f"群聊typing状态启动异常: {str(e)}")
            return {
                'success': False,
                'error': f"启动异常: {str(e)}"
            }
    
    async def start_private_chat_typing(self, employee_code: str) -> Dict:
        """
        在私聊中开始显示typing状态
        
        Args:
            employee_code: 用户employee_code
            
        Returns:
            操作结果
        """
        try:
            url = f"{self.BASE_URL}/single_chat_typing"
            headers = {
                "Authorization": f"Bearer {self._get_access_token()}",
                "Content-Type": "application/json"
            }
            
            data = {"employee_code": employee_code}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"私聊typing状态启动成功: {employee_code}")
                        return {'success': True, 'data': result}
                    else:
                        error_text = await response.text()
                        logger.warning(f"私聊typing状态启动失败: {response.status} - {error_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}",
                            'status_code': response.status
                        }
                        
        except Exception as e:
            logger.error(f"私聊typing状态启动异常: {str(e)}")
            return {
                'success': False,
                'error': f"启动异常: {str(e)}"
            }
    
    async def start_typing(self, group_id: str = None, employee_code: str = None, 
                          thread_id: str = None) -> Dict:
        """
        根据参数自动选择群聊或私聊typing状态
        
        Args:
            group_id: 群组ID
            employee_code: 私聊用户employee_code
            thread_id: 线程ID
            
        Returns:
            操作结果
        """
        if group_id:
            return await self.start_group_chat_typing(group_id, thread_id)
        elif employee_code:
            return await self.start_private_chat_typing(employee_code)
        else:
            return {
                'success': False,
                'error': 'group_id 和 employee_code 不能都为空'
            }
    
    async def with_typing_indicator(self, coroutine, group_id: str = None, 
                                   employee_code: str = None, thread_id: str = None,
                                   retry_typing: bool = True):
        """
        装饰器：在执行长时间操作时显示typing状态
        
        Args:
            coroutine: 要执行的协程
            group_id: 群组ID
            employee_code: 私聊用户employee_code  
            thread_id: 线程ID
            retry_typing: 是否在4秒后重新显示typing状态
            
        Returns:
            协程执行结果
        """
        # 启动typing状态
        typing_result = await self.start_typing(group_id, employee_code, thread_id)
        if not typing_result['success']:
            logger.warning(f"Typing状态启动失败: {typing_result.get('error')}")
        
        # 如果需要长时间处理，设置定时重新显示typing状态
        typing_task = None
        if retry_typing:
            typing_task = asyncio.create_task(
                self._periodic_typing(group_id, employee_code, thread_id)
            )
        
        try:
            # 执行主要操作
            result = await coroutine
            return result
        finally:
            # 取消定时typing任务
            if typing_task:
                typing_task.cancel()
                try:
                    await typing_task
                except asyncio.CancelledError:
                    pass
    
    async def _periodic_typing(self, group_id: str = None, employee_code: str = None, 
                              thread_id: str = None, interval: int = 3):
        """
        定时重新显示typing状态
        
        Args:
            group_id: 群组ID
            employee_code: 私聊用户employee_code
            thread_id: 线程ID
            interval: 间隔时间(秒)
        """
        try:
            while True:
                await asyncio.sleep(interval)
                typing_result = await self.start_typing(group_id, employee_code, thread_id)
                if not typing_result['success']:
                    # 如果typing状态失败，停止重试
                    logger.warning(f"定时typing状态失败，停止重试: {typing_result.get('error')}")
                    break
        except asyncio.CancelledError:
            # 正常取消，不需要记录
            pass
        except Exception as e:
            logger.error(f"定时typing状态异常: {str(e)}")


# 全局typing管理器实例
typing_manager = TypingStatusManager()


# 便捷装饰器函数
async def with_typing(func, group_id: str = None, employee_code: str = None, 
                     thread_id: str = None, retry_typing: bool = True):
    """
    便捷的typing状态装饰器函数
    
    Usage:
        result = await with_typing(
            my_async_function(),
            group_id="group123"
        )
    """
    return await typing_manager.with_typing_indicator(
        func, group_id, employee_code, thread_id, retry_typing
    )


# 上下文管理器
class TypingContext:
    """Typing状态上下文管理器"""
    
    def __init__(self, group_id: str = None, employee_code: str = None, 
                 thread_id: str = None, retry_typing: bool = True):
        self.group_id = group_id
        self.employee_code = employee_code
        self.thread_id = thread_id
        self.retry_typing = retry_typing
        self.typing_task = None
    
    async def __aenter__(self):
        # 启动typing状态
        typing_result = await typing_manager.start_typing(
            self.group_id, self.employee_code, self.thread_id
        )
        if not typing_result['success']:
            logger.warning(f"Typing状态启动失败: {typing_result.get('error')}")
        
        # 启动定时重试
        if self.retry_typing:
            self.typing_task = asyncio.create_task(
                typing_manager._periodic_typing(
                    self.group_id, self.employee_code, self.thread_id
                )
            )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # 取消定时typing任务
        if self.typing_task:
            self.typing_task.cancel()
            try:
                await self.typing_task
            except asyncio.CancelledError:
                pass


# 使用示例：
# async with TypingContext(group_id="group123"):
#     result = await some_long_running_operation()
#     return result 
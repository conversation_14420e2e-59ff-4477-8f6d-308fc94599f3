# 文档处理模块

## 概述

文档处理模块为项目添加了强大的文档读取、分析和处理功能。支持从多种平台（如 Confluence）获取文档内容，并通过 AI 进行总结、翻译、问答、信息提取和分析等操作。

## 架构设计

### 模块结构

```
ai_module/
├── document_processor.py          # 文档处理基类和管理器
├── confluence_processor.py        # Confluence 处理器实现
├── document_config.py            # 配置管理
├── document_integration.py       # 与 AI 系统的集成
├── document_config_example.py    # 配置示例
└── README_DOCUMENT_PROCESSING.md # 本文档
```

### 核心组件

1. **DocumentProcessor** - 抽象基类，定义了文档处理器的标准接口
2. **DocumentManager** - 文档管理器，统一的文档处理入口
3. **ConfluenceProcessor** - Confluence 文档处理器的具体实现
4. **DocumentConfig** - 配置管理器，支持多平台配置
5. **DocumentIntegration** - 集成器，将文档处理功能集成到 AI 系统

### 设计原则

- **模块化**: 每个平台都有独立的处理器，便于扩展
- **可配置**: 支持多种配置方式，灵活适应不同环境
- **可扩展**: 预留了日志平台、追踪平台等的扩展接口
- **安全性**: 支持多种认证方式，敏感信息通过环境变量配置

## 功能特性

### 支持的平台

- ✅ **Confluence**: 完全支持
  - 文档读取（REST API）
  - 多种 URL 格式支持
  - HTML 转纯文本
  - Bearer Token 和 Basic Auth 认证

- 🚧 **日志平台**: 预留接口
  - Kibana
  - Grafana
  - Splunk

- 🚧 **追踪平台**: 预留接口
  - Jaeger
  - Zipkin
  - APM

### 支持的操作

1. **总结 (SUMMARIZE)**: 提取文档主要内容和要点
2. **翻译 (TRANSLATE)**: 翻译文档到指定语言
3. **问答 (QA)**: 基于文档内容回答用户问题
4. **信息提取 (EXTRACT_INFO)**: 提取关键信息和数据
5. **分析 (ANALYZE)**: 深入分析文档内容

### 文档来源

1. **直接 URL**: 用户在查询中提供的文档链接
2. **JIRA 关联**: 从 JIRA 的 `customfield_16700` 字段提取 TRD/PRD 链接

## 使用方法

### 基本使用

```python
# 用户查询示例
"总结这个文档 https://confluence.company.com/wiki/pages/123456"
"翻译 PROJ-123 相关文档为英文"
"这个文档说了什么关于API的内容？"
"从文档中提取关键信息"
"分析文档的完整性"
```

### 命令行帮助

```python
# 获取文档处理帮助
"doc help"
"document help"
"文档帮助"
```

### 程序化调用

```python
from app01.ai_module.document_integration import document_integration

# 处理文档查询
result = await document_integration.process_document_query(
    user_query="总结这个文档的内容",
    jira_key="PROJ-123",  # 可选，用于获取关联文档
    target_language="英文"  # 可选，翻译时使用
)
```

## 配置说明

### Django 配置

在 `settings.py` 中添加：

```python
DOCUMENT_PROCESSING = {
    'confluence': {
        'enabled': True,
        'auth_token': None,  # 或通过环境变量设置
        'username': None,
        'password': None,
        'session_timeout': 30,
        'max_content_length': 50000
    },
    'general': {
        'cache_enabled': True,
        'cache_timeout': 3600,
        'default_language': '中文'
    }
}
```

### 环境变量

```bash
export CONFLUENCE_AUTH_TOKEN="your_token"
export CONFLUENCE_USERNAME="your_username"
export CONFLUENCE_PASSWORD="your_password"
```

### 认证配置

1. **Bearer Token** (推荐):
   - 在 Confluence 用户设置中生成 API Token
   - 设置 `CONFLUENCE_AUTH_TOKEN` 环境变量

2. **Basic Auth**:
   - 使用用户名和密码
   - 设置 `CONFLUENCE_USERNAME` 和 `CONFLUENCE_PASSWORD`

## 集成方式

### AI 助手集成

文档处理功能已集成到 `AIAssistant` 中：

1. **意图检测**: 自动识别文档处理相关的用户查询
2. **特殊指令**: 在 `_handle_special_commands` 中处理文档相关指令
3. **意图处理**: 在 `_process_by_intent` 中添加 `document_processing` 意图
4. **帮助信息**: 在主帮助中添加文档处理功能说明

### 关键集成点

```python
# document_integration.py
document_integration = DocumentIntegration()

# ai_assistant.py
from .document_integration import document_integration

# 在特殊指令处理中
if doc_intent['has_document_intent']:
    doc_result = await document_integration.process_document_query(...)
```

## 扩展指南

### 添加新平台

1. **创建处理器**:
   ```python
   class MyPlatformProcessor(DocumentProcessor):
       def supports_document_type(self, doc_type: DocumentType) -> bool:
           return doc_type == DocumentType.MY_PLATFORM
       
       async def fetch_document(self, source: DocumentSource) -> Dict:
           # 实现文档获取逻辑
           pass
       
       async def process_document(self, request: ProcessingRequest) -> ProcessingResult:
           # 实现文档处理逻辑
           pass
   ```

2. **注册处理器**:
   ```python
   # 在 document_integration.py 中
   my_processor = MyPlatformProcessor(config)
   document_manager.register_processor(my_processor, [DocumentType.MY_PLATFORM])
   ```

3. **添加配置**:
   ```python
   # 在 document_config.py 中添加配置项
   'my_platform': {
       'enabled': False,
       'base_url': None,
       'auth_token': None
   }
   ```

### 添加新操作

1. **扩展 ProcessingAction 枚举**:
   ```python
   class ProcessingAction(Enum):
       SUMMARIZE = "summarize"
       # ... 现有操作
       MY_ACTION = "my_action"  # 新操作
   ```

2. **在处理器中实现**:
   ```python
   async def process_document(self, request: ProcessingRequest) -> ProcessingResult:
       if request.action == ProcessingAction.MY_ACTION:
           return await self._my_action_handler(...)
   ```

## 依赖项

### Python 包

```bash
pip install aiohttp beautifulsoup4 html2text
```

### 具体依赖

- `aiohttp`: 异步 HTTP 请求
- `beautifulsoup4`: HTML 解析
- `html2text`: HTML 转纯文本
- `django`: 配置管理
- `re`: 正则表达式（内置）
- `asyncio`: 异步编程（内置）

## 测试

### 单元测试示例

```python
import pytest
from app01.ai_module.document_integration import document_integration

@pytest.mark.asyncio
async def test_confluence_processing():
    result = await document_integration.process_document_query(
        user_query="总结这个文档的内容",
        jira_key="TEST-123"
    )
    assert result['success'] == True
```

### 集成测试

```python
# 测试完整的 AI 处理流程
from app01.ai_module.ai_assistant import AIAssistant

ai = AIAssistant()
result = await ai.process_query(
    user_query="总结 https://confluence.example.com/wiki/pages/123",
    user_id="test_user"
)
```

## 安全考虑

1. **认证信息**: 通过环境变量设置，避免硬编码
2. **权限控制**: 使用专用服务账号，限制必要权限
3. **内容限制**: 设置最大内容长度，防止内存溢出
4. **超时设置**: 设置合理的请求超时时间
5. **错误处理**: 完善的异常处理和日志记录

## 性能优化

1. **缓存机制**: 缓存处理结果，减少重复请求
2. **并发控制**: 限制并发请求数量
3. **内容截断**: 处理超长文档时自动截断
4. **连接池**: 使用 aiohttp 的连接池

## 故障排除

### 常见问题

1. **认证失败**:
   - 检查 Token 是否正确
   - 确认用户权限
   - 检查网络连接

2. **文档获取失败**:
   - 验证 URL 格式
   - 检查文档权限
   - 确认 Confluence 可访问性

3. **处理超时**:
   - 增加超时时间
   - 检查文档大小
   - 优化网络连接

### 调试建议

1. **启用日志**: 设置适当的日志级别
2. **检查配置**: 验证所有配置项
3. **测试连接**: 手动测试 API 连接
4. **监控性能**: 关注处理时间和内存使用

## 更新日志

### v1.0.0 (当前版本)
- 实现 Confluence 文档处理功能
- 支持总结、翻译、问答、信息提取、分析
- 集成到 AI 助手系统
- 可配置的模块化架构
- 预留日志和追踪平台接口

## 未来计划

1. **日志平台支持**: Kibana, Grafana, Splunk
2. **追踪平台支持**: Jaeger, Zipkin, APM
3. **更多文档格式**: PDF, Word, Excel
4. **增强 AI 功能**: 更智能的文档分析
5. **性能优化**: 更好的缓存和并发控制 
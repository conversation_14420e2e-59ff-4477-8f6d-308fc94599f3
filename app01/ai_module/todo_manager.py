"""
代办事项管理模块
支持用户创建、查看、完成和管理个人代办事项
"""

import asyncio
import json
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.db import transaction
from django.db.models import Q

from app01.models import UserTodoItem, TodoCategory
from .private_chat import PrivateChat

logger = logging.getLogger(__name__)


class TodoManager:
    """代办事项管理器"""
    
    def __init__(self):
        self.private_chat = PrivateChat()
        self.max_todo_per_user = 100  # 每用户最大代办事项数量
    
    async def create_todo(self, user_id: str, user_email: str, employee_code: str,
                         title: str, description: str = "", category: str = "personal",
                         priority: str = "medium", due_date: str = None) -> Dict:
        """
        创建代办事项
        
        Args:
            user_id: 用户ID
            user_email: 用户邮箱
            employee_code: 员工代码
            title: 代办事项标题
            description: 详细描述
            category: 分类 (personal/work/jira/meeting)
            priority: 优先级 (low/medium/high/urgent)
            due_date: 截止日期 "YYYY-MM-DD"
        """
        try:
            def _create_todo():
                # 检查用户代办事项数量
                current_count = UserTodoItem.objects.filter(
                    user_id=user_id,
                    status__in=['pending', 'in_progress']
                ).count()
                
                if current_count >= self.max_todo_per_user:
                    return {
                        'success': False,
                        'error': f'代办事项数量已达上限 ({self.max_todo_per_user})，请先完成一些事项'
                    }
                
                # 解析截止日期
                due_date_obj = None
                if due_date:
                    try:
                        due_date_obj = datetime.strptime(due_date, "%Y-%m-%d").date()
                    except ValueError:
                        return {
                            'success': False,
                            'error': '日期格式错误，请使用 YYYY-MM-DD 格式'
                        }
                
                # 创建代办事项
                todo = UserTodoItem(
                    user_id=user_id,
                    user_email=user_email,
                    employee_code=employee_code,
                    title=title,
                    description=description,
                    category=category,
                    priority=priority,
                    due_date=due_date_obj,
                    status='pending'
                )
                todo.save()
                
                return {
                    'success': True,
                    'todo_id': todo.id,
                    'message': f'代办事项 "{title}" 创建成功',
                    'todo': {
                        'id': todo.id,
                        'title': todo.title,
                        'category': todo.category,
                        'priority': todo.priority,
                        'due_date': todo.due_date.isoformat() if todo.due_date else None,
                        'status': todo.status
                    }
                }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _create_todo)
            
        except Exception as e:
            logger.error(f"创建代办事项失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建失败: {str(e)}'
            }
    
    async def list_todos(self, user_id: str, status: str = None, 
                        category: str = None, limit: int = 20) -> Dict:
        """获取用户代办事项列表"""
        try:
            def _get_todos():
                query = Q(user_id=user_id)
                
                if status:
                    query &= Q(status=status)
                if category:
                    query &= Q(category=category)
                
                # 获取所有符合条件的待办事项
                todos = UserTodoItem.objects.filter(query)
                
                # 自定义排序：优先级(urgent>high>medium>low) -> 截止日期 -> 创建时间
                priority_order = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
                
                todos_list = list(todos)
                todos_list.sort(key=lambda x: (
                    -priority_order.get(x.priority, 0),  # 优先级倒序
                    x.due_date if x.due_date else date.max,  # 截止日期正序，无日期的排在最后
                    -x.created_at.timestamp()  # 创建时间倒序
                ))
                
                # 限制结果数量
                todos = todos_list[:limit]
                
                todo_list = []
                for todo in todos:
                    todo_info = {
                        'id': todo.id,
                        'title': todo.title,
                        'description': todo.description,
                        'category': todo.category,
                        'priority': todo.priority,
                        'status': todo.status,
                        'due_date': todo.due_date.isoformat() if todo.due_date else None,
                        'created_at': todo.created_at.isoformat(),
                        'completed_at': todo.completed_at.isoformat() if todo.completed_at else None,
                        'is_overdue': todo.is_overdue()
                    }
                    todo_list.append(todo_info)
                
                # 统计信息
                stats = {
                    'pending': UserTodoItem.objects.filter(user_id=user_id, status='pending').count(),
                    'in_progress': UserTodoItem.objects.filter(user_id=user_id, status='in_progress').count(),
                    'completed': UserTodoItem.objects.filter(user_id=user_id, status='completed').count(),
                    'overdue': UserTodoItem.objects.filter(
                        user_id=user_id, 
                        status__in=['pending', 'in_progress'],
                        due_date__lt=date.today()
                    ).count()
                }
                
                return {
                    'success': True,
                    'todos': todo_list,
                    'stats': stats,
                    'total_count': len(todo_list)
                }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _get_todos)
            
        except Exception as e:
            logger.error(f"获取代办事项失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取失败: {str(e)}'
            }
    
    async def update_todo_status(self, user_id: str, todo_id: int, 
                                new_status: str, completion_note: str = "") -> Dict:
        """更新代办事项状态"""
        try:
            def _update_status():
                try:
                    todo = UserTodoItem.objects.get(id=todo_id, user_id=user_id)
                    
                    if new_status not in ['pending', 'in_progress', 'completed', 'cancelled']:
                        return {
                            'success': False,
                            'error': '无效的状态值'
                        }
                    
                    old_status = todo.status
                    todo.status = new_status
                    
                    if new_status == 'completed':
                        todo.completed_at = timezone.now()
                        todo.completion_note = completion_note
                    elif new_status in ['pending', 'in_progress']:
                        todo.completed_at = None
                        todo.completion_note = ""
                    
                    todo.save()
                    
                    return {
                        'success': True,
                        'message': f'代办事项状态已从 {old_status} 更新为 {new_status}',
                        'todo': {
                            'id': todo.id,
                            'title': todo.title,
                            'status': todo.status,
                            'completed_at': todo.completed_at.isoformat() if todo.completed_at else None
                        }
                    }
                    
                except UserTodoItem.DoesNotExist:
                    return {
                        'success': False,
                        'error': '代办事项不存在或无权限'
                    }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _update_status)
            
        except Exception as e:
            logger.error(f"更新代办事项状态失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新失败: {str(e)}'
            }
    
    async def delete_todo(self, user_id: str, todo_id: int) -> Dict:
        """删除代办事项"""
        try:
            def _delete_todo():
                try:
                    todo = UserTodoItem.objects.get(id=todo_id, user_id=user_id)
                    title = todo.title
                    todo.delete()
                    
                    return {
                        'success': True,
                        'message': f'代办事项 "{title}" 已删除'
                    }
                    
                except UserTodoItem.DoesNotExist:
                    return {
                        'success': False,
                        'error': '代办事项不存在或无权限'
                    }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _delete_todo)
            
        except Exception as e:
            logger.error(f"删除代办事项失败: {str(e)}")
            return {
                'success': False,
                'error': f'删除失败: {str(e)}'
            }
    
    async def get_daily_summary(self, user_id: str) -> Dict:
        """获取今日代办事项摘要"""
        try:
            def _get_summary():
                today = date.today()
                
                # 今日到期的事项
                due_today = UserTodoItem.objects.filter(
                    user_id=user_id,
                    due_date=today,
                    status__in=['pending', 'in_progress']
                ).count()
                
                # 已逾期的事项
                overdue = UserTodoItem.objects.filter(
                    user_id=user_id,
                    due_date__lt=today,
                    status__in=['pending', 'in_progress']
                ).count()
                
                # 高优先级待办
                high_priority = UserTodoItem.objects.filter(
                    user_id=user_id,
                    priority__in=['high', 'urgent'],
                    status__in=['pending', 'in_progress']
                ).count()
                
                # 今日完成的事项
                completed_today = UserTodoItem.objects.filter(
                    user_id=user_id,
                    status='completed',
                    completed_at__date=today
                ).count()
                
                return {
                    'success': True,
                    'summary': {
                        'due_today': due_today,
                        'overdue': overdue,
                        'high_priority': high_priority,
                        'completed_today': completed_today,
                        'date': today.isoformat()
                    }
                }
            
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, _get_summary)
            
        except Exception as e:
            logger.error(f"获取每日摘要失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取摘要失败: {str(e)}'
            }
    
    def parse_todo_command(self, user_input: str) -> Dict:
        """解析代办事项命令"""
        user_input = user_input.strip().lower()
        
        # 创建代办事项
        if user_input.startswith("todo add") or user_input.startswith("todo create"):
            # 示例: "todo add 完成项目文档 @work @high @2024-01-15"
            parts = user_input.split(" ", 2)
            if len(parts) < 3:
                return {
                    'action': 'create',
                    'error': '请提供代办事项标题，格式: todo add <标题> [@分类] [@优先级] [@日期]'
                }
            
            content = parts[2]
            
            # 提取标题和参数
            title_parts = []
            category = "personal"
            priority = "medium"
            due_date = None
            
            for part in content.split():
                if part.startswith("@"):
                    param = part[1:]
                    if param in ["work", "personal", "jira", "meeting"]:
                        category = param
                    elif param in ["low", "medium", "high", "urgent"]:
                        priority = param
                    elif "-" in param and len(param) == 10:  # 日期格式检查
                        due_date = param
                else:
                    title_parts.append(part)
            
            title = " ".join(title_parts)
            
            return {
                'action': 'create',
                'title': title,
                'category': category,
                'priority': priority,
                'due_date': due_date
            }
        
        # 列出代办事项
        elif user_input.startswith("todo list") or user_input == "todo":
            parts = user_input.split()
            
            status = None
            category = None
            
            if "pending" in parts:
                status = "pending"
            elif "completed" in parts:
                status = "completed"
            elif "overdue" in parts:
                status = "overdue"
            
            if "work" in parts:
                category = "work"
            elif "personal" in parts:
                category = "personal"
            
            return {
                'action': 'list',
                'status': status,
                'category': category
            }
        
        # 完成代办事项
        elif user_input.startswith("todo done") or user_input.startswith("todo complete"):
            parts = user_input.split()
            if len(parts) < 3:
                return {
                    'action': 'complete',
                    'error': '请提供代办事项ID，格式: todo done <ID>'
                }
            
            try:
                todo_id = int(parts[2])
                return {
                    'action': 'complete',
                    'todo_id': todo_id
                }
            except ValueError:
                return {
                    'action': 'complete',
                    'error': '代办事项ID必须是数字'
                }
        
        # 删除代办事项
        elif user_input.startswith("todo delete") or user_input.startswith("todo remove"):
            parts = user_input.split()
            if len(parts) < 3:
                return {
                    'action': 'delete',
                    'error': '请提供代办事项ID，格式: todo delete <ID>'
                }
            
            try:
                todo_id = int(parts[2])
                return {
                    'action': 'delete',
                    'todo_id': todo_id
                }
            except ValueError:
                return {
                    'action': 'delete',
                    'error': '代办事项ID必须是数字'
                }
        
        # 今日摘要
        elif user_input in ["todo today", "todo summary"]:
            return {
                'action': 'summary'
            }
        
        # 帮助信息
        elif user_input in ["todo help", "todo"]:
            return {
                'action': 'help'
            }
        
        else:
            return {
                'action': 'help',
                'error': '未知的代办事项命令'
            }
    
    def format_todo_response(self, action: str, result: Dict) -> str:
        """格式化代办事项响应"""
        if action == 'create':
            if result['success']:
                todo = result['todo']
                response = f"✅ 代办事项创建成功\n\n"
                response += f"📋 **{todo['title']}**\n"
                response += f"🏷️ 分类: {todo['category']}\n"
                response += f"⚡ 优先级: {todo['priority']}\n"
                if todo['due_date']:
                    response += f"📅 截止日期: {todo['due_date']}\n"
                response += f"🆔 ID: {todo['id']}"
                return response
            else:
                return f"❌ 创建失败: {result['error']}"
        
        elif action == 'list':
            if result['success']:
                todos = result['todos']
                stats = result['stats']
                
                if not todos:
                    return "📋 暂无代办事项"
                
                response = f"📋 **代办事项列表** ({len(todos)} 项)\n\n"
                
                for todo in todos:
                    status_emoji = "⏳" if todo['status'] == 'pending' else \
                                  "🚧" if todo['status'] == 'in_progress' else \
                                  "✅" if todo['status'] == 'completed' else "❌"
                    
                    priority_emoji = "🔴" if todo['priority'] == 'urgent' else \
                                   "🟡" if todo['priority'] == 'high' else \
                                   "🟢" if todo['priority'] == 'medium' else "⚪"
                    
                    overdue_marker = " ⚠️" if todo['is_overdue'] else ""
                    
                    response += f"{status_emoji} **#{todo['id']}** {todo['title']}\n"
                    response += f"   {priority_emoji} {todo['priority']} | 🏷️ {todo['category']}"
                    
                    if todo['due_date']:
                        response += f" | 📅 {todo['due_date']}"
                    
                    response += f"{overdue_marker}\n\n"
                
                response += f"📊 **统计**: 待办 {stats['pending']} | 进行中 {stats['in_progress']} | 已完成 {stats['completed']}"
                if stats['overdue'] > 0:
                    response += f" | ⚠️ 逾期 {stats['overdue']}"
                
                return response
            else:
                return f"❌ 获取列表失败: {result['error']}"
        
        elif action == 'complete':
            if result['success']:
                return f"✅ {result['message']}"
            else:
                return f"❌ 完成失败: {result['error']}"
        
        elif action == 'delete':
            if result['success']:
                return f"🗑️ {result['message']}"
            else:
                return f"❌ 删除失败: {result['error']}"
        
        elif action == 'summary':
            if result['success']:
                summary = result['summary']
                response = f"📅 **今日代办摘要** ({summary['date']})\n\n"
                
                if summary['overdue'] > 0:
                    response += f"⚠️ 逾期事项: {summary['overdue']} 项\n"
                
                if summary['due_today'] > 0:
                    response += f"📅 今日到期: {summary['due_today']} 项\n"
                
                if summary['high_priority'] > 0:
                    response += f"🔴 高优先级: {summary['high_priority']} 项\n"
                
                response += f"✅ 今日完成: {summary['completed_today']} 项"
                
                if summary['overdue'] == 0 and summary['due_today'] == 0:
                    response += "\n\n🎉 今日无逾期或到期事项！"
                
                return response
            else:
                return f"❌ 获取摘要失败: {result['error']}"
        
        elif action == 'help':
            return """📋 **代办事项管理帮助**

🔧 **命令格式**:
• `todo add <标题> [@分类] [@优先级] [@日期]` - 创建代办事项
• `todo list [状态] [分类]` - 查看列表
• `todo done <ID>` - 完成事项
• `todo delete <ID>` - 删除事项
• `todo today` - 今日摘要

📝 **示例**:
• `todo add 完成项目文档 @work @high @2024-01-15`
• `todo list pending work`
• `todo done 123`

🏷️ **分类**: personal, work, jira, meeting
⚡ **优先级**: low, medium, high, urgent
📅 **日期格式**: YYYY-MM-DD"""
        
        else:
            return "❓ 未知操作"
    
    async def process_command(self, command_text: str, user_id: str, user_email: str) -> Dict:
        """
        处理Todo命令的主入口方法
        
        Args:
            command_text: 命令文本 (不包含 "todo " 前缀)
            user_id: 用户ID
            user_email: 用户邮箱
            
        Returns:
            处理结果字典
        """
        try:
            # 解析命令
            parsed_command = self.parse_todo_command(f"todo {command_text}")
            
            if 'error' in parsed_command:
                return {
                    'success': False,
                    'error': parsed_command['error'],
                    'action': parsed_command['action']
                }
            
            action = parsed_command['action']
            
            # 根据动作执行相应操作
            if action == 'create':
                result = await self.create_todo(
                    user_id=user_id,
                    user_email=user_email,
                    employee_code=user_id,  # 使用user_id作为employee_code
                    title=parsed_command['title'],
                    category=parsed_command['category'],
                    priority=parsed_command['priority'],
                    due_date=parsed_command['due_date']
                )
                
            elif action == 'list':
                result = await self.list_todos(
                    user_id=user_id,
                    status=parsed_command.get('status'),
                    category=parsed_command.get('category')
                )
                
            elif action == 'complete':
                result = await self.update_todo_status(
                    user_id=user_id,
                    todo_id=parsed_command['todo_id'],
                    new_status='completed'
                )
                
            elif action == 'delete':
                result = await self.delete_todo(
                    user_id=user_id,
                    todo_id=parsed_command['todo_id']
                )
                
            elif action == 'summary':
                result = await self.get_daily_summary(user_id=user_id)
                
            elif action == 'help':
                result = {
                    'success': True,
                    'message': 'help'
                }
                
            else:
                result = {
                    'success': False,
                    'error': f'未支持的操作: {action}'
                }
            
            # 格式化响应
            if result['success']:
                response_text = self.format_todo_response(action, result)
                return {
                    'success': True,
                    'response': response_text,
                    'action': action
                }
            else:
                return {
                    'success': False,
                    'error': result['error'],
                    'action': action
                }
                
        except Exception as e:
            logger.error(f"处理Todo命令异常: {str(e)}")
            return {
                'success': False,
                'error': f'命令处理异常: {str(e)}',
                'action': 'unknown'
            }


# 全局实例
todo_manager = TodoManager() 
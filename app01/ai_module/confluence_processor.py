"""
Confluence 文档处理器
负责从 Confluence 获取文档内容并进行 AI 处理
"""

import asyncio
import aiohttp
import logging
import re
import os
from typing import Dict, List, Optional
from datetime import datetime
from urllib.parse import urlparse, urljoin, parse_qs, unquote
from bs4 import BeautifulSoup
import html2text

from .document_processor import (
    DocumentProcessor, DocumentType, ProcessingAction, 
    DocumentSource, ProcessingRequest, ProcessingResult
)
from .multi_llm_client import multi_llm_client
from .document_config import document_config

logger = logging.getLogger(__name__)

# 添加ic调试
try:
    from icecream import ic
except ImportError:
    def ic(*args):
        if args:
            print(f"DEBUG: {args}")
        return args[0] if args else None


class ConfluenceProcessor(DocumentProcessor):
    """Confluence 文档处理器"""
    
    def __init__(self, config: Dict = None):
        super().__init__(config)
        
        # 只在调试模式下显示详细日志
        debug_mode = os.getenv('DOCUMENT_CONFIG_DEBUG', 'false').lower() == 'true'
        
        self.supported_actions = {
            ProcessingAction.SUMMARIZE,
            ProcessingAction.TRANSLATE,
            ProcessingAction.QA,
            ProcessingAction.EXTRACT_INFO,
            ProcessingAction.ANALYZE
        }
        
        # Confluence 配置
        # 检查传入的config是否已经是confluence配置，还是包含confluence键的全局配置
        if 'confluence' in self.config:
            self.confluence_config = self.config.get('confluence', {})
        else:
            # 传入的config本身就是confluence配置
            self.confluence_config = self.config
        
        self.auth_token = self.confluence_config.get('auth_token')
        self.username = self.confluence_config.get('username')
        self.password = self.confluence_config.get('password')
        self.session_timeout = self.confluence_config.get('session_timeout', 30)
        
        if debug_mode:
            ic("ConfluenceProcessor 初始化完成")
        
        # HTML 转换器配置
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.html_converter.ignore_images = True
        self.html_converter.ignore_tables = False
        self.html_converter.body_width = 0  # 不限制宽度
    
    def supports_document_type(self, doc_type: DocumentType) -> bool:
        """检查是否支持指定的文档类型"""
        return doc_type == DocumentType.CONFLUENCE
    
    async def fetch_document(self, source: DocumentSource) -> Dict:
        """
        获取 Confluence 文档内容
        
        Args:
            source: 文档源信息
            
        Returns:
            文档内容信息
        """
        try:
            url = source.url
            ic("fetch_document 开始")
            ic("请求URL:", url)
            
            # 解析 URL 获取页面信息
            page_info = self._parse_confluence_url(url)
            if not page_info:
                ic("❌ URL解析失败")
                return {
                    'success': False,
                    'error': '无法解析 Confluence URL'
                }
            
            ic("✅ URL解析成功:", page_info)
            
            # 构建 API URL
            api_url = self._build_api_url(page_info)
            ic("构建的API URL:", api_url)
            
            # 获取页面内容
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.session_timeout)) as session:
                headers = self._build_headers()
                ic("即将发送请求，headers:", headers)
                
                async with session.get(api_url, headers=headers) as response:
                    ic("收到响应，状态码:", response.status)
                    ic("响应头:", dict(response.headers))
                    
                    if response.status == 200:
                        ic("✅ 状态码200，尝试解析JSON")
                        try:
                            data = await response.json()
                            ic("✅ JSON解析成功，数据键:", list(data.keys()) if isinstance(data, dict) else "非字典类型")
                            
                            # 解析页面数据
                            page_data = self._parse_page_data(data)
                            ic("✅ 页面数据解析完成，标题:", page_data.get('title', 'N/A'))
                            
                            return {
                                'success': True,
                                'title': page_data['title'],
                                'content': page_data['content'],
                                'plain_text': page_data['plain_text'],
                                'metadata': {
                                    'page_id': page_data['page_id'],
                                    'space_key': page_data['space_key'],
                                    'created_date': page_data.get('created_date'),
                                    'modified_date': page_data.get('modified_date'),
                                    'author': page_data.get('author'),
                                    'url': url
                                }
                            }
                        except Exception as json_error:
                            ic("❌ JSON解析失败:", str(json_error))
                            error_text = await response.text()
                            ic("响应内容前500字符:", error_text[:500])
                            return {
                                'success': False,
                                'error': f'JSON解析失败: {str(json_error)}'
                            }
                    else:
                        ic("❌ 非200状态码")
                        error_text = await response.text()
                        ic("错误响应内容前500字符:", error_text[:500])
                        logger.error(f"Confluence API 请求失败: {response.status}, {error_text}")
                        
                        return {
                            'success': False,
                            'error': f'获取 Confluence 页面失败: HTTP {response.status}'
                        }
                        
        except Exception as e:
            ic("❌ fetch_document异常:", str(e))
            logger.error(f"获取 Confluence 文档失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取文档失败: {str(e)}'
            }
    
    async def process_document(self, request: ProcessingRequest) -> ProcessingResult:
        """
        处理 Confluence 文档请求
        
        Args:
            request: 处理请求
            
        Returns:
            处理结果
        """
        try:
            # 1. 获取文档内容
            fetch_result = await self.fetch_document(request.source)
            if not fetch_result['success']:
                return ProcessingResult(
                    success=False,
                    error=fetch_result['error']
                )
            
            # 2. 根据处理动作执行相应操作
            content = fetch_result['plain_text']
            title = fetch_result['title']
            metadata = fetch_result['metadata']
            
            # 截断过长的内容
            content = self.truncate_content(content)
            
            if request.action == ProcessingAction.SUMMARIZE:
                result = await self._summarize_content(content, title, request.user_query)
            elif request.action == ProcessingAction.TRANSLATE:
                target_lang = request.parameters.get('target_language', '中文')
                result = await self._translate_content(content, title, target_lang)
            elif request.action == ProcessingAction.QA:
                result = await self._answer_question(content, title, request.user_query)
            elif request.action == ProcessingAction.EXTRACT_INFO:
                result = await self._extract_key_info(content, title, request.user_query)
            elif request.action == ProcessingAction.ANALYZE:
                result = await self._analyze_content(content, title, request.user_query)
            else:
                return ProcessingResult(
                    success=False,
                    error=f'不支持的处理动作: {request.action.value}'
                )
            
            if result['success']:
                return ProcessingResult(
                    success=True,
                    content=result['content'],
                    summary=result.get('summary', ''),
                    metadata={
                        **metadata,
                        'action': request.action.value,
                        'processed_at': datetime.now().isoformat()
                    }
                )
            else:
                return ProcessingResult(
                    success=False,
                    error=result['error']
                )
                
        except Exception as e:
            logger.error(f"处理 Confluence 文档失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error=f'处理文档失败: {str(e)}'
            )
    
    def _parse_confluence_url(self, url: str) -> Optional[Dict]:
        """
        解析 Confluence URL 获取页面信息
        
        Args:
            url: Confluence URL
            
        Returns:
            页面信息字典或 None
        """
        try:
            ic("_parse_confluence_url 开始")
            ic("输入URL:", url)
            
            parsed = urlparse(url)
            base_url = f"{parsed.scheme}://{parsed.netloc}"
            ic("解析出的base_url:", base_url)
            ic("路径部分:", parsed.path)
            
            # 常见的 Confluence URL 模式
            patterns = [
                r'/wiki/spaces/([^/]+)/pages/(\d+)',  # /wiki/spaces/SPACE/pages/123456
                r'/wiki/display/([^/]+)/([^?]+)',      # /wiki/display/SPACE/Page+Title
                r'/display/([^/]+)/([^?]+)',           # /display/SPACE/Page+Title (不带wiki前缀)
                r'/pages/viewpage\.action\?pageId=(\d+)',  # /pages/viewpage.action?pageId=123456
            ]
            
            # 检查传统URL模式
            for i, pattern in enumerate(patterns):
                ic(f"尝试模式 {i+1}: {pattern}")
                match = re.search(pattern, url)
                if match:
                    ic(f"✅ 匹配成功，groups: {match.groups()}")
                    if 'pages/' in pattern and len(match.groups()) == 2:
                        # spaces/SPACE/pages/ID 格式
                        result = {
                            'base_url': base_url,
                            'space_key': match.group(1),
                            'page_id': match.group(2),
                            'type': 'page_id'
                        }
                        ic("✅ 返回page_id类型结果:", result)
                        return result
                    elif 'display/' in pattern:
                        # display/SPACE/Title 格式
                        raw_title = match.group(2)
                        ic(f"原始页面标题: {raw_title}")
                        # 先进行URL解码，再替换+为空格
                        page_title = unquote(raw_title).replace('+', ' ')
                        ic(f"处理后的页面标题: {page_title}")
                        result = {
                            'base_url': base_url,
                            'space_key': match.group(1),
                            'page_title': page_title,
                            'type': 'page_title'
                        }
                        ic("✅ 返回page_title类型结果:", result)
                        return result
                    elif 'pageId=' in pattern:
                        # pageId=ID 格式
                        result = {
                            'base_url': base_url,
                            'page_id': match.group(1),
                            'type': 'page_id'
                        }
                        ic("✅ 返回pageId类型结果:", result)
                        return result
                else:
                    ic("❌ 不匹配")
            
            # 检查新格式：/pages/viewpage.action?spaceKey=XXX&title=XXX
            if '/pages/viewpage.action' in url and parsed.query:
                ic("检查viewpage.action格式")
                query_params = parse_qs(parsed.query)
                ic("查询参数:", query_params)
                
                # 检查是否有spaceKey和title参数
                if 'spaceKey' in query_params and 'title' in query_params:
                    space_key = query_params['spaceKey'][0]
                    page_title = unquote(query_params['title'][0]).replace('+', ' ')
                    
                    result = {
                        'base_url': base_url,
                        'space_key': space_key,
                        'page_title': page_title,
                        'type': 'page_title'
                    }
                    ic("✅ 返回spaceKey+title类型结果:", result)
                    return result
                
                # 检查是否只有pageId参数
                elif 'pageId' in query_params:
                    page_id = query_params['pageId'][0]
                    result = {
                        'base_url': base_url,
                        'page_id': page_id,
                        'type': 'page_id'
                    }
                    ic("✅ 返回pageId参数类型结果:", result)
                    return result
            
            ic("❌ 所有模式都不匹配")
            logger.warning(f"无法解析 Confluence URL: {url}")
            return None
            
        except Exception as e:
            ic("❌ 解析异常:", str(e))
            logger.error(f"解析 Confluence URL 失败: {str(e)}")
            return None
    
    def _build_api_url(self, page_info: Dict) -> str:
        """
        构建 Confluence REST API URL
        
        Args:
            page_info: 页面信息
            
        Returns:
            API URL
        """
        base_url = page_info['base_url']
        
        if page_info['type'] == 'page_id':
            # 通过页面ID获取
            page_id = page_info['page_id']
            return f"{base_url}/rest/api/content/{page_id}?expand=body.storage,space,history,version"
        else:
            # 通过空间和标题获取
            space_key = page_info['space_key']
            page_title = page_info['page_title']
            return f"{base_url}/rest/api/content?spaceKey={space_key}&title={page_title}&expand=body.storage,space,history,version"
    
    def _build_headers(self) -> Dict:
        """构建请求头"""
        debug_mode = os.getenv('DOCUMENT_CONFIG_DEBUG', 'false').lower() == 'true'
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        # 添加认证信息
        if self.auth_token:
            if debug_mode:
                ic("使用auth_token进行Bearer认证")
            # Confluence API token 应该使用 Bearer 认证
            headers['Authorization'] = f'Bearer {self.auth_token}'
        elif self.username and self.password:
            if debug_mode:
                ic("使用username/password进行Basic认证")
            import base64
            credentials = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            headers['Authorization'] = f'Basic {credentials}'
        else:
            if debug_mode:
                ic("❌ 没有可用的认证信息！")
        
        ic("最终的headers:", headers)
        return headers
    
    def _parse_page_data(self, data: Dict) -> Dict:
        """
        解析 Confluence API 返回的页面数据
        
        Args:
            data: API 返回的数据
            
        Returns:
            解析后的页面数据
        """
        try:
            # 处理单页面和多页面结果
            if 'results' in data and data['results']:
                page_data = data['results'][0]  # 取第一个结果
            else:
                page_data = data
            
            # 提取基本信息
            title = page_data.get('title', '')
            page_id = page_data.get('id', '')
            
            # 提取空间信息
            space_info = page_data.get('space', {})
            space_key = space_info.get('key', '')
            
            # 提取内容
            body = page_data.get('body', {})
            storage = body.get('storage', {})
            html_content = storage.get('value', '')
            
            # 转换 HTML 为纯文本
            plain_text = self._html_to_text(html_content)
            
            # 提取版本和历史信息
            version_info = page_data.get('version', {})
            history_info = page_data.get('history', {})
            
            return {
                'title': title,
                'page_id': page_id,
                'space_key': space_key,
                'content': html_content,
                'plain_text': plain_text,
                'created_date': history_info.get('createdDate'),
                'modified_date': version_info.get('when'),
                'author': version_info.get('by', {}).get('displayName')
            }
            
        except Exception as e:
            logger.error(f"解析页面数据失败: {str(e)}")
            return {
                'title': '',
                'page_id': '',
                'space_key': '',
                'content': '',
                'plain_text': ''
            }
    
    def _html_to_text(self, html_content: str) -> str:
        """
        将 HTML 内容转换为纯文本
        
        Args:
            html_content: HTML 内容
            
        Returns:
            纯文本内容
        """
        try:
            if not html_content:
                return ''
            
            # 使用 html2text 转换
            text = self.html_converter.handle(html_content)
            
            # 清理多余的空行
            lines = text.split('\n')
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
                    prev_empty = False
                elif not prev_empty:
                    cleaned_lines.append('')
                    prev_empty = True
            
            return '\n'.join(cleaned_lines).strip()
            
        except Exception as e:
            logger.error(f"HTML 转文本失败: {str(e)}")
            # 降级使用 BeautifulSoup
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup.get_text(separator='\n').strip()
            except:
                return html_content
    
    async def _summarize_content(self, content: str, title: str, user_query: str) -> Dict:
        """总结内容"""
        try:
            prompt = f"""请总结以下 Confluence 文档的内容。

文档标题: {title}
用户查询: {user_query}

文档内容:
{content}

请提供一个简洁的总结，包括：
1. 文档的主要内容和目的
2. 关键要点和结论
3. 如果用户有特定问题，请针对性回答

请用中文回答，格式清晰。"""

            result = await multi_llm_client.generate_with_retry(prompt)
            if not result['success']:
                raise Exception(result['error'])
            response = result['content']
            
            return {
                'success': True,
                'content': response,
                'summary': f"已总结文档《{title}》的内容"
            }
            
        except Exception as e:
            logger.error(f"总结内容失败: {str(e)}")
            return {
                'success': False,
                'error': f'总结失败: {str(e)}'
            }
    
    async def _translate_content(self, content: str, title: str, target_language: str) -> Dict:
        """翻译内容"""
        try:
            prompt = f"""请将以下 Confluence 文档翻译为{target_language}。

文档标题: {title}

文档内容:
{content}

请保持原文的格式和结构，提供准确的翻译。"""

            result = await multi_llm_client.generate_with_retry(prompt)
            if not result['success']:
                raise Exception(result['error'])
            response = result['content']
            
            return {
                'success': True,
                'content': response,
                'summary': f"已将文档《{title}》翻译为{target_language}"
            }
            
        except Exception as e:
            logger.error(f"翻译内容失败: {str(e)}")
            return {
                'success': False,
                'error': f'翻译失败: {str(e)}'
            }
    
    async def _answer_question(self, content: str, title: str, user_query: str) -> Dict:
        """基于文档内容回答问题"""
        try:
            prompt = f"""基于以下 Confluence 文档内容，请回答用户的问题。

文档标题: {title}
用户问题: {user_query}

文档内容:
{content}

请基于文档内容准确回答用户的问题。如果文档中没有相关信息，请明确说明。
请用中文回答，格式清晰。"""

            result = await multi_llm_client.generate_with_retry(prompt)
            if not result['success']:
                raise Exception(result['error'])
            response = result['content']
            
            return {
                'success': True,
                'content': response,
                'summary': f"基于文档《{title}》回答了用户问题"
            }
            
        except Exception as e:
            logger.error(f"问答处理失败: {str(e)}")
            return {
                'success': False,
                'error': f'问答失败: {str(e)}'
            }
    
    async def _extract_key_info(self, content: str, title: str, user_query: str) -> Dict:
        """提取关键信息"""
        try:
            prompt = f"""请从以下 Confluence 文档中提取关键信息。

文档标题: {title}
用户需求: {user_query}

文档内容:
{content}

请提取并整理以下信息：
1. 核心概念和定义
2. 重要的数据和指标
3. 关键的流程步骤
4. 重要的链接和资源
5. 注意事项和风险点

如果用户有特定的信息需求，请重点关注相关内容。
请用中文回答，使用清晰的格式。"""

            result = await multi_llm_client.generate_with_retry(prompt)
            if not result['success']:
                raise Exception(result['error'])
            response = result['content']
            
            return {
                'success': True,
                'content': response,
                'summary': f"已从文档《{title}》中提取关键信息"
            }
            
        except Exception as e:
            logger.error(f"信息提取失败: {str(e)}")
            return {
                'success': False,
                'error': f'信息提取失败: {str(e)}'
            }
    
    async def _analyze_content(self, content: str, title: str, user_query: str) -> Dict:
        """分析内容"""
        try:
            prompt = f"""请深入分析以下 Confluence 文档的内容。

文档标题: {title}
分析要求: {user_query}

文档内容:
{content}

请提供深入的分析，包括：
1. 内容的完整性和准确性评估
2. 文档结构和逻辑分析
3. 潜在的改进建议
4. 相关的风险或机会点
5. 与其他系统或流程的关联性

请用中文回答，提供结构化的分析报告。"""

            result = await multi_llm_client.generate_with_retry(prompt)
            if not result['success']:
                raise Exception(result['error'])
            response = result['content']
            
            return {
                'success': True,
                'content': response,
                'summary': f"已对文档《{title}》进行深入分析"
            }
            
        except Exception as e:
            logger.error(f"内容分析失败: {str(e)}")
            return {
                'success': False,
                'error': f'分析失败: {str(e)}'
            }


# 创建 Confluence 处理器实例
confluence_processor = ConfluenceProcessor(document_config.get_config()) 
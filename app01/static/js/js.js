window.addEventListener("load", function () {

    if (axClient == 'phone') {
        document.querySelectorAll('.swicth').forEach(function(item){
            item.style.display = 'none';
            item.parentNode.cssText = '';
        })
    }
    document.querySelectorAll('.swicth').forEach(function(item){

        if(item.querySelector('.default')){
            item.querySelector('.default').onclick = function(){
                this.parentNode.parentNode.style.width = '100%';
            }
        }
        if(item.querySelector('.pad-v')){
            item.querySelector('.pad-v').onclick = function(){
            this.parentNode.parentNode.style.width = '780px';
            }
        }
        if(item.querySelector('.mb')){
            item.querySelector('.mb').onclick = function(){
                this.parentNode.parentNode.style.width = '380px';
            }
        }
    })

    if (document.querySelector('#ax-open-menu')) {
        document.querySelector('#ax-open-menu').onclick = function () {
            axToggle('main .menu')
            if (this.classList.contains('ax-x')) {
                this.classList.remove("ax-x");
                this.classList.add("ax-e");
            } else {
                this.classList.remove("ax-e");
                this.classList.add("ax-x");
            }
        };
    }
    //棣栭〉鎵撳紑浜岀淮鐮�
    let qrs = [];
    this.document.querySelectorAll('.index-platform .qr').forEach(function(item){
        qrs.push(item.lastElementChild);
        item.onclick = function(e){
            let qr = this.lastElementChild;
            axPreventDefault(e);
            axFadeToggle(qr);

            let rest = qrs.filter(function(i){
                return i !== qr;
            })
            rest.forEach(function(i){
                axFadeOut(i);
            })
        }
    })
    //
    document.querySelector(".media_loading,.side_ser .load") ? fadeOut(document.querySelector(".media_loading,.side_ser .load")) : null;
}, false);
window.addEventListener("scroll", function () {

}, false);

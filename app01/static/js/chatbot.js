
function ifclickAdd() {

    var name = "weibin";
    var l = name.length;
    console.log(name);
    console.log(l);
    var tag = document.getElementById("weibin");
    var v2 = tag.innerText;
    alert(v2);


}

function update() {
    // 调用自己的接口去查询数据库的执行状态

}

function updateStatus() {
    var tag = document.getElementById("1");
    var widthnow = parseInt(tag.style.width);
    if (widthnow < 100) {
        widthnow += 10;
        var nowvalue = "width:" + widthnow + "%"
        tag.setAttribute("style", nowvalue)
        console.log(tag.style.width);
        tag.setAttribute("style", nowvalue)
        console.log(tag.style.width);
    }

}

function Get(){
                ajax({
                type:"get",
                url:"http://route.showapi.com/341-1?showapi_appid=63668&showapi_sign=31c73b7db6b34ed59250ecb5c370b6e2",
                dataType:"json",
                success:function(responseData){
                    var data = responseData.showapi_res_body.contentlist;
                    // console.log(data);
                    var html = "";
                    data.forEach(function(txt){
                        html += `<div>
                                    <h3>${txt.title}</h3>
                                    <p>${txt.text}</p>
                                </div>`;
                    });
                    document.getElementById("txt").innerHTML = html;

                }
            });
                axios
  .get("/user?ID=12345")
  .then(function (response) {
    //处理成功情况
    console.log(response);
  })
  .catch(function (error) {
    //处理错误情况
    console.log(error);
  })
  .then(function () {
    //总是会执行
  });

}

setInterval(updateStatus, 1000);

@charset "utf-8";
/*
 *Last modified: 2022-06-27 21:30:00
 *Url: https://www.axui.cn
*/
/*全局设置*/
:root {
  --nav-width: 22rem;
}
.ax-demo-admin {
  width: 100%;
  height: 100%;
}
/*主导航*/
.ax-demo-admin nav {
  border-right: 1px solid #ebebeb;
  background-color: #fff;
  width: calc(var(--nav-width) + 1px);
  transition-property: width,left;
  transition-duration: 200ms;
  transition-timing-function: ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 630;
}
.ax-demo-admin .ax-nav-overlay {
  background-color: rgba(0, 0, 0, 0.4);
  width: 100vw;
  height: 100vh;
  opacity: 0;
  transition: opacity 200ms ease;
  position: fixed;
  left: -10000px;
  top: 0;
  z-index: 620;
}
.ax-demo-admin nav .ax-nav-header {
  border-bottom: 1px solid #ebebeb;
  position: relative;
  height: 4.4rem;
  overflow: hidden;
  flex-shrink: 0;
}
.ax-demo-admin nav .ax-nav-header .ax-logo {
  position: relative;
}
.ax-demo-admin nav .ax-nav-header .ax-logo img {
  height: 4.4rem;
}
.ax-demo-admin nav .ax-nav-header .ax-close-nav {
  color: #333333;
  width: 4.4rem;
  height: 4.4rem;
  line-height: 4.4rem;
  text-align: center;
  position: absolute;
  right: 0;
  top: 0;
  background-color: #fff;
  z-index: 2;
}
.ax-demo-admin nav .ax-nav-main {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}
.ax-demo-admin nav .ax-nav-main .ax-nav-search {
  height: 3.8rem;
  line-height: 3.8rem;
  border-bottom: 1px solid #ebebeb;
  position: relative;
  display: none;
}
.ax-demo-admin nav .ax-nav-main .ax-nav-search input {
  padding: 0 1.4rem;
  border: none;
}
.ax-demo-admin nav .ax-nav-main .ax-nav-search input:focus {
  box-shadow: none;
}
.ax-demo-admin nav .ax-nav-main .ax-nav-search *[class*="font"] {
  font-size: 1.2rem;
  color: #b3b3b3;
  width: 3.8rem;
  height: 3.8rem;
  line-height: 3.8rem;
  text-align: center;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}
/*菜单折叠*/
.ax-demo-admin nav.ax-nav-fold {
  width: 5.2rem;
}
.ax-demo-admin nav.ax-nav-fold .ax-nav-header .ax-close-nav {
  width: 5.2rem;
  transform: rotateY(180deg);
}
.ax-demo-admin nav.ax-nav-fold .ax-menu .ax-legend {
  margin-right: 1.4rem;
}
.ax-demo-admin nav.ax-nav-fold-all {
  width: 0;
}
.ax-demo-admin nav .ax-mask {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0);
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
  display: none;
}
.ax-demo-admin nav.ax-nav-fold .ax-mask {
  display: block;
}
/*折叠后菜单的ul收起来*/
.ax-demo-admin nav.ax-nav-fold .ax-menu > li > ul {
  display: none!important;
}
/*页面头部*/
.ax-demo-admin header {
  width: calc(100% - var(--nav-width));
  background-color: #fff;
  border-bottom: 1px solid #ebebeb;
  transition: all 200ms ease;
  position: fixed;
  left: var(--nav-width);
  top: 0;
  z-index: 2;
}
.ax-demo-admin header ~ .ax-space-header {
  height: 4.4rem;
}
.ax-demo-admin nav.ax-nav-fold ~ header {
  width: calc(100% - 5.2rem);
  left: 5.2rem;
}
.ax-demo-admin header .ax-breadcrumb {
  height: 4.4rem;
  line-height: 4.4rem;
  padding-left: 1.4rem;
}
.ax-demo-admin header .ax-header-icons a {
  color: #adc1cc;
  font-size: 1.6rem;
  float: left;
  width: 3.6rem;
  line-height: 4.4rem;
  text-align: center;
  position: relative;
  transition: all 200ms ease;
}
.ax-demo-admin header .ax-header-icons a:hover {
  color: #198cff;
}
.ax-demo-admin header .ax-header-icons a .ax-dot {
  position: absolute;
  right: 0.4rem;
  top: 0.8rem;
}
.ax-header-icons a .ax-badge {
  position: absolute;
  right: 0px;
  top: 0.4rem;
}
.ax-demo-admin header .ax-header-search {
  position: relative;
  margin: calc((4.4rem - 2.8rem)/2);
}
.ax-demo-admin header .ax-header-search input[type="text"] {
  height: 2.8rem;
  line-height: 2.8rem;
}
.ax-demo-admin header .ax-header-search *[class*="font"] {
  font-size: 1.2rem;
  color: #b3b3b3;
  width: 2.8rem;
  height: 2.8rem;
  line-height: 2.8rem;
  text-align: center;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}
.ax-demo-admin header .ax-header-search *[class*="font"]:hover {
  color: #198cff;
  font-size: 1.4rem;
}
.ax-demo-admin header .ax-header-user {
  margin: calc((4.4rem - 2.8rem)/2);
}
/*页面主体*/
.ax-demo-admin main {
  transition: all 200ms ease;
  margin-left: var(--nav-width);
  min-width: 1000px;
  max-width: calc(100% - var(--nav-width) - 12rem);
  overflow-x: hidden;
}
.ax-demo-admin nav.ax-nav-fold ~ main {
  margin-left: 5.2rem;
  max-width: calc(100% - 5.2rem - 12rem);
}
/*页面底部版权*/
.ax-demo-admin footer {
  padding: 0 1.4rem;
  text-align: center;
  color: #b3b3b3;
}
.ax-demo-admin footer a {
  color: #b3b3b3;
}
/*适配手机和平板*/
@media screen and (max-width: 900px) {
  .ax-demo-admin nav.ax-nav-fold ~ main,
  .ax-demo-admin main {
    margin-left: 0;
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }
  .ax-demo-admin nav.ax-nav-fold ~ header,
  .ax-demo-admin header {
    width: 100%;
    left: 0px;
    padding-left: 5.2rem;
  }
  .ax-demo-admin nav.ax-hide ~ header {
    padding-left: 5.2rem;
  }
  .ax-demo-admin .ax-nav-overlay.ax-correction {
    left: 0;
  }
  .ax-demo-admin .ax-nav-overlay.ax-show {
    opacity: 1;
  }
  .ax-demo-admin .ax-nav-overlay.ax-hide {
    left: -10000px;
  }
  .ax-demo-admin header .ax-header-search {
    width: 110px;
  }
  .ax-demo-admin header .ax-breadcrumb {
    padding-left: 0;
  }
  .ax-demo-admin nav.ax-nav-fold {
    width: var(--nav-width);
    left: calc(-1 * var(--nav-width) - 1px);
  }
  .ax-demo-admin nav.ax-nav-fold .ax-mask {
    display: none;
  }
  .ax-demo-admin nav .ax-nav-header {
    overflow: visible;
  }
  .ax-demo-admin nav.ax-nav-fold .ax-nav-header .ax-close-nav {
    right: -5.2rem;
  }
  .ax-demo-admin nav .ax-nav-main .ax-nav-search {
    display: block;
  }
  .ax-demo-admin header .ax-header-search {
    display: none;
  }
}

@charset "utf-8";
/*全局*/
body,
html {
  height: auto;
  overflow-y: auto;
  background-color: #fff;
}
body.index {
  background-image: url(../images/bg-left.png), url(../images/bg-right.png);
  background-repeat: no-repeat;
  background-position: left top, right 26rem;
}
header .ax-row {
  width: 120rem;
  margin: 0 auto;
}
header .ax-row .ax-btns {
  padding-right: 0;
}
header.frame-header {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
}
.outer {
  width: 120rem;
  margin: 0 auto;
}
header .logo {
  margin: 0 auto;
}
header .logo img {
  height: 6rem;
}
nav.frame-nav {
  line-height: 6rem;
}
nav.frame-nav a {
  text-align: center;
  width: 8rem;
  position: relative;
}
nav.frame-nav a.selected {
  color: #198cff;
}
nav.frame-nav a.selected:before {
  content: '';
  height: 0.6rem;
  width: 0.6rem;
  border-radius: 100%;
  background-color: #198cff;
  position: absolute;
  left: 50%;
  bottom: 1rem;
  margin-left: -0.3rem;
}
footer.frame-footer {
  text-align: center;
  color: #808080;
}
footer.frame-footer .cr {
  text-align: center;
  padding: 4.4rem 0;
}
footer.frame-footer .cr .h01 a {
  color: #808080;
}
footer.frame-footer .cr .h01 a:hover {
  color: #198cff;
}
footer.frame-footer .cr .h02 a {
  margin: 0 1.4rem;
}
main .menu {
  width: 25rem;
}
.site-nav {
  display: none;
}
/**/
main .content {
  margin-left: 2.2rem;
}
main .content > * > h1 {
  font-size: 2.2rem;
  line-height: 4.4rem;
  font-weight: normal;
}
main .content > * > h2 {
  font-size: 1.6rem;
  font-weight: bold;
}
main .content h2 * {
  vertical-align: middle;
}
main .content > * > h3 {
  font-size: 1.4rem;
  font-weight: bold;
}
.para {
  border: 1px solid #ebebeb;
  border-radius: 0.6rem;
  box-sizing: border-box;
  padding: 4.4rem 0.8rem 0.8rem 0.8rem;
  margin: 2.8rem auto;
  position: relative;
}
.para:before {
  content: 'API/参数表';
  position: absolute;
  left: 0;
  top: 0;
  line-height: 4.4rem;
  padding: 0 1rem;
  color: #808080;
}
.para .inner {
  background-color: #fff;
  box-sizing: border-box;
}
.para .ax-table td,
.para .ax-table th {
  text-align: left;
}
.para .ax-table tbody tr:hover {
  background: transparent;
}
.para .ax-table tr {
  border-top: 1px solid #f0f0f0;
  border-bottom: none;
}
.inline {
  border: 1px solid #ebebeb;
  border-radius: 0.6rem;
  box-sizing: border-box;
  padding: 1.4rem;
  margin: 2.8rem auto;
  position: relative;
}
.inline .inner {
  background-color: #fff;
  box-sizing: border-box;
}
.inline .inner pre {
  font-family: "微软雅黑", "microsoft yahei", "Arial", "Helvetica Neue", "sans-serif", "宋体", "simsun";
  color: #666666;
  line-height: 2.2rem;
}
.inline .ax-table td,
.para .ax-table th {
  text-align: left;
}
.inline .ax-table tbody tr:hover {
  background: transparent;
}
.inline .ax-table tr {
  border-top: none;
  border-bottom: none;
}
.demo {
  border: 1px solid #ebebeb;
  border-radius: 0.6rem;
  background-color: #fafafa;
  box-sizing: border-box;
  padding: 4.4rem 0.8rem 0.8rem 0.8rem;
  margin: 2.8rem auto;
  position: relative;
  z-index: 4;
}
.demo:before {
  content: '效果演示';
  position: absolute;
  left: 0;
  top: 0;
  line-height: 4.4rem;
  padding: 0 1rem;
  color: #808080;
}
.demo .inner {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  box-sizing: border-box;
  position: relative;
}
.demo .swicth {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  line-height: 4.4rem;
  color: #666666;
}
.demo .swicth a {
  padding: 0 1rem;
  position: relative;
}
.demo .swicth a:after {
  width: 1px;
  height: 1rem;
  background-color: #b3b3b3;
  right: 0;
  top: 50%;
  margin-top: -0.5rem;
  position: absolute;
  content: '';
}
.demo .swicth a:last-child:after {
  display: none;
}
.tip {
  padding: 1.4rem;
  background-color: rgba(25, 140, 255, 0.06);
  border-radius: 0.4rem;
  border-left: 0.4rem solid #198cff;
  margin: 2.8rem 0;
  line-height: 2.2rem;
}
.txt code {
  color: #ff8400;
  background-color: rgba(255, 132, 0, 0.1);
  border: 1px solid rgba(255, 132, 0, 0.2);
  border-radius: 0.3rem;
  font-size: 1.2rem;
  padding: 0 0.4rem;
  margin: 0 0.4rem;
  font-family: Consolas, Menlo, Courier, monospace;
}
.txt ul {
  padding-left: 2.8rem;
}
.txt ul li {
  position: relative;
}
.txt ul li:before {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #ccc;
  border-radius: 100%;
  content: '';
  position: absolute;
  left: -1.2rem;
  top: 1.2rem;
}
.txt img {
  max-width: 40rem;
  border: 1px solid #ebebeb;
  box-sizing: border-box;
}
.index-intro {
  text-align: left;
}
.index-intro .outer {
  padding: 16rem 0 10rem 0;
  background-image: url(../images/bg-header.png);
  background-repeat: no-repeat;
  background-position: right center;
  position: relative;
}
.index-intro .h01 {
  font-size: 2.2rem;
  line-height: 5.4rem;
}
.index-intro .h02 {
  color: #808080;
  padding-bottom: calc(1.4rem*2);
}
.index-intro .h03 {
  padding-bottom: calc(1.4rem*2);
}
.index-intro .h04 {
  font-size: 1.2rem;
}
.index-sta {
  margin: calc(1.4rem*4) auto;
}
.index-for {
  margin: calc(1.4rem*2) auto;
}
.index-for .item {
  border-radius: calc(0.3rem*4);
  border: 1px solid #ebebeb;
  background-color: #fff;
  box-shadow: 1px 1px 0.5rem rgba(0, 0, 0, 0.06);
  margin-bottom: calc(1.4rem*2);
}
.index-for .h01 {
  text-align: center;
  margin: calc(1.4rem*2);
}
.index-for .h01 img {
  width: 64px;
  height: 64px;
  border-radius: 100%;
  margin: auto;
}
.index-for .item01 img {
  background-color: beige;
}
.index-for .item02 img {
  background-color: aliceblue;
}
.index-for .item03 img {
  background-color: lavenderblush;
}
.index-for .h02 {
  font-size: 1.8rem;
  text-align: center;
  margin: 1.4rem;
}
.index-for .h03 {
  color: #666666;
  margin: 1.4rem calc(1.4rem*2) calc(1.4rem*2) calc(1.4rem*2);
}
.index-for .ax-col {
  justify-content: flex-start;
  align-items: center;
}
.index-ad {
  color: #fff;
  margin: calc(1.4rem*2) auto;
  text-align: center;
}
.index-ad .outer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
  background-color: #198cff;
  border-radius: calc(0.3rem*4);
  box-shadow: 1px 1px 0.5rem rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}
.index-ad .outer img {
  position: absolute;
}
.index-ad .outer img:nth-child(1) {
  height: 380px;
  left: -290px;
  top: 0px;
}
.index-ad .outer img:nth-child(2) {
  height: 380px;
  right: -160px;
  top: -80px;
}
.index-ad .text {
  position: relative;
  z-index: 2;
}
.index-ad .h03 {
  margin-top: 1.4rem;
}
.index-md {
  margin: calc(1.4rem*2) auto;
}
.index-md .item {
  border-radius: calc(0.3rem*4);
  border: 1px solid #ebebeb;
  background-color: #fff;
  box-shadow: 1px 1px 0.5rem rgba(0, 0, 0, 0.06);
  margin-bottom: calc(1.4rem*4);
  overflow: hidden;
  position: relative;
  transition: all 200ms linear;
}
.index-md .item img {
  border-radius: calc(0.3rem*4);
  display: block;
  width: 100%;
}
.index-md .item span {
  font-size: 1.2rem;
  text-align: center;
  width: 100%;
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  display: block;
  position: absolute;
  left: 0;
  bottom: -2.8rem;
  transition: all 200ms linear;
}
.index-md .item:hover {
  transform: scale(1.2);
}
.index-md .item:hover span {
  background-color: rgba(0, 0, 0, 0.5);
  bottom: 0;
}
.index-platform .ax-col {
  text-align: center;
}
.index-platform .item {
  text-align: center;
  position: relative;
}
.index-platform .item img {
  display: block;
}
.index-platform .item img:last-child {
  width: 160px;
  border-radius: calc(0.3rem*10);
  box-shadow: 1px 1px 0.5rem rgba(0, 0, 0, 0.06);
  border: 1px solid #ebebeb;
  transform: translate(-50%, -100%);
  position: absolute;
  left: 50%;
  top: 0;
  z-index: 4;
  display: none;
}
.index-feature .ax-gutter-line {
  height: 12.6rem;
}
.index-feature .item .h01 {
  font-size: 1.8rem;
  margin-bottom: 1.4rem;
}
.index-feature .item .h02 {
  color: #666666;
}
.index-feature .item01 .item {
  margin: 0 2.8rem 2.8rem 0;
}
.index-feature .item02 .item {
  margin: 0 2.8rem 2.8rem 2.8rem;
}
.index-feature .item03 .item {
  margin: 0 0 2.8rem 2.8rem;
}
.index-feature .item04 .item {
  margin: 2.8rem 2.8rem 0 0;
}
.index-feature .item05 .item {
  margin: 2.8rem 2.8rem 0 2.8rem;
}
.index-feature .item06 .item {
  margin: 2.8rem 0 0 2.8rem;
}
.index-about {
  padding-top: calc(1.4rem*4);
}
.index-about .ax-gutter-line {
  height: 37rem;
  margin: 0 calc(1.4rem*4);
}
.index-about .h01 {
  font-size: 2.2rem;
  line-height: 2.8rem;
  margin-bottom: 1.4rem;
}
.index-about .h02 {
  padding-bottom: 2.8rem;
}
.index-block .ax-break-text {
  width: 30rem;
  margin: 4.4rem auto;
}
.index-block .ax-break-text span {
  font-size: 1.8rem;
  line-height: 2.8rem;
  color: #333333;
  margin: auto calc(1.4rem*2);
}
.demos .item {
  border-radius: calc(0.3rem*4);
  background-color: #fff;
  box-shadow: 1px 1px 0.5rem rgba(0, 0, 0, 0.06);
  transition: all 200ms linear;
  border: 1px solid #ebebeb;
  position: relative;
}
.demos .item .ax-flag {
  border-top-left-radius: calc(0.3rem*4);
}
.demos .item .h01 {
  display: block;
}
.demos .item .h01 img {
  width: 100%;
  border-radius: calc(0.3rem*4) calc(0.3rem*4) 0 0;
}
.demos .item .h02 {
  padding: 1.4rem;
}
.demos .item .h02 .ax-col {
  height: 56px;
}
.demos .item .h02 .ax-iconfont {
  width: 2.2rem;
  height: 2.2rem;
  text-align: center;
  line-height: 2.2rem;
  background-color: #ebebeb;
  border-radius: 2.2rem;
  color: #666666;
}
.demos .item .h02 .ax-iconfont:hover {
  color: #fff;
  background-color: #198cff;
}
.demos .item:hover {
  box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
}
.demo-section {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  color: #808080;
  height: 8rem;
  line-height: 8rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
}
.demo-section-border {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ebebeb;
  background-color: #fff;
  color: #808080;
  height: 8rem;
  line-height: 8rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
}
.demo-list-pic .demo-block {
  background-color: #fff;
  height: 16rem;
  line-height: 16rem;
  text-align: center;
  margin: 1.4rem 1.4rem 0 1.4rem;
}
.demo-aside-more {
  display: block;
  line-height: 4.4rem;
  height: 4.4rem;
  text-align: center;
  border-top: 1px solid #ebebeb;
}
.demo-footer {
  padding-left: 1.4rem;
}
.demo-icon {
  text-align: center;
}
.demo-icon .h01 {
  height: 8rem;
  line-height: 8rem;
}
.demo-icon .h01 span {
  font-size: 2.8rem;
  color: #666666;
}
.demo-icon .h01 svg {
  width: 2.8rem;
  height: 8rem;
  fill: #666666;
}
.demo-icon .h02 {
  font-size: 1.2rem;
  height: 3.8rem;
  line-height: 3.8rem;
  white-space: nowrap;
  color: #808080;
}
.color-series div:nth-child(1) div {
  height: 10rem;
}
.color-series div:nth-child(2) div {
  height: 3rem;
  position: relative;
}
.color-series div.ax-col {
  cursor: pointer;
}
.color-series div.ax-col:before {
  content: '';
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 2px solid rgba(0, 0, 0, 0.2);
  position: absolute;
  left: 0;
  top: 0;
  display: none;
}
.color-series div.ax-col.focus:before {
  display: block;
}
.color-series div:nth-child(1) div:nth-child(1) i {
  color: #fff;
  line-height: 1.8rem;
  position: absolute;
  left: 1.4rem;
  top: 1rem;
}
.color-series.color-primary div:nth-child(1) div:nth-child(1) {
  background-color: #198cff;
}
.color-series.color-primary div:nth-child(1) div:nth-child(2) {
  background-color: #6619ff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(1) {
  background-color: #3399ff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(2) {
  background-color: #4ca6ff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(3) {
  background-color: #66b3ff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(4) {
  background-color: #80bfff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(5) {
  background-color: #99ccff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(6) {
  background-color: #b2d9ff;
}
.color-series.color-primary div:nth-child(2) div:nth-child(7) {
  background-color: #d7e7f5;
}
.color-series.color-primary div:nth-child(2) div:nth-child(8) {
  background-color: #f0f7ff;
}
.color-series.color-secondary div:nth-child(1) div:nth-child(1) {
  background-color: #475b66;
}
.color-series.color-secondary div:nth-child(1) div:nth-child(2) {
  background-color: #63a0be;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(1) {
  background-color: #5a6f7b;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(2) {
  background-color: #728894;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(3) {
  background-color: #8ca3af;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(4) {
  background-color: #a4bac6;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(5) {
  background-color: #bacfda;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(6) {
  background-color: #d5e6f0;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(7) {
  background-color: #dfe3e5;
}
.color-series.color-secondary div:nth-child(2) div:nth-child(8) {
  background-color: #f5f8fa;
}
.color-series.color-success div:nth-child(1) div:nth-child(1) {
  background-color: #41a358;
}
.color-series.color-success div:nth-child(1) div:nth-child(2) {
  background-color: #b2d956;
}
.color-series.color-success div:nth-child(2) div:nth-child(1) {
  background-color: #51b068;
}
.color-series.color-success div:nth-child(2) div:nth-child(2) {
  background-color: #64be79;
}
.color-series.color-success div:nth-child(2) div:nth-child(3) {
  background-color: #7aca8d;
}
.color-series.color-success div:nth-child(2) div:nth-child(4) {
  background-color: #94d7a3;
}
.color-series.color-success div:nth-child(2) div:nth-child(5) {
  background-color: #aee4bb;
}
.color-series.color-success div:nth-child(2) div:nth-child(6) {
  background-color: #cbf2d4;
}
.color-series.color-success div:nth-child(2) div:nth-child(7) {
  background-color: #e1f0e5;
}
.color-series.color-success div:nth-child(2) div:nth-child(8) {
  background-color: #edfaf0;
}
.color-series.color-danger div:nth-child(1) div:nth-child(1) {
  background-color: #dc3545;
}
.color-series.color-danger div:nth-child(1) div:nth-child(2) {
  background-color: #ffc000;
}
.color-series.color-danger div:nth-child(2) div:nth-child(1) {
  background-color: #df4957;
}
.color-series.color-danger div:nth-child(2) div:nth-child(2) {
  background-color: #e35f6c;
}
.color-series.color-danger div:nth-child(2) div:nth-child(3) {
  background-color: #e87883;
}
.color-series.color-danger div:nth-child(2) div:nth-child(4) {
  background-color: #ee919a;
}
.color-series.color-danger div:nth-child(2) div:nth-child(5) {
  background-color: #f4a8b0;
}
.color-series.color-danger div:nth-child(2) div:nth-child(6) {
  background-color: #fac5ca;
}
.color-series.color-danger div:nth-child(2) div:nth-child(7) {
  background-color: #f0dddf;
}
.color-series.color-danger div:nth-child(2) div:nth-child(8) {
  background-color: #fff0f1;
}
.color-series.color-warning div:nth-child(1) div:nth-child(1) {
  background-color: #ffc107;
}
.color-series.color-warning div:nth-child(1) div:nth-child(1) i {
  color: #333333;
}
.color-series.color-warning div:nth-child(1) div:nth-child(2) {
  background-color: #ffde7a;
}
.color-series.color-warning div:nth-child(2) div:nth-child(1) {
  background-color: #ffc821;
}
.color-series.color-warning div:nth-child(2) div:nth-child(2) {
  background-color: #ffce3b;
}
.color-series.color-warning div:nth-child(2) div:nth-child(3) {
  background-color: #ffd454;
}
.color-series.color-warning div:nth-child(2) div:nth-child(4) {
  background-color: #ffdb6e;
}
.color-series.color-warning div:nth-child(2) div:nth-child(5) {
  background-color: #ffe187;
}
.color-series.color-warning div:nth-child(2) div:nth-child(6) {
  background-color: #ffe7a1;
}
.color-series.color-warning div:nth-child(2) div:nth-child(7) {
  background-color: #f5edd7;
}
.color-series.color-warning div:nth-child(2) div:nth-child(8) {
  background-color: #fffaeb;
}
.color-series.color-info div:nth-child(1) div:nth-child(1) {
  background-color: #14ccc9;
}
.color-series.color-info div:nth-child(1) div:nth-child(2) {
  background-color: #2c7be6;
}
.color-series.color-info div:nth-child(2) div:nth-child(1) {
  background-color: #27d4d1;
}
.color-series.color-info div:nth-child(2) div:nth-child(2) {
  background-color: #40dedb;
}
.color-series.color-info div:nth-child(2) div:nth-child(3) {
  background-color: #57e5e3;
}
.color-series.color-info div:nth-child(2) div:nth-child(4) {
  background-color: #70eae8;
}
.color-series.color-info div:nth-child(2) div:nth-child(5) {
  background-color: #8bf1ef;
}
.color-series.color-info div:nth-child(2) div:nth-child(6) {
  background-color: #a7f8f7;
}
.color-series.color-info div:nth-child(2) div:nth-child(7) {
  background-color: #cef0f0;
}
.color-series.color-info div:nth-child(2) div:nth-child(8) {
  background-color: #ebfaf9;
}
.color-series.color-ad div:nth-child(1) div:nth-child(1) {
  background-color: #ff8400;
}
.color-series.color-ad div:nth-child(1) div:nth-child(2) {
  background-color: #ffdd23;
}
.color-series.color-ad div:nth-child(2) div:nth-child(1) {
  background-color: #ff901a;
}
.color-series.color-ad div:nth-child(2) div:nth-child(2) {
  background-color: #ff9c33;
}
.color-series.color-ad div:nth-child(2) div:nth-child(3) {
  background-color: #ffa94c;
}
.color-series.color-ad div:nth-child(2) div:nth-child(4) {
  background-color: #ffb566;
}
.color-series.color-ad div:nth-child(2) div:nth-child(5) {
  background-color: #ffc180;
}
.color-series.color-ad div:nth-child(2) div:nth-child(6) {
  background-color: #ffce99;
}
.color-series.color-ad div:nth-child(2) div:nth-child(7) {
  background-color: #f5e7d7;
}
.color-series.color-ad div:nth-child(2) div:nth-child(8) {
  background-color: #fff5eb;
}
.color-series.color-title div:nth-child(1) div:nth-child(1) {
  background-color: #333333;
}
.color-series.color-title div:nth-child(1) div:nth-child(2) {
  background-color: #666666;
}
.color-series.color-title div:nth-child(2) div:nth-child(1) {
  background-color: #4d4d4d;
}
.color-series.color-title div:nth-child(2) div:nth-child(2) {
  background-color: #666666;
}
.color-series.color-title div:nth-child(2) div:nth-child(3) {
  background-color: #808080;
}
.color-series.color-title div:nth-child(2) div:nth-child(4) {
  background-color: #999999;
}
.color-series.color-title div:nth-child(2) div:nth-child(5) {
  background-color: #b3b3b3;
}
.color-series.color-title div:nth-child(2) div:nth-child(6) {
  background-color: #cccccc;
}
.color-series.color-title div:nth-child(2) div:nth-child(7) {
  background-color: #ebebeb;
}
.color-series.color-title div:nth-child(2) div:nth-child(8) {
  background-color: #f5f5f5;
}
.color-group {
  text-align: center;
  line-height: 1.8rem;
}
.color-group div:nth-child(1) div {
  padding: 1.4rem 0;
  color: #fff;
}
.color-group div:nth-child(2) div {
  padding: 1.4rem 0;
}
.color-group.color-primary div:nth-child(1) div:nth-child(1) {
  background-color: #198cff;
}
.color-group.color-primary div:nth-child(1) div:nth-child(2) {
  background-color: #0b72da;
}
.color-group.color-primary div:nth-child(2) div:nth-child(1) {
  background-color: #6dacea;
}
.color-group.color-primary div:nth-child(2) div:nth-child(2) {
  background-color: #d7e7f5;
}
.color-group.color-primary div:nth-child(2) div:nth-child(3) {
  background-color: #f0f7ff;
}
.color-group.color-secondary div:nth-child(1) div:nth-child(1) {
  background-color: #475b66;
}
.color-group.color-secondary div:nth-child(1) div:nth-child(2) {
  background-color: #383e42;
}
.color-group.color-secondary div:nth-child(2) div:nth-child(1) {
  background-color: #adc1cc;
}
.color-group.color-secondary div:nth-child(2) div:nth-child(2) {
  background-color: #dfe3e5;
}
.color-group.color-secondary div:nth-child(2) div:nth-child(3) {
  background-color: #f5f8fa;
}
.color-group.color-success div:nth-child(1) div:nth-child(1) {
  background-color: #41a358;
}
.color-group.color-success div:nth-child(1) div:nth-child(2) {
  background-color: #3b7649;
}
.color-group.color-success div:nth-child(2) div:nth-child(1) {
  background-color: #adccb5;
}
.color-group.color-success div:nth-child(2) div:nth-child(2) {
  background-color: #e1f0e5;
}
.color-group.color-success div:nth-child(2) div:nth-child(3) {
  background-color: #edfaf0;
}
.color-group.color-danger div:nth-child(1) div:nth-child(1) {
  background-color: #dc3545;
}
.color-group.color-danger div:nth-child(1) div:nth-child(2) {
  background-color: #b22c39;
}
.color-group.color-danger div:nth-child(2) div:nth-child(1) {
  background-color: #ccadb0;
}
.color-group.color-danger div:nth-child(2) div:nth-child(2) {
  background-color: #f0dddf;
}
.color-group.color-danger div:nth-child(2) div:nth-child(3) {
  background-color: #fff0f1;
}
.color-group.color-info div:nth-child(1) div:nth-child(1) {
  background-color: #14ccc9;
}
.color-group.color-info div:nth-child(1) div:nth-child(2) {
  background-color: #189593;
}
.color-group.color-info div:nth-child(2) div:nth-child(1) {
  background-color: #adcccb;
}
.color-group.color-info div:nth-child(2) div:nth-child(2) {
  background-color: #cef0f0;
}
.color-group.color-info div:nth-child(2) div:nth-child(3) {
  background-color: #ebfaf9;
}
.color-group.color-warning div:nth-child(1) div:nth-child(1) {
  background-color: #ffc107;
  color: #333333;
}
.color-group.color-warning div:nth-child(1) div:nth-child(2) {
  background-color: #c8990b;
}
.color-group.color-warning div:nth-child(2) div:nth-child(1) {
  background-color: #ccc4ad;
}
.color-group.color-warning div:nth-child(2) div:nth-child(2) {
  background-color: #f5edd7;
}
.color-group.color-warning div:nth-child(2) div:nth-child(3) {
  background-color: #fffaeb;
}
.color-group.color-ad div:nth-child(1) div:nth-child(1) {
  background-color: #ff8400;
}
.color-group.color-ad div:nth-child(1) div:nth-child(2) {
  background-color: #c2690a;
}
.color-group.color-ad div:nth-child(2) div:nth-child(1) {
  background-color: #ccbdad;
}
.color-group.color-ad div:nth-child(2) div:nth-child(2) {
  background-color: #f5e7d7;
}
.color-group.color-ad div:nth-child(2) div:nth-child(3) {
  background-color: #fff5eb;
}
.color-group.color-title div:nth-child(1) div:nth-child(1) {
  background-color: #333333;
}
.color-group.color-title div:nth-child(1) div:nth-child(2) {
  background-color: #000000;
}
.color-group.color-title div:nth-child(2) div:nth-child(1) {
  background-color: #cccccc;
}
.color-group.color-title div:nth-child(2) div:nth-child(2) {
  background-color: #ebebeb;
}
.color-group.color-title div:nth-child(2) div:nth-child(3) {
  background-color: #f5f5f5;
}
.lighter-outer.ax-tab .ax-tab-nav {
  margin-bottom: -5.4rem;
  position: relative;
  z-index: 2;
  text-align: left;
  padding-left: 1rem;
  width: 30rem;
}
.lighter-outer.ax-tab .ax-menu-tab .ax-item {
  padding: 0rem 1rem;
}
.lighter-outer.ax-tab .syntaxhighlighter:before {
  display: none;
}
body .ax-scrollnav-v {
  right: auto;
  left: calc(50% + 60rem + 1.4rem);
}
.lighter-outer {
  margin-top: 1.4rem;
}
/*适配*/
@media screen and (max-width: 900px) {
  .ax-header.ax-inherit {
    position: fixed;
  }
  .ax-header .ax-row {
    width: 100%;
  }
  .ax-header.ax-inherit + .ax-space-header {
    display: block;
  }
  .outer {
    width: 100%;
  }
  main .content {
    margin-left: 0;
    padding: 0 1.4rem;
    box-sizing: border-box;
  }
  main .menu {
    display: none;
    box-shadow: 1px 0 0 0 rgba(0, 0, 0, 0.12);
    padding-left: 1.4rem;
    box-sizing: border-box;
    width: 25rem;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 620;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #fff;
  }
  main .menu .ax-menu {
    width: 100%;
  }
  .site-nav {
    display: block;
  }
  .txt img {
    max-width: 100%;
  }
  .demo {
    padding: 4.4rem 0 0 0;
    border-width: 0;
    background-color: transparent;
    margin: auto -1.4rem;
  }
  .demo:before {
    padding-left: 1.4rem;
  }
  .demo .inner {
    border-width: 0;
    border-top-width: 1px;
    padding: 1.4rem 0 0 0 !important;
  }
  .demo .inner .ax-margin {
    margin: 0;
  }
  .demo iframe {
    border-top: 1px solid #ebebeb;
    border-bottom: 1px solid #ebebeb;
  }
  .demo-icon .h01 {
    height: 6rem;
    line-height: 6rem;
  }
  .demo-icon .h01 svg {
    height: 6rem;
  }
  .demo-icon .h02 {
    font-size: 0.8rem;
  }
  body.index {
    background-image: none;
  }
  .index-intro {
    padding: 0 1.4rem;
    text-align: center;
  }
  .index-intro .outer {
    padding: 16rem 0 0 0;
    background-image: url(../images/bg-header.png);
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100% auto;
  }
  .index-intro .ax-btn {
    width: 100%;
  }
  .index-intro .ax-btn:first-child {
    margin-bottom: 1.4rem;
  }
  .index-sta {
    margin: calc(1.4rem*2) auto;
  }
  .index-block .ax-break-text {
    width: calc(100% - 1.4rem*2);
    margin: calc(1.4rem*2) auto;
  }
  .index-for {
    position: relative;
  }
  .index-for:after {
    content: '';
    width: calc(100% - 1.4rem*2);
    height: 1px;
    background-color: #ebebeb;
    position: absolute;
    left: 1.4rem;
    bottom: 0;
  }
  .index-for .outer {
    width: calc(100% - 1.4rem*2);
    margin: 0 1.4rem;
  }
  .index-ad .outer {
    width: calc(100% - 1.4rem*2);
    margin: 0 1.4rem;
  }
  .index-ad .outer img:nth-child(1) {
    height: 280px;
    left: -240px;
  }
  .index-ad .outer img:nth-child(2) {
    display: none;
  }
  .index-feature .item {
    margin: 1.4rem !important;
  }
  .index-md .outer {
    width: calc(100% - 1.4rem*2);
    margin: 0 1.4rem;
  }
  .index-md .item {
    margin-bottom: 1.4rem;
  }
  .index-platform .item {
    margin-bottom: 1.4rem;
  }
  .ax-row[class*="ax-split"] *[class*="ax-gutter"] {
    display: block !important;
    height: 1px !important;
    width: calc(100% - 1.4rem*2) !important;
    transform: none !important;
    margin: 0 auto;
  }
  .index-about {
    padding-top: 0;
  }
  .index-about .ax-col > div {
    padding: calc(1.4rem*2) 1.4rem 0 1.4rem;
  }
  .index-about .ax-gutter-line {
    display: none;
  }
  .txt code {
    font-size: 1.4rem;
  }
  .demos {
    padding: 1.4rem;
  }
  .index-platform .item img:last-child {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

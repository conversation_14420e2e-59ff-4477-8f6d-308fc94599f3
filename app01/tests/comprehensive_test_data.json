{"test_metadata": {"version": "1.0", "description": "ChatBot AutoRelease 全面测试数据集", "created": "2025-07-15", "coverage_goals": {"intent_types": ["jira_query", "jira_write", "task_management", "document_query", "general_help"], "command_types": ["traditional", "ai", "spcpm"], "message_types": ["private", "group"], "validation_types": ["content_contains", "jql_validation", "error_message", "intent_classification"]}}, "test_categories": {"traditional_commands": {"description": "传统指令测试 - 覆盖所有非AI指令", "test_cases": [{"category": "bug_query", "description": "Bug查询指令测试", "variations": [{"name": "带JIRA单号的Bug查询", "message_type": "group", "message": "@ChatbotAR bug SPCB-55410", "expected_keywords": ["bug", "Bug", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["has_jira_key", "group_context"]}, {"name": "群组上下文Bug查询", "message_type": "group", "group_id": "test_group_spcb_55410", "message": "@ChatbotAR bug", "expected_keywords": ["bug", "Bug", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["group_context_extraction", "implicit_jira_key"]}, {"name": "私聊Bug查询(应失败)", "message_type": "private", "message": "bug", "expected_keywords": ["格式", "完整", "SPCB"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["private_no_context", "error_handling"]}, {"name": "无效JIRA单号Bug查询", "message_type": "group", "message": "@ChatbotAR bug INVALID-12345", "expected_keywords": ["错误", "不存在", "无效"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["invalid_jira_key", "error_handling"]}]}, {"category": "timeline_query", "description": "Timeline查询指令测试", "variations": [{"name": "SPCB Timeline查询", "message_type": "group", "message": "@ChatbotAR timeline SPCB-55410", "expected_keywords": ["timeline", "时间线", "Timeline", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["spcb_timeline", "explicit_jira_key"]}, {"name": "简写TL查询", "message_type": "group", "message": "@ChatbotAR tl SPCB-55410", "expected_keywords": ["timeline", "时间线", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["shorthand_command", "spcb_timeline"]}, {"name": "SPCPM Timeline查询", "message_type": "group", "message": "@ChatbotAR timeline SPCPM-123456", "expected_keywords": ["timeline", "SPCPM", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["spcpm_timeline", "spcpm_integration"]}, {"name": "群组上下文Timeline查询", "message_type": "group", "group_id": "test_group_spcb_55410", "message": "@ChatbotAR timeline", "expected_keywords": ["timeline", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["group_context_extraction", "implicit_timeline"]}]}, {"category": "checklist", "description": "Checklist确认指令测试", "variations": [{"name": "单项SO确认", "message_type": "group", "message": "@ChatbotAR SPCB-55410 SO", "expected_keywords": ["confirmed", "确认", "Signed off"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["single_checklist", "so_confirmation"]}, {"name": "多项确认", "message_type": "group", "message": "@ChatbotAR SPCB-55410 SO\\CM\\CC", "expected_keywords": ["confirmed", "确认", "Signed off", "Code Merged"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["multiple_checklist", "batch_confirmation"]}, {"name": "全称确认", "message_type": "group", "message": "@ChatbotAR SPCB-55410 Signed off", "expected_keywords": ["confirmed", "确认", "Signed off"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["full_name_checklist", "so_confirmation"]}, {"name": "小写确认", "message_type": "group", "message": "@ChatbotAR SPCB-55410 so", "expected_keywords": ["confirmed", "确认"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["lowercase_checklist", "case_insensitive"]}, {"name": "缺少JIRA单号", "message_type": "group", "message": "@ChatbotAR SO", "expected_keywords": ["缺少", "jira单号", "格式"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["missing_jira_key", "error_handling"]}]}, {"category": "jira_query", "description": "JIRA单号查询测试", "variations": [{"name": "纯SPCB单号查询", "message_type": "group", "message": "@ChatbotAR SPCB-55410", "expected_keywords": ["SPCB-55410", "状态", "优先级", "经办人"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["pure_jira_query", "spcb_issue"]}, {"name": "私聊JIRA查询", "message_type": "private", "message": "SPCB-55410", "expected_keywords": ["SPCB-55410", "状态"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["private_jira_query", "direct_query"]}, {"name": "SPCPM单号查询", "message_type": "group", "message": "@ChatbotAR SPCPM-123456", "expected_keywords": ["SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["spcpm_query", "spcpm_integration"]}]}, {"category": "new_group", "description": "创建群组指令测试", "variations": [{"name": "创建SPCB群组", "message_type": "group", "message": "@ChatbotAR new group SPCB-55410", "expected_keywords": ["群组", "group", "创建", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["group_creation", "spcb_group"]}, {"name": "创建SPCPM群组", "message_type": "group", "message": "@ChatbotAR new group SPCPM-123456", "expected_keywords": ["群组", "group", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["group_creation", "spcpm_group"]}, {"name": "缺少JIRA单号的群组创建", "message_type": "group", "message": "@ChatbotAR new group", "expected_keywords": ["缺少", "JIRA", "单号"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["missing_jira_key", "error_handling"]}]}]}, "spcpm_commands": {"description": "SPCPM专用指令测试", "test_cases": [{"category": "spcpm_timeline_reminder", "description": "SPCPM Timeline提醒管理", "variations": [{"name": "添加Timeline提醒", "message_type": "group", "message": "@ChatbotAR tr+ SPCPM-123456", "expected_keywords": ["提醒", "添加", "成功", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["add_reminder", "spcpm_timeline"]}, {"name": "移除Timeline提醒", "message_type": "group", "message": "@ChatbotAR tr- SPCPM-123456", "expected_keywords": ["提醒", "移除", "删除", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["remove_reminder", "spcpm_timeline"]}, {"name": "列出Timeline提醒", "message_type": "group", "message": "@ChatbotAR list timeline reminder", "expected_keywords": ["提醒", "列表", "timeline"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["list_reminders", "reminder_management"]}, {"name": "简写列表提醒", "message_type": "group", "message": "@ChatbotAR list tr", "expected_keywords": ["提醒", "列表"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["shorthand_list", "reminder_management"]}]}, {"category": "spcpm_role_mention", "description": "SPCPM角色@功能测试", "variations": [{"name": "@所有人", "message_type": "group", "message": "@ChatbotAR all SPCPM-123456", "expected_keywords": ["mention", "@", "所有", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["mention_all", "spcpm_roles"]}, {"name": "@QA人员", "message_type": "group", "message": "@ChatbotAR qa SPCPM-123456", "expected_keywords": ["mention", "@", "qa", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["mention_qa", "spcpm_roles"]}, {"name": "@开发人员", "message_type": "group", "message": "@ChatbotAR dev SPCPM-123456", "expected_keywords": ["mention", "@", "dev", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["mention_dev", "spcpm_roles"]}, {"name": "@PM人员", "message_type": "group", "message": "@ChatbotAR pm SPCPM-123456", "expected_keywords": ["mention", "@", "pm", "SPCPM-123456"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["mention_pm", "spcpm_roles"]}]}, {"category": "spcpm_help", "description": "SPCPM帮助功能测试", "variations": [{"name": "SPCPM通用帮助", "message_type": "group", "message": "@ChatbotAR spcpm", "expected_keywords": ["SPCPM", "帮助", "功能", "指令"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["spcpm_general_help", "help_system"]}, {"name": "特定SPCPM帮助", "message_type": "group", "message": "@ChatbotAR SPCPM-123456", "expected_keywords": ["SPCPM-123456", "帮助", "功能"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["spcpm_specific_help", "contextual_help"]}]}]}, "ai_jira_query": {"description": "AI JIRA查询功能测试 - 覆盖所有意图类型和表述方式", "test_cases": [{"category": "subtask_query", "description": "子任务查询 - 多种表述方式", "variations": [{"name": "未完成子任务查询(修复验证)", "message_type": "private", "message": "我有哪些未完成的子任务", "expected_keywords": ["sub-task", "SPCB-", "经办人"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["subtask_filter", "status_exclusion", "currentUser_assignee"]}, {"name": "我的子任务", "message_type": "private", "message": "我的子任务", "expected_keywords": ["assignee = currentUser()", "type = sub-task"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["subtask_filter", "assignee_filter"]}, {"name": "查看我负责的子任务", "message_type": "private", "message": "查看我负责的子任务", "expected_keywords": ["assignee", "sub-task", "subtask"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["natural_language", "subtask_filter"]}, {"name": "显示我的subtask", "message_type": "private", "message": "显示我的subtask", "expected_keywords": ["assignee", "subtask"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["english_keyword", "subtask_filter"]}, {"name": "我需要做的子任务", "message_type": "private", "message": "我需要做的子任务", "expected_keywords": ["status not in (Closed, Done, Icebox)"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["need_to_do", "status_exclusion"]}, {"name": "待处理的子任务", "message_type": "private", "message": "待处理的子任务", "expected_keywords": ["sub-task", "SPCB-", "经办人"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["pending_tasks", "status_exclusion"]}]}, {"category": "bug_query", "description": "Bug查询 - 多种表述方式", "variations": [{"name": "我的Bug查询", "message_type": "private", "message": "查询我的bug", "expected_keywords": ["assignee = currentUser()", "type = Bug"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["bug_filter", "assignee_filter"]}, {"name": "我有哪些bug", "message_type": "private", "message": "我有哪些bug", "expected_keywords": ["assignee", "Bug"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["natural_question", "bug_filter"]}, {"name": "显示分配给我的缺陷", "message_type": "private", "message": "显示分配给我的缺陷", "expected_keywords": ["assignee", "Bug"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["chinese_synonym", "bug_filter"]}, {"name": "我的Bug列表", "message_type": "private", "message": "我待处理的Bug列表", "expected_keywords": ["assignee", "Bug"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["list_format", "bug_filter"]}, {"name": "未解决的bug", "message_type": "private", "message": "我未解决的bug", "expected_keywords": ["status not in (Closed, Done, Icebox)", "Bug"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["unresolved_status", "bug_filter"]}]}, {"category": "task_query", "description": "任务查询 - 多种表述方式", "variations": [{"name": "我的任务", "message_type": "private", "message": "我的子任务", "expected_keywords": ["assignee = currentUser()"], "validation_type": "jql_validation", "should_succeed": true, "intent": "jira_query", "test_branches": ["general_task", "assignee_filter"]}, {"name": "高优先级任务", "message_type": "private", "message": "我有哪些未完成的子任务", "expected_keywords": ["priority", "High"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["priority_filter", "high_priority"]}, {"name": "进行中的任务", "message_type": "private", "message": "我进行中的任务", "expected_keywords": ["status", "doing"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["status_filter", "in_progress"]}, {"name": "今天要做的任务", "message_type": "private", "message": "今天要做的任务", "expected_keywords": ["assignee", "today"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["time_filter", "daily_tasks"]}, {"name": "紧急任务", "message_type": "private", "message": "我上周完整了哪些的单", "expected_keywords": ["endOfWeek(-1)", "resolved"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_query", "test_branches": ["urgent_priority", "priority_filter"]}]}]}, "ai_subtask_creation": {"description": "AI子任务创建功能测试 - 覆盖多种创建方式", "test_cases": [{"category": "basic_creation", "description": "基本子任务创建", "variations": [{"name": "标准格式创建", "message_type": "private", "message": "在SPCB-55410建单：完成登录功能测试 1d", "expected_keywords": ["创建", "子任务", "成功", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_write", "test_branches": ["standard_format", "subtask_creation"]}, {"name": "特殊格式创建", "message_type": "private", "message": "在SPCB-55410建单：--测试 2d", "expected_keywords": ["创建", "子任务", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_write", "test_branches": ["special_format", "title_inheritance"]}, {"name": "无工作量创建", "message_type": "private", "message": "在SPCB-55410建单：测试任务", "expected_keywords": ["创建", "子任务", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_write", "test_branches": ["no_workload", "default_estimation"]}]}, {"category": "natural_language_creation", "description": "自然语言子任务创建", "variations": [{"name": "自然语言创建1", "message_type": "private", "message": "帮我在SPCB-55410下创建一个测试任务，大概需要半天", "expected_keywords": ["创建", "子任务", "测试", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_write", "test_branches": ["natural_language", "time_estimation"]}, {"name": "自然语言创建2", "message_type": "private", "message": "给SPCB-55410新建一个开发子任务，工作量1天", "expected_keywords": ["创建", "子任务", "开发", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_write", "test_branches": ["natural_language", "development_task"]}, {"name": "简化表达创建", "message_type": "private", "message": "SPCB-55410加个任务：UI测试", "expected_keywords": ["创建", "子任务", "UI测试", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "jira_write", "test_branches": ["simplified_expression", "ui_task"]}]}]}, "ai_task_management": {"description": "AI定时任务管理测试 - 覆盖任务管理功能", "test_cases": [{"category": "task_creation", "description": "定时任务创建", "variations": [{"name": "每日任务创建", "message_type": "private", "message": "帮我设置一个每天早上9点查询我未完成任务的定时任务", "expected_keywords": ["定时任务", "创建", "成功", "每天", "9点"], "validation_type": "content_contains", "should_succeed": true, "intent": "task_management", "test_branches": ["daily_schedule", "task_creation"]}, {"name": "每周任务创建", "message_type": "private", "message": "创建一个每周一早上提醒我查看bug的任务", "expected_keywords": ["定时任务", "每周", "周一", "bug"], "validation_type": "content_contains", "should_succeed": true, "intent": "task_management", "test_branches": ["weekly_schedule", "bug_reminder"]}, {"name": "自定义时间任务", "message_type": "private", "message": "每天下午3点提醒我检查JIRA", "expected_keywords": ["定时任务", "下午", "3点", "JIRA"], "validation_type": "content_contains", "should_succeed": true, "intent": "task_management", "test_branches": ["custom_time", "jira_reminder"]}]}, {"category": "task_query", "description": "定时任务查询", "variations": [{"name": "查看定时任务", "message_type": "private", "message": "我的定时任务有哪些", "expected_keywords": ["定时任务", "列表"], "validation_type": "content_contains", "should_succeed": true, "intent": "task_management", "test_branches": ["task_list", "user_tasks"]}, {"name": "显示任务列表", "message_type": "private", "message": "显示我的所有定时任务", "expected_keywords": ["定时任务", "任务"], "validation_type": "content_contains", "should_succeed": true, "intent": "task_management", "test_branches": ["task_list", "all_tasks"]}, {"name": "查询任务状态", "message_type": "private", "message": "我的定时任务运行情况", "expected_keywords": ["定时任务", "运行", "状态"], "validation_type": "content_contains", "should_succeed": true, "intent": "task_management", "test_branches": ["task_status", "execution_info"]}]}]}, "ai_document_query": {"description": "AI文档查询功能测试", "test_cases": [{"category": "confluence_query", "description": "Confluence文档查询", "variations": [{"name": "TRD文档查询", "message_type": "private", "message": "SPCB-53542的PRD 主要是说什么", "expected_keywords": ["TRD", "文档", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "document_query", "test_branches": ["trd_document", "confluence_search"]}, {"name": "PRD文档查询", "message_type": "private", "message": "总结SPCB-55410的PRD", "expected_keywords": ["PRD", "文档", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "document_query", "test_branches": ["prd_document", "document_location"]}]}, {"category": "log_query", "description": "日志查询", "variations": [{"name": "错误日志查询", "message_type": "private", "message": "查询SPCB-55410相关的错误日志", "expected_keywords": ["日志", "错误", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "document_query", "test_branches": ["error_logs", "log_search"]}, {"name": "部署日志查询", "message_type": "private", "message": "SPCB-55410的部署日志", "expected_keywords": ["部署", "日志", "SPCB-55410"], "validation_type": "content_contains", "should_succeed": true, "intent": "document_query", "test_branches": ["deployment_logs", "log_search"]}]}]}, "error_handling": {"description": "错误处理和边界测试", "test_cases": [{"category": "invalid_input", "description": "无效输入测试", "variations": [{"name": "无效JIRA单号", "message_type": "private", "message": "INVALID-12345的状态", "expected_keywords": ["错误", "不存在", "无效", "JIRA"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["invalid_jira", "error_handling"]}, {"name": "空AI查询", "message_type": "private", "message": "/ai", "expected_keywords": ["帮助", "功能", "使用"], "validation_type": "content_contains", "should_succeed": true, "intent": "general_help", "test_branches": ["empty_query", "help_response"]}, {"name": "无意义查询", "message_type": "private", "message": "随便说点什么", "expected_keywords": ["帮助", "不理解", "功能"], "validation_type": "content_contains", "should_succeed": true, "intent": "general_help", "test_branches": ["meaningless_query", "fallback_response"]}, {"name": "超长查询", "message_type": "private", "message": "查询我的任务 查询我的任务 查询我的任务 查询我的任务 查询我的任务 查询我的任务 查询我的任务 查询我的任务 查询我的任务 查询我的任务", "expected_keywords": ["任务", "查询"], "validation_type": "content_contains", "should_succeed": true, "test_branches": ["long_query", "text_processing"]}]}, {"category": "permission_test", "description": "权限测试", "variations": [{"name": "无权限JIRA查询", "message_type": "private", "message": "查询RESTRICTED-12345", "expected_keywords": ["权限", "无法访问", "限制"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["permission_denied", "access_control"]}, {"name": "高级功能权限测试", "message_type": "private", "message": "删除所有定时任务", "expected_keywords": ["权限", "不允许", "管理员"], "validation_type": "error_message", "should_succeed": false, "test_branches": ["admin_function", "permission_check"]}]}]}}, "validation_rules": {"content_contains": {"description": "检查响应内容是否包含预期关键词", "method": "keyword_match", "parameters": {"case_sensitive": false, "partial_match": true, "min_matches": 1}}, "jql_validation": {"description": "验证生成的JQL查询是否正确", "method": "jql_analysis", "parameters": {"check_syntax": true, "check_fields": true, "check_operators": true}}, "error_message": {"description": "验证错误消息是否合适", "method": "error_check", "parameters": {"check_helpful": true, "check_specific": true, "check_actionable": true}}, "intent_classification": {"description": "验证AI意图识别是否正确", "method": "intent_analysis", "parameters": {"expected_intent": null, "confidence_threshold": 0.8}}}, "test_config": {"environment": {"base_url": "http://autorelease.chatbot.shopee.io", "timeout": 30, "retry_count": 2, "delay_between_tests": 1}, "thresholds": {"response_time_threshold": 10.0, "required_message_count": 1, "success_rate_threshold": 85.0, "intent_confidence_threshold": 0.8}, "test_user": {"user_id": "test_user_001", "user_name": "测试用户", "user_email": "<EMAIL>", "employee_code": "test_001"}, "test_groups": {"default_group": "test_group_001", "spcb_group": "test_group_spcb_55410", "spcpm_group": "test_group_spcpm_123456"}}}
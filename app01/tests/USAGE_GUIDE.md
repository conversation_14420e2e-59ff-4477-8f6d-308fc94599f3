# 数据驱动测试使用指南

## 🚀 快速开始

### 1. 运行所有测试

```bash
# 运行所有测试用例
python app01/tests/data_driven_test_runner.py

# 指定服务器地址
python app01/tests/data_driven_test_runner.py --url http://localhost:8000
```

### 2. 查看可用测试分类

```bash
python app01/tests/data_driven_test_runner.py --list
```

### 3. 运行特定分类的测试

```bash
# 只测试传统指令
python app01/tests/data_driven_test_runner.py --category traditional_commands

# 只测试AI JIRA查询
python app01/tests/data_driven_test_runner.py --category ai_jira_query

# 只测试子任务创建
python app01/tests/data_driven_test_runner.py --category ai_subtask_creation

# 只测试SPCPM功能
python app01/tests/data_driven_test_runner.py --category spcpm_commands
```

### 4. 自定义测试参数

```bash
# 设置超时时间
python app01/tests/data_driven_test_runner.py --timeout 60

# 设置测试间隔
python app01/tests/data_driven_test_runner.py --delay 2.0

# 生成简洁报告
python app01/tests/data_driven_test_runner.py --report-type summary
```

## 📊 测试分类说明

### 🔧 traditional_commands (传统指令)
- **bug_query**: Bug查询指令测试
- **timeline_query**: Timeline查询指令测试  
- **checklist**: Checklist确认指令测试
- **jira_query**: JIRA单号查询测试
- **new_group**: 创建群组指令测试

### 🤖 ai_jira_query (AI JIRA查询)
- **subtask_query**: 子任务查询 - 多种表述方式
- **bug_query**: Bug查询 - 多种表述方式
- **task_query**: 任务查询 - 多种表述方式

### 🎯 ai_subtask_creation (AI子任务创建)
- **basic_creation**: 基本子任务创建
- **natural_language_creation**: 自然语言子任务创建

### ⏰ ai_task_management (AI定时任务管理)
- **task_creation**: 定时任务创建
- **task_query**: 定时任务查询

### 📄 ai_document_query (AI文档查询)
- **confluence_query**: Confluence文档查询
- **log_query**: 日志查询

### 🔧 spcpm_commands (SPCPM专用指令)
- **spcpm_timeline_reminder**: SPCPM Timeline提醒管理
- **spcpm_role_mention**: SPCPM角色@功能
- **spcpm_help**: SPCPM帮助功能

### ⚠️ error_handling (错误处理)
- **invalid_input**: 无效输入测试
- **permission_test**: 权限测试

## 📋 测试报告

测试完成后会生成详细的报告文件，保存在 `./reports/` 目录下：

- `test_detailed_YYYYMMDD_HHMMSS.txt` - 详细报告
- `test_summary_YYYYMMDD_HHMMSS.txt` - 简洁报告

报告包含：
- 📊 总体统计信息
- 📋 按分类的详细统计
- ⏱️ 响应时间分析
- ❌ 失败测试详情

## 🎯 重点验证项目

### 1. "未完成子任务"修复验证 ⭐⭐⭐

```bash
# 专门验证子任务查询修复
python app01/tests/data_driven_test_runner.py --category ai_jira_query
```

验证点：
- ✅ JQL包含正确的状态排除条件
- ✅ 不会返回closed状态的子任务
- ✅ 支持多种表述方式

### 2. 意图识别准确性

测试AI是否能正确识别用户意图：
- `jira_query` - JIRA查询意图
- `jira_write` - JIRA写入意图
- `task_management` - 任务管理意图
- `document_query` - 文档查询意图
- `general_help` - 通用帮助意图

## 🔧 自定义测试用例

### 添加新的测试用例

编辑 `comprehensive_test_data.json` 文件：

```json
{
  "name": "新测试用例",
  "message_type": "private",
  "message": "/ai 新的查询",
  "expected_keywords": ["预期", "关键词"],
  "validation_type": "content_contains",
  "should_succeed": true,
  "intent": "jira_query",
  "test_branches": ["new_branch", "test_coverage"]
}
```

### 验证类型说明

- **content_contains**: 检查响应内容是否包含预期关键词
- **jql_validation**: 验证生成的JQL查询语法
- **error_message**: 验证错误消息的合适性
- **intent_classification**: 验证AI意图识别的准确性

## 🚨 注意事项

### 1. 安全性
- ✅ 所有测试都使用拦截接口，不会影响真实用户
- ✅ 不会发送真实的SeaTalk消息
- ✅ 可以安全地在生产环境测试

### 2. 性能
- 测试间隔默认为1秒，避免过快请求
- 响应时间阈值为10秒
- 支持并发测试（需要谨慎使用）

### 3. 维护
- 测试数据与代码分离，易于维护
- 支持版本控制和变更追踪
- 可以轻松添加新的测试用例

## 🎯 最佳实践

### 1. 测试执行顺序

1. **快速测试** - 验证核心功能
   ```bash
   python quick_test.py
   ```

2. **分类测试** - 针对特定功能深度测试
   ```bash
   python app01/tests/data_driven_test_runner.py --category ai_jira_query
   ```

3. **全面测试** - 完整的回归测试
   ```bash
   python app01/tests/data_driven_test_runner.py
   ```

### 2. 问题排查

如果测试失败：

1. 检查服务器是否正常运行
2. 查看详细的错误信息
3. 检查测试数据配置
4. 验证网络连接

### 3. 持续集成

建议将测试集成到CI/CD流水线：

```bash
# 在部署前执行快速测试
python quick_test.py

# 定期执行全面测试
python app01/tests/data_driven_test_runner.py
```

## 📞 支持

如有问题或建议，请：

1. 检查测试日志和错误信息
2. 查看服务器端日志
3. 验证测试配置文件
4. 联系开发团队

---

**🎉 现在你可以开始全面测试ChatBot AutoRelease的所有功能了！** 
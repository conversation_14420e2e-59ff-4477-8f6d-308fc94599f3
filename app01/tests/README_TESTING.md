# ChatbotAR 测试框架使用指南

## 概述

ChatbotAR 测试框架提供了一种端到端测试方法，可以测试系统的各种功能，同时保持中间过程的真实性（如 JIRA 读写操作），但拦截最终返回给用户的 Seatalk 消息。这样可以在不干扰实际用户的情况下进行全面测试。

## 测试工具

本测试框架包含以下工具：

1. **jira_test_cases.py**: 数据驱动的测试脚本，包含多个测试套件和测试用例
2. **run_test.py**: 简单的单测试运行工具，用于快速测试和调试

## 使用方法

### 运行单个测试

使用 `run_test.py` 可以快速运行单个测试：

```bash
python run_test.py <服务器URL> [消息内容]
```

示例：

```bash
# 使用默认消息测试
python run_test.py http://localhost:8000

# 测试特定命令
python run_test.py http://localhost:8000 "@AutoRelease SPCB-12345"

# 测试私聊消息
python run_test.py http://localhost:8000 "/ai 查询SPCB-12345的详情"
```

### 运行测试套件

使用 `jira_test_cases.py` 可以运行预定义的测试套件：

```bash
python jira_test_cases.py [参数]
```

可用参数：

- `--suite <套件名称>`: 运行指定的测试套件
- `--url <服务器URL>`: 指定测试服务器地址
- `--output <文件名>`: 指定结果输出文件
- `--wait <秒数>`: 指定测试间隔时间
- `--list`: 列出所有可用的测试套件

示例：

```bash
# 列出所有可用的测试套件
python jira_test_cases.py --list

# 运行 JIRA 查询测试套件
python jira_test_cases.py --suite jira_query --url http://localhost:8000

# 运行所有测试套件，并设置等待时间为 2 秒
python jira_test_cases.py --url http://localhost:8000 --wait 2.0
```

## 测试套件说明

测试框架包含以下测试套件：

1. **jira_query**: JIRA 查询功能测试
2. **jira_field_update**: JIRA 字段更新功能测试
3. **timeline**: Timeline 相关功能测试
4. **bug**: Bug 相关功能测试
5. **mr**: MR 相关功能测试
6. **group**: 群组相关功能测试
7. **ai**: AI 功能测试

## 查看测试结果

测试完成后，可以通过以下方式查看结果：

1. **控制台输出**: 测试过程和结果会在控制台实时输出
2. **JSON 结果文件**: 测试结果会保存到指定的 JSON 文件中
3. **HTML 报告**: 每个测试会生成一个 HTML 报告，可以通过控制台输出的链接访问

## 自定义测试用例

如果需要添加新的测试用例，可以编辑 `jira_test_cases.py` 文件中的 `TEST_CASES` 字典。每个测试用例需要包含以下字段：

- `name`: 测试名称
- `description`: 测试描述
- `request`: 测试请求数据
- `expected`: 预期结果

示例：

```python
{
    "name": "自定义测试",
    "description": "这是一个自定义测试用例",
    "request": {
        "test_mode": True,
        "message_type": "group",
        "user_id": "test_user_001",
        "user_name": "测试用户",
        "message": "@AutoRelease 自定义命令",
        "group_id": "test_group_001"
    },
    "expected": {
        "success": True,
        "contains_text": ["预期包含的文本"]
    }
}
```

## 注意事项

1. 确保测试服务器已启动并且可以访问
2. 测试过程中会真实执行 JIRA 读写操作，请谨慎使用
3. 测试完成后，可能需要手动清理测试产生的数据
4. 建议在测试环境中使用，避免影响生产环境

## 故障排除

如果测试失败，可以检查以下几点：

1. 确认测试服务器是否正常运行
2. 检查网络连接是否正常
3. 查看服务器日志，寻找可能的错误
4. 确认测试用例中的 JIRA 单号是否存在
5. 检查用户权限是否足够执行相应操作 
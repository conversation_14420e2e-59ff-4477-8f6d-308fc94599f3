#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
命令处理器测试脚本
用于测试指令匹配系统
"""

import os
import sys
import json
import django
from icecream import ic

# 设置Django环境 - 修改为正确的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    # 尝试备用设置
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_ar_be.settings')
        django.setup()
        print("✅ 使用备用设置初始化成功")
    except Exception as e2:
        print(f"❌ 备用设置也失败: {e2}")
        print("⚠️  继续运行测试，但可能某些功能不可用")

try:
    from app01.command_processor import command_processor, CommandPattern
    print("✅ 成功导入command_processor模块")
except ImportError as e:
    print(f"❌ 导入command_processor失败: {e}")
    print("⚠️  测试将无法运行")
    sys.exit(1)


def test_pattern_match():
    """测试单个模式匹配"""
    print("\n=== 测试单个模式匹配 ===")
    
    # 创建一个测试模式
    pattern = CommandPattern(
        name="test_pattern",
        patterns=[
            r'^bug\s+SP[A-Z]+-\d+$',
            r'^SP[A-Z]+-\d+\s+bug$',
            r'^bug$'
        ],
        context_aware=True
    )
    
    # 测试用例
    test_cases = [
        {"command": "bug SPCB-54865", "expected": True},
        {"command": "SPCB-54865 bug", "expected": True},
        {"command": "bug", "expected": True},
        {"command": "something else", "expected": False}
    ]
    
    # 运行测试
    for test_case in test_cases:
        command = test_case["command"]
        expected = test_case["expected"]
        
        # 创建一个简单的模拟上下文
        class MockContext:
            def __init__(self):
                self.group_name = "[SPCB-54865]测试群"
        
        mock_context = MockContext()
        
        # 测试匹配
        score = pattern.match(command, mock_context)
        result = score > 0
        
        status = "✅ 通过" if result == expected else "❌ 失败"
        print(f"{status} - 命令: '{command}', 期望: {expected}, 结果: {result}, 分数: {score}")


def test_command_matching():
    """测试指令匹配系统"""
    print("\n=== 测试指令匹配系统 ===")
    
    # 测试用例
    test_cases = [
        # ===== Bug查询指令 =====
        {"command": "bug SPCB-54865", "group_name": None, "expected": "bug_query", "description": "Bug查询指令 - 标准格式"},
        {"command": "SPCB-54865 bug", "group_name": None, "expected": "bug_query", "description": "Bug查询指令 - 倒序格式"},
        {"command": "bug", "group_name": "[SPCB-54865]机器人调试群", "expected": "bug_query", "description": "Bug查询指令 - 上下文模式"},
        {"command": "查看bug", "group_name": "[SPCB-54865]机器人调试群", "expected": "bug_query", "description": "Bug查询指令 - 查看前缀"},
        {"command": "显示bug", "group_name": "[SPCB-54865]机器人调试群", "expected": "bug_query", "description": "Bug查询指令 - 显示前缀"},
        {"command": "bug列表", "group_name": "[SPCB-54865]机器人调试群", "expected": "bug_query", "description": "Bug查询指令 - 列表后缀"},
        {"command": "bug list", "group_name": "[SPCB-54865]机器人调试群", "expected": "bug_query", "description": "Bug查询指令 - 英文列表"},
        
        # ===== Timeline查询指令 =====
        {"command": "timeline SPCB-54865", "group_name": None, "expected": "timeline_query", "description": "Timeline查询指令 - 标准格式"},
        {"command": "SPCB-54865 timeline", "group_name": None, "expected": "timeline_query", "description": "Timeline查询指令 - 倒序格式"},
        {"command": "tl SPCB-54865", "group_name": None, "expected": "timeline_query", "description": "Timeline查询指令 - 缩写格式"},
        {"command": "SPCB-54865 tl", "group_name": None, "expected": "timeline_query", "description": "Timeline查询指令 - 缩写倒序"},
        {"command": "timeline", "group_name": "[SPCB-54865]机器人调试群", "expected": "timeline_query", "description": "Timeline查询指令 - 上下文模式"},
        {"command": "tl", "group_name": "[SPCB-54865]机器人调试群", "expected": "timeline_query", "description": "Timeline查询指令 - 缩写上下文"},
        {"command": "时间线", "group_name": "[SPCB-54865]机器人调试群", "expected": "timeline_query", "description": "Timeline查询指令 - 中文格式"},
        {"command": "查看时间线", "group_name": "[SPCB-54865]机器人调试群", "expected": "timeline_query", "description": "Timeline查询指令 - 查看前缀"},
        {"command": "显示时间线", "group_name": "[SPCB-54865]机器人调试群", "expected": "timeline_query", "description": "Timeline查询指令 - 显示前缀"},
        
        
        # ===== 创建群组指令 =====
        {"command": "new group SPCB-54865", "group_name": None, "expected": "new_group", "description": "创建群组指令 - 英文格式"},
        {"command": "SPCB-54865 new group", "group_name": None, "expected": "new_group", "description": "创建群组指令 - 倒序格式"},
        {"command": "创建群组 SPCB-54865", "group_name": None, "expected": "new_group", "description": "创建群组指令 - 中文格式"},
        {"command": "SPCB-54865 创建群组", "group_name": None, "expected": "new_group", "description": "创建群组指令 - 中文倒序"},
        {"command": "新建群组 SPCB-54865", "group_name": None, "expected": "new_group", "description": "创建群组指令 - 新建格式"},
        {"command": "SPCB-54865 新建群组", "group_name": None, "expected": "new_group", "description": "创建群组指令 - 新建倒序"},
        
        # ===== Checklist指令 =====
        {"command": "SPCB-54865 SO", "group_name": None, "expected": "checklist", "description": "Checklist指令 - SO格式"},
        {"command": "SPCB-54865 CM", "group_name": None, "expected": "checklist", "description": "Checklist指令 - CM格式"},
        {"command": "SPCB-54865 CC", "group_name": None, "expected": "checklist", "description": "Checklist指令 - CC格式"},
        {"command": "SPCB-54865 DB", "group_name": None, "expected": "checklist", "description": "Checklist指令 - DB格式"},
        {"command": "SO", "group_name": "[SPCB-54865]机器人调试群", "expected": "checklist", "description": "Checklist指令 - 上下文SO"},
        {"command": "CM", "group_name": "[SPCB-54865]机器人调试群", "expected": "checklist", "description": "Checklist指令 - 上下文CM"},
        {"command": "CC", "group_name": "[SPCB-54865]机器人调试群", "expected": "checklist", "description": "Checklist指令 - 上下文CC"},
        {"command": "DB", "group_name": "[SPCB-54865]机器人调试群", "expected": "checklist", "description": "Checklist指令 - 上下文DB"},
        
        # ===== JIRA单号查询 =====
        {"command": "SPCB-54865", "group_name": None, "expected": "jira_query", "description": "JIRA单号查询 - 纯单号"},
        {"command": "SPCB-12345", "group_name": None, "expected": "jira_query", "description": "JIRA单号查询 - 不同单号"},
        {"command": "SPCPM-67890", "group_name": None, "expected": "jira_query", "description": "JIRA单号查询 - SPCPM单号"},
        {"command": "查看 SPCB-54865", "group_name": None, "expected": "jira_query", "description": "JIRA单号查询 - 查看前缀"},
        {"command": "显示 SPCB-54865", "group_name": None, "expected": "jira_query", "description": "JIRA单号查询 - 显示前缀"},
        
        
        # ===== 不应该匹配add_service的指令 =====
        {"command": "add subtask", "group_name": None, "expected": None, "description": "不应该匹配add_service - 子任务"},
        {"command": "add 子任务", "group_name": None, "expected": None, "description": "不应该匹配add_service - 中文子任务"},
        {"command": "add reminder", "group_name": None, "expected": None, "description": "不应该匹配add_service - 提醒"},
        {"command": "add schedule", "group_name": None, "expected": None, "description": "不应该匹配add_service - 调度"},
        {"command": "add timeline", "group_name": None, "expected": None, "description": "不应该匹配add_service - 时间线"},
        {"command": "add SPCPM-12345 timeline reminder", "group_name": None, "expected": None, "description": "不应该匹配add_service - 复杂指令"},
        {"command": "add todo", "group_name": None, "expected": None, "description": "不应该匹配add_service - 待办"},
        {"command": "todo add", "group_name": None, "expected": None, "description": "不应该匹配add_service - 倒序待办"},
        
  
        
        # ===== 帮助指令 =====
        {"command": "help", "group_name": None, "expected": "help", "description": "帮助指令 - 英文格式"},
        {"command": "帮助", "group_name": None, "expected": "help", "description": "帮助指令 - 中文格式"},
        {"command": "?", "group_name": None, "expected": "help", "description": "帮助指令 - 问号格式"},
        {"command": "你能做什么？", "group_name": None, "expected": "help", "description": "帮助指令 - 问句格式"},
        {"command": "使用说明", "group_name": None, "expected": "help", "description": "帮助指令 - 说明格式"},

        
        # ===== 增强的帮助指令测试 =====
        {"command": "如何检查bug", "group_name": None, "expected": "help", "description": "帮助指令 - 如何检查"},
        {"command": "怎么创建定时任务", "group_name": None, "expected": "help", "description": "帮助指令 - 怎么创建"},
        {"command": "如何使用固定指令", "group_name": None, "expected": "help", "description": "帮助指令 - 如何使用"},
        {"command": "如何建群", "group_name": None, "expected": "help", "description": "帮助指令 - 如何建群"},
        {"command": "查看bug的步骤", "group_name": None, "expected": "help", "description": "帮助指令 - 查看步骤"},
        {"command": "定时任务创建指南", "group_name": None, "expected": "help", "description": "帮助指令 - 创建指南"},
        {"command": "建群操作说明", "group_name": None, "expected": "help", "description": "帮助指令 - 操作说明"},
        {"command": "功能使用手册", "group_name": None, "expected": "help", "description": "帮助指令 - 使用手册"},
        {"command": "指令帮助文档", "group_name": None, "expected": "help", "description": "帮助指令 - 帮助文档"},
        
        # ===== 定时任务指令（应该匹配schedule_task） =====
        {"command": "每个工作日的 17:35，查询：issue in linkedIssues(\"SPCB-54865\", \"is blocked by\") AND type = bug 的内容提醒到本群", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - jira 查询结果提醒到本群"},
        {"command": "每个工作日的 17:35，查询：issue in linkedIssues(\"SPCB-54865\", \"is blocked by\") AND type = bug 的内容提醒给我", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - jira 查询结果提醒到私聊"},
        {"command": "每天下午 5 点提醒我喝杯茶", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - 非 jira 查询结果提醒到私聊"},
        {"command": "每周一上午9点半提醒我查看邮件", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - 非 jira 查询结果提醒到私聊"},
        {"command": "每天早上 10 点提醒我有哪些需要处理的 bug", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - jira 查询结果提醒到私聊"},
        {"command": "每个工作日早上 10 点提醒我有哪些未完成的子任务", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - jira 查询结果提醒到私聊"},
        
        # ===== 包含关键词但应该是定时任务的指令 =====
        {"command": "每天提醒我查看SPCB-54865的bug", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - jira 查询结果提醒到私聊"},
        {"command": "每晚 7 点发送本群需求的 bug总结到群里", 
         "group_name": "[SPCB-54865]机器人调试群", 
         "expected": "schedule_task", "description": "定时任务指令 - jira 查询结果提醒到本群"},

        # ===== 边界情况测试 =====
        {"command": "", "group_name": None, "expected": None, "description": "边界情况 - 空字符串"},
        {"command": "   ", "group_name": None, "expected": None, "description": "边界情况 - 纯空格"},
        {"command": "random text", "group_name": None, "expected": None, "description": "边界情况 - 随机文本"},
        {"command": "完全不相关的指令", "group_name": None, "expected": None, "description": "边界情况 - 不相关指令"},
        
        # ===== 大小写测试 =====
        {"command": "BUG SPCB-54865", "group_name": None, "expected": "bug_query", "description": "大小写测试 - 大写BUG"},
        {"command": "Bug SPCB-54865", "group_name": None, "expected": "bug_query", "description": "大小写测试 - 首字母大写"},
        {"command": "TIMELINE SPCB-54865", "group_name": None, "expected": "timeline_query", "description": "大小写测试 - 大写TIMELINE"},
        {"command": "Timeline SPCB-54865", "group_name": None, "expected": "timeline_query", "description": "大小写测试 - 首字母大写Timeline"},

        
        # ===== 特殊字符和空格测试 =====
        {"command": "bug  SPCB-54865", "group_name": None, "expected": "bug_query", "description": "特殊字符测试 - 双空格"},  # 双空格
        {"command": "  bug SPCB-54865  ", "group_name": None, "expected": "bug_query", "description": "特殊字符测试 - 前后空格"},  # 前后空格
    ]
    
    # 运行测试
    results = []
    for i, test_case in enumerate(test_cases):
        # 打印测试信息
        print(f"\n测试 {i+1}: 命令='{test_case['command']}', 群名='{test_case['group_name']}', 期望='{test_case['expected']}'")
        
        # 直接测试匹配模式
        for pattern in command_processor.command_patterns:
            if pattern.name == test_case["expected"]:
                # 创建一个简单的模拟上下文
                class MockContext:
                    def __init__(self, group_name):
                        self.group_name = group_name
                
                mock_context = MockContext(test_case["group_name"])
                score = pattern.match(test_case["command"], mock_context)
                print(f"直接模式匹配 - 模式: {pattern.name}, 分数: {score}")
        
        # 使用test_command_match方法
        result = command_processor.test_command_match(
            test_case["command"], 
            test_case["group_name"]
        )
        
        # 添加期望结果和测试结果
        result["expected"] = test_case["expected"]
        result["passed"] = result["best_match"] == test_case["expected"]
        
        results.append(result)
        
        # 打印测试结果
        status = "✅ 通过" if result["passed"] else "❌ 失败"
        print(f"测试 {i+1}: {status}")
        print(f"  命令: {test_case['command']}")
        print(f"  群名: {test_case['group_name']}")
        print(f"  期望: {test_case['expected']}")
        print(f"  结果: {result['best_match']}")
        print(f"  所有匹配分数: {result['all_scores']}")
        if "error" in result:
            print(f"  错误: {result['error']}")
    
    # 计算统计信息
    total = len(results)
    passed = sum(1 for r in results if r["passed"])
    failed = total - passed
    
    print(f"\n测试统计: 总共 {total} 个测试，通过 {passed} 个，失败 {failed} 个")
    
    # 如果有失败的测试，打印详细信息
    if failed > 0:
        print("\n失败的测试详情:")
        for i, result in enumerate(results):
            if not result["passed"]:
                print(f"测试 {i+1}:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                print()



    # 运行测试
    results = []
    for i, test_case in enumerate(test_cases):
        print(f"\n测试 {i+1}: {test_case['description']}")
        print(f"  命令: '{test_case['command']}'")
        print(f"  期望: {test_case['expected']}")
        
        # 使用test_command_match方法
        result = command_processor.test_command_match(
            test_case["command"], 
            None
        )
        
        # 添加期望结果和测试结果
        result["expected"] = test_case["expected"]
        result["description"] = test_case["description"]
        result["passed"] = result["best_match"] == test_case["expected"]
        
        results.append(result)
        
        # 打印测试结果
        status = "✅ 通过" if result["passed"] else "❌ 失败"
        print(f"  结果: {status}")
        print(f"  实际: {result['best_match']}")
        if not result["passed"]:
            print(f"  所有匹配分数: {result['all_scores']}")
    
    # 计算统计信息
    total = len(results)
    passed = sum(1 for r in results if r["passed"])
    failed = total - passed
    
    print(f"\n=== 添加服务指令测试统计 ===")
    print(f"总共 {total} 个测试，通过 {passed} 个，失败 {failed} 个")
    
    # 如果有失败的测试，打印详细信息
    if failed > 0:
        print("\n失败的测试详情:")
        for i, result in enumerate(results):
            if not result["passed"]:
                print(f"测试 {i+1}: {result['description']}")
                print(f"  命令: '{result['command']}'")
                print(f"  期望: {result['expected']}")
                print(f"  实际: {result['best_match']}")
                print(f"  所有匹配分数: {result['all_scores']}")
                print()


def test_ai_intent_commands():
    """专门测试应该交给AI处理的指令"""
    print("\n=== 专门测试AI意图识别指令 ===")
    
    # 测试用例
    test_cases = [
        # 定时任务指令（应该被识别为定时任务）
        {"command": "每个工作日的 17:35，查询：issue in linkedIssues(\"SPCB-54865\", \"is blocked by\") AND type = bug 的内容提醒到本群", 
         "expected": "schedule_task", "description": "复杂定时任务指令"},
        {"command": "每天提醒我查看SPCB-54865的bug", 
         "expected": "schedule_task", "description": "简单定时任务指令"},
        {"command": "每周一上午9点提醒我检查项目进度", 
         "expected": "schedule_task", "description": "周期性定时任务"},
        {"command": "每个工作日上午 10 点提醒我”检查项目进度“", 
         "expected": "schedule_task", "description": "定时提醒指令"},
        {"command": "每周五下午5点提醒我更新项目进度", 
         "expected": "schedule_task", "description": "周期性项目更新"},
        
        # 自然语言查询指令
        {"command": "帮我查看一下SPCB-53541 的最新状态", 
         "expected": "jira_query", "description": "自然语言状态查询"},
        {"command": "SPCB-53541 现在怎么样了", 
         "expected": "jira_query", "description": "口语化状态查询"},
        {"command": "告诉我SPCB-53541 的进度", 
         "expected": "jira_query", "description": "自然语言进度查询"},
        {"command": "SPCB-53541 有什么问题吗", 
         "expected": "jira_query", "description": "自然语言问题查询"},
        
        # 复杂查询指令
        {"command": "查询我有哪些需要处理的bug", 
         "expected": "jira_query", "description": "复杂条件查询"},
        {"command": "我有哪些需要处理的 bug", 
         "expected": "jira_query", "description": "复杂条件查询"},
        {"command": "我有哪些需要处理的子任务", 
         "expected": "jira_query", "description": "复杂条件查询"},
        {"command": "我有哪些未完成的子任务", 
         "expected": "jira_query", "description": "复杂条件查询"},
        {"command": "我有哪些未完成的sub-task", 
         "expected": "jira_query", "description": "复杂条件查询"},
        
        # 操作指令
        {"command": "更新SPCB-54865的状态为Done", 
         "expected": None, "description": "状态更新指令"},
        {"command": "为SPCB-54865添加评论", 
         "expected": None, "description": "添加评论指令"},
        {"command": "分配SPCB-54865给张三", 
         "expected": None, "description": "分配指令"},
        {"command": "设置SPCB-54865的优先级为High", 
         "expected": None, "description": "设置优先级指令"},
        
        # 统计和分析指令
        {"command": "统计SPCB-54865的bug数量", 
         "expected": None, "description": "统计指令"},
        {"command": "我上周完成了多少 sub-task", 
         "expected": None, "description": "分析指令"},
        {"command": "我本月完成的子任务总工作量是多少", 
         "expected": None, "description": "分析指令"},
        {"command": "我上周完成的sub-task总工作量是多少", 
         "expected": None, "description": "分析指令"},
        {"command": "我上周处理了几个bug", 
         "expected": None, "description": "分析指令"},
        {"command": "我上周提交了几个 bug", 
         "expected": None, "description": "分析指令"},

        
        # 文档处理指令
        {"command": "总结SPCB-54865的PRD文档", 
         "expected": None, "description": "文档总结指令"},
        {"command": "翻译SPCB-54865的需求文档", 
         "expected": None, "description": "文档翻译指令"},
        {"command": "提取SPCB-54865文档的主要内容", 
         "expected": None, "description": "文档内容提取"},
        {"command": "分析SPCB-54865的PRD文档", 
         "expected": None, "description": "文档分析指令"},
        
        # 模糊指令
        {"command": "SPCB-54865", 
         "expected": "jira_query", "description": "纯JIRA单号（应该匹配传统指令）"},
        {"command": "查看SPCB-54865", 
         "expected": "jira_query", "description": "简单查看指令（应该匹配传统指令）"},
        {"command": "显示SPCB-54865", 
         "expected": "jira_query", "description": "简单显示指令（应该匹配传统指令）"},
        
        # 边界情况
        {"command": "请帮我", 
         "expected": None, "description": "不完整的请求"},
        {"command": "谢谢", 
         "expected": None, "description": "感谢语句"},
        {"command": "好的", 
         "expected": None, "description": "确认语句"},
        {"command": "明白了", 
         "expected": None, "description": "理解确认"},
        
        # ===== 增强的自然语言意图测试 =====
        {"command": "请教一下如何检查SPCB-54865的bug", 
         "expected": "help", "description": "自然语言bug检查指导"},
        {"command": "能告诉我怎么为SPCPM-12345创建定时提醒吗", 
         "expected": "help", "description": "自然语言定时任务创建指导"},
        {"command": "请问固定指令的使用方法是什么", 
         "expected": "help", "description": "自然语言指令使用指导"},
        {"command": "建群的具体操作步骤是怎样的", 
         "expected": "help", "description": "自然语言建群指导"},
        {"command": "SPCB-54865的bug检查流程", 
         "expected": "help", "description": "带JIRA编号的流程指导"},
        {"command": "定时任务设置的最佳实践", 
         "expected": "help", "description": "最佳实践指导"},
        {"command": "固定指令的常用示例", 
         "expected": "help", "description": "示例请求"},
        {"command": "建群功能的注意事项", 
         "expected": "help", "description": "注意事项查询"},
    ]
    
    # 运行测试
    results = []
    for i, test_case in enumerate(test_cases):
        print(f"\n测试 {i+1}: {test_case['description']}")
        print(f"  命令: '{test_case['command']}'")
        print(f"  期望: {test_case['expected']}")
        
        # 使用test_command_match方法
        result = command_processor.test_command_match(
            test_case["command"], 
            "[SPCB-54865]机器人调试群"  # 提供群名上下文
        )
        
        # 添加期望结果和测试结果
        result["expected"] = test_case["expected"]
        result["description"] = test_case["description"]
        result["passed"] = result["best_match"] == test_case["expected"]
        
        results.append(result)
        
        # 打印测试结果
        status = "✅ 通过" if result["passed"] else "❌ 失败"
        print(f"  结果: {status}")
        print(f"  实际: {result['best_match']}")
        if not result["passed"]:
            print(f"  所有匹配分数: {result['all_scores']}")
    
    # 计算统计信息
    total = len(results)
    passed = sum(1 for r in results if r["passed"])
    failed = total - passed
    
    print(f"\n=== AI意图识别指令测试统计 ===")
    print(f"总共 {total} 个测试，通过 {passed} 个，失败 {failed} 个")
    
    # 如果有失败的测试，打印详细信息
    if failed > 0:
        print("\n失败的测试详情:")
        for i, result in enumerate(results):
            if not result["passed"]:
                print(f"测试 {i+1}: {result['description']}")
                print(f"  命令: '{result['command']}'")
                print(f"  期望: {result['expected']}")
                print(f"  实际: {result['best_match']}")
                print(f"  所有匹配分数: {result['all_scores']}")
                print()


def test_ai_intent_recognition():
    """专门测试AI意图识别和实体识别功能"""
    print("\n=== 专门测试AI意图识别和实体识别 ===")
    
    # 由于AI模块需要配置，我们先测试AI意图识别模块的基本功能
    try:
        from app01.ai_module.ai_assistant import AIAssistant
        from app01.ai_module.prompts.intent_detection import IntentRanker
        
        print("✅ AI模块导入成功")
        
        # 测试IntentRanker
        print("\n--- 测试IntentRanker ---")
        ranker = IntentRanker()
        
        # 测试用例
        test_cases = [
            # 应该被识别为jira_write的指令
            {
                "command": "为SPCB-54865创建一个子任务",
                "expected_intent": "jira_write",
                "expected_operation": "create_subtask",
                "description": "创建子任务指令"
            },
            {
                "command": "在SPCB-54865下添加一个bug",
                "expected_intent": "jira_write", 
                "expected_operation": "create_issue",
                "description": "创建issue指令"
            },
            {
                "command": "更新SPCB-54865的状态为Done",
                "expected_intent": "jira_write",
                "expected_operation": "update_issue",
                "description": "更新状态指令"
            },
            
            # 应该被识别为jira_read的指令
            {
                "command": "查看SPCB-54865的详细信息",
                "expected_intent": "jira_read",
                "expected_operation": "get_issue_details",
                "description": "查看详情指令"
            },
      
            
            # 应该被识别为document_processing的指令
            {
                "command": "总结SPCB-54865的PRD文档",
                "expected_intent": "document_processing",
                "expected_operation": "summarize",
                "description": "文档总结指令"
            },
            {
                "command": "翻译SPCB-54865的需求文档",
                "expected_intent": "document_processing",
                "expected_operation": "translate",
                "description": "文档翻译指令"
            },
            {
                "command": "提取SPCB-54865文档的主要内容",
                "expected_intent": "document_processing",
                "expected_operation": "extract_content",
                "description": "内容提取指令"
            },
            
            # 应该被识别为scheduling的指令
            {
                "command": "每天提醒我查看SPCB-54865的bug",
                "expected_intent": "scheduling",
                "expected_operation": "create_reminder",
                "description": "创建提醒指令"
            },
            {
                "command": "每周一上午9点提醒我检查项目进度",
                "expected_intent": "scheduling",
                "expected_operation": "create_schedule",
                "description": "创建定时任务指令"
            },
            
            # 应该被识别为add_service的指令（不应该被AI识别）
            {
                "command": "add shopee-chatbot-mmfchatbotconsole-live",
                "expected_intent": "add_service",  # 这个应该被传统模式匹配，不应该被AI识别
                "expected_operation": None,
                "description": "添加服务指令（应该被传统模式匹配）"
            },
            
            # 模糊指令
            {
                "command": "SPCB-54865现在怎么样了",
                "expected_intent": "jira_read",
                "expected_operation": "get_issue_status",
                "description": "模糊状态查询"
            },

        ]
        
        print(f"准备测试 {len(test_cases)} 个AI意图识别用例")
        
        # 由于AI模块可能需要配置，我们主要测试模块的基本结构
        print("\n测试IntentRanker基本功能:")
        
        # 测试特殊规则应用
        for i, test_case in enumerate(test_cases):
            print(f"\n测试 {i+1}: {test_case['description']}")
            print(f"  命令: '{test_case['command']}'")
            print(f"  期望意图: {test_case['expected_intent']}")
            
            # 测试特殊规则应用
            try:
                # 测试特殊规则
                special_score = ranker._apply_special_intent_rules(
                    test_case['command'], 
                    test_case['expected_intent']
                )
                print(f"  特殊规则分数: {special_score}")
                
                # 测试关键词检测
                has_jira_key = bool(ranker._extract_jira_keys(test_case['command']))
                print(f"  包含JIRA单号: {has_jira_key}")
                
                if has_jira_key:
                    jira_keys = ranker._extract_jira_keys(test_case['command'])
                    print(f"  提取的JIRA单号: {jira_keys}")
                
            except Exception as e:
                print(f"  测试异常: {str(e)}")
        
        print("\n✅ IntentRanker基本功能测试完成")
        
        # 测试AIAssistant基本功能
        print("\n--- 测试AIAssistant ---")
        try:
            assistant = AIAssistant()
            print("✅ AIAssistant实例化成功")
            
            # 测试基本方法是否存在
            methods_to_check = [
                'detect_intent',
                'extract_entities', 
                'process_query',
                'generate_response'
            ]
            
            for method_name in methods_to_check:
                if hasattr(assistant, method_name):
                    print(f"✅ 方法 {method_name} 存在")
                else:
                    print(f"❌ 方法 {method_name} 不存在")
                    
        except Exception as e:
            print(f"❌ AIAssistant测试失败: {str(e)}")
        
    except ImportError as e:
        print(f"❌ AI模块导入失败: {str(e)}")
        print("这可能是因为AI模块需要额外的配置或依赖")
    except Exception as e:
        print(f"❌ AI意图识别测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())


def test_entity_extraction():
    """测试实体提取功能"""
    print("\n=== 专门测试实体提取功能 ===")
    
    try:
        from app01.ai_module.prompts.intent_detection import IntentRanker
        
        ranker = IntentRanker()
        
        # 测试实体提取用例
        test_cases = [
            {
                "command": "为SPCB-54865创建一个子任务",
                "expected_entities": {
                    "jira_key": "SPCB-54865",
                    "operation": "create",
                    "issue_type": "subtask"
                },
                "description": "创建子任务"
            },
            {
                "command": "在SPCB-12345下添加一个bug",
                "expected_entities": {
                    "jira_key": "SPCB-12345", 
                    "operation": "add",
                    "issue_type": "bug"
                },
                "description": "添加bug"
            },
            {
                "command": "更新SPCPM-67890的状态为Done",
                "expected_entities": {
                    "jira_key": "SPCPM-67890",
                    "operation": "update",
                    "field": "status",
                    "value": "Done"
                },
                "description": "更新状态"
            },
            {
                "command": "每天提醒我查看SPCB-54865的bug",
                "expected_entities": {
                    "jira_key": "SPCB-54865",
                    "frequency": "每天",
                    "action": "查看",
                    "target": "bug"
                },
                "description": "定时提醒"
            },
            {
                "command": "add shopee-chatbot-mmfchatbotconsole-live",
                "expected_entities": {
                    "service_name": "shopee-chatbot-mmfchatbotconsole-live",
                    "operation": "add"
                },
                "description": "添加服务"
            }
        ]
        
        # ===== 新增的自然语言实体提取测试 =====
        test_cases.append({
            "command": "如何检查SPCB-54865的bug",
            "expected_entities": {
                "intent_type": "help",
                "topic": "bug_check",
                "jira_key": "SPCB-54865"
            },
            "description": "提取bug检查指导实体"
        })
        test_cases.append({
            "command": "怎么为SPCPM-12345创建每天提醒",
            "expected_entities": {
                "intent_type": "help",
                "topic": "reminder_creation",
                "jira_key": "SPCPM-12345",
                "frequency": "每天"
            },
            "description": "提取定时任务创建实体"
        })
        test_cases.append({
            "command": "固定指令的使用方法",
            "expected_entities": {
                "intent_type": "help",
                "topic": "command_usage"
            },
            "description": "提取指令使用指导实体"
        })
        test_cases.append({
            "command": "建群功能的具体操作步骤",
            "expected_entities": {
                "intent_type": "help",
                "topic": "group_creation",
                "detail_level": "具体步骤"
            },
            "description": "提取建群指导实体"
        })
        test_cases.append({
            "command": "SPCB-54865的bug检查流程",
            "expected_entities": {
                "intent_type": "help",
                "topic": "bug_check_workflow",
                "jira_key": "SPCB-54865"
            },
            "description": "提取带JIRA编号的工作流实体"
        })
        
        print(f"准备测试 {len(test_cases)} 个实体提取用例")
        
        for i, test_case in enumerate(test_cases):
            print(f"\n测试 {i+1}: {test_case['description']}")
            print(f"  命令: '{test_case['command']}'")
            
            try:
                # 测试JIRA单号提取
                jira_keys = ranker._extract_jira_keys(test_case['command'])
                print(f"  提取的JIRA单号: {jira_keys}")
                
                # 测试关键词检测
                keywords = {
                    "create": ["创建", "新建", "添加", "create", "add"],
                    "update": ["更新", "修改", "update", "modify"],
                    "delete": ["删除", "移除", "delete", "remove"],
                    "view": ["查看", "显示", "获取", "view", "show", "get"],
                    "schedule": ["每天", "每周", "每月", "定时", "定期", "提醒"]
                }
                
                detected_keywords = {}
                for category, words in keywords.items():
                    for word in words:
                        if word in test_case['command']:
                            detected_keywords[category] = word
                            break
                
                print(f"  检测到的关键词: {detected_keywords}")
                
                # 测试服务名称提取（针对add指令）
                if test_case['command'].startswith('add '):
                    service_name = test_case['command'][4:]  # 去掉"add "
                    print(f"  提取的服务名称: {service_name}")
                
            except Exception as e:
                print(f"  测试异常: {str(e)}")
        
        print("\n✅ 实体提取测试完成")
        
    except Exception as e:
        print(f"❌ 实体提取测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())


if __name__ == "__main__":
    # 先测试单个模式匹配
    test_pattern_match()
    
    # 再测试完整的指令匹配系统
    test_command_matching()
    
    # 测试AI意图识别指令
    test_ai_intent_commands()
    
    # 测试AI意图识别和实体识别
    test_ai_intent_recognition()
    
    # 测试实体提取
    test_entity_extraction() 
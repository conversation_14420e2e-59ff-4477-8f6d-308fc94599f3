#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用mock_seatalk_api测试命令处理器
"""

import os
import sys
import json
import requests
import django
from pprint import pprint

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()


def test_with_mock_api():
    """使用mock_seatalk_api测试命令处理器"""
    
    # 测试用例
    test_cases = [
        # 测试Bug查询指令
        {
            "message_type": "group",
            "user_id": "liang.tang",
            "user_name": "测试用户",
            "message": "@ChatbotAR bug",
            "group_id": "test_group_001",
            "group_title": "[SPCB-54865]机器人调试群",
            "description": "Bug查询指令 - 纯bug命令，从群名获取单号"
        },
        
        # 测试定时任务指令
        {
            "message_type": "group",
            "user_id": "liang.tang",
            "user_name": "测试用户",
            "message": "@ChatbotAR 每个工作日的 17:35，查询：issue in linkedIssues(\"SPCB-54865\", \"is blocked by\") AND type = bug 的内容提醒到本群",
            "group_id": "test_group_001",
            "group_title": "[SPCB-54865]机器人调试群",
            "description": "定时任务指令 - 包含bug关键词但应该识别为定时任务"
        }
    ]
    
    # API端点
    api_url = "http://127.0.0.1:8000/api/mock-seatalk/webhook/"
    
    # 运行测试
    results = []
    for i, test_case in enumerate(test_cases):
        print(f"\n执行测试 {i+1}: {test_case['description']}")
        
        # 构建请求数据
        request_data = {
            "test_mode": True,
            "message_type": test_case["message_type"],
            "user_id": test_case["user_id"],
            "user_name": test_case["user_name"],
            "message": test_case["message"],
            "group_id": test_case.get("group_id"),
            "timestamp": "2025-06-29T19:46:25.748760Z"
        }
        
        try:
            # 发送请求
            print(f"发送请求到 {api_url}")
            response = requests.post(api_url, json=request_data)
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                print(f"测试成功，拦截到 {len(result.get('intercepted_messages', []))} 条消息")
                
                # 添加测试信息
                result["test_case"] = test_case
                results.append(result)
                
                # 打印拦截的消息
                print("拦截的消息:")
                for msg in result.get("intercepted_messages", []):
                    print(f"- 类型: {msg.get('type')}")
                    text = msg.get("text", "")
                    print(f"- 内容: {text[:100]}..." if len(text) > 100 else f"- 内容: {text}")
            else:
                print(f"测试失败: HTTP {response.status_code}")
                print(response.text)
        except Exception as e:
            print(f"测试异常: {str(e)}")
    
    # 打印测试结果
    print("\n测试结果摘要:")
    for i, result in enumerate(results):
        print(f"测试 {i+1}: {result['test_case']['description']}")
        print(f"- 成功: {result['success']}")
        print(f"- 响应时间: {result.get('response_time', 'N/A')} 秒")
        print(f"- 拦截消息数: {len(result.get('intercepted_messages', []))}")
        print()


if __name__ == "__main__":
    test_with_mock_api() 
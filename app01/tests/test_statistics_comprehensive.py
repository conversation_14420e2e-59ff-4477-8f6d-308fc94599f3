#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计模块综合测试
测试统计模块的各个组件和功能
"""

import json
import time
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from django.test import TestCase, RequestFactory, override_settings
from django.utils import timezone
from django.contrib.auth.models import User
from django.urls import reverse

from app01.models import (
    BotAccessEvent, CommandExecutionRecord, 
    SystemHealthSnapshot, SystemPerformanceMetrics,
    CronJobExecutionMonitor
)
from app01.statistics.middleware import StatisticsMiddleware
from app01.statistics.collectors import (
    BotAccessEventCollector, CommandExecutionCollector
)
from app01.statistics.services import (
    RealtimeStatsService, PerformanceAnalysisService,
    CronjobMonitoringService
)
from app01.statistics.decorators import (
    track_command_execution, create_command_tracker
)


class StatisticsModelsTest(TestCase):
    """测试统计模型"""
    
    def setUp(self):
        """设置测试数据"""
        self.test_user_id = 'test_user_001'
        self.test_email = '<EMAIL>'
        
    def test_bot_access_event_creation(self):
        """测试机器人访问事件创建"""
        event = BotAccessEvent.objects.create(
            event_id='test_event_001',
            event_type='user_enter_chatroom_with_bot',
            user_id=self.test_user_id,
            email=self.test_email,
            timestamp=int(timezone.now().timestamp()),
            event_time=timezone.now()
        )
        
        self.assertEqual(event.user_id, self.test_user_id)
        self.assertEqual(event.email, self.test_email)
        self.assertTrue(event.created_at)
        
    def test_command_execution_record_creation(self):
        """测试指令执行记录创建"""
        start_time = timezone.now()
        end_time = start_time + timedelta(seconds=2.5)
        
        record = CommandExecutionRecord.objects.create(
            user_id=self.test_user_id,
            user_email=self.test_email,
            command_type='ai_query',
            raw_input='测试查询',
            intent='query_tasks',
            success=True,
            processing_time=2.5,
            start_time=start_time,
            end_time=end_time,
            response_content='测试响应内容',
            database_queries=3,
            api_calls=['test_api']
        )
        
        self.assertEqual(record.user_id, self.test_user_id)
        self.assertEqual(record.command_type, 'ai_query')
        self.assertTrue(record.success)
        self.assertEqual(record.processing_time, 2.5)
        self.assertEqual(record.response_length, len('测试响应内容'))
        
    def test_system_health_snapshot_creation(self):
        """测试系统健康快照创建"""
        snapshot = SystemHealthSnapshot.objects.create(
            snapshot_time=timezone.now(),
            overall_status='healthy',
            total_users_today=25,
            active_users_now=10,
            total_commands_today=150,
            success_rate_today=95.5,
            avg_response_time=1.2,
            memory_usage=65.0,
            cpu_usage=45.0
        )
        
        self.assertEqual(snapshot.overall_status, 'healthy')
        self.assertEqual(snapshot.total_users_today, 25)
        self.assertEqual(snapshot.success_rate_today, 95.5)


class StatisticsCollectorsTest(TestCase):
    """测试数据收集器"""
    
    def setUp(self):
        self.bot_collector = BotAccessEventCollector()
        self.command_collector = CommandExecutionCollector()
        
    def test_bot_access_collector(self):
        """测试机器人访问事件收集器"""
        event_data = {
            'event_id': 'test_event_001',
            'event_type': 'user_enter_chatroom_with_bot',
            'user_id': 'test_user_001',
            'email': '<EMAIL>',
            'timestamp': int(timezone.now().timestamp()),
            'event_time': timezone.now()
        }
        
        result = self.bot_collector.collect_access_event(event_data)
        self.assertTrue(result)
        
        # 验证数据库中的记录
        event = BotAccessEvent.objects.get(event_id='test_event_001')
        self.assertEqual(event.user_id, 'test_user_001')
        
    def test_command_execution_collector(self):
        """测试指令执行收集器"""
        context_data = {
            'user_id': 'test_user_001',
            'user_email': '<EMAIL>',
            'command_type': 'ai_query',
            'raw_input': '测试查询',
            'intent': 'query_tasks'
        }
        
        # 开始跟踪
        execution_id = self.command_collector.start_command_tracking(context_data)
        self.assertIsNotNone(execution_id)
        
        # 完成跟踪
        result_data = {
            'success': True,
            'response_content': '测试响应',
            'processing_time': 1.5,
            'api_calls': ['test_api'],
            'database_queries': 2
        }
        
        success = self.command_collector.finish_command_tracking(execution_id, result_data)
        self.assertTrue(success)
        
        # 验证数据库记录
        record = CommandExecutionRecord.objects.get(execution_id=execution_id)
        self.assertEqual(record.user_id, 'test_user_001')
        self.assertTrue(record.success)
        self.assertEqual(record.response_content, '测试响应')


class StatisticsServicesTest(TestCase):
    """测试统计服务"""
    
    def setUp(self):
        self.stats_service = RealtimeStatsService()
        self.create_test_data()
        
    def create_test_data(self):
        """创建测试数据"""
        now = timezone.now()
        today = now.date()
        
        # 创建访问事件
        for i in range(5):
            BotAccessEvent.objects.create(
                event_id=f'test_event_{i}',
                event_type='user_enter_chatroom_with_bot',
                user_id=f'test_user_{i}',
                email=f'test{i}@example.com',
                timestamp=int(now.timestamp()),
                event_time=now - timedelta(minutes=i*10)
            )
        
        # 创建指令记录
        for i in range(10):
            CommandExecutionRecord.objects.create(
                user_id=f'test_user_{i%3}',
                command_type='ai_query' if i % 2 == 0 else 'jira_query',
                raw_input=f'测试查询{i}',
                success=i < 8,  # 80%成功率
                processing_time=1.0 + i * 0.1,
                start_time=now - timedelta(hours=i),
                end_time=now - timedelta(hours=i) + timedelta(seconds=1.0 + i * 0.1)
            )
        
        # 创建系统健康快照
        SystemHealthSnapshot.objects.create(
            snapshot_time=now,
            overall_status='healthy',
            total_users_today=5,
            active_users_now=3,
            total_commands_today=10,
            success_rate_today=80.0,
            avg_response_time=1.5
        )
        
    def test_realtime_dashboard_data(self):
        """测试实时面板数据获取"""
        data = self.stats_service.get_realtime_dashboard_data()
        
        self.assertNotIn('error', data)
        self.assertIn('active_users', data)
        self.assertIn('total_users', data)
        self.assertIn('today_commands', data)
        self.assertIn('success_rate', data)
        
        # 验证数据合理性
        self.assertGreaterEqual(data['active_users'], 0)
        self.assertGreaterEqual(data['success_rate'], 0)
        self.assertLessEqual(data['success_rate'], 100)
        
    def test_command_trends(self):
        """测试指令趋势数据"""
        data = self.stats_service.get_command_trends(days=7)
        
        self.assertNotIn('error', data)
        self.assertIn('dates', data)
        self.assertIn('total', data)
        self.assertIn('success', data)
        self.assertIn('failed', data)
        
        # 验证数据结构
        self.assertEqual(len(data['dates']), 7)
        self.assertEqual(len(data['total']), 7)
        
    def test_user_activity_stats(self):
        """测试用户活动统计"""
        data = self.stats_service.get_user_activity_stats(days=7)
        
        self.assertNotIn('error', data)
        self.assertIn('summary', data)
        
        summary = data['summary']
        self.assertIn('total_users', summary)
        self.assertIn('total_commands', summary)


class StatisticsMiddlewareTest(TestCase):
    """测试统计中间件"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.get_response = MagicMock(return_value=MagicMock(status_code=200))
        self.middleware = StatisticsMiddleware(self.get_response)
        
    @override_settings(
        STATISTICS_MIDDLEWARE_ENABLED=True,
        STATISTICS_API_PATHS=['/api/']
    )
    def test_middleware_enabled_for_api(self):
        """测试中间件对API请求的处理"""
        request = self.factory.get('/api/test')
        
        with patch('app01.statistics.middleware.StatisticsMiddleware._record_request_stats') as mock_record:
            response = self.middleware(request)
            
            # 验证统计记录被调用
            mock_record.assert_called_once()
            
    @override_settings(
        STATISTICS_MIDDLEWARE_ENABLED=True,
        STATISTICS_COLLECT_ALL_REQUESTS=False,
        STATISTICS_API_PATHS=['/api/']
    )
    def test_middleware_skips_non_api(self):
        """测试中间件跳过非API请求"""
        request = self.factory.get('/admin/')
        
        with patch('app01.statistics.middleware.StatisticsMiddleware._record_request_stats') as mock_record:
            response = self.middleware(request)
            
            # 验证统计记录未被调用
            mock_record.assert_not_called()


class StatisticsDecoratorsTest(TestCase):
    """测试统计装饰器"""
    
    def test_command_tracker_context_manager(self):
        """测试指令跟踪上下文管理器"""
        context_data = {
            'user_id': 'test_user_001',
            'command_type': 'ai_query',
            'raw_input': '测试查询'
        }
        
        tracker = create_command_tracker(
            user_id='test_user_001',
            command_type='ai_query',
            raw_input='测试查询'
        )
        
        with tracker:
            # 模拟一些处理时间
            time.sleep(0.1)
            
            # 更新结果
            tracker.update_result(
                response_content='测试响应',
                processed_command='处理后的查询'
            )
        
        # 验证记录已创建
        self.assertTrue(CommandExecutionRecord.objects.filter(
            user_id='test_user_001',
            command_type='ai_query'
        ).exists())


class StatisticsAPITest(TestCase):
    """测试统计API接口"""
    
    def setUp(self):
        self.create_test_data()
        
    def create_test_data(self):
        """创建测试数据"""
        # 创建一些测试数据
        for i in range(5):
            CommandExecutionRecord.objects.create(
                user_id=f'test_user_{i}',
                command_type='ai_query',
                raw_input=f'测试查询{i}',
                success=True,
                processing_time=1.0,
                start_time=timezone.now() - timedelta(hours=i),
                end_time=timezone.now() - timedelta(hours=i) + timedelta(seconds=1)
            )
            
    def test_realtime_dashboard_api(self):
        """测试实时面板API"""
        response = self.client.get('/api/statistics/realtime/dashboard/')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('data', data)
        
        dashboard_data = data['data']
        self.assertIn('active_users', dashboard_data)
        self.assertIn('today_commands', dashboard_data)
        
    def test_command_trends_api(self):
        """测试指令趋势API"""
        response = self.client.get('/api/statistics/realtime/command-trends/?days=7')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        
    def test_command_records_api(self):
        """测试指令记录查询API"""
        response = self.client.get('/api/statistics/data/command-records/?page_size=10')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('records', data['data'])
        self.assertIn('pagination', data['data'])


class StatisticsIntegrationTest(TestCase):
    """统计模块集成测试"""
    
    def test_end_to_end_command_tracking(self):
        """测试端到端的指令跟踪流程"""
        # 1. 创建指令跟踪器
        tracker = create_command_tracker(
            user_id='integration_test_user',
            command_type='ai_query',
            raw_input='集成测试查询'
        )
        
        # 2. 使用上下文管理器执行
        with tracker:
            # 模拟处理
            time.sleep(0.1)
            
            # 更新结果
            tracker.update_result(
                response_content='集成测试响应',
                api_calls=['test_api_1', 'test_api_2'],
                database_queries=3
            )
        
        # 3. 验证数据被正确记录
        record = CommandExecutionRecord.objects.get(
            user_id='integration_test_user'
        )
        
        self.assertEqual(record.command_type, 'ai_query')
        self.assertEqual(record.raw_input, '集成测试查询')
        self.assertTrue(record.success)
        self.assertEqual(record.response_content, '集成测试响应')
        self.assertEqual(record.api_calls, ['test_api_1', 'test_api_2'])
        self.assertEqual(record.database_queries, 3)
        
        # 4. 测试通过API获取这条记录
        response = self.client.get(
            f'/api/statistics/data/command-records/?user_id=integration_test_user'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        
        records = data['data']['records']
        self.assertEqual(len(records), 1)
        self.assertEqual(records[0]['user_id'], 'integration_test_user')
        
    def test_statistics_dashboard_page(self):
        """测试统计面板页面"""
        response = self.client.get('/api/statistics/dashboard/')
        
        # 应该返回HTML页面
        self.assertEqual(response.status_code, 200)
        self.assertIn('text/html', response.get('Content-Type', ''))


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['app01.tests.test_statistics_comprehensive']) 
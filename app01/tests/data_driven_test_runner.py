#!/usr/bin/env python3
"""
数据驱动测试执行器
基于 comprehensive_test_data.json 执行全面测试
"""

import json
import requests
import time
import argparse
import sys
from datetime import datetime
from typing import Dict, List, Any, Tuple
import os

class ValidationEngine:
    """验证引擎 - 支持多种验证类型"""
    
    def __init__(self):
        self.validation_rules = {
            'content_contains': self._validate_content_contains,
            'jql_validation': self._validate_jql,
            'error_message': self._validate_error_message,
            'intent_classification': self._validate_intent
        }
    
    def validate_response(self, test_case: Dict, response_data: Dict) -> Tuple[bool, str]:
        """验证响应是否符合预期"""
        validation_type = test_case.get('validation_type', 'content_contains')
        validation_func = self.validation_rules.get(validation_type)
        
        if not validation_func:
            return False, f"未知的验证类型: {validation_type}"
        
        return validation_func(test_case, response_data)
    
    def _validate_content_contains(self, test_case: Dict, response_data: Dict) -> Tuple[bool, str]:
        """验证内容包含关键词"""
        expected_keywords = test_case.get('expected_keywords', [])
        intercepted_messages = response_data.get('intercepted_messages', [])
        
        if not intercepted_messages:
            return False, "没有拦截到任何消息"
        
        # 合并所有消息内容
        all_content = ' '.join([msg.get('text', '') for msg in intercepted_messages])
        
        # 检查每个关键词
        missing_keywords = []
        for keyword in expected_keywords:
            if keyword.lower() not in all_content.lower():
                missing_keywords.append(keyword)
        
        if missing_keywords:
            return False, f"缺少关键词: {', '.join(missing_keywords)}"
        
        return True, "所有关键词都匹配"
    
    def _validate_jql(self, test_case: Dict, response_data: Dict) -> Tuple[bool, str]:
        """验证JQL查询语法"""
        expected_keywords = test_case.get('expected_keywords', [])
        intercepted_messages = response_data.get('intercepted_messages', [])
        
        if not intercepted_messages:
            return False, "没有拦截到任何消息"
        
        all_content = ' '.join([msg.get('text', '') for msg in intercepted_messages])
        
        # 检查JQL关键词
        for keyword in expected_keywords:
            if keyword.lower() not in all_content.lower():
                return False, f"JQL验证失败: 缺少关键词 '{keyword}'"
        
        return True, "JQL验证通过"
    
    def _validate_error_message(self, test_case: Dict, response_data: Dict) -> Tuple[bool, str]:
        """验证错误消息"""
        expected_keywords = test_case.get('expected_keywords', [])
        intercepted_messages = response_data.get('intercepted_messages', [])
        
        if not intercepted_messages:
            return False, "没有拦截到任何消息"
        
        all_content = ' '.join([msg.get('text', '') for msg in intercepted_messages])
        
        # 检查错误关键词
        for keyword in expected_keywords:
            if keyword.lower() not in all_content.lower():
                return False, f"错误消息验证失败: 缺少关键词 '{keyword}'"
        
        return True, "错误消息验证通过"
    
    def _validate_intent(self, test_case: Dict, response_data: Dict) -> Tuple[bool, str]:
        """验证意图分类"""
        expected_intent = test_case.get('intent')
        if not expected_intent:
            return True, "无需验证意图"
        
        # 这里可以添加意图验证逻辑
        # 目前简单返回通过
        return True, f"意图验证通过: {expected_intent}"

class TestExecutor:
    """测试执行器"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url
        self.timeout = timeout
        self.validation_engine = ValidationEngine()
        self.results = []
    
    def execute_test_case(self, test_case: Dict, category: str) -> Dict:
        """执行单个测试用例"""
        start_time = time.time()
        
        try:
            # 构造请求数据
            request_data = {
                "test_mode": True,
                "message_type": test_case.get('message_type', 'private'),
                "user_id": "test_user_001",
                "user_name": "测试用户",
                "message": test_case['message'],
                "timestamp": datetime.now().isoformat()
            }
            
            # 如果是群聊，添加群组ID
            if test_case.get('message_type') == 'group':
                request_data['group_id'] = test_case.get('group_id', 'test_group_001')
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/api/mock-seatalk/webhook/",
                json=request_data,
                timeout=self.timeout
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                response_data = response.json()
                success = response_data.get('success', False)
                
                if success:
                    # 验证响应
                    validation_passed, validation_msg = self.validation_engine.validate_response(
                        test_case, response_data
                    )
                    
                    test_passed = validation_passed == test_case.get('should_succeed', True)
                    
                    return {
                        'test_case': test_case,
                        'category': category,
                        'success': test_passed,
                        'response_time': response_time,
                        'validation_passed': validation_passed,
                        'validation_message': validation_msg,
                        'intercepted_messages': response_data.get('intercepted_messages', []),
                        'response_data': response_data,
                        'error': None
                    }
                else:
                    return {
                        'test_case': test_case,
                        'category': category,
                        'success': False,
                        'response_time': response_time,
                        'validation_passed': False,
                        'validation_message': "API返回失败",
                        'intercepted_messages': [],
                        'response_data': response_data,
                        'error': "API返回失败"
                    }
            else:
                return {
                    'test_case': test_case,
                    'category': category,
                    'success': False,
                    'response_time': time.time() - start_time,
                    'validation_passed': False,
                    'validation_message': f"HTTP错误: {response.status_code}",
                    'intercepted_messages': [],
                    'response_data': None,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {
                'test_case': test_case,
                'category': category,
                'success': False,
                'response_time': time.time() - start_time,
                'validation_passed': False,
                'validation_message': f"执行异常: {str(e)}",
                'intercepted_messages': [],
                'response_data': None,
                'error': str(e)
            }

class TestReporter:
    """测试报告生成器"""
    
    def __init__(self):
        self.reports_dir = "./reports"
        os.makedirs(self.reports_dir, exist_ok=True)
    
    def generate_summary(self, results: List[Dict]) -> str:
        """生成测试总结"""
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        # 按类别统计
        categories = {}
        for result in results:
            category = result['category']
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0}
            categories[category]['total'] += 1
            if result['success']:
                categories[category]['passed'] += 1
        
        summary = f"""
📊 数据驱动测试总结报告
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📈 总体结果:
   总测试数: {total_tests}
   通过: {passed_tests} ✅
   失败: {failed_tests} ❌
   成功率: {success_rate:.1f}%

📋 分类统计:
"""
        
        for category, stats in categories.items():
            category_rate = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
            status_icon = "✅" if category_rate >= 80 else "⚠️" if category_rate >= 60 else "❌"
            summary += f"   {status_icon} {category}: {stats['passed']}/{stats['total']} ({category_rate:.1f}%)\n"
        
        # 平均响应时间
        avg_time = sum(r['response_time'] for r in results if r['response_time'] > 0) / max(1, len(results))
        summary += f"\n⏱️ 平均响应时间: {avg_time:.2f}秒"
        
        return summary
    
    def generate_detailed_report(self, results: List[Dict]) -> str:
        """生成详细报告"""
        report = self.generate_summary(results)
        report += "\n\n📋 详细测试结果:\n"
        report += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
        
        current_category = None
        for i, result in enumerate(results, 1):
            test_case = result['test_case']
            category = result['category']
            
            if category != current_category:
                report += f"\n📂 {category.upper()} 类别:\n"
                current_category = category
            
            status_icon = "✅" if result['success'] else "❌"
            status_text = "通过" if result['success'] else "失败"
            
            report += f"\n[{i:2d}] {status_icon} {test_case['name']} - {status_text}\n"
            report += f"    消息: {test_case['message']}\n"
            report += f"    响应时间: {result['response_time']:.2f}秒\n"
            report += f"    验证结果: {result['validation_message']}\n"
            
            if not result['success'] and result['error']:
                report += f"    错误: {result['error']}\n"
            
            # 显示拦截的消息
            messages = result.get('intercepted_messages', [])
            if messages:
                report += f"    拦截消息数: {len(messages)}\n"
                for j, msg in enumerate(messages[:2]):  # 只显示前2条
                    msg_text = msg.get('text', '')[:100]
                    report += f"      消息{j+1}: {msg_text}...\n"
        
        return report
    
    def save_report(self, results: List[Dict], report_type: str = "detailed") -> str:
        """保存报告到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if report_type == "summary":
            content = self.generate_summary(results)
            filename = f"test_summary_{timestamp}.txt"
        else:
            content = self.generate_detailed_report(results)
            filename = f"test_detailed_{timestamp}.txt"
        
        filepath = os.path.join(self.reports_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return filepath

def load_test_data(data_file: str = "app01/tests/comprehensive_test_data.json") -> Dict:
    """加载测试数据"""
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载测试数据失败: {str(e)}")
        sys.exit(1)

def test_connection(base_url: str) -> bool:
    """测试服务器连接"""
    try:
        response = requests.get(f"{base_url}/api/mock-seatalk/results/", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    parser = argparse.ArgumentParser(description="数据驱动测试执行器")
    parser.add_argument("--url", default="http://autorelease.chatbot.shopee.io", 
                       help="测试服务器地址")
    parser.add_argument("--category", help="指定测试分类")
    parser.add_argument("--list", action="store_true", help="列出所有可用分类")
    parser.add_argument("--timeout", type=int, default=30, help="请求超时时间")
    parser.add_argument("--delay", type=float, default=1.0, help="测试间隔时间")
    parser.add_argument("--report-type", choices=["summary", "detailed"], default="detailed",
                       help="报告类型")
    
    args = parser.parse_args()
    
    print("🚀 ChatBot AutoRelease 数据驱动测试执行器")
    print("="*60)
    
    # 测试连接
    if not test_connection(args.url):
        print(f"❌ 无法连接到服务器: {args.url}")
        print("请确保服务器正在运行")
        sys.exit(1)
    
    print(f"✅ 服务器连接正常: {args.url}")
    
    # 加载测试数据
    test_data = load_test_data()
    
    # 列出所有分类
    if args.list:
        print("\n📋 可用测试分类:")
        for category in test_data['test_categories'].keys():
            print(f"   - {category}")
        return
    
    # 创建测试执行器
    executor = TestExecutor(args.url, args.timeout)
    reporter = TestReporter()
    
    # 收集测试用例
    all_test_cases = []
    
    for category_name, category_data in test_data['test_categories'].items():
        # 如果指定了分类，只执行该分类
        if args.category and category_name != args.category:
            continue
        
        test_cases = category_data.get('test_cases', [])
        for test_case in test_cases:
            variations = test_case.get('variations', [])
            for variation in variations:
                all_test_cases.append((variation, category_name))
    
    if not all_test_cases:
        print("❌ 没有找到匹配的测试用例")
        if args.category:
            print(f"请检查分类名称: {args.category}")
        return
    
    print(f"\n📊 开始执行 {len(all_test_cases)} 个测试用例...")
    print(f"⏱️ 预计耗时: {len(all_test_cases) * args.delay:.1f} 秒")
    
    # 执行测试
    results = []
    for i, (test_case, category) in enumerate(all_test_cases, 1):
        print(f"\n[{i}/{len(all_test_cases)}] 执行: {test_case['name']}")
        
        result = executor.execute_test_case(test_case, category)
        results.append(result)
        
        # 显示结果
        status_icon = "✅" if result['success'] else "❌"
        print(f"   {status_icon} {result['validation_message']} ({result['response_time']:.2f}s)")
        
        # 测试间隔
        if i < len(all_test_cases):
            time.sleep(args.delay)
    
    # 生成报告
    print("\n" + "="*60)
    print("📋 生成测试报告...")
    
    summary = reporter.generate_summary(results)
    print(summary)
    
    # 保存详细报告
    report_file = reporter.save_report(results, args.report_type)
    print(f"\n📄 报告已保存: {report_file}")
    
    # 显示失败测试
    failed_tests = [r for r in results if not r['success']]
    if failed_tests:
        print(f"\n⚠️ 失败的测试 ({len(failed_tests)}个):")
        for result in failed_tests:
            print(f"   ❌ {result['test_case']['name']}: {result['validation_message']}")
    
    # 返回状态码
    success_rate = (len(results) - len(failed_tests)) / len(results) * 100
    if success_rate >= 80:
        print(f"\n🎉 测试完成！成功率: {success_rate:.1f}%")
        sys.exit(0)
    else:
        print(f"\n⚠️ 测试完成，但成功率较低: {success_rate:.1f}%")
        sys.exit(1)

if __name__ == "__main__":
    main() 
"""
消息去重管理命令
用于监控、清理和管理消息去重缓存
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django.db.models import Count
import json

from app01.ai_module.message_deduplication import (
    get_message_stats, 
    cleanup_old_messages, 
    force_cleanup_memory_cache,
    MEMORY_DEDUP_CACHE
)
from app01.models import ProcessedMessage


class Command(BaseCommand):
    help = '管理消息去重缓存系统'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=[
                'status',      # 查看状态
                'cleanup',     # 清理旧记录
                'reset',       # 重置内存缓存
                'monitor',     # 监控模式
                'fix-duplicates'  # 修复重复处理问题
            ],
            help='要执行的操作'
        )
        
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='清理多少天前的记录（默认7天）'
        )
        
        parser.add_argument(
            '--event-id',
            type=str,
            help='检查特定event_id的处理状态'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'status':
            self.show_status(options)
        elif action == 'cleanup':
            self.cleanup_old_records(options)
        elif action == 'reset':
            self.reset_memory_cache()
        elif action == 'monitor':
            self.monitor_system()
        elif action == 'fix-duplicates':
            self.fix_duplicate_records()

    def show_status(self, options):
        """显示系统状态"""
        self.stdout.write(self.style.SUCCESS('🔍 消息去重系统状态检查'))
        self.stdout.write('-' * 50)
        
        # 获取统计信息
        stats = get_message_stats()
        
        self.stdout.write(f"📊 数据库记录:")
        self.stdout.write(f"  • 总处理消息数: {stats['total_processed']}")
        self.stdout.write(f"  • 今日处理数: {stats['today_processed']}")
        self.stdout.write(f"  • 平均处理时间: {stats['avg_processing_time']:.2f}秒")
        
        self.stdout.write(f"\n💾 内存缓存:")
        self.stdout.write(f"  • 当前缓存大小: {stats['memory_cache_size']}")
        self.stdout.write(f"  • 最大缓存大小: {stats['memory_cache_max_size']}")
        self.stdout.write(f"  • 缓存使用率: {stats['memory_cache_size']/stats['memory_cache_max_size']*100:.1f}%")
        
        # 检查最近的重复消息
        recent_duplicates = ProcessedMessage.objects.filter(
            processed_at__gte=timezone.now() - timedelta(hours=1)
        ).values('event_id').annotate(count=Count('event_id')).filter(count__gt=1)
        
        if recent_duplicates:
            self.stdout.write(f"\n⚠️ 最近1小时发现 {len(recent_duplicates)} 个重复event_id")
            for dup in recent_duplicates[:5]:
                self.stdout.write(f"  • Event ID: {dup['event_id']} (重复 {dup['count']} 次)")
        
        # 检查特定event_id
        if options.get('event_id'):
            self.check_specific_event(options['event_id'])

    def check_specific_event(self, event_id):
        """检查特定event_id的处理状态"""
        self.stdout.write(f"\n🔍 检查Event ID: {event_id}")
        self.stdout.write('-' * 30)
        
        # 检查数据库记录
        db_records = ProcessedMessage.objects.filter(event_id=event_id)
        if db_records.exists():
            self.stdout.write(f"📋 数据库记录: {db_records.count()} 条")
            for i, record in enumerate(db_records, 1):
                self.stdout.write(f"  {i}. 处理时间: {record.processed_at}")
                self.stdout.write(f"     处理耗时: {record.processing_duration:.2f}秒")
                self.stdout.write(f"     用户: {record.seatalk_id}")
                self.stdout.write(f"     群组: {record.group_id or '私聊'}")
        else:
            self.stdout.write("📋 数据库记录: 无")
        
        # 检查内存缓存
        if event_id in MEMORY_DEDUP_CACHE:
            cached_data = MEMORY_DEDUP_CACHE[event_id]
            cache_time = cached_data[1] if len(cached_data) > 1 else 0
            self.stdout.write(f"💾 内存缓存: 存在 (缓存时间: {timezone.datetime.fromtimestamp(cache_time)})")
        else:
            self.stdout.write("💾 内存缓存: 不存在")

    def cleanup_old_records(self, options):
        """清理旧记录"""
        days = options['days']
        self.stdout.write(self.style.WARNING(f'🧹 清理 {days} 天前的消息记录...'))
        
        deleted_count = cleanup_old_messages(days)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ 成功清理了 {deleted_count} 条记录')
        )

    def reset_memory_cache(self):
        """重置内存缓存"""
        self.stdout.write(self.style.WARNING('🔄 重置内存缓存...'))
        
        cache_size = force_cleanup_memory_cache()
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ 成功清理了 {cache_size} 条内存缓存记录')
        )

    def monitor_system(self):
        """监控系统状态"""
        self.stdout.write(self.style.SUCCESS('📊 消息去重系统监控报告'))
        self.stdout.write('=' * 50)
        
        # 检查最近处理的消息
        recent_messages = ProcessedMessage.objects.filter(
            processed_at__gte=timezone.now() - timedelta(minutes=30)
        ).order_by('-processed_at')[:10]
        
        if recent_messages:
            self.stdout.write(f"\n📈 最近30分钟处理的消息 (共 {recent_messages.count()} 条):")
            for msg in recent_messages:
                status = "✅" if msg.processing_duration > 0 else "⏳"
                self.stdout.write(
                    f"  {status} {msg.processed_at.strftime('%H:%M:%S')} - "
                    f"Event: {msg.event_id[:8]}... - "
                    f"耗时: {msg.processing_duration:.1f}s - "
                    f"用户: {msg.seatalk_id}"
                )
        
        # 检查处理时间异常的消息
        slow_messages = ProcessedMessage.objects.filter(
            processed_at__gte=timezone.now() - timedelta(hours=1),
            processing_duration__gt=25  # 超过25秒的消息
        )
        
        if slow_messages:
            self.stdout.write(f"\n⚠️ 处理时间异常的消息 (>25秒):")
            for msg in slow_messages:
                self.stdout.write(
                    f"  🐌 {msg.processed_at.strftime('%H:%M:%S')} - "
                    f"耗时: {msg.processing_duration:.1f}s - "
                    f"内容: {msg.query_content[:50]}..."
                )

    def fix_duplicate_records(self):
        """修复重复记录问题"""
        self.stdout.write(self.style.WARNING('🔧 检查并修复重复记录...'))
        
        # 查找重复的event_id
        duplicates = ProcessedMessage.objects.values('event_id').annotate(
            count=Count('event_id')
        ).filter(count__gt=1)
        
        if not duplicates:
            self.stdout.write(self.style.SUCCESS('✅ 未发现重复记录'))
            return
        
        self.stdout.write(f"⚠️ 发现 {len(duplicates)} 个重复的event_id:")
        
        fixed_count = 0
        for dup in duplicates:
            event_id = dup['event_id']
            count = dup['count']
            
            # 保留最早的记录，删除其他的
            records = ProcessedMessage.objects.filter(event_id=event_id).order_by('processed_at')
            if records.count() > 1:
                # 保留第一条记录
                keep_record = records.first()
                # 删除其他记录
                deleted_records = records.exclude(id=keep_record.id)
                deleted_count = deleted_records.count()
                deleted_records.delete()
                
                self.stdout.write(
                    f"  🔧 Event ID: {event_id} - 保留1条，删除{deleted_count}条重复记录"
                )
                fixed_count += deleted_count
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ 修复完成，共删除 {fixed_count} 条重复记录')
        ) 
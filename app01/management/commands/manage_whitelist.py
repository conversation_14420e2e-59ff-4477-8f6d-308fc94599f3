"""
白名单管理命令
用于管理高级功能的用户白名单
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta

from app01.models import AdvancedTaskFeatureWhitelist


class Command(BaseCommand):
    help = '管理高级功能白名单'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['add', 'remove', 'list', 'update', 'check'],
            help='操作类型'
        )
        
        parser.add_argument(
            '--user-id',
            type=str,
            help='用户ID'
        )
        
        parser.add_argument(
            '--user-email',
            type=str,
            help='用户邮箱'
        )
        
        parser.add_argument(
            '--employee-code',
            type=str,
            help='员工代码'
        )
        
        parser.add_argument(
            '--features',
            type=str,
            help='功能列表，用逗号分隔 (group_notification,task_template,batch_management,statistics_report,all_features)'
        )
        
        parser.add_argument(
            '--max-tasks',
            type=int,
            default=20,
            help='最大任务数限制'
        )
        
        parser.add_argument(
            '--max-templates',
            type=int,
            default=10,
            help='最大模板数限制'
        )
        
        parser.add_argument(
            '--expires-days',
            type=int,
            help='权限过期天数'
        )
        
        parser.add_argument(
            '--granted-by',
            type=str,
            help='授权人'
        )
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'add':
            self.add_whitelist(options)
        elif action == 'remove':
            self.remove_whitelist(options)
        elif action == 'list':
            self.list_whitelist(options)
        elif action == 'update':
            self.update_whitelist(options)
        elif action == 'check':
            self.check_whitelist(options)
    
    def add_whitelist(self, options):
        """添加白名单"""
        user_id = options.get('user_id')
        if not user_id:
            self.stdout.write(
                self.style.ERROR('必须提供 --user-id')
            )
            return
        
        features = options.get('features', '').split(',') if options.get('features') else []
        features = [f.strip() for f in features if f.strip()]
        
        if not features:
            self.stdout.write(
                self.style.ERROR('必须提供 --features')
            )
            return
        
        # 检查是否已存在
        existing = AdvancedTaskFeatureWhitelist.objects.filter(user_id=user_id).first()
        if existing:
            self.stdout.write(
                self.style.WARNING(f'用户 {user_id} 已存在白名单记录，使用 update 命令更新')
            )
            return
        
        # 计算过期时间
        expires_at = None
        if options.get('expires_days'):
            expires_at = timezone.now() + timedelta(days=options['expires_days'])
        
        # 创建白名单记录
        whitelist = AdvancedTaskFeatureWhitelist(
            user_id=user_id,
            user_email=options.get('user_email'),
            employee_code=options.get('employee_code'),
            allowed_features=features,
            max_tasks=options.get('max_tasks', 20),
            max_templates=options.get('max_templates', 10),
            granted_by=options.get('granted_by'),
            expires_at=expires_at,
            is_active=True
        )
        whitelist.save()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✅ 成功为用户 {user_id} 添加白名单权限\n'
                f'   功能: {", ".join(features)}\n'
                f'   最大任务数: {whitelist.max_tasks}\n'
                f'   最大模板数: {whitelist.max_templates}\n'
                f'   过期时间: {expires_at or "永不过期"}'
            )
        )
    
    def remove_whitelist(self, options):
        """移除白名单"""
        user_id = options.get('user_id')
        if not user_id:
            self.stdout.write(
                self.style.ERROR('必须提供 --user-id')
            )
            return
        
        try:
            whitelist = AdvancedTaskFeatureWhitelist.objects.get(user_id=user_id)
            whitelist.is_active = False
            whitelist.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ 成功移除用户 {user_id} 的白名单权限')
            )
            
        except AdvancedTaskFeatureWhitelist.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'❌ 用户 {user_id} 不存在白名单记录')
            )
    
    def list_whitelist(self, options):
        """列出白名单"""
        whitelists = AdvancedTaskFeatureWhitelist.objects.all().order_by('-granted_at')
        
        if not whitelists.exists():
            self.stdout.write('📋 暂无白名单记录')
            return
        
        self.stdout.write(f'📋 白名单记录 (共 {whitelists.count()} 个):')
        self.stdout.write('-' * 80)
        
        for whitelist in whitelists:
            status = "✅ 激活" if whitelist.is_active else "❌ 禁用"
            expires = whitelist.expires_at.strftime('%Y-%m-%d %H:%M:%S') if whitelist.expires_at else "永不过期"
            
            self.stdout.write(
                f'👤 用户ID: {whitelist.user_id}\n'
                f'   邮箱: {whitelist.user_email or "未设置"}\n'
                f'   员工代码: {whitelist.employee_code or "未设置"}\n'
                f'   状态: {status}\n'
                f'   功能: {", ".join(whitelist.allowed_features)}\n'
                f'   限制: 任务{whitelist.max_tasks}个, 模板{whitelist.max_templates}个\n'
                f'   授权时间: {whitelist.granted_at.strftime("%Y-%m-%d %H:%M:%S")}\n'
                f'   授权人: {whitelist.granted_by or "系统"}\n'
                f'   过期时间: {expires}\n'
            )
            self.stdout.write('-' * 80)
    
    def update_whitelist(self, options):
        """更新白名单"""
        user_id = options.get('user_id')
        if not user_id:
            self.stdout.write(
                self.style.ERROR('必须提供 --user-id')
            )
            return
        
        try:
            whitelist = AdvancedTaskFeatureWhitelist.objects.get(user_id=user_id)
            
            # 更新功能列表
            if options.get('features'):
                features = options['features'].split(',')
                features = [f.strip() for f in features if f.strip()]
                whitelist.allowed_features = features
            
            # 更新限制
            if options.get('max_tasks'):
                whitelist.max_tasks = options['max_tasks']
            
            if options.get('max_templates'):
                whitelist.max_templates = options['max_templates']
            
            # 更新过期时间
            if options.get('expires_days'):
                whitelist.expires_at = timezone.now() + timedelta(days=options['expires_days'])
            
            # 更新其他信息
            if options.get('user_email'):
                whitelist.user_email = options['user_email']
            
            if options.get('employee_code'):
                whitelist.employee_code = options['employee_code']
            
            if options.get('granted_by'):
                whitelist.granted_by = options['granted_by']
            
            whitelist.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ 成功更新用户 {user_id} 的白名单权限')
            )
            
        except AdvancedTaskFeatureWhitelist.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'❌ 用户 {user_id} 不存在白名单记录')
            )
    
    def check_whitelist(self, options):
        """检查白名单状态"""
        user_id = options.get('user_id')
        if not user_id:
            self.stdout.write(
                self.style.ERROR('必须提供 --user-id')
            )
            return
        
        try:
            whitelist = AdvancedTaskFeatureWhitelist.objects.get(user_id=user_id)
            
            # 检查是否过期
            is_expired = False
            if whitelist.expires_at and timezone.now() > whitelist.expires_at:
                is_expired = True
            
            status = "✅ 有效" if whitelist.is_active and not is_expired else "❌ 无效"
            if is_expired:
                status += " (已过期)"
            elif not whitelist.is_active:
                status += " (已禁用)"
            
            self.stdout.write(
                f'🎫 用户 {user_id} 的白名单状态:\n'
                f'   状态: {status}\n'
                f'   功能: {", ".join(whitelist.allowed_features)}\n'
                f'   限制: 任务{whitelist.max_tasks}个, 模板{whitelist.max_templates}个\n'
                f'   授权时间: {whitelist.granted_at.strftime("%Y-%m-%d %H:%M:%S")}\n'
                f'   过期时间: {whitelist.expires_at.strftime("%Y-%m-%d %H:%M:%S") if whitelist.expires_at else "永不过期"}'
            )
            
        except AdvancedTaskFeatureWhitelist.DoesNotExist:
            self.stdout.write(
                self.style.WARNING(f'⚠️ 用户 {user_id} 没有白名单记录')
            ) 
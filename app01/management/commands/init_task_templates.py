"""
初始化任务模板命令
创建一些常用的任务模板供用户使用
"""

from django.core.management.base import BaseCommand
from django.utils import timezone

from app01.models import TaskTemplate


class Command(BaseCommand):
    help = '初始化常用任务模板'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新创建模板（会删除现有模板）'
        )
    
    def handle(self, *args, **options):
        if options['force']:
            # 删除现有模板
            TaskTemplate.objects.all().delete()
            self.stdout.write(
                self.style.WARNING('🗑️ 已删除所有现有模板')
            )
        
        # 定义模板数据
        templates = [
            # 每日模板
            {
                'name': '每日Bug检查',
                'description': '检查今天新增的Bug',
                'category': 'daily',
                'query_template': 'project = {project} AND type = Bug AND created >= startOfDay()',
                'default_schedule': '每天 09:00',
                'variables': ['project'],
                'is_public': True,
                'created_by': 'system'
            },
            {
                'name': '每日任务完成情况',
                'description': '检查今天完成的任务',
                'category': 'daily',
                'query_template': 'assignee = {assignee} AND status = Done AND updated >= startOfDay()',
                'default_schedule': '每天 18:00',
                'variables': ['assignee'],
                'is_public': True,
                'created_by': 'system'
            },
            {
                'name': '每日待办事项',
                'description': '检查今天的待办任务',
                'category': 'daily',
                'query_template': 'assignee = {assignee} AND status in ("To Do", "Doing") AND duedate <= endOfDay()',
                'default_schedule': '每天 08:30',
                'variables': ['assignee'],
                'is_public': True,
                'created_by': 'system'
            },
            
            # 每周模板
            {
                'name': '周度项目进展',
                'description': '检查本周项目的整体进展',
                'category': 'weekly',
                'query_template': 'project = {project} AND updated >= startOfWeek()',
                'default_schedule': '每周五 17:00',
                'variables': ['project'],
                'is_public': True,
                'created_by': 'system'
            },
            {
                'name': '周度团队工作量',
                'description': '统计团队本周的工作量',
                'category': 'weekly',
                'query_template': 'project = {project} AND assignee in ({team_members}) AND updated >= startOfWeek()',
                'default_schedule': '每周五 16:00',
                'variables': ['project', 'team_members'],
                'is_public': True,
                'created_by': 'system'
            },
            
            # 每月模板
            {
                'name': '月度Bug统计',
                'description': '统计本月的Bug情况',
                'category': 'monthly',
                'query_template': 'project = {project} AND type = Bug AND created >= startOfMonth()',
                'default_schedule': '每月最后一天 17:00',
                'variables': ['project'],
                'is_public': True,
                'created_by': 'system'
            },
            {
                'name': '月度发布准备',
                'description': '检查月度发布的准备情况',
                'category': 'monthly',
                'query_template': 'project = {project} AND fixVersion = {version} AND status not in (Closed, Done, Icebox)',
                'default_schedule': '每月25日 10:00',
                'variables': ['project', 'version'],
                'is_public': True,
                'created_by': 'system'
            },
            
            # 项目管理模板
            {
                'name': '项目风险监控',
                'description': '监控项目中的高优先级问题',
                'category': 'project',
                'query_template': 'project = {project} AND priority in (Highest, High) AND status not in (Closed, Done, Icebox)',
                'default_schedule': '每天 10:00',
                'variables': ['project'],
                'is_public': True,
                'created_by': 'system'
            },
            {
                'name': '项目里程碑检查',
                'description': '检查项目里程碑的完成情况',
                'category': 'project',
                'query_template': 'project = {project} AND labels = milestone AND duedate <= {days_ahead}d',
                'default_schedule': '每周一 09:00',
                'variables': ['project', 'days_ahead'],
                'is_public': True,
                'created_by': 'system'
            },
            
            # Bug跟踪模板
            {
                'name': '严重Bug监控',
                'description': '监控严重级别的Bug',
                'category': 'bug_tracking',
                'query_template': 'type = Bug AND priority in (High, Highest) AND status in ("To Do", "Doing")',
                'default_schedule': '每2小时',
                'variables': [],
                'is_public': True,
                'created_by': 'system'
            },
            {
                'name': '长期未解决Bug',
                'description': '检查长期未解决的Bug',
                'category': 'bug_tracking',
                'query_template': 'type = Bug AND status not in (Closed, Done, Icebox) AND created <= -{days}d',
                'default_schedule': '每周三 14:00',
                'variables': ['days'],
                'is_public': True,
                'created_by': 'system'
            },
            
            # 自定义模板示例
            {
                'name': '个人工作总结',
                'description': '个人每日工作总结模板',
                'category': 'custom',
                'query_template': 'assignee = currentUser() AND updated >= startOfDay()',
                'default_schedule': '每天 17:30',
                'variables': [],
                'is_public': True,
                'created_by': 'system'
            }
        ]
        
        created_count = 0
        for template_data in templates:
            # 检查是否已存在同名模板
            if not TaskTemplate.objects.filter(name=template_data['name']).exists():
                template = TaskTemplate(
                    name=template_data['name'],
                    description=template_data['description'],
                    category=template_data['category'],
                    query_template=template_data['query_template'],
                    default_schedule=template_data['default_schedule'],
                    variables=template_data['variables'],
                    is_public=template_data['is_public'],
                    created_by=template_data['created_by']
                )
                template.save()
                created_count += 1
                
                self.stdout.write(
                    f'✅ 创建模板: {template.name} ({template.category})'
                )
            else:
                self.stdout.write(
                    f'⚠️ 模板已存在: {template_data["name"]}'
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 模板初始化完成！\n'
                f'   新创建: {created_count} 个模板\n'
                f'   总计: {TaskTemplate.objects.count()} 个模板'
            )
        )
        
        # 显示模板统计
        categories = TaskTemplate.objects.values_list('category', flat=True).distinct()
        self.stdout.write('\n📊 模板分类统计:')
        for category in categories:
            count = TaskTemplate.objects.filter(category=category).count()
            self.stdout.write(f'   {category}: {count} 个')
        
        self.stdout.write(
            '\n💡 使用提示:\n'
            '   • 用户可以通过 "advanced template list" 查看所有模板\n'
            '   • 用户可以通过 "advanced template create from <ID> \'任务名\'" 从模板创建任务\n'
            '   • 模板中的变量用 {variable_name} 格式表示\n'
            '   • 管理员可以通过Django Admin界面管理模板'
        ) 
"""
Django 管理命令：更新 JIRA 字段映射
使用方法：
python manage.py update_jira_fields
python manage.py update_jira_fields --force
"""

import asyncio
import logging
from typing import Dict
from django.core.management.base import BaseCommand
from django.utils import timezone

from app01.ai_module.field_mapper import field_mapper

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Django 管理命令：更新 JIRA 字段映射"""
    
    help = '更新 JIRA 字段映射关系'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制更新，忽略缓存时间检查',
        )
        parser.add_argument(
            '--show-status',
            action='store_true',
            help='显示当前字段映射状态',
        )
    
    def handle(self, *args, **options):
        if options['show_status']:
            self._show_status()
            return
        
        self.stdout.write(
            self.style.SUCCESS('🚀 开始更新 JIRA 字段映射...')
        )
        
        try:
            result = self._run_update_task(force=options['force'])
            
            if result['success']:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ {result['message']}")
                )
                if 'total_fields' in result:
                    self.stdout.write(f"📊 总字段数: {result['total_fields']}")
                if 'custom_fields' in result:
                    self.stdout.write(f"🔧 自定义字段数: {result['custom_fields']}")
                if 'execution_time' in result:
                    self.stdout.write(f"⏱️ 执行时间: {result['execution_time']:.2f} 秒")
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ 更新失败: {result['error']}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ 命令执行异常: {str(e)}")
            )
    
    def _run_update_task(self, force: bool = False) -> Dict:
        """运行更新任务"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            start_time = timezone.now()
            
            if force:
                # 强制更新
                result = loop.run_until_complete(field_mapper.force_update())
            else:
                # 检查是否需要更新
                status = loop.run_until_complete(field_mapper.get_update_status())
                
                if not status['needs_update']:
                    return {
                        'success': True,
                        'message': '字段映射仍然有效，无需更新',
                        'last_update': status['last_update'],
                        'total_fields': status['total_fields']
                    }
                
                # 执行更新
                result = loop.run_until_complete(field_mapper.force_update())
            
            execution_time = (timezone.now() - start_time).total_seconds()
            result['execution_time'] = execution_time
            
            return result
            
        finally:
            loop.close()
    
    def _show_status(self):
        """显示当前状态"""
        self.stdout.write(
            self.style.SUCCESS('📋 JIRA 字段映射状态')
        )
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            status = loop.run_until_complete(field_mapper.get_update_status())
            
            self.stdout.write(f"📅 上次更新: {status['last_update'] or '从未更新'}")
            if status['hours_since_update']:
                self.stdout.write(f"⏰ 距离上次更新: {status['hours_since_update']:.1f} 小时")
            self.stdout.write(f"🔄 需要更新: {'是' if status['needs_update'] else '否'}")
            self.stdout.write(f"📊 总字段数: {status['total_fields']}")
            self.stdout.write(f"🔧 自定义字段数: {status['custom_fields_count']}")
            self.stdout.write(f"⭐ 重要字段数: {status['important_fields_count']}")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ 获取状态失败: {str(e)}")
            )
        finally:
            loop.close() 
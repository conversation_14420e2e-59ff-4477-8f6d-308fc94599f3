"""
定时任务执行器 - Django管理命令
用于定期检查和执行用户的定时任务
"""

import asyncio
import logging
import time
from datetime import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone

from app01.ai_module.task_scheduler import task_scheduler

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '运行用户定时任务执行器'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='检查间隔（秒），默认60秒'
        )
        
        parser.add_argument(
            '--once',
            action='store_true',
            help='只执行一次，不循环'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='详细输出'
        )
    
    def handle(self, *args, **options):
        interval = options['interval']
        run_once = options['once']
        verbose = options['verbose']
        
        if verbose:
            logging.basicConfig(level=logging.INFO)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'启动定时任务执行器 (检查间隔: {interval}秒)'
            )
        )
        
        if run_once:
            # 只执行一次
            asyncio.run(self._execute_once())
        else:
            # 循环执行
            asyncio.run(self._run_scheduler(interval))
    
    async def _execute_once(self):
        """执行一次任务检查"""
        try:
            result = await task_scheduler.execute_pending_tasks()
            
            if result['success']:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"✅ {result['message']}"
                    )
                )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f"❌ 执行失败: {result['error']}"
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(
                    f"❌ 执行异常: {str(e)}"
                )
            )
    
    async def _run_scheduler(self, interval: int):
        """循环运行调度器"""
        self.stdout.write("🚀 定时任务调度器已启动...")
        
        while True:
            try:
                start_time = time.time()
                
                # 执行待处理任务
                result = await task_scheduler.execute_pending_tasks()
                
                execution_time = time.time() - start_time
                
                if result['success']:
                    if result['executed_count'] > 0:
                        # 使用新加坡时区显示时间
                        import pytz
                        sg_tz = pytz.timezone('Asia/Singapore')
                        sg_time = timezone.now().astimezone(sg_tz)
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"[{sg_time.strftime('%Y-%m-%d %H:%M:%S')}] "
                                f"✅ {result['message']} (耗时: {execution_time:.2f}s)"
                            )
                        )
                else:
                    # 使用新加坡时区显示时间
                    import pytz
                    sg_tz = pytz.timezone('Asia/Singapore')
                    sg_time = timezone.now().astimezone(sg_tz)
                    self.stdout.write(
                        self.style.ERROR(
                            f"[{sg_time.strftime('%Y-%m-%d %H:%M:%S')}] "
                            f"❌ 执行失败: {result['error']}"
                        )
                    )
                
                # 等待下次检查
                await asyncio.sleep(interval)
                
            except KeyboardInterrupt:
                self.stdout.write(
                    self.style.WARNING(
                        "\n🛑 收到中断信号，正在停止调度器..."
                    )
                )
                break
                
            except Exception as e:
                # 使用新加坡时区显示时间
                import pytz
                sg_tz = pytz.timezone('Asia/Singapore')
                sg_time = timezone.now().astimezone(sg_tz)
                self.stdout.write(
                    self.style.ERROR(
                        f"[{sg_time.strftime('%Y-%m-%d %H:%M:%S')}] "
                        f"❌ 调度器异常: {str(e)}"
                    )
                )
                # 异常后等待一段时间再继续
                await asyncio.sleep(min(interval, 30))
        
        self.stdout.write(
            self.style.SUCCESS("👋 定时任务调度器已停止")
        ) 
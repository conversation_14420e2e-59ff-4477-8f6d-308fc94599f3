from django.core.management.base import BaseCommand
from app01.group_sync_manager import daily_sync_groups


class Command(BaseCommand):
    help = '手动同步机器人加入的所有群组信息'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制执行同步，忽略频率限制',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('开始执行群组同步任务...')
        )
        
        try:
            if options['force']:
                # 强制执行，清除缓存限制
                from app01.group_sync_manager import DAILY_SYNC_CACHE
                DAILY_SYNC_CACHE.clear()
                self.stdout.write('已清除缓存限制，强制执行同步')
            
            daily_sync_groups()
            
            self.stdout.write(
                self.style.SUCCESS('✅ 群组同步任务完成')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 群组同步任务失败: {str(e)}')
            )
            raise 
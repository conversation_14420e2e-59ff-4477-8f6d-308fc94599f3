"""
数据库连接重试工具
用于处理MySQL连接超时和断开连接的问题
"""

import time
import logging
from functools import wraps
from django.db import connection, transaction
from django.db.utils import OperationalError, InterfaceError
from typing import Callable, Any

logger = logging.getLogger(__name__)


def db_retry(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    数据库操作重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟时间倍数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    # 在每次重试前检查并重置数据库连接
                    if attempt > 0:
                        logger.info(f"🔄 数据库操作重试 {attempt}/{max_retries}")
                        _reset_db_connection()
                    
                    return func(*args, **kwargs)
                    
                except (OperationalError, InterfaceError) as e:
                    last_exception = e
                    error_code = getattr(e, 'args', [None])[0]
                    
                    # 检查是否是连接相关的错误
                    if _is_connection_error(error_code, str(e)):
                        if attempt < max_retries:
                            logger.warning(f"⚠️ 数据库连接错误 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                            logger.info(f"⏳ 等待 {current_delay:.1f} 秒后重试...")
                            time.sleep(current_delay)
                            current_delay *= backoff
                            continue
                        else:
                            logger.error(f"❌ 数据库连接重试失败，已达到最大重试次数: {str(e)}")
                    else:
                        # 非连接错误，直接抛出
                        logger.error(f"❌ 数据库操作失败（非连接错误）: {str(e)}")
                        raise
                        
                except Exception as e:
                    # 其他异常直接抛出
                    logger.error(f"❌ 数据库操作异常: {str(e)}")
                    raise
            
            # 所有重试都失败了
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


def _is_connection_error(error_code: Any, error_message: str) -> bool:
    """
    判断是否是连接相关的错误
    
    Args:
        error_code: 错误代码
        error_message: 错误消息
        
    Returns:
        bool: 是否是连接错误
    """
    # MySQL连接相关的错误代码
    connection_error_codes = [
        2006,  # MySQL server has gone away
        2013,  # Lost connection to MySQL server during query
        4031,  # The client was disconnected by the server because of inactivity
        1053,  # Server shutdown in progress
        1040,  # Too many connections
    ]
    
    # 检查错误代码
    if error_code in connection_error_codes:
        return True
    
    # 检查错误消息中的关键词
    connection_keywords = [
        'server has gone away',
        'lost connection',
        'disconnected by the server',
        'inactivity',
        'connection timeout',
        'connection refused',
        'can\'t connect',
        'connection lost',
    ]
    
    error_lower = error_message.lower()
    return any(keyword in error_lower for keyword in connection_keywords)


def _reset_db_connection():
    """重置数据库连接"""
    try:
        # 关闭现有连接
        if connection.connection is not None:
            logger.info("🔌 关闭现有数据库连接")
            connection.close()
        
        # 确保连接被重置
        connection.ensure_connection()
        logger.info("✅ 数据库连接已重置")
        
    except Exception as e:
        logger.warning(f"⚠️ 重置数据库连接时出现异常: {str(e)}")


def db_retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    异步数据库操作重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟时间倍数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            import asyncio
            from asgiref.sync import sync_to_async
            
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    # 在每次重试前检查并重置数据库连接
                    if attempt > 0:
                        logger.info(f"🔄 异步数据库操作重试 {attempt}/{max_retries}")
                        await sync_to_async(_reset_db_connection)()
                    
                    return await func(*args, **kwargs)
                    
                except (OperationalError, InterfaceError) as e:
                    last_exception = e
                    error_code = getattr(e, 'args', [None])[0]
                    
                    # 检查是否是连接相关的错误
                    if _is_connection_error(error_code, str(e)):
                        if attempt < max_retries:
                            logger.warning(f"⚠️ 异步数据库连接错误 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")
                            logger.info(f"⏳ 等待 {current_delay:.1f} 秒后重试...")
                            await asyncio.sleep(current_delay)
                            current_delay *= backoff
                            continue
                        else:
                            logger.error(f"❌ 异步数据库连接重试失败，已达到最大重试次数: {str(e)}")
                    else:
                        # 非连接错误，直接抛出
                        logger.error(f"❌ 异步数据库操作失败（非连接错误）: {str(e)}")
                        raise
                        
                except Exception as e:
                    # 其他异常直接抛出
                    logger.error(f"❌ 异步数据库操作异常: {str(e)}")
                    raise
            
            # 所有重试都失败了
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator


def test_db_connection():
    """测试数据库连接"""
    try:
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute('SELECT 1')
        result = cursor.fetchone()
        logger.info(f"✅ 数据库连接测试成功: {result}")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 测试数据库连接
    import os
    import django
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
    django.setup()
    
    test_db_connection()

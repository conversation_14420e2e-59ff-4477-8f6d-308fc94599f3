import concurrent.futures
from dateutil.parser import parse as date_parse
from jira import JIRA
from .models import SPCPMTimelineReminder
from icecream import ic
from jira.exceptions import JIRAError

# 从配置文件或环境变量获取JIRA_TOKEN
from django.conf import settings
JIRA_TOKEN = getattr(settings, 'JIRA_TOKEN', None)
if not JIRA_TOKEN:
    try:
        from .views import JIRA_TOKEN
    except ImportError:
        JIRA_TOKEN = None

# SPCPM项目中的日期字段配置，每个元组包含三个元素：
# 1. 字段显示名称
# 2. JIRA系统中的自定义字段ID
# 3. 字段类型（start表示开始日期，end表示结束日期）
# 整合时间线时，start类型取最早日期，end类型取最晚日期
SPCPM_DATE_FIELDS = [
    ("PRD Review Start Date", "customfield_11545", "start"),
    ("PRD Review End Date", "customfield_11546", "end"),
    ("Tech Design Start Date", "customfield_12208", "start"),
    ("Tech Design End Date", "customfield_12209", "end"),
    ("Planned Dev Start Date", "customfield_11520", "start"),
    ("Planned Dev Due Date", "customfield_11509", "end"),
    ("Dev Start Date", "customfield_11516", "start"),
    ("Dev Due Date", "customfield_10304", "end"),  # 从列表中找到的Dev Due Date字段
    ("Planned Integration Start Date", "customfield_12634", "start"),
    ("Planned Integration End Date", "customfield_12635", "end"),
    ("Integration Start Date", "customfield_12636", "start"),
    ("Integration End Date", "customfield_12637", "end"),
    ("Planned QA Start Date", "customfield_11521", "start"),
    ("Planned QA Due Date", "customfield_11510", "end"),
    ("QA Start Date", "customfield_11517", "start"),
    ("QA Due Date", "customfield_10305", "end"),  # 从列表中找到的QA Due Date字段
    ("Planned UAT Start Date", "customfield_11522", "start"),
    ("Planned UAT Due Date", "customfield_11511", "end"),
    ("UAT Start Date", "customfield_11519", "start"),
    ("UAT Due Date", "customfield_11512", "end"),
    ("Planned Release Date", "customfield_11513", "end"),
    ("Release Date", "customfield_11514", "end"),
]

# SPCPM项目中的人员相关字段配置，键为JIRA系统中的字段ID，值为显示名称
# 这些字段用于从JIRA工单中提取相关人员信息，支持在提醒消息中@相关人员
# 也用于处理"@ChatbotAR all/qa/dev/pm/pj SPCPM-23678"类型的命令，根据指定角色@相关人员
# - assignee: 经办人
# - customfield_10307: 开发人员
# - customfield_37801: 前端开发人员列表
# - customfield_37800: 后端开发人员列表
# - customfield_10308: 测试人员
# - customfield_12202: 测试人员列表
# - customfield_10306: 产品经理
# - customfield_10600: 项目经理
SPCPM_PEOPLE_FIELDS = {
    "assignee": "Assignee",
    "customfield_10307": "Developer",
    "customfield_37801": "FE List",
    "customfield_37800": "BE List",
    "customfield_10308": "QA",
    "customfield_12202": "QA List",
    "customfield_10306": "Product Manager",
    "customfield_10600": "Project Manager"
}

def get_spcpm_tickets(request_id, jira=None):
    """
    根据SPCPM Request ID获取所有相关的JIRA工单
    
    Args:
        request_id: SPCPM请求ID，如SPCPM-123456
        jira: 可选的JIRA连接对象，如果未提供则创建新连接
        
    Returns:
        list: 包含所有相关JIRA工单的列表
    """
    if jira is None:
        jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    jql = f'"Request ID" ~ {request_id}'
    issues = jira.search_issues(jql, maxResults=100)
    return issues

def get_developers_from_epic_tasks(epic_key, jira=None):
    """
    从Epic下的Task中获取开发人员信息
    
    Args:
        epic_key: Epic的JIRA Key，如SPCB-12345
        jira: 可选的JIRA连接对象，如果未提供则创建新连接
        
    Returns:
        list: 包含所有Task开发人员邮箱的列表（去重后）
    """
    if jira is None:
        jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
    
    # 构建JQL查询，获取Epic下的所有未关闭Task
    jql = f'"Epic Link" = {epic_key} AND issuetype = Task AND status != closed'
    ic(f"查询Epic {epic_key} 下的Task，JQL: {jql}")
    
    try:
        # 查询Task
        tasks = jira.search_issues(jql, maxResults=100)
        ic(f"找到Epic {epic_key} 下的Task数量: {len(tasks)}")
        
        # 收集所有Task的开发人员信息
        developers = set()
        for task in tasks:
            # 获取Developer字段
            developer = getattr(task.fields, "customfield_10307", None)
            if developer and hasattr(developer, 'emailAddress'):
                developers.add(developer.emailAddress)
                ic(f"从Task {task.key} 添加开发人员: {developer.emailAddress}")
        
        ic(f"Epic {epic_key} 下的Task开发人员总数: {len(developers)}")
        return list(developers)
    except Exception as e:
        ic(f"获取Epic {epic_key} 下的Task开发人员信息失败: {str(e)}")
        return []

def fetch_ticket_fields(issue, date_fields, people_fields):
    """
    从JIRA工单中提取日期和人员字段
    
    Args:
        issue: JIRA工单对象
        date_fields: 日期字段配置列表
        people_fields: 人员字段配置字典
        
    Returns:
        dict: 包含提取的日期和人员信息的字典
    """
    result = {"dates": {}, "people": {}}
    # 提取日期字段
    for name, field_id, _ in date_fields:
        value = getattr(issue.fields, field_id, None)
        result["dates"][name] = value
    # 提取人员字段
    for field_id, name in people_fields.items():
        value = getattr(issue.fields, field_id, None)
        if value:
            if isinstance(value, list):
                # 如果是列表（如多个开发人员），提取所有人的邮箱
                result["people"][name] = [v.emailAddress if hasattr(v, 'emailAddress') else str(v) for v in value]
            else:
                # 单个人员，提取邮箱
                result["people"][name] = value.emailAddress if hasattr(value, 'emailAddress') else str(value)
    return result

def merge_spcpm_timeline_and_people(ticket_results):
    """
    合并多个JIRA工单的时间线和人员信息
    
    对于时间线：
    - 优先使用实际日期，如果实际日期不存在，则使用计划日期
    - 开始类型的日期取最早值
    - 结束类型的日期取最晚值
    
    对于人员：合并所有不同工单中的人员列表
    
    Args:
        ticket_results: 从多个工单提取的字段结果列表
        
    Returns:
        tuple: (合并后的时间线字典, 合并后的人员字典)
    """
    timeline = {}
    people = {}
    
    # 创建日期字段映射，将计划日期与实际日期对应起来
    date_field_mapping = {
        "Planned Dev Start Date": "Dev Start Date",
        "Planned Dev Due Date": "Dev Due Date",
        "Planned Integration Start Date": "Integration Start Date",
        "Planned Integration End Date": "Integration End Date",
        "Planned QA Start Date": "QA Start Date",
        "Planned QA Due Date": "QA Due Date",
        "Planned UAT Start Date": "UAT Start Date",
        "Planned UAT Due Date": "UAT Due Date",
        "Planned Release Date": "Release Date",
    }
    
    # 按里程碑类型分组收集日期
    milestone_dates = {}
    
    # 首先收集所有日期数据，按里程碑类型和日期类型（计划/实际）分组
    for name, _, typ in SPCPM_DATE_FIELDS:
        values = []
        for t in ticket_results:
            v = t["dates"].get(name)
            if v:
                try:
                    # 将字符串日期转换为datetime对象
                    dt = date_parse(v) if isinstance(v, str) else v
                    values.append(dt)
                except:
                    continue
        
        if values:
            # 根据字段类型选择最早或最晚日期
            if typ == "start":
                milestone_dates[name] = min(values)
            else:
                milestone_dates[name] = max(values)
            # 添加日志记录每个字段的值
            ic(f"字段 {name} 收集到 {len(values)} 个值，使用 {milestone_dates[name].strftime('%Y-%m-%d')}")
    
    # 添加日志记录所有收集到的日期字段
    ic(f"收集到的所有日期字段: {[k for k in milestone_dates.keys()]}")
    
    # 处理日期字段，优先使用实际日期
    for planned_name, actual_name in date_field_mapping.items():
        # 检查是否有实际日期
        if actual_name in milestone_dates:
            # 使用实际日期
            timeline[actual_name] = milestone_dates[actual_name].strftime('%Y-%m-%d')
            ic(f"使用实际日期 {actual_name}: {timeline[actual_name]}")
        # 如果没有实际日期但有计划日期，则使用计划日期
        elif planned_name in milestone_dates:
            timeline[planned_name] = milestone_dates[planned_name].strftime('%Y-%m-%d')
            ic(f"使用计划日期 {planned_name}: {timeline[planned_name]}")
        else:
            ic(f"未找到 {planned_name} 或 {actual_name} 的日期值")
    
    # 处理没有实际/计划对应关系的日期字段（如PRD Review）
    for name, _, _ in SPCPM_DATE_FIELDS:
        if name in milestone_dates and name not in date_field_mapping.values() and name not in date_field_mapping:
            timeline[name] = milestone_dates[name].strftime('%Y-%m-%d')
            ic(f"添加其他日期字段 {name}: {timeline[name]}")
    
    # 合并人员信息，去重
    for t in ticket_results:
        for k, v in t["people"].items():
            if k not in people:
                people[k] = set()
            if isinstance(v, list):
                people[k].update(v)
            else:
                people[k].add(v)
    
    # 将集合转换回列表
    people = {k: list(v) for k, v in people.items()}
    return timeline, people

def get_spcpm_timeline_and_people(request_id):
    """
    获取SPCPM项目的完整时间线和相关人员信息
    
    这是主要的对外接口函数，整合了所有相关工单的时间线和人员信息
    
    Args:
        request_id: SPCPM请求ID，如SPCPM-123456
        
    Returns:
        tuple: (时间线字典, 人员字典, 相关工单ID列表)
    """
    try:
        # 创建JIRA连接
        jira = JIRA(server="https://jira.shopee.io", token_auth=JIRA_TOKEN)
        # 获取所有相关工单
        issues = get_spcpm_tickets(request_id, jira)
        ticket_results = []
        
        # 使用线程池并行处理多个工单，提高性能
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            future_to_issue = {executor.submit(fetch_ticket_fields, issue, SPCPM_DATE_FIELDS, SPCPM_PEOPLE_FIELDS): issue.key for issue in issues}
            for future in concurrent.futures.as_completed(future_to_issue):
                try:
                    ticket_results.append(future.result())
                except Exception as e:
                    continue
        
        # 合并时间线和人员信息
        timeline, people = merge_spcpm_timeline_and_people(ticket_results)
        
        # 获取Epic下的Task开发人员信息
        epic_keys = [i.key for i in issues if i.fields.issuetype.name == 'Epic']
        ic(f"找到 {request_id} 相关的Epic数量: {len(epic_keys)}")
        
        # 记录原始Developer信息
        original_developers = people.get("Developer", [])
        if isinstance(original_developers, list):
            ic(f"原始Epic Developer字段中的开发人员数量: {len(original_developers)}")
        else:
            ic(f"原始Epic Developer字段中的开发人员: {original_developers}")
        
        task_developers = set()
        for epic_key in epic_keys:
            ic(f"开始处理Epic: {epic_key}")
            developers = get_developers_from_epic_tasks(epic_key, jira)
            task_developers.update(developers)
        
        # 如果找到了Task开发人员，用它们替换Epic中的Developer字段
        if task_developers:
            ic(f"从所有Task中找到的开发人员总数: {len(task_developers)}")
            people["Developer"] = list(task_developers)
            ic(f"更新后的Developer字段: {people['Developer']}")
        else:
            ic(f"未从Task中找到开发人员，保留原始Developer字段")
        
        return timeline, people, [i.key for i in issues]
    except JIRAError as e:
        # 导入简化JIRA错误的函数
        from .seatalk_group_manager import simplify_jira_error
        # 简化错误信息
        simplified_error = simplify_jira_error(str(e))
        ic(f"获取SPCPM时间线时出现JIRA错误: {simplified_error}")
        # 返回空的时间线和人员信息
        return {}, {}, []
    except Exception as e:
        ic(f"获取SPCPM时间线时出现一般错误: {str(e)}")
        # 返回空的时间线和人员信息
        return {}, {}, []

def format_spcpm_timeline_message(request_id, timeline):
    """
    格式化SPCPM时间线信息为可读的消息文本
    
    Args:
        request_id: SPCPM请求ID
        timeline: 时间线字典
        
    Returns:
        str: 格式化后的时间线消息文本
    """
    msg = f"Project Timeline for {request_id}:\n\n"
    
    # 将日期字段按阶段分组，优先使用实际日期
    mapping = {
        "PRD": ["PRD Review Start Date", "PRD Review End Date"],
        "Tech Design": ["Tech Design Start Date", "Tech Design End Date"],
        "Development": ["Dev Start Date", "Dev Due Date", "Planned Dev Start Date", "Planned Dev Due Date"],
        "Integration": ["Integration Start Date", "Integration End Date", "Planned Integration Start Date", "Planned Integration End Date"],
        "QA": ["QA Start Date", "QA Due Date", "Planned QA Start Date", "Planned QA Due Date"],
        "UAT": ["UAT Start Date", "UAT Due Date", "Planned UAT Start Date", "Planned UAT Due Date"],
        "Release": ["Release Date", "Planned Release Date"]
    }
    
    # 记录timeline中的所有键
    ic(f"Timeline中的所有字段: {list(timeline.keys())}")
    
    # 按阶段格式化输出，优先使用实际日期
    for stage, fields in mapping.items():
        ic(f"处理阶段: {stage}, 可用字段: {fields}")
        
        # 检查该阶段在timeline中有哪些字段
        available_fields = [f for f in fields if f in timeline]
        ic(f"阶段 {stage} 在timeline中的可用字段: {available_fields}")
        
        # 特殊处理Release阶段，因为Release通常只有一个日期
        if stage == "Release" and available_fields:
            # 优先使用实际日期，如果没有则使用计划日期
            release_date = None
            for field in available_fields:
                if not "Planned" in field:  # 实际日期
                    release_date = timeline[field]
                    ic(f"阶段 {stage} 使用实际日期: {field} = {release_date}")
                    break
            
            # 如果没有找到实际日期，则使用计划日期
            if not release_date and available_fields:
                field = available_fields[0]
                release_date = timeline[field]
                ic(f"阶段 {stage} 使用计划日期: {field} = {release_date}")
            
            if release_date:
                msg += f"{stage}: {release_date}\n"
                ic(f"阶段 {stage} 最终显示: {release_date}")
            else:
                msg += f"{stage}: -\n"
                ic(f"阶段 {stage} 无可用日期，显示为 -")
            continue
        
        # 处理其他阶段
        # 优先级顺序：实际开始日期 > 计划开始日期, 实际结束日期 > 计划结束日期
        start_date = None
        end_date = None
        
        # 查找开始日期，优先使用实际日期
        for field in fields:
            if "Start Date" in field and field in timeline:
                if not "Planned" in field:  # 实际日期
                    start_date = timeline[field]
                    ic(f"阶段 {stage} 使用实际开始日期: {field} = {start_date}")
                    break
                elif start_date is None:  # 计划日期（仅在没有实际日期时使用）
                    start_date = timeline[field]
                    ic(f"阶段 {stage} 使用计划开始日期: {field} = {start_date}")
        
        # 查找结束日期，优先使用实际日期
        for field in fields:
            if ("End Date" in field or "Due Date" in field) and field in timeline:
                if not "Planned" in field:  # 实际日期
                    end_date = timeline[field]
                    ic(f"阶段 {stage} 使用实际结束日期: {field} = {end_date}")
                    break
                elif end_date is None:  # 计划日期（仅在没有实际日期时使用）
                    end_date = timeline[field]
                    ic(f"阶段 {stage} 使用计划结束日期: {field} = {end_date}")
        
        # 构建阶段的日期显示
        stage_dates = []
        if start_date:
            stage_dates.append(start_date)
        if end_date:
            stage_dates.append(end_date)
        
        if stage_dates:
            msg += f"{stage}: {', '.join(stage_dates)}\n"
            ic(f"阶段 {stage} 最终显示: {', '.join(stage_dates)}")
        else:
            msg += f"{stage}: -\n"
            ic(f"阶段 {stage} 无可用日期，显示为 -")
    
    return msg 
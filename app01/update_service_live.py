import requests
import json
from icecream import ic
from datetime import datetime
import os
from pathlib import Path
from .config import DEPLOY_PATHS, ensure_directories

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

def log_with_timestamp(message):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    ic(f"[{timestamp}]," + message)

def get_service(project_dict, project_id_dict):
    detail_url = "http://luban.cs.test.shopee.io/chatbot-config-tool/space/get_chatbot_services"
    rep = requests.get(url=detail_url)
    rep_text = json.loads(rep.text)
    if rep_text['msg'] == 'success':
        for i in rep_text['data']:
            if i['git_link']:
                project_id = get_project_id(url=i['git_link'])
                if project_id and project_id != 69794:
                    project_id_dict[
                        i['git_link'].replace("https://git.garena.com/", "").replace(".git", "")] = project_id
                    project_dict.update(
                        {"name": i['git_link'].replace("https://git.garena.com/", "").replace(".git", "")})


def get_project_id(url):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    url = url.replace("https://git.garena.com/", "")
    url = url.replace(".git", "")
    url = url.replace("/", "%2f")
    real_url = BASE_URL + '{url}/merge_requests?private_token=kBV8bRxCbEqk2G8eyFyz'. \
        format(url=url)
    get_feedback_isue = requests.get(real_url)
    text_all = json.loads(get_feedback_isue.text)
    if get_feedback_isue.text:
        text_all = json.loads(get_feedback_isue.text)
    else:
        text_all={}
    if text_all:
        if type(text_all) == dict and text_all['message'] == '404 Project Not Found':
            return None
        else:
            return text_all[0]['project_id']


class Params():
    def __init__(self, json_path):
        with open(json_path) as f:
            params = json.load(f)  # 将json格式数据转换为字典
            self.__dict__.update(params)

    def save(self, json_path):
        with open(json_path, 'w') as f:
            json.dump(self.__dict__, f, indent=4)

    def update(self, json_path):
        with open(json_path) as f:
            params = json.load(f)
            self.__dict__.update(params)
    @property
    def dict(self):
        return self.__dict__


if __name__ == '__main__':
    # 确保目录存在
    ensure_directories()
    
    project_dict = {}
    project_id_dict = {}
    get_service(project_dict=project_dict, project_id_dict=project_id_dict)
    project_id_dict["shopee/chatbot/web-csat-rn"] = 85497
    project_id_dict["shopee/seller-fe/cs-chat"] = 19635
    project_id_dict["shopee/chatbot/web-chatbot-sdk"] = 52405
    json_str = json.dumps(project_dict, indent=4)
    json_str_id = json.dumps(project_id_dict, indent=4)
    print(json_str_id)

    # 写入项目根目录的services.json
    services_path = os.path.join(BASE_DIR, 'services.json')
    with open(services_path, 'w') as f:
        f.write(json_str)

    # 写入vue-admin项目的services_id.json
    vue_admin_services_path = os.path.join(DEPLOY_PATHS['VUE_ADMIN_DIR'], 'services_id.json')
    with open(vue_admin_services_path, 'w') as f:
        f.write(json_str_id)

    # 写入autodeploy项目的services_id.json
    autodeploy_services_path = os.path.join(DEPLOY_PATHS['AUTODEPLOY_DIR'], 'app01', 'services_id.json')
    with open(autodeploy_services_path, 'w') as f:
        f.write(json_str_id)
        log_with_timestamp("services_id.json 更新成功")
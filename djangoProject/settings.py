"""
Django settings for djangoProject project.

Generated by 'django-admin startproject' using Django 4.1.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 创建日志目录
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOGS_DIR):
    os.makedirs(LOGS_DIR)

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-nq)%i_p0z*37)-+2n#5(da#!2!#q4@08l9!#iumci%+_qpuw#9'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    'unfold',  # Django Unfold 必须在 django.contrib.admin 之前
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'app01',
    'corsheaders',
    'django_crontab'
]

CRONJOBS = [
    # 每小时整点执行回调函数
    ('0 * * * *', 'app01.views.callback', f'>> {os.path.join(LOGS_DIR, "crontab.log")} 2>&1'),

    # ===== 统计系统定时任务 =====
    # 1. 每15分钟执行系统健康检查
    ('*/15 * * * *', 'app01.statistics.cron_jobs.system_health_check', f'>> {os.path.join(LOGS_DIR, "statistics_health.log")} 2>&1'),

    # 2. 每小时执行统计汇总
    ('0 * * * *', 'app01.statistics.cron_jobs.hourly_statistics_summary', f'>> {os.path.join(LOGS_DIR, "statistics_hourly.log")} 2>&1'),

    # 3. 每日凌晨1点执行日统计汇总
    ('0 1 * * *', 'app01.statistics.cron_jobs.daily_statistics_summary', f'>> {os.path.join(LOGS_DIR, "statistics_daily.log")} 2>&1'),

    # 4. 每周一凌晨2点执行周统计汇总
    ('0 2 * * 1', 'app01.statistics.cron_jobs.weekly_statistics_summary', f'>> {os.path.join(LOGS_DIR, "statistics_weekly.log")} 2>&1'),

    # 5. 每月1号凌晨3点执行月统计汇总
    ('0 3 1 * *', 'app01.statistics.cron_jobs.monthly_statistics_summary', f'>> {os.path.join(LOGS_DIR, "statistics_monthly.log")} 2>&1'),

    # 6. 每周日凌晨4点执行数据清理
    ('0 4 * * 0', 'app01.statistics.cron_jobs.cleanup_old_statistics_job', f'>> {os.path.join(LOGS_DIR, "statistics_cleanup.log")} 2>&1'),
    
    # 每分钟执行一次，更新DB里的历史发布单数据
    ('* * * * *', 'app01.views.save_releases', f'>> {os.path.join(LOGS_DIR, "crontab_release_save.log")} 2>&1'),
    
    # 每天10点、15点、18点发送服务部署失败的消息提醒
    ('0 10,15,18 * * *', 'app01.views.failuremsg_final', f'>> {os.path.join(LOGS_DIR, "crontab_msg.log")} 2>&1'),
    
    # 每天10-19点整点执行，提醒 TL 合并未处理的MR（合并请求）
    ('0 10-19 * * *', 'app01.views.MRnotdeal', f'>> {os.path.join(LOGS_DIR, "crontab_msg_MR.log")} 2>&1'),
    
    # 每天9-19点整点执行，处理新的MR消息（CS channel 组）
    ('0 9-19 * * *', 'app01.views.MRnotdealchannel_new', f'>> {os.path.join(LOGS_DIR, "crontab_msg_MR_channel.log")} 2>&1'),
    
    # 每天9-19点整点执行，处理MR数据（CS DATA 组）
    ('0 9-19 * * *', 'app01.views.MRnotdeal_data', f'>> {os.path.join(LOGS_DIR, "crontab_msg_MR_data.log")} 2>&1'),
    
    # 每分钟执行一次，AR平台-日历页面，保存发布单列表到DB
    ('* * * * *', 'app01.views.save_JIRA_Release_List_Details', f'>> {os.path.join(LOGS_DIR, "crontab_save_JIRA_Release_List_Details.log")} 2>&1'),
    
    # 每分钟执行一次，AR平台-日历页面，保存日历发布信息到DB
    ('* * * * *', 'app01.views.saveCalendarJiraReleaseList', f'>> {os.path.join(LOGS_DIR, "crontab_saveCalendarJiraReleaseList.log")} 2>&1'),
    
    # 每10分钟执行一次，根据标题获取所有发布版本并更新数据库
    ('*/10 * * * *', 'app01.views.get_all_release_by_title', f'>> {os.path.join(LOGS_DIR, "crontab_db_update.log")} 2>&1'),
    
    # 工作时间（10-12点，14-19点）整点执行，自动Seatalk路由功能
    ('0 10-12,14-19 * * *', 'app01.views.cronjob_auto_seatalk_router', f'>> {os.path.join(LOGS_DIR, "cronjob_auto_seatalk_router.log")} 2>&1'),
    
    # 每10分钟执行一次，获取未发布的版本信息
    ('*/10 * * * *', 'app01.views.get_unreleased_versions', f'>> {os.path.join(LOGS_DIR, "get_unreleased_versions.log")} 2>&1'),
    
    # 每天10点和16点执行，SPS线上聊天机器人bug提醒
    ('0 10,16 * * *', 'app01.views.cronjob_SPS_live_bug_of_chatbot_reminder', f'>> {os.path.join(LOGS_DIR, "cronjob_SPS_live_bug_of_chatbot_reminder.log")} 2>&1'),
    
    # 每天10点和16点执行，SPCB线上bug提醒
    ('0 10,16 * * *', 'app01.views.cronjob_SPCB_live_bug_reminder', f'>> {os.path.join(LOGS_DIR, "cronjob_SPCB_live_bug_reminder.log")} 2>&1'),
    
    # 每5分钟执行一次，新的SPS聊天机器人线上bug镜像监控
    ('*/5 * * * *', 'app01.views.cronjob_new_SPS_live_bug_of_chatbot_mirror', f'>> {os.path.join(LOGS_DIR, "cronjob_new_SPS_live_bug_of_chatbot_mirror.log")} 2>&1'),
    
    # 每天10点和16点执行，SPS聊天线上bug提醒
    ('0 10,16 * * *', 'app01.views.cronjob_SPS_live_bug_of_chat_reminder', f'>> {os.path.join(LOGS_DIR, "cronjob_SPS_live_bug_of_chat_reminder.log")} 2>&1'),
    
    # 每5分钟执行一次，新的SPS聊天线上bug镜像监控
    ('*/5 * * * *', 'app01.views.cronjob_new_SPS_live_bug_of_chat_mirror', f'>> {os.path.join(LOGS_DIR, "cronjob_new_SPS_live_bug_of_chat_mirror.log")} 2>&1'),
    
    # 每天10点和15点执行，聊天机器人临时任务提醒
    ('0 10,15 * * *', 'app01.views.cronjob_chatbot_adhoc_reminder', f'>> {os.path.join(LOGS_DIR, "cronjob_chatbot_adhoc_reminder.log")} 2>&1'),
    
    # 每天晚上22点执行，更新服务线上状态
    ('0 22 * * *', 'app01.views.update_service_live', f'>> {os.path.join(LOGS_DIR, "update_service_live.log")} 2>&1'),
    
    # 每小时执行一次，检查时间线变化
    ('0 * * * *', 'app01.seatalk_group_manager.check_timeline_changes', f'>> {os.path.join(LOGS_DIR, "check_timeline_changes_route.log")} 2>&1'),
    
    # 每5分钟执行一次，检查JIRA任务分配人变化
    ('*/5 * * * *', 'app01.seatalk_group_manager.check_jira_assignee_changes', f'>> {os.path.join(LOGS_DIR, "check_jira_assignee_changes.log")} 2>&1'),
    
    # 每天上午10点执行，Epic提醒，直接调用check_epic_milestone_reminders函数
    ('0 10 * * *', 'app01.epic_milestone_reminder.check_epic_milestone_reminders', f'>> {os.path.join(LOGS_DIR, "cronjob_epic_reminder.log")} 2>&1'),

    # 每周一和周四上午10点执行，业务需求Epic标签检查提醒
    ('0 10 * * 1,4', 'app01.epic_milestone_reminder.check_business_requirement_epic_reminders', f'>> {os.path.join(LOGS_DIR, "cronjob_business_requirement_epic_reminder.log")} 2>&1'),
    
    # 每天凌晨2点执行，同步机器人加入的所有群组信息
    ('0 2 * * *', 'app01.group_sync_manager.daily_sync_groups', f'>> {os.path.join(LOGS_DIR, "cronjob_group_sync.log")} 2>&1'),
    
    # 每5分钟执行一次，检查SPUAT Chat产品线UAT问题
    ('*/5 * * * *', 'app01.seatalk_group_manager.check_spuat_chat_tickets', f'>> {os.path.join(LOGS_DIR, "check_spuat_chat_tickets.log")} 2>&1'),
    
    # 工作日（周一到周五）上午10点30分执行，SPCPM项目里程碑提醒
    ('30 10 * * 1-5', 'app01.views.cronjob_spcpm_timeline_reminder', f'>> {os.path.join(LOGS_DIR, "cronjob_spcpm_timeline_reminder.log")} 2>&1'),
    
    ('0 9 * * *', 'app01.automergemaster.merge', ('release', 'master')),

    # 每天下午6点，把 master 分支合入 uat 分支
    ('0 18 * * *', 'app01.automergemaster.merge', ('master', 'uat')),    
    # 每个工作日18:00执行，检查Epic状态变更并自动创建群组
    ('0 18 * * 1-5', 'app01.seatalk_group_manager.check_epic_status_changes_for_group_creation', f'>> {os.path.join(LOGS_DIR, "auto_create_group_for_epic.log")} 2>&1'),
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'app01.statistics.middleware.StatisticsMiddleware',  # 统计数据收集中间件
]

ROOT_URLCONF = 'djangoProject.urls'
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True



TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': []
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'djangoProject.wsgi.application'
# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'OPTIONS': {
#             'read_default_file': 'my.cnf',
#         },
#     }
# }
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'chatbotcicd',
        'USER': 'root',
        'PASSWORD': '',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
            'autocommit': True,
            # 连接超时设置 - 增加超时时间
            'connect_timeout': 10,
            'read_timeout': 120,    # 增加到2分钟
            'write_timeout': 120,   # 增加到2分钟
            # MySQL服务器变量设置
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'; SET wait_timeout=28800; SET interactive_timeout=28800;",
        },
        # 数据库连接池配置 - 禁用连接池避免连接超时问题
        'CONN_MAX_AGE': 0,  # 禁用连接池，每次都新建连接
        'CONN_HEALTH_CHECKS': True,  # 启用连接健康检查
        'TEST': {
            'CHARSET': 'utf8mb4',
            'COLLATION': 'utf8mb4_unicode_ci',
        }
    }
}

# ===== 统计系统配置 =====

# 启用统计功能
STATISTICS_ENABLED = True
STATISTICS_ASYNC = True
STATISTICS_BUFFER_SIZE = 100
STATISTICS_FLUSH_INTERVAL = 60

# 中间件配置
STATISTICS_MIDDLEWARE_ENABLED = True
STATISTICS_COLLECT_ALL_REQUESTS = False
STATISTICS_API_PATHS = ['/api/']
STATISTICS_DB_QUERY_TRACKING = True
STATISTICS_USER_ACTIVITY_TRACKING = True
STATISTICS_SESSION_TIMEOUT = 1800

# 服务配置
STATISTICS_SERVICE_ENABLED = True
STATISTICS_REPORTS_ENABLED = True

# 数据保留配置（天数）
STATISTICS_DATA_RETENTION_DAYS = 90
STATISTICS_ACCESS_EVENT_RETENTION_DAYS = 30
STATISTICS_HEALTH_SNAPSHOT_RETENTION_DAYS = 7

# 告警配置
STATISTICS_ALERT_ERROR_RATE_THRESHOLD = 10.0
STATISTICS_ALERT_RESPONSE_TIME_THRESHOLD = 5.0
STATISTICS_ALERT_CRONJOB_FAILURE_THRESHOLD = 5

# 性能配置
STATISTICS_SLOW_QUERY_THRESHOLD = 1.0
STATISTICS_MAX_RESPONSE_CONTENT_LENGTH = 5000
STATISTICS_MAX_ERROR_MESSAGE_LENGTH = 1000

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django Unfold 配置
UNFOLD = {
    "SITE_TITLE": "ChatBot AutoRelease 管理后台",
    "SITE_HEADER": "ChatBot AutoRelease",
    "SITE_URL": "/",
    "SITE_ICON": {
        "light": "/static/icon-light.svg",  # 可选：浅色主题图标
        "dark": "/static/icon-dark.svg",    # 可选：深色主题图标
    },
    "SITE_LOGO": {
        "light": "/static/logo-light.svg",  # 可选：浅色主题Logo
        "dark": "/static/logo-dark.svg",    # 可选：深色主题Logo
    },
    "SITE_SYMBOL": "🤖",  # 使用emoji作为图标
    "SHOW_HISTORY": True,
    "SHOW_VIEW_ON_SITE": True,
    "ENVIRONMENT": "ChatBot AutoRelease Production",
    "DASHBOARD_CALLBACK": "app01.admin.dashboard_callback",
    "LOGIN": {
        "image": "/static/login-bg.jpg",
        "redirect_after": "/admin/",
    },
    "STYLES": [
        lambda request: "/static/css/custom-admin.css",
    ],
    "SCRIPTS": [
        lambda request: "/static/js/custom-admin.js",
    ],
    "COLORS": {
        "primary": {
            "50": "250 245 255",
            "100": "243 232 255",
            "200": "233 213 255",
            "300": "196 181 253",
            "400": "147 51 234",
            "500": "124 58 237",
            "600": "109 40 217",
            "700": "91 33 182",
            "800": "76 29 149",
            "900": "59 7 100",
        },
    },
    "EXTENSIONS": {
        "modeltranslation": {
            "flags": {
                "en": "🇺🇸",
                "fr": "🇫🇷",
                "nl": "🇳🇱",
            },
        },
    },
    "SIDEBAR": {
        "show_search": True,
        "show_all_applications": True,
        "navigation": [
            {
                "title": "导航",
                "separator": True,
                "items": [
                    {
                        "title": "统计监控",
                        "icon": "📊",
                        "link": "/statistics/dashboard/",
                    },
                    {
                        "title": "系统管理",
                        "icon": "⚙️",
                        "link": "/admin/",
                    },
                ],
            },
        ],
    },
}

# 计划任务配置
CRONTAB_LOCK_JOBS = True
CRONTAB_DJANGO_PROJECT_NAME = 'djangoProject'
CRONTAB_DJANGO_MANAGE_PATH = os.path.join(BASE_DIR, 'manage.py')
CRONTAB_DJANGO_SETTINGS_MODULE = 'djangoProject.settings'
CRONTAB_PYTHON_EXECUTABLE = '/data/chatbot-ar-be/venv/bin/python'


DOCUMENT_PROCESSING = {
    # Confluence 配置
    'confluence': {
        'enabled': True,  # 启用 Confluence 功能
        'auth_token': None,  # 您的 Confluence API Token
        'username': None,  # 使用 token 认证时不需要用户名
        'password': None,  # 使用 token 认证时不需要密码
        'session_timeout': 30,  # 请求超时时间（秒）
        'max_content_length': 50000,  # 最大内容长度
        'timeout': 30,  # 超时时间
        'retry_count': 3,  # 重试次数
        'retry_delay': 1  # 重试延迟（秒）
    },
    
    # 日志平台配置（预留，暂未启用）
    'log_platforms': {
        'enabled': False,  # 暂时关闭
        'kibana': {
            'enabled': False,
            'base_url': None,
            'auth_token': None,
            'timeout': 30
        },
        'grafana': {
            'enabled': False,
            'base_url': None,
            'auth_token': None,
            'timeout': 30
        }
    },
    
    # 追踪平台配置（预留，暂未启用）
    'trace_platforms': {
        'enabled': False,  # 暂时关闭
        'jaeger': {
            'enabled': False,
            'base_url': None,
            'auth_token': None,
            'timeout': 30
        }
    },
    
    # 通用配置
    'general': {
        'cache_enabled': True,  # 启用缓存
        'cache_timeout': 3600,  # 缓存超时时间（1小时）
        'max_concurrent_requests': 10,  # 最大并发请求数
        'default_timeout': 30,  # 默认超时时间
        'max_content_length': 50000,  # 最大内容长度
        'supported_languages': ['中文', '英文', '日文', '韩文'],  # 支持的翻译语言
        'default_language': '中文'  # 默认语言
    }
}
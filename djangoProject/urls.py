"""djangoProject URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from app01 import views

urlpatterns = [
    path('admin/', admin.site.urls),  # 启用Django Unfold管理后台
    path('deploy', views.deploy, name="deploy"),
    path('apiCallback', views.apiCallback, name="apiCallback"),
    path('api/merge', views.merge, name="merge"),
    path('api/tag', views.tag, name="tag"),
    path('api/read_data_json', views.read_data_json, name="read_data_json"),#前端在使用此接口
    path('api/create_jira_ticket', views.create_jira_ticket, name="create_jira_ticket"),
    path('api/new_read_data_json', views.new_read_data_json, name="new_read_data_json"),#前端不在使用此接口，数据较少。
    path('api/log_analysis', views.log_analysis, name="log_analysis"),
    path('api/get_jira_release_list', views.get_jira_release_list, name="get_jira_release_list"),
    path('api/send_title', views.get_single_release_by_title, name="get_single_release_by_title"),
    path('api/pod_update', views.pod_update, name="pod_update"),
    path('api/startAuto', views.startAuto, name="startAuto"),
    path('api/seatalk', views.seatalk, name="seatalk"),
    path('api/autocheckdata', views.autocheckdata, name="autocheckdata"),
    path('api/getCalendarJiraReleaseList', views.getCalendarJiraReleaseList, name="getCalendarJiraReleaseList"),
    path('api/getAllJiraReleaseList', views.getAllJiraReleaseList, name="getAllJiraReleaseList"),
    path('api/get_all_jira_release_list_details', views.get_all_jira_release_list_details,
         name="get_all_jira_release_list_details"),
    path('api/get_seatalk_recall', views.get_seatalk_recall, name="get_seatalk_recall"),
    # path('callback', views.callback, name="callback"),
    path('api/real_time_push_message', views.real_time_push_message, name="real_time_push_message"),
    path('api/jira_seatalk', views.jira_seatalk, name="jira_seatalk"),
    path('api/mr_seatalk_msg', views.mr_seatalk_msg, name="mr_seatalk_msg"),
    path('api/mr_copy_msg', views.mr_copy_msg, name="mr_copy_msg"),
    path('api/autochecknewdata', views.autochecknewdata, name="autochecknewdata"),
    path('api/get_jira_release_list_finished', views.get_jira_release_list_finished,
         name="get_jira_release_list_finished"),
    path('api/start_single_ar', views.start_single_ar, name="start_single_ar"),
    path('api/mr_seatalk_single_feature_msg', views.mr_seatalk_single_feature_msg,
         name="mr_seatalk_single_feature_msg"),
    path('api/get_key_jira_release_list', views.get_key_jira_release_list, name="get_key_jira_release_list"),
    path('api/get_all_release_by_title_api', views.get_all_release_by_title_api, name="get_all_release_by_title_api"),
    path('api/signed_off_seatalk', views.signed_off_seatalk, name="signed_off_seatalk"),
    path('api/ar_start_merge', views.ar_start_merge, name="ar_start_merge"),
    path('api/get_unreleased_versions', views.get_unreleased_versions, name="get_unreleased_versions"),
    path('api/check-timeline/', views.check_timeline_changes_route, name='check_timeline'),
    path('api/epic-reminder/', views.epic_reminder_manual_trigger, name='epic_reminder_manual_trigger'),

    # 统计API路由
    path('api/statistics/', include('app01.statistics.urls')),

    # 包含测试框架的URL
    path('', include('app01.urls')),
]

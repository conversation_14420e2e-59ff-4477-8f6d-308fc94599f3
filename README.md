# ChatBot AutoRelease 平台功能介绍和使用指南

## 项目概述

ChatBot AutoRelease (简称 AR) 是一个集成化的自动发布和项目管理平台，主要服务于 Shopee Chatbot 团队的开发、测试、发布流程。系统分为前端展示界面（chatbot-ar-fe）和后端服务（chatbot-ar-be），提供完整的 DevOps 解决方案。

## 系统架构

- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Django + PostgreSQL
- **集成系统**: JIRA、GitLab、SeaTalk、Jenkins/Space
- **部署地址**: https://autorelease.chatbot.shopee.io

---

## 第一部分：AutoRelease 平台

### 1.1 功能概述

AutoRelease 平台是系统的核心模块，主要处理项目发布相关的自动化流程，包括发布单管理、MR 创建、代码合并、部署监控等功能。

### 1.2 主要功能模块

#### 1.2.1 发布单检查 (Release Check)
**访问路径**: `/releasecheck`
**功能描述**: 实时监控和管理项目发布单的状态

**核心功能**:
- **发布单筛选**: 支持按项目类型（Bus/Adhoc/Hotfix）和项目分类筛选
- **实时状态更新**: 自动拉取 JIRA 发布单数据并展示
- **Progress 跟踪**: 展示每个需求的完成进度，包括：
  - Signed off 状态
  - Code Merged 状态  
  - Config Changed 状态
  - DB Changed 状态
  - Bug 解决率统计

**操作指南**:
1. 选择目标发布单
2. 点击"更新"按钮拉取最新数据
3. 查看各项目的完成状态
4. 使用展开功能查看 MR 详细信息

#### 1.2.2 自动化 MR 管理
**功能描述**: 批量创建和管理合并请求（Merge Request）

**核心功能**:
- **批量创建 MR**: 根据发布单自动为所有相关仓库创建 MR
- **MR 状态跟踪**: 实时监控 MR 的合并状态
- **智能提醒**: 通过 SeaTalk 发送 MR 相关通知

**操作流程**:
1. 在发布单详情中展开需求项
2. 点击"创建"按钮启动自动 MR 创建
3. 系统自动识别相关仓库和分支
4. 批量创建 MR 并返回链接
5. 可通过"发送"按钮将 MR 信息推送到 SeaTalk

#### 1.2.3 SeaTalk 集成通知
**功能描述**: 与 SeaTalk 深度集成，提供智能通知服务

**通知类型**:
- **Signed off 提醒**: 发布前签名确认提醒
- **MR 提醒**: 合并请求状态通知
- **Checklist 提醒**: 发布检查清单提醒

**使用方法**:
- 点击对应的提醒按钮（"Signed off提醒"、"MR提醒"、"Checklist提醒"）
- 系统自动识别相关人员并发送 @提醒
- 支持单个需求和整个发布单的通知

### 1.3 发布历史 (Release History)
**访问路径**: `/releaseHistory`
**功能描述**: 查看历史发布记录和统计信息

**主要功能**:
- 历史发布单列表
- 发布状态统计
- 项目进度跟踪
- Bug 解决率分析

### 1.4 发布日历 (Release Calendar)
**访问路径**: `/calendar`
**功能描述**: 可视化展示发布计划和时间线

**主要功能**:
- 月度发布日历视图
- 发布单时间节点标记
- 快速跳转到 JIRA 发布单
- 发布统计数据展示

**使用指南**:
1. 日历中标记的日期表示有发布计划
2. 点击日期下的链接可直接跳转到对应的 JIRA 发布单
3. 页面上方显示发布统计信息

---

## 第二部分：CI/CD 相关功能

### 2.1 功能概述

CI/CD 模块主要处理持续集成和持续部署相关的自动化流程，包括构建触发、部署管理、环境配置等。

### 2.2 部署配置管理
**访问路径**: `/deployConfig` (已集成到主平台)
**后端核心**: `views.py` 中的 `deploy` 函数

### 2.3 自动化部署流程

#### 2.3.1 核心部署函数 (`deploy`)
**功能描述**: 接收 pipeline 触发请求，执行自动化部署

**支持的部署环境**:
- `test`: 测试环境
- `uat`: UAT 环境  
- `master`: 生产环境
- 自定义分支: 支持 PFB (Pull From Branch) 模式

**关键特性**:
- **智能分支选择**: 根据环境自动选择对应分支
- **多地区部署**: 自动配置不同服务的部署地区
- **RN 项目特殊处理**: 为 React Native 项目提供专门的部署参数
- **错误处理**: 完善的异常处理和日志记录

#### 2.3.2 服务部署配置

系统预配置了多种服务类型的部署策略：

**全地区部署服务** (`cids["all"]`):
- `shopee-chatbot-api`
- `shopee-csdata-ssarfe`
- `shopee-chatbot-admin-*`

**单地区部署服务** (`cids["single"]`):
- 大部分 static 静态资源服务
- `dashboardstatic`

**自定义地区部署**:
- `chatbotstatic`: "PL,ES,ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,CL"
- `shopee-autotrainingportal-adminstatic`: 全地区+CN

#### 2.3.3 部署监控
**访问路径**: `/monitor`
**功能描述**: 实时监控部署状态和服务健康

**监控内容**:
- 部署任务状态跟踪
- 构建日志查看
- 服务健康状态
- 部署历史记录

### 2.4 Callback 机制
**功能描述**: 处理 Jenkins/Space 的部署回调

**核心功能**:
- 接收部署状态回调
- 更新数据库中的部署记录
- 发送部署结果通知
- 触发后续自动化流程

**使用流程**:
1. 外部 CI/CD 系统调用 `/apiCallback` 接口
2. 系统解析回调数据并更新状态
3. 根据部署结果发送通知
4. 记录部署日志和统计信息

---

## 第三部分：SeaTalk 项目管理功能

### 3.1 功能概述

SeaTalk 项目管理模块提供了深度集成的项目协作功能，包括群组管理、Epic 里程碑提醒、JIRA 工单处理等。

### 3.2 SeaTalk 群组管理 (`seatalk_group_manager.py`)

#### 3.2.1 自动群组创建
**功能描述**: 根据 JIRA Epic 自动创建对应的 SeaTalk 群组

**核心函数**: `auto_create_group_for_jira(jira_key)`

**创建流程**:
1. 从 JIRA 获取 Epic 详细信息
2. 提取项目成员信息（PM、开发、QA等）
3. 自动添加团队 TL (Team Leader)
4. 创建 SeaTalk 群组并设置群主
5. 发送 Epic Timeline 信息到群组

**群主配置规则**:
- SPCB 项目: `<EMAIL>`
- SPCT 项目: `<EMAIL>`
- 其他项目: `<EMAIL>`

**成员收集逻辑**:
- Product Manager (PM)
- Developer (开发负责人)
- FE List (前端开发列表)
- BE List (后端开发列表)
- QA / QA List (测试人员)
- 自动添加对应的团队 TL

#### 3.2.2 Timeline 变更同步
**功能描述**: 监控 JIRA Epic 的时间线变更并同步到群组

**核心函数**: `sync_timeline_changes()`

**监控内容**:
- Epic 关键日期变更
- 人员变更
- 状态变更

**变更通知**:
- 实时推送变更信息到对应群组
- @相关人员提醒
- 提供变更对比信息

#### 3.2.3 定时检查机制
**检查频率**: 每60分钟执行一次
**核心函数**: `check_timeline_changes()`

**检查范围**:
- 最近61分钟内更新的 Epic
- 项目范围: SPCB, SPCT
- 变更类型: 时间线、人员、状态

### 3.3 Epic 里程碑提醒 (`epic_milestone_reminder.py`)

#### 3.3.1 提醒类型配置

**1. 联调开始提醒 (`integration_start`)**
- 提醒时间: 提前1个工作日
- 目标受众: 开发团队
- 提醒内容: 联调准备工作检查

**2. 提测时间提醒 (`qa_start`)**
- 提醒时间: 提前1个工作日
- 目标受众: 开发团队
- 提醒内容: 测试环境部署、自测完成确认

**3. UAT开始提醒 (`uat_start`)**
- 提醒时间: 提前1个工作日
- 目标受众: PM
- 提醒内容: UAT测试用例确认、资源协调

**4. UAT Sign-off提醒 (`uat_signoff`)**
- 提醒时间: 提前1个工作日
- 目标受众: PM
- 提醒内容: UAT完成确认、Bug修复状态

**5. 发布准备提醒 (`release_reminder`)**
- 提醒时间: 每周四
- 目标受众: 开发团队
- 提醒内容: 发布准备工作确认

#### 3.3.2 配置和使用

**项目配置**:
```python
"enabled_projects": ["SPCB"]  # 支持的项目列表
```

**群组获取**:
- 从 `SeatalkGroup` 数据库表动态查询
- 根据 Epic key 匹配群组名称
- 自动容错和异常处理

**定时执行**:
- 执行时间: 每天早上10点
- 工作日检查: 自动跳过周末
- 错误通知: 异常自动推送给管理员

#### 3.3.3 手动触发
**API 接口**: `/api/epic-reminder/`
**使用方法**:
```bash
curl -X POST http://your-domain/api/epic-reminder/
```

### 3.4 SPCPM 项目管理功能

#### 3.4.1 SPCPM 角色获取优化
**功能描述**: 优化SPCPM项目中开发人员信息的获取方式，从Epic下的Task中获取更准确的开发人员信息

**核心文件**: `spcpm_timeline_reminder.py`

**主要改进**:
- 从Epic下的所有未关闭Task中获取开发人员信息，而不仅依赖Epic的Developer字段
- 使用JQL查询`"Epic Link" = EPIC-KEY AND issuetype = Task AND status != closed`获取相关Task
- 对多个Task中的开发人员信息进行去重合并
- 仅针对developer/dev角色进行优化，其他角色（如qa、pm等）的获取逻辑保持不变

**使用方法**:
- 命令格式保持不变: `@ChatbotAR dev SPCPM-12345`
- 系统自动从Task中获取更准确的开发人员信息
- 如果Task中没有找到开发人员，则回退到使用Epic的Developer字段

**技术细节**:
- 两步查询流程: 先通过"Request ID"获取Epic，再通过Epic获取Task
- 完善的调试日志记录，便于问题排查
- 单元测试覆盖主要功能点

**相关文档**:
- 详细说明文档: `docs/SPCPM_DEVELOPER_TASK_OPTIMIZATION.md`

### 3.5 SeaTalk 机器人回调功能

#### 3.5.1 支持的命令

**Timeline 查询**:
- 命令: `timeline` 或 `tl`
- 功能: 查询 Epic 的详细时间线信息
- 使用: `@ChatbotAR timeline` 或在消息中包含 Epic key

**Checklist 操作**:
- 命令: `/checklist`
- 支持字段:
  - `SO`: Signed off
  - `CM`: Code Merged  
  - `CC`: Config Changed
  - `DC`: DB Changed

**Bug 状态查询**:
- 命令: `@ChatbotAR bug`
- 功能: 查询需求下的 Bug 状态

#### 3.5.2 群组识别机制
**Epic Key 提取**:
1. 优先从消息内容中提取 SP*-XXXXX 格式
2. 如消息中无 Epic key，从群名中提取
3. 支持正则匹配: `SP[A-Z]+-\d{1,6}`

### 3.6 定时任务和监控

#### 3.6.1 Live Bug 提醒
**功能描述**: 定时提醒未解决的线上问题

**SPS Live Bug 提醒** (`cronjob_SPS_live_bug_of_chatbot_reminder`):
- 执行时间: 每天10点
- 查询范围: SPS 项目中 Chatbot 产品线的 doing 状态 Live Bug
- 通知群组: CS Bot Live issue DOD 群

**SPCB Live Bug 提醒** (`cronjob_SPCB_live_bug_reminder`):
- 执行时间: 每天10点
- 查询范围: SPCB 项目中未解决的 Live Bug
- 通知群组: CS Bot Live issue DOD 群

#### 3.6.2 MR 处理提醒
**功能描述**: 定时提醒团队处理未合并的 MR

**执行频率**: 
- 每天10-19点整点执行
- 分别处理不同团队的 MR

**提醒对象**:
- Team Leader
- 相关开发人员
- 项目负责人

---

## 第四部分：Jira 工具集成

### 4.1 Jira Tools 模块
**访问路径**: `/jiratools`
**功能描述**: 提供 JIRA 相关的自动化工具

**主要功能**:
- 自定义 JQL 查询配置
- 定时任务管理
- SeaTalk 群组绑定
- 手动触发执行

### 4.2 使用指南

#### 4.2.1 创建新的监控任务
1. 点击"New"按钮
2. 填写任务描述
3. 配置 JQL 查询语句
4. 设置 Cron 表达式
5. 绑定 SeaTalk 群组 ID
6. 保存配置

#### 4.2.2 管理现有任务
- 查看任务列表和配置
- 开启/关闭任务开关
- 点击"立即执行"测试任务
- 修改任务配置

---

## 第五部分：系统接入和配置指南

### 5.1 环境要求

**前端环境**:
- Node.js >= 14
- Yarn 包管理器
- Vue 3 + Vite

**后端环境**:
- Python 3.8+
- Django 4.1+
- PostgreSQL
- Redis (缓存)

### 5.2 核心配置

#### 5.2.1 JIRA 集成配置
```python
JIRA_URL = "https://jira.shopee.io"
JIRA_TOKEN = "your-jira-token"
```

#### 5.2.2 SeaTalk 机器人配置
```python
# SeaTalk 回调处理
SEATALK_BOT_TOKEN = "your-bot-token"
SEATALK_WEBHOOK_URL = "your-webhook-url"
```

#### 5.2.3 部署配置
```python
Config = {
    "branch": {
        "test": "origin/test",
        "uat": "origin/uat", 
        "master": "origin/master"
    },
    "cids": {
        "single": "SG",
        "all": "ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,ES,CL"
    }
}
```

### 5.3 数据库模型

#### 5.3.1 核心模型
- `Deploy`: 部署记录模型
- `Autorelease`: 自动发布数据
- `SeatalkGroup`: SeaTalk 群组管理
- `CalendarJiraReleaseList`: 发布日历数据

#### 5.3.2 数据迁移
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5.4 定时任务配置

#### 5.4.1 Crontab 设置
```bash
# 每小时检查部署回调
0 * * * * curl -X POST http://your-domain/api/callback/

# 每天10点检查 Epic 里程碑
0 10 * * * curl -X POST http://your-domain/api/epic-reminder/

# 每60分钟检查 Timeline 变更  
0 * * * * python manage.py check_timeline_changes
```

#### 5.4.2 Django Crontab 配置
```python
CRONJOBS = [
    ('0 * * * *', 'app01.views.callback'),
    ('* * * * *', 'app01.views.save_releases'),
    ('0 10,15,18 * * *', 'app01.views.failuremsg_final'),
    # ... 更多定时任务
]
```

### 5.5 权限和安全

#### 5.5.1 JIRA 权限配置
- 确保 JIRA token 具备读取项目信息的权限
- 配置合适的项目范围限制

#### 5.5.2 SeaTalk 权限配置  
- 机器人需要群组发送消息权限
- 配置合适的群组访问范围

#### 5.5.3 部署权限配置
- Jenkins/Space API 访问权限
- GitLab API 访问权限

---

## 第六部分：故障排除和最佳实践

### 6.1 常见问题解决

#### 6.1.1 JIRA 连接问题
**问题**: JIRA API 调用失败
**解决方案**:
1. 检查 JIRA token 是否有效
2. 确认网络连接和防火墙设置
3. 验证 JIRA 项目访问权限

#### 6.1.2 SeaTalk 消息发送失败
**问题**: SeaTalk 通知无法发送
**解决方案**:
1. 验证 SeaTalk 机器人 token
2. 确认群组 ID 正确性
3. 检查机器人群组权限

#### 6.1.3 部署任务异常
**问题**: 自动部署失败
**解决方案**:
1. 检查 Jenkins/Space API 配置
2. 确认项目配置映射正确
3. 查看部署日志详细信息

### 6.2 监控和日志

#### 6.2.1 系统日志
- Django 应用日志
- Crontab 执行日志
- 部署操作日志

#### 6.2.2 错误通知
- 所有系统异常自动推送到调试群
- 关键操作失败邮件通知
- Epic 提醒功能异常自动报告

### 6.3 性能优化

#### 6.3.1 数据库优化
- 定期清理历史数据
- 合理设置数据库索引
- 使用连接池管理

#### 6.3.2 API 优化
- 合理设置超时时间
- 实现请求重试机制
- 使用缓存减少重复请求

### 6.4 扩展开发

#### 6.4.1 添加新的提醒类型
1. 在 `EPIC_REMINDER_CONFIG` 中添加配置
2. 在检查函数中添加对应逻辑
3. 配置相关的 JIRA 字段映射

#### 6.4.2 集成新的外部系统
1. 创建对应的 API 客户端
2. 添加配置项和认证信息
3. 实现数据同步逻辑

---

## 第七部分：API 接口文档

### 7.1 核心 API 接口

#### 7.1.1 发布管理接口
- `POST /api/startAuto`: 启动自动发布流程
- `POST /api/merge`: 执行代码合并操作
- `GET /api/get_jira_release_list`: 获取发布单列表
- `POST /api/autochecknewdata`: 自动检查新数据

#### 7.1.2 SeaTalk 集成接口
- `POST /api/jira_seatalk`: 发送 JIRA 相关通知
- `POST /api/mr_seatalk_msg`: 发送 MR 消息
- `POST /api/get_seatalk_recall`: 处理 SeaTalk 回调

#### 7.1.3 部署相关接口
- `POST /deploy`: 触发自动部署
- `POST /apiCallback`: 接收部署回调
- `GET /api/read_data_json`: 读取部署数据

### 7.2 使用示例

#### 7.2.1 启动自动发布
```javascript
const response = await fetch('/api/startAuto', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        releaseTitle: 'your-release-title',
        features: ['feature1', 'feature2']
    })
});
```

#### 7.2.2 发送 SeaTalk 通知
```javascript
const response = await fetch('/api/jira_seatalk', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        message: 'notification-message',
        groupId: 'seatalk-group-id'
    })
});
```

---

## 结语

ChatBot AutoRelease 平台为 Shopee Chatbot 团队提供了完整的 DevOps 解决方案，涵盖了从项目规划、开发协作、代码管理到自动化部署的全生命周期管理。通过深度集成 JIRA、SeaTalk、GitLab 等工具，大幅提升了团队的工作效率和协作质量。

如有任何问题或需要技术支持，请联系：
- **项目负责人**: <EMAIL>

更多详细信息和最新更新，请参考项目 Git 仓库和相关技术文档。
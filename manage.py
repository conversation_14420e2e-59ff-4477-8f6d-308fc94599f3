#!/usr/bin/env python
"""
命令行工具，用于管理Django项目
"""
import os
import sys

def main():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)

if __name__ == '__main__':
    main()

"""
数据库迁移脚本，用于创建SPCPMTimelineReminder表

在服务器上执行以下命令：

1. 生成迁移文件
python manage.py makemigrations app01

2. 应用迁移
python manage.py migrate app01

或者一次性迁移所有app：
python manage.py makemigrations
python manage.py migrate
"""

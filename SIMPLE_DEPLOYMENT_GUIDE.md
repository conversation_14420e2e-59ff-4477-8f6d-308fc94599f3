# ChatBot AutoRelease 统计系统简化部署方案

## 🎯 部署概述
**目标**: 为现有系统添加数据统计功能  
**数据库**: 使用现有MySQL，新增6个统计表  
**风险**: 低风险，只新增功能，不影响现有用户  
**时间**: 约15-20分钟  

## 🚀 一键部署脚本

### 创建部署脚本
```bash
# 创建简单部署脚本
cat > deploy_statistics.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 开始部署统计系统..."

# 1. 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin main

# 2. 备份数据库
echo "💾 备份数据库..."
mysqldump -u root chatbotcicd > backup_$(date +%Y%m%d_%H%M%S).sql
echo "✅ 数据库备份完成"

# 3. 执行数据库迁移
echo "🗄️ 执行数据库迁移..."
python manage.py migrate

# 4. 重启服务
echo "🔄 重启服务..."
# 根据你的服务管理方式选择一个：
sudo systemctl restart chatbot-ar-be
# 或者: sudo supervisorctl restart chatbot-ar-be
# 或者: pm2 restart chatbot-ar-be

# 5. 验证部署
echo "✅ 验证部署..."
sleep 5
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:8000/api/statistics/dashboard/

echo "🎉 部署完成！"
echo "📊 监控面板: http://your-domain/api/statistics/dashboard/"
EOF

chmod +x deploy_statistics.sh
```

### 执行部署
```bash
./deploy_statistics.sh
```

## 📋 手动部署步骤

如果你更喜欢手动控制每个步骤：

### 步骤1: 拉取代码 (2分钟)
```bash
cd /path/to/chatbot-ar-be
git pull origin main
```

### 步骤2: 备份数据库 (3分钟)
```bash
# 备份MySQL数据库
mysqldump -u root chatbotcicd > backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
ls -lh backup_*.sql
```

### 步骤3: 执行迁移 (5分钟)
```bash
# 检查迁移状态
python manage.py showmigrations app01

# 执行迁移
python manage.py migrate

# 验证新表
python manage.py dbshell -e "SHOW TABLES LIKE 'bot_%';"
```

### 步骤4: 重启服务 (2分钟)
```bash
# 根据你的服务管理方式选择：

# 方式1: systemd
sudo systemctl restart chatbot-ar-be

# 方式2: supervisor
sudo supervisorctl restart chatbot-ar-be

# 方式3: PM2
pm2 restart chatbot-ar-be

# 方式4: 手动重启
pkill -f "manage.py runserver"
nohup python manage.py runserver 0.0.0.0:8000 &
```

### 步骤5: 验证部署 (3分钟)
```bash
# 检查服务状态
curl -I http://localhost:8000/

# 检查统计功能
curl http://localhost:8000/api/statistics/dashboard/

# 检查新表
python manage.py dbshell -e "SELECT COUNT(*) FROM bot_access_event;"
```

## 📊 新增功能

部署完成后，你将获得：

### 🌐 Web监控面板
- **访问地址**: `http://your-domain/api/statistics/dashboard/`
- **功能**: 实时数据展示、用户活动统计、系统性能监控

### 🔌 API接口
```bash
# 实时监控数据
curl http://localhost:8000/api/statistics/realtime/dashboard/

# 指令执行趋势
curl http://localhost:8000/api/statistics/realtime/command-trends/

# 日报数据
curl http://localhost:8000/api/statistics/reports/daily/
```

## 🛡️ 安全保障

### 零影响设计
- ✅ 异步数据收集，不影响现有功能
- ✅ 新增独立数据表，不修改现有数据
- ✅ 统计功能异常不会影响用户使用
- ✅ 可随时禁用统计功能

### 配置说明
部署后会自动添加以下配置到 `settings.py`：
```python
# 统计系统配置
STATISTICS_ENABLED = True
STATISTICS_ASYNC = True
STATISTICS_SERVICE_ENABLED = True
```

## 🚨 紧急回滚

如果出现问题，快速回滚：

### 方式1: 禁用统计功能 (1分钟)
```bash
# 临时禁用统计功能
sed -i 's/STATISTICS_ENABLED = True/STATISTICS_ENABLED = False/' djangoProject/settings.py
sudo systemctl restart chatbot-ar-be
```

### 方式2: 完全回滚 (5分钟)
```bash
# 回滚数据库迁移
python manage.py migrate app01 0023

# 恢复代码版本
git reset --hard HEAD~1

# 重启服务
sudo systemctl restart chatbot-ar-be
```

### 方式3: 恢复数据库备份 (10分钟)
```bash
# 恢复数据库备份
mysql -u root chatbotcicd < backup_YYYYMMDD_HHMMSS.sql

# 重启服务
sudo systemctl restart chatbot-ar-be
```

## 🔍 故障排查

### 常见问题
1. **迁移失败**
   ```bash
   python manage.py migrate app01 0024 --verbosity=2
   ```

2. **服务启动失败**
   ```bash
   python manage.py check
   tail -f /var/log/your-app/error.log
   ```

3. **统计页面无法访问**
   ```bash
   curl -I http://localhost:8000/api/statistics/dashboard/
   ```

## ✅ 部署检查清单

- [ ] 代码已拉取到最新版本
- [ ] 数据库备份已完成
- [ ] 迁移执行成功 (6个新表)
- [ ] 服务重启成功
- [ ] 统计页面可以访问
- [ ] 现有功能正常工作

## 📞 支持信息

**技术负责人**: <EMAIL>  
**部署时间**: 建议业务低峰期  
**预计耗时**: 15-20分钟  

---

## 🎉 部署完成后

访问 `http://your-domain/api/statistics/dashboard/` 查看统计监控面板！

**新增数据表**:
- `bot_access_event` - 用户访问记录
- `command_execution_record` - 指令执行记录  
- `system_performance_metrics` - 性能指标
- `cronjob_execution_monitor` - 定时任务监控
- `user_activity_summary` - 用户活动汇总
- `system_health_snapshot` - 系统健康快照

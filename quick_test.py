#!/usr/bin/env python3
"""
快速测试脚本 - 验证核心功能
"""

import requests
import json
import time
from datetime import datetime

def quick_test(base_url="http://localhost:8081"):
    """执行快速测试"""
    
    print("🚀 开始快速功能测试...")
    
    # 核心测试用例
    test_cases = [
        {
            "name": "🔧 传统指令 - MR检查",
            "request": {
                "test_mode": True,
                "message_type": "group",
                "user_id": "test_user",
                "message": "@ChatbotAR chatbot mr"
            }
        },
        {
            "name": "🔧 传统指令 - Bug查询",
            "request": {
                "test_mode": True,
                "message_type": "group", 
                "user_id": "test_user",
                "message": "@ChatbotAR bug SPCB-12345"
            }
        },
        {
            "name": "🤖 AI功能 - 未完成子任务查询(修复验证)",
            "request": {
                "test_mode": True,
                "message_type": "private",
                "user_id": "test_user",
                "message": "/ai 我有哪些未完成的子任务"
            },
            "validation": "status not in (Closed, Done, Icebox)"
        },
        {
            "name": "🤖 AI功能 - Bug查询",
            "request": {
                "test_mode": True,
                "message_type": "private",
                "user_id": "test_user", 
                "message": "/ai 查询我的bug"
            }
        },
        {
            "name": "🤖 AI功能 - 项目进度查询",
            "request": {
                "test_mode": True,
                "message_type": "private",
                "user_id": "test_user",
                "message": "/ai SPCB-12345的进度如何"
            }
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}/{len(test_cases)}] {test_case['name']}")
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/api/mock-seatalk/webhook/",
                json=test_case['request'],
                timeout=30
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                success = data.get('success', False)
                messages = data.get('intercepted_messages', [])
                
                # 特殊验证
                validation_passed = True
                validation_msg = "基本验证通过"
                
                if 'validation' in test_case:
                    validation_keyword = test_case['validation']
                    message_content = ' '.join([msg.get('text', '') for msg in messages])
                    if validation_keyword not in message_content:
                        validation_passed = False
                        validation_msg = f"验证失败：未找到关键词 '{validation_keyword}'"
                
                if success and messages and validation_passed:
                    print(f"   ✅ 成功 ({response_time:.2f}s) - 拦截了{len(messages)}条消息")
                    results.append({"name": test_case['name'], "status": "PASS", "time": response_time})
                else:
                    print(f"   ❌ 失败 ({response_time:.2f}s) - {validation_msg}")
                    results.append({"name": test_case['name'], "status": "FAIL", "time": response_time})
                    
            else:
                print(f"   ❌ HTTP错误 {response.status_code}")
                results.append({"name": test_case['name'], "status": "ERROR", "time": 0})
                
        except Exception as e:
            print(f"   ⚠️  异常: {str(e)}")
            results.append({"name": test_case['name'], "status": "ERROR", "time": 0})
        
        # 测试间隔
        if i < len(test_cases):
            time.sleep(1)
    
    # 生成总结
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    passed = sum(1 for r in results if r['status'] == 'PASS')
    failed = sum(1 for r in results if r['status'] == 'FAIL') 
    errors = sum(1 for r in results if r['status'] == 'ERROR')
    total = len(results)
    
    print(f"总测试数: {total}")
    print(f"通过: {passed} ✅")
    print(f"失败: {failed} ❌") 
    print(f"错误: {errors} ⚠️")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    avg_time = sum(r['time'] for r in results if r['time'] > 0) / max(1, sum(1 for r in results if r['time'] > 0))
    print(f"平均响应时间: {avg_time:.2f}秒")
    
    print("\n📋 详细结果:")
    for result in results:
        status_emoji = {"PASS": "✅", "FAIL": "❌", "ERROR": "⚠️"}[result['status']]
        print(f"  {status_emoji} {result['name']} ({result['time']:.2f}s)")
    
    # 重点验证
    print("\n🎯 重点验证结果:")
    subtask_test = next((r for r in results if "未完成子任务查询" in r['name']), None)
    if subtask_test:
        if subtask_test['status'] == 'PASS':
            print("  ✅ 未完成子任务查询修复验证 - 通过")
        else:
            print("  ❌ 未完成子任务查询修复验证 - 失败")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print(f"\n⚠️  有 {failed + errors} 个测试未通过，请检查系统状态。")
        return False

def test_connection(base_url="http://localhost:8081"):
    """测试连接"""
    try:
        response = requests.get(f"{base_url}/api/mock-seatalk/results/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return False

if __name__ == "__main__":
    print("ChatBot AutoRelease 快速测试工具")
    print("="*50)
    
    # 测试连接
    if not test_connection():
        print("\n请确保后端服务正在运行在 http://localhost:8081")
        exit(1)
    
    # 执行测试
    success = quick_test()
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not success:
        exit(1)
```

### 2. `test_config.json` - 测试配置文件

```json
{
  "test_environment": {
    "base_url": "http://localhost:8081",
    "timeout": 30,
    "retry_count": 2,
    "delay_between_tests": 1
  },
  "test_user": {
    "user_id": "test_user_001",
    "user_name": "测试用户",
    "user_email": "<EMAIL>"
  },
  "test_groups": {
    "default_group": "test_group_001",
    "spcb_group": "test_group_spcb_12345",
    "spcpm_group": "test_group_spcpm_123456"
  },
  "validation_rules": {
    "response_time_threshold": 10.0,
    "required_message_count": 1,
    "success_rate_threshold": 80.0
  },
  "test_data": {
    "valid_jira_keys": [
      "SPCB-12345",
      "SPCB-67890",
      "SPCPM-123456"
    ],
    "invalid_jira_keys": [
      "INVALID-12345",
      "RESTRICTED-12345"
    ]
  }
}

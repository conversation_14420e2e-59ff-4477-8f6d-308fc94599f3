#!/usr/bin/env python3
"""
测试数据库连接修复效果
验证MySQL连接超时问题和重试机制是否正常工作
"""

import os
import sys
import django
import time
import logging
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from django.db import connection
from app01.utils.db_retry import test_db_connection, db_retry
from app01.ai_module.conversation_manager import ConversationManager
from app01.ai_module.task_scheduler import TaskScheduler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_database_configuration():
    """测试数据库配置"""
    print("🔧 测试数据库配置...")
    
    try:
        # 检查数据库配置
        config = connection.settings_dict
        print(f"📊 数据库配置:")
        print(f"  - ENGINE: {config.get('ENGINE')}")
        print(f"  - NAME: {config.get('NAME')}")
        print(f"  - HOST: {config.get('HOST')}")
        print(f"  - PORT: {config.get('PORT')}")
        print(f"  - CONN_MAX_AGE: {config.get('CONN_MAX_AGE')}")
        print(f"  - CONN_HEALTH_CHECKS: {config.get('CONN_HEALTH_CHECKS')}")
        
        options = config.get('OPTIONS', {})
        print(f"  - read_timeout: {options.get('read_timeout')}")
        print(f"  - write_timeout: {options.get('write_timeout')}")
        print(f"  - connect_timeout: {options.get('connect_timeout')}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库配置检查失败: {e}")
        return False


def test_basic_connection():
    """测试基本数据库连接"""
    print("\n🔌 测试基本数据库连接...")
    return test_db_connection()


@db_retry(max_retries=3, delay=1.0, backoff=2.0)
def test_retry_mechanism():
    """测试重试机制"""
    print("\n🔄 测试数据库重试机制...")
    
    try:
        cursor = connection.cursor()
        cursor.execute('SELECT 1 as test_value')
        result = cursor.fetchone()
        print(f"✅ 重试机制测试成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 重试机制测试失败: {e}")
        raise


async def test_conversation_manager():
    """测试会话管理器"""
    print("\n💬 测试会话管理器...")
    
    try:
        manager = ConversationManager()
        
        # 测试创建会话
        result = await manager.get_or_create_session(
            user_id="test_user_db_fix",
            group_id="test_group_db_fix"
        )
        
        if result['success']:
            session_id = result['session']['session_id']
            print(f"✅ 会话创建成功: {session_id}")
            
            # 测试获取会话
            get_result = await manager.get_session(session_id)
            if get_result['success']:
                print(f"✅ 会话获取成功")
                return True
            else:
                print(f"❌ 会话获取失败: {get_result.get('error')}")
                return False
        else:
            print(f"❌ 会话创建失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 会话管理器测试异常: {e}")
        return False


async def test_task_scheduler():
    """测试任务调度器"""
    print("\n📅 测试任务调度器...")
    
    try:
        scheduler = TaskScheduler()
        
        # 测试执行待处理任务
        result = await scheduler.execute_pending_tasks()
        
        if result['success']:
            print(f"✅ 任务调度器测试成功: {result['message']}")
            return True
        else:
            print(f"❌ 任务调度器测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 任务调度器测试异常: {e}")
        return False


def test_connection_timeout_simulation():
    """模拟连接超时情况"""
    print("\n⏱️ 模拟连接超时测试...")
    
    try:
        # 强制关闭连接
        if connection.connection is not None:
            connection.close()
            print("🔌 已关闭数据库连接")
        
        # 等待一段时间
        time.sleep(2)
        
        # 尝试重新连接
        cursor = connection.cursor()
        cursor.execute('SELECT NOW() as current_time')
        result = cursor.fetchone()
        print(f"✅ 连接恢复成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 连接超时模拟测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始数据库修复效果测试...")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试数据库配置
    test_results.append(("数据库配置", test_database_configuration()))
    
    # 2. 测试基本连接
    test_results.append(("基本连接", test_basic_connection()))
    
    # 3. 测试重试机制
    try:
        test_results.append(("重试机制", test_retry_mechanism()))
    except Exception:
        test_results.append(("重试机制", False))
    
    # 4. 测试连接超时模拟
    test_results.append(("连接超时模拟", test_connection_timeout_simulation()))
    
    # 5. 测试会话管理器
    test_results.append(("会话管理器", await test_conversation_manager()))
    
    # 6. 测试任务调度器
    test_results.append(("任务调度器", await test_task_scheduler()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据库修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
        return False


if __name__ == "__main__":
    import asyncio
    
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
